from functools import wraps
from django.contrib.admin.views.decorators import staff_member_required
from django.shortcuts import redirect
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

def super_admin_or_staff(view_func):
    """
    Decorator that checks if the user is either a super_admin or staff member.
    Super admin users bypass the staff check.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # Check if user is a super_admin
        if request.user.is_authenticated and request.user.user_type and request.user.user_type.code == 'SUPER_ADMIN':
            # Super admin bypass - allow access
            return view_func(request, *args, **kwargs)
        
        # If not a super_admin, apply the standard staff_member_required check
        return staff_member_required(view_func)(request, *args, **kwargs)
    
    return _wrapped_view 

def it_group_required(view_func):
    """
    Decorator that checks if the user is in the IT group.
    Redirects to main page with an error message if not.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # Check if user is authenticated and in the IT group
        if request.user.is_authenticated and "IT" in [group.name for group in request.user.groups.all()]:
            # User is in IT group - allow access
            return view_func(request, *args, **kwargs)
        
        # Not in IT group - redirect with message
        messages.error(request, _('You do not have permission to access this page.'))
        return redirect('website:main')
    
    return _wrapped_view 