{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Resources Management" %}{% endblock %}

{% block extra_css %}
<style>
    /* Base styles for select element */
    select {
        background-color: #1a2542 !important;
        color: white !important;
        appearance: none;
        background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3E%3Cpath stroke='%23ffffff' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='M6 8l4 4 4-4'/%3E%3C/svg%3E");
        background-position: right 0.5rem center;
        background-repeat: no-repeat;
        background-size: 1.5em 1.5em;
        padding-right: 2.5rem;
    }

    /* Style for dropdown options */
    select option {
        background-color: #1a2542;
        color: white;
        padding: 8px;
    }
    
    /* Style for dropdown hover state */
    select option:hover,
    select option:focus,
    select option:active,
    select option:checked {
        background-color: #2a3b5e !important;
        color: white !important;
    }

    /* Style for select when opened */
    select:focus {
        background-color: #1a2542;
        color: white;
        border-color: #2a51a3;
        box-shadow: 0 0 0 2px rgba(42, 81, 163, 0.2);
    }

    /* Firefox specific styles */
    @-moz-document url-prefix() {
        select {
            color: white !important;
            background-color: #1a2542 !important;
        }
        
        select option {
            background-color: #1a2542 !important;
            color: white !important;
        }
    }

    /* Webkit (Chrome/Safari) specific styles */
    select::-webkit-scrollbar {
        width: 8px;
    }

    select::-webkit-scrollbar-track {
        background: #1a2542;
    }

    select::-webkit-scrollbar-thumb {
        background: #2a3b5e;
        border-radius: 4px;
    }

    select::-webkit-scrollbar-thumb:hover {
        background: #374a77;
    }

    .tab-button.active {
        background-color: rgba(255, 255, 255, 0.1);
        color: white;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            {% trans "Resources Management" %}
        </h1>
    </div>

    <!-- Tabs -->
    <div class="flex space-x-4 mb-6">
        <button class="text-white/70 hover:text-white px-4 py-2 rounded-md transition-colors tab-button active flex items-center" data-tab="rooms">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            {% trans "Rooms" %}
        </button>
        <button class="text-white/70 hover:text-white px-4 py-2 rounded-md transition-colors tab-button flex items-center" data-tab="equipment">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            </svg>
            {% trans "Equipment" %}
        </button>
    </div>

    <!-- Rooms Tab Content -->
    <div id="roomsTab" class="tab-content active">
        <!-- Room Management Section -->
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">{% trans "Room Management" %}</h2>
            <button onclick="openRoomModal()" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                </svg>
                {% trans "Add Room" %}
            </button>
        </div>

        {% include 'website/rooms/_filters.html' %}

        <!-- Rooms Table -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/10">
                    <thead class="bg-white/5">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Room Name" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Type" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Capacity" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Fixed Equipment" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Current Course" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Status" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Actions" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-transparent divide-y divide-white/10">
                        {% for room in rooms %}
                        <tr class="hover:bg-white/5">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-white">{{ room.name }}</div>
                                <div class="text-sm text-white/70">ID: #{{ room.room_id }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-white">{{ room.room_type.name }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                {{ room.capacity }}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex flex-wrap gap-1">
                                    {% for equipment in room.fixed_equipment.all %}
                                        <span class="px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
                                            {{ equipment.code }}
                                        </span>
                                    {% empty %}
                                        <span class="text-sm text-white/70">{% trans "No fixed equipment" %}</span>
                                    {% endfor %}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                {% with current_course=room.current_course %}
                                {% if current_course %}
                                    <div class="text-sm text-white">{{ current_course.name }}</div>
                                    <div class="text-sm text-white/70">
                                        {{ current_course.start_time|time:"g:i A" }} - {{ current_course.end_time|time:"g:i A" }}
                                    </div>
                                {% else %}
                                    <div class="text-sm text-white/70">{% trans "No active course" %}</div>
                                {% endif %}
                                {% endwith %}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                    {% if room.availability.status == 'READY' %}bg-green-100 text-green-800
                                    {% elif room.availability.status == 'BOOKED' %}bg-blue-100 text-blue-800
                                    {% elif room.availability.status == 'BUSY' %}bg-red-100 text-red-800
                                    {% else %}bg-gray-100 text-gray-800{% endif %}">
                                    {{ room.availability.get_status_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <div class="flex justify-end space-x-3">
                                    <a href="{% url 'website:room_detail' room.room_id %}" class="text-primary hover:text-primary/90">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                                        </svg>
                                    </a>
                                    {% comment %} this button is not working with adding equipment if u want to add one go for view room then edit  {% endcomment %}
                                    {% comment %} <button onclick="editRoom({{ room.room_id }})" class="text-primary hover:text-primary/90">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                        </svg>
                                    </button> {% endcomment %}
                                    <button onclick="deleteRoom({{ room.room_id }})" class="text-red-400 hover:text-red-500">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                        </svg>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-white/70">
                                {% trans "No rooms found" %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Equipment Tab Content -->
    <div id="equipmentTab" class="tab-content hidden">
        <!-- Equipment Management Section -->
        <div class="flex justify-between items-center mb-6">
            <h2 class="text-xl font-bold text-white">{% trans "Equipment Management" %}</h2>
            <div class="flex space-x-3">
                <button onclick="openCategoryModal()" class="px-4 py-2 text-sm font-medium text-white bg-green-600 hover:bg-green-700 rounded-md transition-colors">
                    {% trans "Add Category" %}
                </button>
                <button onclick="openBrandModal()" class="px-4 py-2 text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 rounded-md transition-colors">
                    {% trans "Add Brand" %}
                </button>
                <button onclick="openModelModal()" class="px-4 py-2 text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 rounded-md transition-colors">
                    {% trans "Add Model" %}
                </button>
                <button onclick="openEquipmentModal()" class="px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors">
                    {% trans "Add Equipment" %}
                </button>
            </div>
        </div>

        <!-- Equipment Sub-Tabs -->
        <div class="mb-6">
            <div class="border-b border-white/10">
                <nav class="flex space-x-8" aria-label="Equipment Tabs">
                    <button class="equipment-tab-button border-b-2 border-transparent px-1 pb-4 text-sm font-medium text-white/70 hover:text-white hover:border-white/30 whitespace-nowrap active flex items-center" data-tab="equipment-list">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 4a2 2 0 114 0v1a1 1 0 001 1h3a1 1 0 011 1v3a1 1 0 01-1 1h-1a2 2 0 100 4h1a1 1 0 011 1v3a1 1 0 01-1 1h-3a1 1 0 01-1-1v-1a2 2 0 10-4 0v1a1 1 0 01-1 1H7a1 1 0 01-1-1v-3a1 1 0 00-1-1H4a2 2 0 110-4h1a1 1 0 001-1V7a1 1 0 011-1h3a1 1 0 001-1V4z"/>
                        </svg>
                        {% trans "Equipment" %}
                    </button>
                    <button class="equipment-tab-button border-b-2 border-transparent px-1 pb-4 text-sm font-medium text-white/70 hover:text-white hover:border-white/30 whitespace-nowrap flex items-center" data-tab="categories">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                        </svg>
                        {% trans "Categories" %}
                    </button>
                    <button class="equipment-tab-button border-b-2 border-transparent px-1 pb-4 text-sm font-medium text-white/70 hover:text-white hover:border-white/30 whitespace-nowrap flex items-center" data-tab="brands">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 4v12l-4-2-4 2V4M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        {% trans "Brands" %}
                    </button>
                    <button class="equipment-tab-button border-b-2 border-transparent px-1 pb-4 text-sm font-medium text-white/70 hover:text-white hover:border-white/30 whitespace-nowrap flex items-center" data-tab="models">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 5a1 1 0 011-1h14a1 1 0 011 1v2a1 1 0 01-1 1H5a1 1 0 01-1-1V5zM4 13a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H5a1 1 0 01-1-1v-6zM16 13a1 1 0 011-1h2a1 1 0 011 1v6a1 1 0 01-1 1h-2a1 1 0 01-1-1v-6z"/>
                        </svg>
                        {% trans "Models" %}
                    </button>
                </nav>
            </div>
        </div>

        <!-- Equipment List Tab -->
        <div id="equipment-list-tab" class="equipment-tab-content">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Equipment" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Category" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Brand/Model" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Status" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Actions" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-transparent divide-y divide-white/10">
                            {% for equipment in equipment_list %}
                            <tr class="hover:bg-white/5">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">{{ equipment.name }}</div>
                                    <div class="text-sm text-white/70">{{ equipment.code }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ equipment.category }}</div>
                                    <div class="flex items-center mt-1">
                                        <span class="px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800">
                                            {% trans "Stock" %}: {{ equipment.category.stock }}
                                        </span>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ equipment.brand }}</div>
                                    <div class="text-sm text-white/70">{{ equipment.model }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full 
                                        {% if equipment.status == 'AVAILABLE' %}bg-green-100 text-green-800
                                        {% elif equipment.status == 'BUSY' %}bg-yellow-100 text-yellow-800
                                        {% elif equipment.status == 'BOOKED' %}bg-orange-100 text-orange-800
                                        {% elif equipment.status == 'READY' %}bg-emerald-100 text-emerald-800
                                        {% elif equipment.status == 'FIXED' %}bg-indigo-100 text-indigo-800
                                        {% elif equipment.status == 'MAINTENANCE' %}bg-blue-100 text-blue-800
                                        {% else %}bg-red-100 text-red-800{% endif %}">
                                        {{ equipment.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button onclick="editEquipment({{ equipment.equipment_id }})" class="text-primary hover:text-primary/90">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button onclick="deleteEquipment({{ equipment.equipment_id }})" class="text-red-400 hover:text-red-500">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-white/70">
                                    {% trans "No equipment found" %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Categories Tab -->
        <div id="categories-tab" class="equipment-tab-content hidden">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Name" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Stock" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Status" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Actions" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-transparent divide-y divide-white/10">
                            {% for category in categories %}
                            <tr class="hover:bg-white/5">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">{{ category.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800">
                                        {{ category.stock }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if category.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                        {% if category.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button onclick="openCategoryModal(true, {{ category.category_id }})" class="text-primary hover:text-primary/90">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button onclick="deleteCategory({{ category.category_id }})" class="text-red-400 hover:text-red-500">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="4" class="px-6 py-4 text-center text-white/70">
                                    {% trans "No categories found" %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Brands Tab -->
        <div id="brands-tab" class="equipment-tab-content hidden">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Name" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Status" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Actions" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-transparent divide-y divide-white/10">
                            {% for brand in brands %}
                            <tr class="hover:bg-white/5">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">{{ brand.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if brand.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                        {% if brand.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button onclick="openBrandModal(true, {{ brand.brand_id }})" class="text-primary hover:text-primary/90">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button onclick="deleteBrand({{ brand.brand_id }})" class="text-red-400 hover:text-red-500">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="3" class="px-6 py-4 text-center text-white/70">
                                    {% trans "No brands found" %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Models Tab -->
        <div id="models-tab" class="equipment-tab-content hidden">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Name" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Brand" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Stock" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Status" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                    {% trans "Actions" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-transparent divide-y divide-white/10">
                            {% for model in models %}
                            <tr class="hover:bg-white/5">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">{{ model.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">{{ model.brand.name }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">{{ model.stock }}</td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if model.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                        {% if model.is_active %}{% trans "Active" %}{% else %}{% trans "Inactive" %}{% endif %}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button onclick="openModelModal(true, {{ model.model_id }})" class="text-primary hover:text-primary/90">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button onclick="deleteModel({{ model.model_id }})" class="text-red-400 hover:text-red-500">
                                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="5" class="px-6 py-4 text-center text-white/70">
                                    {% trans "No models found" %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

{% include 'website/rooms/modal.html' %}
{% include 'website/rooms/equipment_modal.html' %}
{% include 'website/rooms/category_modal.html' %}
{% include 'website/rooms/brand_modal.html' %}
{% include 'website/rooms/model_modal.html' %}

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        // Function to switch tabs
        function switchTab(tabName) {
            // Update button states
            tabButtons.forEach(btn => {
                if (btn.getAttribute('data-tab') === tabName) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });
            
            // Update content visibility
            tabContents.forEach(content => {
                if (content.id === tabName + 'Tab') {
                    content.classList.remove('hidden');
                    content.classList.add('active');
                } else {
                    content.classList.add('hidden');
                    content.classList.remove('active');
                }
            });

            // Save current tab to localStorage
            localStorage.setItem('activeTab', tabName);
        }

        // Add click event listeners to tab buttons
        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                switchTab(tabName);
            });
        });

        // Restore active tab from localStorage on page load
        const savedTab = localStorage.getItem('activeTab');
        if (savedTab) {
            switchTab(savedTab);
        }

        // Equipment sub-tabs functionality
        const equipmentTabButtons = document.querySelectorAll('.equipment-tab-button');
        const equipmentTabContents = document.querySelectorAll('.equipment-tab-content');

        function switchEquipmentTab(tabName) {
            // Update button states
            equipmentTabButtons.forEach(btn => {
                if (btn.getAttribute('data-tab') === tabName) {
                    btn.classList.add('active');
                    btn.classList.add('border-primary');
                    btn.classList.add('text-white');
                } else {
                    btn.classList.remove('active');
                    btn.classList.remove('border-primary');
                    btn.classList.remove('text-white');
                }
            });
            
            // Update content visibility
            equipmentTabContents.forEach(content => {
                if (content.id === tabName + '-tab') {
                    content.classList.remove('hidden');
                } else {
                    content.classList.add('hidden');
                }
            });

            // Save current equipment tab to localStorage
            localStorage.setItem('activeEquipmentTab', tabName);
        }

        // Add click event listeners to equipment tab buttons
        equipmentTabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const tabName = button.getAttribute('data-tab');
                switchEquipmentTab(tabName);
            });
        });

        // Restore active equipment tab from localStorage
        const savedEquipmentTab = localStorage.getItem('activeEquipmentTab');
        if (savedEquipmentTab) {
            switchEquipmentTab(savedEquipmentTab);
        } else {
            // Default to equipment-list tab
            switchEquipmentTab('equipment-list');
        }
    });

    let isEditing = false;
    let currentRoomId = null;

    function openRoomModal(editing = false, roomId = null) {
        // Set global variables
        window.isEditing = editing;
        window.currentRoomId = roomId;
        
        // Show the modal
        const modal = document.getElementById('roomModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
        
        // Toggle equipment section based on editing mode
        if (typeof toggleEquipmentSection === 'function') {
            toggleEquipmentSection(editing);
        } else {
            // Fallback if function not found
            const equipmentSection = document.getElementById('equipmentSection');
            const modalTitle = document.getElementById('modalTitle');
            
            if (editing) {
                if (equipmentSection) equipmentSection.classList.remove('hidden');
                if (modalTitle) modalTitle.textContent = '{% trans "Edit Room" %}';
            } else {
                if (equipmentSection) equipmentSection.classList.add('hidden');
                if (modalTitle) modalTitle.textContent = '{% trans "Create Room" %}';
            }
        }
    }

    function closeRoomModal() {
        document.getElementById('roomModal').classList.add('hidden');
        document.getElementById('roomForm').reset();
        isEditing = false;
        currentRoomId = null;
    }

    async function handleSave() {
        if (isEditing) {
            await updateRoom(currentRoomId);
        } else {
            await saveRoom();
        }
    }

    async function saveRoom() {
        const form = document.getElementById('roomForm');
        if (!form.checkValidity()) {
            alert('{% trans "Please fill in all required fields" %}');
            return;
        }

        const formData = {
            name: form.querySelector('[name="name"]').value,
            room_type: form.querySelector('[name="room_type"]').value,
            capacity: parseInt(form.querySelector('[name="capacity"]').value),
            description: form.querySelector('[name="description"]').value
        };

        try {
            const response = await fetch('{% url "website:create_room" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            if (response.ok) {
                window.location.reload();
            } else {
                alert(data.message || '{% trans "Failed to create room" %}');
            }
        } catch (error) {
            console.error('Error creating room:', error);
            alert('{% trans "An error occurred while creating the room" %}');
        }
        closeRoomModal();
    }

    async function editRoom(roomId) {
        try {
            const response = await fetch(`{% url 'website:edit_room' room_id=1 %}`.replace('1', roomId), {
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });
            const data = await response.json();
            
            if (response.ok) {
                // Use the new openEditRoomModal function with the room data
                if (typeof openEditRoomModal === 'function') {
                    openEditRoomModal(roomId, data.room);
                } else {
                    // Fallback to old method if the new function isn't available
                    const form = document.getElementById('roomForm');
                    form.querySelector('#name').value = data.room.name || '';
                    form.querySelector('#room_type').value = data.room.room_type || '';
                    form.querySelector('#capacity').value = data.room.capacity || '';
                    form.querySelector('#description').value = data.room.description || '';
                    
                    // Show equipment section
                    const equipmentSection = document.getElementById('equipmentSection');
                    if (equipmentSection) {
                        equipmentSection.classList.remove('hidden');
                    }
                    
                    // Set editing mode
                    window.isEditing = true;
                    window.currentRoomId = roomId;
                    openRoomModal(true, roomId);
                    
                    // Load equipment using the available equipment API
                    if (typeof loadAvailableEquipment === 'function') {
                        loadAvailableEquipment(roomId, data.room.fixed_equipment || []);
                    }
                }
            } else {
                alert('{% trans "Failed to fetch room details" %}');
            }
        } catch (error) {
            console.error('Error fetching room:', error);
            alert('{% trans "An error occurred while fetching room details" %}');
        }
    }

    async function updateRoom(roomId) {
        const form = document.getElementById('roomForm');
        if (!form.checkValidity()) {
            alert('{% trans "Please fill in all required fields" %}');
            return;
        }

        const formData = {
            name: form.querySelector('[name="name"]').value,
            room_type: form.querySelector('[name="room_type"]').value,
            capacity: parseInt(form.querySelector('[name="capacity"]').value),
            description: form.querySelector('[name="description"]').value
        };

        try {
            const response = await fetch(`{% url 'website:edit_room' room_id=1 %}`.replace('1', roomId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            if (response.ok) {
                window.location.reload();
            } else {
                alert(data.message || '{% trans "Failed to update room" %}');
            }
        } catch (error) {
            console.error('Error updating room:', error);
            alert('{% trans "An error occurred while updating the room" %}');
        }
        closeRoomModal();
    }

    async function deleteRoom(roomId) {
        if (!confirm('{% trans "Are you sure you want to delete this room?" %}')) {
            return;
        }

        try {
            const response = await fetch(`{% url 'website:delete_room' room_id=1 %}`.replace('1', roomId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();
            if (response.ok) {
                window.location.reload();
            } else {
                alert(data.message || '{% trans "Failed to delete room" %}');
            }
        } catch (error) {
            console.error('Error deleting room:', error);
            alert('{% trans "An error occurred while deleting the room" %}');
        }
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    async function editEquipment(equipmentId) {
        try {
            // Open the modal first with editing mode
            openEquipmentModal(true, equipmentId);
        } catch (error) {
            console.error('Error:', error);
            alert('{% trans "An error occurred while fetching equipment details" %}');
        }
    }

    async function deleteEquipment(equipmentId) {
        if (!confirm('{% trans "Are you sure you want to delete this equipment?" %}')) {
            return;
        }

        try {
            const response = await fetch(`{% url 'website:equipment_detail' '0' %}`.replace('0', equipmentId), {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();
            
            if (response.ok && data.status === 'success') {
                window.location.reload();
            } else {
                throw new Error(data.message || '{% trans "Failed to delete equipment" %}');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || '{% trans "An error occurred while deleting the equipment" %}');
        }
    }

    async function deleteCategory(categoryId) {
        if (!confirm('{% trans "Are you sure you want to delete this category?" %}')) {
            return;
        }

        try {
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const response = await fetch(`/${languagePrefix}/api/categories/${categoryId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Accept': 'application/json'
                },
                credentials: 'include'
            });

            const data = await response.json();
            if (response.ok) {
                window.location.reload();
            } else {
                throw new Error(data.message || '{% trans "Failed to delete category" %}');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || '{% trans "An error occurred while deleting the category" %}');
        }
    }

    async function deleteBrand(brandId) {
        if (!confirm('{% trans "Are you sure you want to delete this brand?" %}')) {
            return;
        }

        try {
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const response = await fetch(`/${languagePrefix}/api/brands/${brandId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Accept': 'application/json'
                },
                credentials: 'include'
            });

            const data = await response.json();
            if (response.ok) {
                window.location.reload();
            } else {
                throw new Error(data.message || '{% trans "Failed to delete brand" %}');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || '{% trans "An error occurred while deleting the brand" %}');
        }
    }

    async function deleteModel(modelId) {
        if (!confirm('{% trans "Are you sure you want to delete this model?" %}')) {
            return;
        }

        try {
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const response = await fetch(`/${languagePrefix}/api/models/${modelId}/`, {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Accept': 'application/json'
                },
                credentials: 'include'
            });

            const data = await response.json();
            if (response.ok) {
                window.location.reload();
            } else {
                throw new Error(data.message || '{% trans "Failed to delete model" %}');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || '{% trans "An error occurred while deleting the model" %}');
        }
    }
</script>

{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %}