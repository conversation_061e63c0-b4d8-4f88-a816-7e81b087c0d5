{% extends "website/base.html" %}
{% load i18n %}
{% load static %}
{% load website_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
            </svg>
            {{ page_title }}
        </h1>
        <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {% trans "Enroll in New Course" %}
        </a>
    </div>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Pending Surveys Section -->
    <div class="mb-8 bg-blue-900/20 backdrop-blur-md rounded-lg border border-blue-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-blue-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% trans "Course Feedback" %}
                <span id="pending-surveys-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-blue-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:gb_user_surveys' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                {% trans "View All Feedback" %}
            </a>
        </div>

        <div id="pending-surveys-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Questionnaires Section -->
    <div class="mb-8 bg-green-900/20 backdrop-blur-md rounded-lg border border-green-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-green-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                {% trans "Course Questionnaires" %}
                <span id="pending-questionnaires-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-green-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:gb_user_questionnaires' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                {% trans "View All Questionnaires" %}
            </a>
        </div>

        <div id="pending-questionnaires-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Supervisor Approval Requests Section -->
    <div class="mb-8 bg-orange-900/20 backdrop-blur-md rounded-lg border border-orange-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-orange-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                </svg>
                {% trans "Team Approval Requests" %}
                <span id="pending-approval-requests-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-orange-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:supervisor_pending_requests' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-orange-600 text-white hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500">
                {% trans "View All Requests" %}
            </a>
        </div>

        <div id="pending-approval-requests-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% trans "My Course Reservations" %}</h2>
        </div>

        {% if reservations %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Start Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "End Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Sessions" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Reserved On" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for reservation in reservations %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ reservation.course_instance.course.name_en }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.course.get_category_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.start_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.start_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.end_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.end_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.sessions.count }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.created_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {{ reservation.status|status_to_color_class }}">
                                {{ reservation.get_status_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="{% url 'website:course_detail' course_id=reservation.course_instance.course.course_id %}" class="text-primary hover:text-primary/80 mr-4">
                                {% trans "View" %}
                            </a>
                            <button type="button" 
                                    class="text-blue-500 hover:text-blue-400 mr-4 view-attendance-btn" 
                                    data-course-instance-id="{{ reservation.course_instance.instance_id }}"
                                    data-course-name="{{ reservation.course_instance.course.name_en }}">
                                {% trans "Attendance" %}
                            </button>
                            {% if reservation.status == 'UPCOMING' or reservation.status == 'WAITING_LIST' %}
                            <div class="text-xs text-gray-400 mt-1 cancellation-deadline" data-reservation-id="{{ reservation.reservation_id }}">
                                {% trans "Cancellation deadline" %}: <span class="deadline-display">{% trans "Loading..." %}</span>
                            </div>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Reservations" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% trans "You haven't enrolled in any courses yet." %}</p>
            <div class="mt-6">
                <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Browse Available Courses" %}
                </a>
            </div>
        </div>
        {% endif %}
    </div>
    
    {% if reservations %}
    <div class="mt-6 bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-4 text-sm text-gray-300">
        <h3 class="font-medium mb-2">{% trans "Cancellation Policy" %}</h3>
        <p>{% trans "Please note that you can only cancel your reservation before the cancellation deadline, which is calculated based on business days before the course start time. Business days exclude weekends (Friday and Saturday) and holidays." %}</p>
        <p class="mt-2">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
        <p class="mt-2">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
    </div>
    {% endif %}
</div>

<!-- Emergency Cancellation Modal -->
<div id="emergencyCancellationModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="emergencyModalTitle">
                            {% trans "Emergency Cancellation Request" %}
                        </h3>
                        <div class="mt-4 mb-4 p-3 bg-blue-900/30 border border-blue-700/50 rounded-md">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-300">
                                        {% trans "This form is for emergency cancellation requests after the standard cancellation deadline has passed. Please provide a valid reason and supporting documentation if available. Your request will be reviewed by administration." %}
                                    </p>
                                </div>
                            </div>
                        </div>
                        <form id="emergencyCancellationForm" method="POST" action="{% url 'website:gb_emergency_cancellation_request' %}" enctype="multipart/form-data">
                            {% csrf_token %}
                            <input type="hidden" id="emergency_reservation_id" name="reservation_id" value="">
                            
                            <div class="mb-4">
                                <label for="reason" class="block text-sm font-medium text-gray-300">
                                    {% trans "Reason for Cancellation" %} <span class="text-red-500">*</span>
                                </label>
                                <textarea id="reason" name="reason" rows="4" required 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary"></textarea>
                                <p class="mt-1 text-sm text-gray-400">
                                    {% trans "Please explain in detail why you need to cancel after the standard deadline." %}
                                </p>
                            </div>
                            
                            <div class="mb-4">
                                <label for="attachment" class="block text-sm font-medium text-gray-300">
                                    {% trans "Supporting Document" %} <span class="text-gray-400">({% trans "optional" %})</span>
                                </label>
                                <input type="file" id="attachment" name="attachment" 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary">
                                <p class="mt-1 text-sm text-gray-400">
                                    {% trans "Upload any supporting document (e.g., medical certificate)." %}
                                </p>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="submitEmergencyCancellationBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Submit Request" %}
                </button>
                <button type="button" id="closeEmergencyCancellationBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Cancellation Confirmation Modal -->
<div id="cancellationConfirmModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            {% trans "Confirm Cancellation" %}
                        </h3>
                        <div class="mt-4 mb-4 p-3 bg-gray-800 border border-gray-700 rounded-md text-gray-300">
                            <p>{% trans "Please note that you can only cancel your reservation before the cancellation deadline, which is calculated based on business days before the course start time. Business days exclude weekends (Friday and Saturday) and holidays." %}</p>
                            <p class="mt-2">{% trans "For example, if the cancellation period is 3 business days, you must cancel at least 3 business days before the course starts. If the course starts on Monday, and the cancellation period is 3 business days, you must cancel by the previous Tuesday (assuming no holidays in between)." %}</p>
                            <p class="mt-2">{% trans "If you need to cancel after the deadline has passed, please submit an emergency cancellation request." %}</p>
                        </div>
                        <form id="cancellationForm" method="POST" action="{% url 'website:gb_user_reservations_view' %}">
                            {% csrf_token %}
                            <input type="hidden" id="cancellation_reservation_id" name="reservation_id" value="">
                            <input type="hidden" name="cancel_reservation" value="true">
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmCancellationBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm Cancel" %}
                </button>
                <button type="button" id="closeCancellationBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <div class="bg-gray-900 px-6 pt-6 pb-5 sm:p-8 sm:pb-6">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-white" id="attendanceModalTitle">
                                {% trans "Course Attendance" %}
                            </h3>
                            <button type="button" id="closeAttendanceModal" class="text-gray-400 hover:text-white">
                                <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div id="attendanceModalContent">
                            <div class="flex items-center justify-center h-32">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Approval/Rejection Modal -->
<div id="approvalModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="approvalModalTitle">
                            {% trans "Confirm Action" %}
                        </h3>
                        <div class="mt-4" id="approvalModalContent">
                            <p class="text-sm text-gray-300" id="approvalModalMessage">
                                {% trans "Are you sure you want to perform this action?" %}
                            </p>
                        </div>
                        
                        <form id="approvalForm" method="POST" action="">
                            {% csrf_token %}
                            <input type="hidden" id="approval_action" name="action" value="">
                            
                            <div class="mt-4">
                                <label for="approval_notes" class="block text-sm font-medium text-gray-300">
                                    {% trans "Notes" %} <span class="text-gray-400">({% trans "optional" %})</span>
                                </label>
                                <textarea id="approval_notes" name="notes" rows="3" 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary"
                                    placeholder="{% trans 'Add any comments about your decision...' %}"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmApprovalBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm" %}
                </button>
                <button type="button" id="closeApprovalBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Find all cancellation deadline elements
        const deadlineElements = document.querySelectorAll('.cancellation-deadline');
        
        // Function to format the deadline display
        function formatDeadline(hours, minutes, businessDays) {
            if (hours === 0 && minutes === 0) {
                return '{% trans "Deadline has passed" %}';
            }
            
            let result = '';
            if (hours > 0) {
                result += hours + ' {% trans "hours" %}';
            }
            
            if (minutes > 0) {
                if (result) result += ', ';
                result += minutes + ' {% trans "minutes" %}';
            }
            
            return result + ' (' + businessDays + ' {% trans "business days before start" %})';
        }
        
        // Function to fetch and update a single deadline
        function updateDeadline(element) {
            const reservationId = element.getAttribute('data-reservation-id');
            const displayElement = element.querySelector('.deadline-display');
            
            // Make API request
            fetch(`{% url 'website:gb_get_cancellation_deadline' %}?reservation_id=${reservationId}`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        // Format and display the deadline
                        displayElement.textContent = formatDeadline(
                            data.hours_remaining, 
                            data.minutes_remaining, 
                            data.cancellation_deadline_business_days
                        );
                        
                        // Find the parent cell and check if we need to add buttons
                        const cell = element.closest('td');
                        if (cell) {
                            // Check if we already have buttons to avoid duplicates
                            const existingButtons = cell.querySelector('.cancel-btn, .emergency-cancel-btn');
                            if (!existingButtons) {
                                // Create a form container for regular cancellation
                                const formContainer = document.createElement('div');
                                formContainer.className = 'mt-2';
                                
                                // Create the cancellation form
                                const form = document.createElement('form');
                                form.className = 'inline';
                                form.method = 'POST';
                                form.action = '{% url 'website:gb_user_reservations_view' %}';
                                
                                // Add CSRF token
                                const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;
                                const csrfInput = document.createElement('input');
                                csrfInput.type = 'hidden';
                                csrfInput.name = 'csrfmiddlewaretoken';
                                csrfInput.value = csrfToken;
                                form.appendChild(csrfInput);
                                
                                // Add reservation ID
                                const reservationInput = document.createElement('input');
                                reservationInput.type = 'hidden';
                                reservationInput.name = 'reservation_id';
                                reservationInput.value = reservationId;
                                form.appendChild(reservationInput);
                                
                                // Add cancel_reservation flag
                                const actionInput = document.createElement('input');
                                actionInput.type = 'hidden';
                                actionInput.name = 'cancel_reservation';
                                actionInput.value = 'true';
                                form.appendChild(actionInput);
                                
                                // Create cancel button
                                if (data.can_cancel) {
                                    const cancelBtn = document.createElement('button');
                                    cancelBtn.type = 'button';
                                    cancelBtn.className = 'text-red-500 hover:text-red-400 mr-2 cancel-btn';
                                    cancelBtn.textContent = '{% trans "Cancel" %}';
                                    cancelBtn.addEventListener('click', function(e) {
                                        e.preventDefault();
                                        openCancellationConfirmModal(reservationId);
                                    });
                                    form.appendChild(cancelBtn);
                                } else {
                                    // Create emergency cancellation button if deadline has passed
                                    const emergencyBtn = document.createElement('button');
                                    emergencyBtn.type = 'button';
                                    emergencyBtn.className = 'text-orange-500 hover:text-orange-400 emergency-cancel-btn';
                                    emergencyBtn.setAttribute('data-reservation-id', reservationId);
                                    emergencyBtn.textContent = '{% trans "Emergency Cancel" %}';
                                    
                                    // Check if emergency request already exists
                                    if (data.has_emergency_request) {
                                        emergencyBtn.disabled = true;
                                        emergencyBtn.classList.add('opacity-50', 'cursor-not-allowed');
                                        emergencyBtn.title = '{% trans "You have already submitted an emergency cancellation request for this reservation" %}';
                                        
                                        // Add explanatory text
                                        const infoText = document.createElement('div');
                                        infoText.className = 'text-xs text-yellow-500/80 mt-1';
                                        infoText.textContent = '{% trans "You can only submit one emergency cancellation request per reservation" %}';
                                        form.parentNode.insertBefore(infoText, form.nextSibling.nextSibling);
                                    } else {
                                        emergencyBtn.addEventListener('click', function(e) {
                                            e.preventDefault();
                                            openEmergencyCancellationModal(reservationId);
                                        });
                                    }
                                    
                                    form.appendChild(emergencyBtn);
                                }
                                
                                formContainer.appendChild(form);
                                element.parentNode.insertBefore(formContainer, element.nextSibling);
                            }
                        }
                    } else {
                        displayElement.textContent = '{% trans "Error fetching deadline" %}';
                    }
                })
                .catch(error => {
                    console.error('Error fetching cancellation deadline:', error);
                    displayElement.textContent = '{% trans "Error fetching deadline" %}';
                });
        }
        
        // Function to open emergency cancellation modal
        function openEmergencyCancellationModal(reservationId) {
            const modal = document.getElementById('emergencyCancellationModal');
            const reservationIdInput = document.getElementById('emergency_reservation_id');
            
            // Check if button is disabled (meaning a request already exists)
            const btn = document.querySelector(`.emergency-cancel-btn[data-reservation-id="${reservationId}"]`);
            if (btn && btn.disabled) {
                alert('{% trans "You have already submitted an emergency cancellation request for this reservation. You cannot submit multiple requests for the same reservation." %}');
                return;
            }
            
            if (modal && reservationIdInput) {
                reservationIdInput.value = reservationId;
                modal.classList.remove('hidden');
            }
        }
        
        // Function to open cancellation confirmation modal
        function openCancellationConfirmModal(reservationId) {
            const modal = document.getElementById('cancellationConfirmModal');
            const reservationIdInput = document.getElementById('cancellation_reservation_id');
            
            if (modal && reservationIdInput) {
                reservationIdInput.value = reservationId;
                modal.classList.remove('hidden');
            }
        }
        
        // Close emergency cancellation modal
        document.getElementById('closeEmergencyCancellationBtn').addEventListener('click', function() {
            document.getElementById('emergencyCancellationModal').classList.add('hidden');
        });
        
        // Close cancellation confirmation modal
        document.getElementById('closeCancellationBtn').addEventListener('click', function() {
            document.getElementById('cancellationConfirmModal').classList.add('hidden');
        });
        
        // Confirm cancellation and submit form
        document.getElementById('confirmCancellationBtn').addEventListener('click', function() {
            document.getElementById('cancellationForm').submit();
        });
        
        // Submit emergency cancellation form
        document.getElementById('submitEmergencyCancellationBtn').addEventListener('click', function() {
            // Validate reason field
            const reasonField = document.getElementById('reason');
            if (!reasonField.value.trim()) {
                alert('{% trans "Please provide a reason for your cancellation request." %}');
                reasonField.focus();
                return;
            }
            
            // Submit the form
            document.getElementById('emergencyCancellationForm').submit();
        });
        
        // Modify all cancel buttons to use our custom modal
        document.querySelectorAll('button[name="cancel_reservation"]').forEach(button => {
            const form = button.closest('form');
            if (form) {
                form.onsubmit = function(e) {
                    e.preventDefault();
                    const reservationId = form.querySelector('input[name="reservation_id"]').value;
                    openCancellationConfirmModal(reservationId);
                    return false;
                };
            }
        });
        
        // Update all deadline elements
        deadlineElements.forEach(updateDeadline);
        
        // Set up a timer to update deadlines every minute
        setInterval(() => {
            deadlineElements.forEach(updateDeadline);
        }, 60000); // Update every minute

        // Attendance Modal Functions
        const attendanceModal = document.getElementById('attendanceModal');
        const attendanceModalTitle = document.getElementById('attendanceModalTitle');
        const attendanceModalContent = document.getElementById('attendanceModalContent');
        
        // Open attendance modal
        document.querySelectorAll('.view-attendance-btn').forEach(button => {
            button.addEventListener('click', function() {
                const courseInstanceId = this.getAttribute('data-course-instance-id');
                const courseName = this.getAttribute('data-course-name');
                
                // Update modal title
                attendanceModalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - {% trans "Attendance" %}`;
                
                // Show loading state
                attendanceModalContent.innerHTML = `
                    <div class="flex items-center justify-center h-32">
                        <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                    </div>
                `;
                
                // Show modal
                attendanceModal.classList.remove('hidden');
                
                // Fetch attendance data
                fetch(`{% url 'website:user_attendance_api' %}?course_instance_id=${courseInstanceId}`)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`HTTP error! Status: ${response.status}`);
                        }
                        return response.json();
                    })
                    .then(data => {
                        if (data.status === 'success') {
                            if (data.sessions.length === 0) {
                                attendanceModalContent.innerHTML = `
                                    <div class="text-center py-10">
                                        <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-300">{% trans "No Sessions Found" %}</h3>
                                        <p class="mt-1 text-sm text-gray-400">{% trans "There are no sessions associated with this course instance." %}</p>
                                    </div>
                                `;
                                return;
                            }
                            
                            // Create table with sessions
                            let tableHtml = `
                                <div class="overflow-x-auto rounded-lg border border-gray-700" style="max-height: 65vh;">
                                    <table class="min-w-full divide-y divide-gray-700" style="table-layout: fixed;">
                                        <thead class="bg-gray-800">
                                            <tr>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Session ID" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Date" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                    {% trans "Time" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Room" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                    {% trans "Status" %}
                                                </th>
                                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                    {% trans "Attendance" %}
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody class="bg-gray-900 divide-y divide-gray-800">
                            `;
                            
                            data.sessions.forEach(session => {
                                let statusClass, statusLabel;
                                
                                switch(session.status) {
                                    case 'upcoming':
                                        statusClass = 'bg-blue-900/30 text-blue-400 border border-blue-800/50';
                                        statusLabel = '{% trans "Upcoming" %}';
                                        break;
                                    case 'in_progress':
                                        statusClass = 'bg-green-900/30 text-green-400 border border-green-800/50';
                                        statusLabel = '{% trans "In Progress" %}';
                                        break;
                                    case 'completed':
                                        statusClass = 'bg-gray-800/30 text-gray-400 border border-gray-700/50';
                                        statusLabel = '{% trans "Completed" %}';
                                        break;
                                    default:
                                        statusClass = 'bg-gray-800 text-white';
                                        statusLabel = session.status;
                                }
                                
                                let attendanceStatus;
                                if (session.first_half && session.second_half) {
                                    attendanceStatus = `
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400 border border-green-800/50">
                                            <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                            </svg>
                                            {% trans "Attended" %}
                                        </span>
                                    `;
                                } else if (session.first_half || session.second_half) {
                                    attendanceStatus = `
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/30 text-yellow-400 border border-yellow-800/50">
                                            <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                            </svg>
                                            {% trans "Partial" %}
                                        </span>
                                    `;
                                } else {
                                    if (session.status === 'completed') {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/30 text-red-400 border border-red-800/50">
                                                <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                {% trans "Absent" %}
                                            </span>
                                        `;
                                    } else {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800/50 text-gray-400 border border-gray-700/50">
                                                {% trans "Not Started" %}
                                            </span>
                                        `;
                                    }
                                }
                                
                                tableHtml += `
                                    <tr class="hover:bg-gray-800/50">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            ${session.session_id}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            ${session.date}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            ${session.time}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                            ${session.room}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap">
                                            <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${statusClass}">
                                                ${statusLabel}
                                            </span>
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-center">
                                            ${attendanceStatus}
                                        </td>
                                    </tr>
                                `;
                            });
                            
                            tableHtml += `
                                        </tbody>
                                    </table>
                                </div>
                                <div class="mt-5 bg-blue-900/20 border border-blue-800/50 rounded-md p-4 text-sm text-blue-300">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <p class="font-medium text-blue-400 mb-1">{% trans "Attendance Information" %}</p>
                                            <p>{% trans "Attendance is marked by trainers during each session. If you believe there's an error in your attendance record, please contact administration." %}</p>
                                            <p class="mt-2">{% trans "Attendance status key:" %}</p>
                                            <ul class="list-disc list-inside mt-1 ml-2">
                                                <li>{% trans "<strong>Attended</strong>: You were present for the entire session (both first and second half)." %}</li>
                                                <li>{% trans "<strong>Partial</strong>: You were present for only part of the session (either first or second half)." %}</li>
                                                <li>{% trans "<strong>Absent</strong>: You were not present for any part of the session." %}</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            `;
                            
                            attendanceModalContent.innerHTML = tableHtml;
                        } else {
                            attendanceModalContent.innerHTML = `
                                <div class="text-center py-10">
                                    <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                                    <p class="mt-1 text-sm text-gray-400">${data.message || '{% trans "An error occurred while fetching attendance data." %}'}</p>
                                </div>
                            `;
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching attendance data:', error);
                        attendanceModalContent.innerHTML = `
                            <div class="text-center py-10">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "An error occurred while fetching attendance data." %}</p>
                            </div>
                        `;
                    });
            });
        });
        
        // Close attendance modal
        document.getElementById('closeAttendanceModal').addEventListener('click', function() {
            attendanceModal.classList.add('hidden');
        });

        // --- New JS for Surveys and Questionnaires ---

        function fetchPendingSurveys() {
            const container = document.getElementById('pending-surveys-container');
            const countBadge = document.getElementById('pending-surveys-count');
            
            fetch("{% url 'website:gb_pending_surveys_api' %}?detailed=true")
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        countBadge.textContent = data.count;
                        if (data.count > 0) {
                            let html = '<div class="grid grid-cols-1 gap-4">';
                            const itemsToShow = data.surveys.slice(0, 3);
                            itemsToShow.forEach(item => {
                                html += `
                                    <div class="bg-blue-800/30 rounded-lg p-4 border border-blue-700/30 flex justify-between items-center">
                                        <div>
                                            <div class="font-medium text-white">${item.survey.title}</div>
                                            <div class="text-sm text-gray-300">${item.course.name}</div>
                                            <div class="text-xs text-blue-300 mt-1">${item.survey.question_count} ${item.survey.question_count === 1 ? '{% trans "question" %}' : '{% trans "questions" %}'}</div>
                                        </div>
                                        <a href="/gb-surveys/take/${item.survey.survey_id}/${item.reservation.reservation_id}/" class="bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium py-2 px-4 rounded-md shadow-sm">
                                            {% trans "Complete Survey" %}
                                        </a>
                                    </div>
                                `;
                            });
                            html += '</div>';
                            container.innerHTML = html;
                        } else {
                            container.innerHTML = `
                                <div class="text-center py-6">
                                    <svg class="mx-auto h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Feedback" %}</h3>
                                    <p class="mt-1 text-sm text-gray-400">{% trans "You don't have any pending course feedback to complete." %}</p>
                                </div>
                            `;
                        }
                    } else {
                        countBadge.textContent = '0';
                        container.innerHTML = `<p class="text-red-400">{% trans "Error fetching feedback." %}</p>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    countBadge.textContent = '0';
                    container.innerHTML = `<p class="text-red-400">{% trans "Error fetching feedback." %}</p>`;
                });
        }

        function fetchPendingQuestionnaires() {
            const container = document.getElementById('pending-questionnaires-container');
            const countBadge = document.getElementById('pending-questionnaires-count');

            fetch("{% url 'website:gb_pending_questionnaires_api' %}?detailed=true")
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        countBadge.textContent = data.count;
                        if (data.count > 0) {
                            let html = '<div class="grid grid-cols-1 gap-4">';
                            const itemsToShow = data.questionnaires.slice(0, 3);
                            itemsToShow.forEach(item => {
                                html += `
                                    <div class="bg-green-800/30 rounded-lg p-4 border border-green-700/30 flex justify-between items-center">
                                        <div>
                                            <div class="font-medium text-white">${item.questionnaire.title}</div>
                                            <div class="text-sm text-gray-300">${item.course.name}</div>
                                            <div class="text-xs text-green-300 mt-1">${item.questionnaire.question_count} ${item.questionnaire.question_count === 1 ? '{% trans "question" %}' : '{% trans "questions" %}'}</div>
                                        </div>
                                        <a href="/gb-questionnaires/take/${item.questionnaire.questionnaire_id}/${item.reservation.reservation_id}/" class="bg-green-600 hover:bg-green-700 text-white text-sm font-medium py-2 px-4 rounded-md shadow-sm">
                                            {% trans "Complete Questionnaire" %}
                                        </a>
                                    </div>
                                `;
                            });
                            html += '</div>';
                            container.innerHTML = html;
                        } else {
                            container.innerHTML = `
                                <div class="text-center py-6">
                                    <svg class="mx-auto h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Questionnaires" %}</h3>
                                    <p class="mt-1 text-sm text-gray-400">{% trans "You don't have any pending course questionnaires to complete." %}</p>
                                </div>
                            `;
                        }
                    } else {
                        countBadge.textContent = '0';
                        container.innerHTML = `<p class="text-red-400">{% trans "Error fetching questionnaires." %}</p>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    countBadge.textContent = '0';
                    container.innerHTML = `<p class="text-red-400">{% trans "Error fetching questionnaires." %}</p>`;
                });
        }

        function fetchPendingApprovalRequests() {
            const container = document.getElementById('pending-approval-requests-container');
            const countBadge = document.getElementById('pending-approval-requests-count');

            fetch("{% url 'website:supervisor_pending_requests_api' %}?detailed=true")
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        countBadge.textContent = data.count;
                        if (data.count > 0) {
                            let html = '<div class="grid grid-cols-1 gap-4">';
                            const itemsToShow = data.requests.slice(0, 3);
                            itemsToShow.forEach(item => {
                                const requestDate = new Date(item.created_at).toLocaleDateString();
                                const courseStartDate = new Date(item.course.start_date).toLocaleDateString();
                                html += `
                                    <div class="bg-orange-800/30 rounded-lg p-4 border border-orange-700/30">
                                        <div class="flex justify-between items-start">
                                            <div class="flex-1">
                                                <div class="font-medium text-white">${item.employee.name}</div>
                                                <div class="text-sm text-gray-300">${item.course.name}</div>
                                                <div class="text-xs text-orange-300 mt-1">
                                                    {% trans "Course starts" %}: ${courseStartDate} | {% trans "Requested" %}: ${requestDate}
                                                </div>
                                                <div class="text-xs text-gray-400 mt-1">
                                                    {% trans "Available seats" %}: ${item.course.available_seats}
                                                </div>
                                                ${item.request_message ? `<div class="text-xs text-gray-300 mt-2 bg-gray-800/50 p-2 rounded"><strong>{% trans "Message" %}:</strong> ${item.request_message}</div>` : ''}
                                            </div>
                                            <div class="flex space-x-2 ml-4">
                                                <button onclick="openApprovalModal('${item.request_id}', 'approve')" class="bg-green-600 hover:bg-green-700 text-white text-xs font-medium py-1 px-3 rounded-md shadow-sm">
                                                    {% trans "Approve" %}
                                                </button>
                                                <button onclick="openApprovalModal('${item.request_id}', 'reject')" class="bg-red-600 hover:bg-red-700 text-white text-xs font-medium py-1 px-3 rounded-md shadow-sm">
                                                    {% trans "Reject" %}
                                                </button>
                                            </div>
                                        </div>
                                    </div>
                                `;
                            });
                            html += '</div>';
                            container.innerHTML = html;
                        } else {
                            container.innerHTML = `
                                <div class="text-center py-6">
                                    <svg class="mx-auto h-10 w-10 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Requests" %}</h3>
                                    <p class="mt-1 text-sm text-gray-400">{% trans "You don't have any pending approval requests from your team members." %}</p>
                                </div>
                            `;
                        }
                    } else {
                        countBadge.textContent = '0';
                        container.innerHTML = `<p class="text-red-400">{% trans "Error fetching approval requests." %}</p>`;
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    countBadge.textContent = '0';
                    container.innerHTML = `<p class="text-red-400">{% trans "Error fetching approval requests." %}</p>`;
                });
        }

        fetchPendingSurveys();
        fetchPendingQuestionnaires();
        fetchPendingApprovalRequests();
    });

    // Global functions for approval modal
    function openApprovalModal(requestId, action) {
        const modal = document.getElementById('approvalModal');
        const form = document.getElementById('approvalForm');
        const actionInput = document.getElementById('approval_action');
        const titleElement = document.getElementById('approvalModalTitle');
        const messageElement = document.getElementById('approvalModalMessage');
        const confirmBtn = document.getElementById('confirmApprovalBtn');
        const notesTextarea = document.getElementById('approval_notes');
        
        // Set form action URL
        form.action = `{% url 'website:supervisor_process_request' request_id='PLACEHOLDER' %}`.replace('PLACEHOLDER', requestId);
        
        // Set action value
        actionInput.value = action;
        
        // Clear previous notes
        notesTextarea.value = '';
        
        // Update modal content based on action
        if (action === 'approve') {
            titleElement.textContent = '{% trans "Approve Request" %}';
            messageElement.textContent = '{% trans "Are you sure you want to approve this enrollment request?" %}';
            confirmBtn.textContent = '{% trans "Approve" %}';
            confirmBtn.className = 'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm';
        } else if (action === 'reject') {
            titleElement.textContent = '{% trans "Reject Request" %}';
            messageElement.textContent = '{% trans "Are you sure you want to reject this enrollment request?" %}';
            confirmBtn.textContent = '{% trans "Reject" %}';
            confirmBtn.className = 'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm';
        }
        
        // Show modal
        modal.classList.remove('hidden');
    }

    // Close approval modal
    document.getElementById('closeApprovalBtn').addEventListener('click', function() {
        document.getElementById('approvalModal').classList.add('hidden');
    });

    // Confirm approval action
    document.getElementById('confirmApprovalBtn').addEventListener('click', function() {
        document.getElementById('approvalForm').submit();
    });
</script>
{% endblock %} 