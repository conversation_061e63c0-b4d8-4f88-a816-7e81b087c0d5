{% extends "website/base.html" %}
{% load i18n %}
{% load static %}
{% load website_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            {{ page_title }}
        </h1>
        <a href="{% url 'website:supervisor_pending_requests' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            {% trans "Back to Requests" %}
        </a>
    </div>

    <!-- Request Details -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white">{% trans "Enrollment Request Details" %}</h2>
        </div>

        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Employee Information -->
                <div>
                    <h3 class="text-lg font-medium text-white mb-4">{% trans "Employee Information" %}</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Name" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.employee.english_name|default:enrollment_request.employee.full_name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Employee Number" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.employee.employee_number }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Email" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.user.email }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Department" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.employee.department|default:"-" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Position" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.employee.position_name|default:"-" }}</dd>
                        </div>
                    </dl>
                </div>

                <!-- Course Information -->
                <div>
                    <h3 class="text-lg font-medium text-white mb-4">{% trans "Course Information" %}</h3>
                    <dl class="space-y-3">
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Course Name" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.course_instance.course.name_en }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Category" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.course_instance.course.get_category_display }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Start Date" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.course_instance.start_date|date:"M d, Y h:i A" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "End Date" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.course_instance.end_date|date:"M d, Y h:i A" }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-400">{% trans "Available Seats" %}</dt>
                            <dd class="text-sm text-white">{{ enrollment_request.course_instance.available_seats }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Request Information -->
            <div class="mt-6">
                <h3 class="text-lg font-medium text-white mb-4">{% trans "Request Information" %}</h3>
                <dl class="space-y-3">
                    <div>
                        <dt class="text-sm font-medium text-gray-400">{% trans "Request Date" %}</dt>
                        <dd class="text-sm text-white">{{ enrollment_request.created_at|date:"M d, Y h:i A" }}</dd>
                    </div>
                    {% if enrollment_request.request_message %}
                    <div>
                        <dt class="text-sm font-medium text-gray-400">{% trans "Employee Message" %}</dt>
                        <dd class="text-sm text-white bg-gray-800/50 p-3 rounded-md">{{ enrollment_request.request_message }}</dd>
                    </div>
                    {% endif %}
                </dl>
            </div>

            <!-- Action Buttons -->
            <div class="mt-8 flex space-x-4">
                <button onclick="openApprovalModal('{{ enrollment_request.request_id }}', 'approve')" 
                        class="inline-flex items-center px-6 py-3 text-sm font-medium rounded-md bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                    </svg>
                    {% trans "Approve Request" %}
                </button>
                <button onclick="openApprovalModal('{{ enrollment_request.request_id }}', 'reject')" 
                        class="inline-flex items-center px-6 py-3 text-sm font-medium rounded-md bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                    {% trans "Reject Request" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Approval/Rejection Modal (same as in pending_requests.html) -->
<div id="approvalModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="approvalModalTitle">
                            {% trans "Confirm Action" %}
                        </h3>
                        <div class="mt-4" id="approvalModalContent">
                            <p class="text-sm text-gray-300" id="approvalModalMessage">
                                {% trans "Are you sure you want to perform this action?" %}
                            </p>
                        </div>
                        
                        <form id="approvalForm" method="POST" action="">
                            {% csrf_token %}
                            <input type="hidden" id="approval_action" name="action" value="">
                            
                            <div class="mt-4">
                                <label for="approval_notes" class="block text-sm font-medium text-gray-300">
                                    {% trans "Notes" %} <span class="text-gray-400">({% trans "optional" %})</span>
                                </label>
                                <textarea id="approval_notes" name="notes" rows="3" 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary"
                                    placeholder="{% trans 'Add any comments about your decision...' %}"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmApprovalBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm" %}
                </button>
                <button type="button" id="closeApprovalBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Global functions for approval modal (same as in pending_requests.html)
        window.openApprovalModal = function(requestId, action) {
            const modal = document.getElementById('approvalModal');
            const form = document.getElementById('approvalForm');
            const actionInput = document.getElementById('approval_action');
            const titleElement = document.getElementById('approvalModalTitle');
            const messageElement = document.getElementById('approvalModalMessage');
            const confirmBtn = document.getElementById('confirmApprovalBtn');
            const notesTextarea = document.getElementById('approval_notes');
            
            // Set form action URL
            form.action = `{% url 'website:supervisor_process_request' request_id='PLACEHOLDER' %}`.replace('PLACEHOLDER', requestId);
            
            // Set action value
            actionInput.value = action;
            
            // Clear previous notes
            notesTextarea.value = '';
            
            // Update modal content based on action
            if (action === 'approve') {
                titleElement.textContent = '{% trans "Approve Request" %}';
                messageElement.textContent = '{% trans "Are you sure you want to approve this enrollment request?" %}';
                confirmBtn.textContent = '{% trans "Approve" %}';
                confirmBtn.className = 'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm';
            } else if (action === 'reject') {
                titleElement.textContent = '{% trans "Reject Request" %}';
                messageElement.textContent = '{% trans "Are you sure you want to reject this enrollment request?" %}';
                confirmBtn.textContent = '{% trans "Reject" %}';
                confirmBtn.className = 'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm';
            }
            
            // Show modal
            modal.classList.remove('hidden');
        };

        // Close approval modal
        document.getElementById('closeApprovalBtn').addEventListener('click', function() {
            document.getElementById('approvalModal').classList.add('hidden');
        });

        // Confirm approval action
        document.getElementById('confirmApprovalBtn').addEventListener('click', function() {
            document.getElementById('approvalForm').submit();
        });

        // Close modal when clicking outside
        document.getElementById('approvalModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    });
</script>
{% endblock %} 