from django.db.models.signals import post_save
from django.dispatch import receiver
from django.utils import timezone
import logging

from .models import Reservation, Survey, SurveyResponse, CustomUser, Questionnaire, QuestionnaireResponse, Session

logger = logging.getLogger(__name__)

@receiver(post_save, sender=Reservation)
def create_survey_response_on_completion(sender, instance, created, **kwargs):
    """
    Signal handler to automatically create a pending survey response
    when a reservation status is changed to 'COMPLETED'.
    """
    # Add debug logging to understand what's happening during signal execution
    logger.debug(f"Signal triggered for reservation {instance.reservation_id}, status: {instance.status}, completed_at: {instance.completed_at}, user: {instance.user.username}, is_corporate: {bool(instance.user.company_name)}")
    
    # Only process when a reservation is updated (not created) and status is COMPLETED
    if not created and instance.status == 'COMPLETED':
        try:
            # If completed_at is not set, set it now to ensure we meet all conditions
            if not instance.completed_at:
                instance.completed_at = timezone.now()
                instance.save(update_fields=['completed_at'])
                logger.debug(f"Set missing completed_at for reservation {instance.reservation_id}")
                # Will trigger this signal again with completed_at set, so return
                return
            
            # Don't create duplicates
            existing_response = SurveyResponse.objects.filter(
                reservation=instance,
                user=instance.user
            ).exists()
            
            if existing_response:
                logger.debug(f"Survey response already exists for reservation {instance.reservation_id}")
                return
            
            # Get the course from the instance
            course = instance.course_instance.course
            course_instance = instance.course_instance
            
            logger.debug(f"Looking for surveys for course instance {course_instance.instance_id}, course {course.name_en}")
            
            # Get surveys directly for this course instance
            active_surveys = Survey.objects.filter(
                course_instance=course_instance,
                is_active=True
            ).order_by('-created_at')
            
            if not active_surveys.exists():
            #     # Try to find surveys based on course category
            #     active_surveys = Survey.objects.filter(
            #         is_active=True,
            #         course_instance__course__category=course.category
            #     ).order_by('-created_at')
                
            #     logger.debug(f"Fallback to category search, found {active_surveys.count()} surveys for category {course.category}")
                
            #     if not active_surveys.exists():
            #         # Last resort: look for any active survey
            #         active_surveys = Survey.objects.filter(is_active=True).order_by('-created_at')
            #         logger.debug(f"Last resort fallback, found {active_surveys.count()} active surveys")
            # else:
                logger.debug(f"Found {active_surveys.count()} surveys for course instance {course_instance.instance_id}")
            
            if active_surveys.exists():
                survey = active_surveys.first()
                
                # Create the pending survey response
                survey_response = SurveyResponse.objects.create(
                    survey=survey,
                    user=instance.user,
                    reservation=instance,
                    status='PENDING',
                    created_at=timezone.now()
                )
                
                logger.info(f"Created pending survey response {survey_response.response_id} " 
                           f"for user {instance.user.id} and reservation {instance.reservation_id}")
                
                # Could add notification to user here if needed
            else:
                logger.warning(f"No active survey found for reservation {instance.reservation_id}")
                
        except Exception as e:
            logger.error(f"Error creating survey response for reservation {instance.reservation_id}: {e}", exc_info=True)

@receiver(post_save, sender=Reservation)
def create_questionnaire_response_on_in_progress(sender, instance, created, **kwargs):
    """
    Signal handler to automatically create a pending questionnaire response
    when a reservation status is changed to 'IN_PROGRESS'.
    """
    # Only process when a reservation is updated (not created) and status is IN_PROGRESS
    if not created and instance.status == 'IN_PROGRESS':
        try:
            # Get the course from the instance
            course = instance.course_instance.course
            course_instance = instance.course_instance
            
            # Try to find appropriate sessions
            sessions = {}
            if course_instance:
                course_sessions = Session.objects.filter(
                    course_instance=course_instance
                )
                # Map sessions by their order/sequence number if possible
                for session in course_sessions:
                    session_idx = list(course_sessions).index(session) + 1
                    sessions[session_idx] = session
            
            # Get active questionnaires for this course
            active_questionnaires = Questionnaire.objects.filter(
                course=course,
                is_active=True
            ).order_by('session_number')
            
            if not active_questionnaires.exists():
                logger.warning(f"No active questionnaires found for course {course.name_en} in reservation {instance.reservation_id}")
                return
                
            # For each active questionnaire, create a pending response if none exists
            for questionnaire in active_questionnaires:
                # Check if a response already exists
                existing_response = QuestionnaireResponse.objects.filter(
                    questionnaire=questionnaire,
                    user=instance.user,
                    course=course,
                    session_number=questionnaire.session_number
                ).exists()
                
                if existing_response:
                    logger.debug(f"Questionnaire response already exists for questionnaire {questionnaire.questionnaire_id} in reservation {instance.reservation_id}")
                    continue
                
                # Try to find the appropriate session for this questionnaire
                session = sessions.get(questionnaire.session_number)
                
                # Create the pending questionnaire response
                questionnaire_response = QuestionnaireResponse.objects.create(
                    questionnaire=questionnaire,
                    user=instance.user,
                    course=course,
                    session_number=questionnaire.session_number,
                    course_instance=course_instance,
                    session=session,
                    status='PENDING_SUBMISSION',
                    created_at=timezone.now()
                )
                
                logger.info(f"Created pending questionnaire response {questionnaire_response.response_id} " 
                           f"for user {instance.user.id} and questionnaire {questionnaire.questionnaire_id}")
                
        except Exception as e:
            logger.error(f"Error creating questionnaire response for reservation {instance.reservation_id}: {e}")

@receiver(post_save, sender=Reservation)
def update_questionnaire_response_on_completion(sender, instance, created, **kwargs):
    """
    Signal handler to automatically update questionnaire responses to PENDING_TRAINER_REVIEW
    when a reservation status is changed to 'COMPLETED'.
    """
    # Only process when a reservation is updated (not created) and status is COMPLETED
    if not created and instance.status == 'COMPLETED':
        try:
            # Get the course from the instance
            course = instance.course_instance.course
            course_instance = instance.course_instance
            
            # Find all questionnaire responses for this user and course instance
            questionnaire_responses = QuestionnaireResponse.objects.filter(
                user=instance.user,
                course=course,
                course_instance=course_instance,
                status__in=['PENDING_SUBMISSION']
            )
            
            if not questionnaire_responses.exists():
                logger.debug(f"No pending questionnaire responses found for reservation {instance.reservation_id}")
                return
            
            # Update all questionnaire responses to COMPLETED_TRAINER_REVIEWED
            count = questionnaire_responses.update(
                status='PENDING_TRAINER_REVIEW',
                completed_at=timezone.now() if not instance.completed_at else instance.completed_at
            )
            
            logger.info(f"Updated {count} questionnaire responses to COMPLETED_TRAINER_REVIEWED for reservation {instance.reservation_id}")
                
        except Exception as e:
            logger.error(f"Error updating questionnaire responses for reservation {instance.reservation_id}: {e}", exc_info=True)
