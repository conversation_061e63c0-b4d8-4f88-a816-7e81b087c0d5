{% load i18n %}

<!-- Category Modal -->
<div id="categoryModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-md mx-4">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-white/10">
            <h3 id="categoryModalTitle" class="text-lg font-medium text-white">{% trans "Add Category" %}</h3>
            <button onclick="closeCategoryModal()" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-4">
            <form id="categoryForm" class="space-y-4">
                <div>
                    <label for="categoryName" class="block text-sm font-medium text-white/70 mb-1">{% trans "Category Name" %} *</label>
                    <input type="text" name="name" id="categoryName" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end p-4 border-t border-white/10 space-x-3">
            <button onclick="closeCategoryModal()" 
                class="px-3 py-1.5 text-sm font-medium text-white/70 hover:text-white bg-white/5 hover:bg-white/10 rounded-md transition-colors">
                {% trans "Cancel" %}
            </button>
            <button onclick="saveCategory()" 
                class="px-3 py-1.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors">
                {% trans "Save Category" %}
            </button>
        </div>
    </div>
</div>

<script>
    let isEditingCategory = false;
    let currentCategoryId = null;

    function openCategoryModal(editing = false, categoryId = null) {
        try {
            isEditingCategory = editing;
            currentCategoryId = categoryId;
            document.getElementById('categoryModal').classList.remove('hidden');
            
            if (editing) {
                document.getElementById('categoryModalTitle').textContent = '{% trans "Edit Category" %}';
                loadCategoryData(categoryId);
            } else {
                document.getElementById('categoryModalTitle').textContent = '{% trans "Add Category" %}';
                document.getElementById('categoryForm').reset();
            }
        } catch (error) {
            console.error('Error in openCategoryModal:', error);
            alert('{% trans "An error occurred while opening the modal" %}');
        }
    }

    function closeCategoryModal() {
        document.getElementById('categoryModal').classList.add('hidden');
        document.getElementById('categoryForm').reset();
        isEditingCategory = false;
        currentCategoryId = null;
    }

    async function loadCategoryData(categoryId) {
        try {
            // Get the current language prefix from the URL or default to '/en'
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const url = `/${languagePrefix}/api/categories/${categoryId}/`;
            console.log('Loading category data from:', url);
            
            const response = await fetch(url, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                credentials: 'include'
            });
            
            if (!response.ok) {
                throw new Error('{% trans "Failed to fetch category details" %}');
            }
            
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            const data = JSON.parse(responseText);
            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to load category details" %}');
            }

            const form = document.getElementById('categoryForm');
            form.querySelector('[name="name"]').value = data.category.name;
        } catch (error) {
            console.error('Error:', error);
            alert('{% trans "An error occurred while fetching category details" %}');
        }
    }

    async function saveCategory() {
        try {
            const form = document.getElementById('categoryForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = {
                name: form.querySelector('[name="name"]').value.trim(),
            };
            
            console.log('Sending category data:', formData);
            const csrfToken = getCookie('csrftoken');
            console.log('CSRF Token:', csrfToken);

            // Get the current language prefix from the URL or default to '/en'
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            
            // Determine URL and method based on whether we're editing or creating
            const method = isEditingCategory ? 'PUT' : 'POST';
            const url = isEditingCategory 
                ? `/${languagePrefix}/api/categories/${currentCategoryId}/`
                : `/${languagePrefix}/api/categories/`;
            
            console.log(`Sending ${method} request to:`, url);
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(formData),
                credentials: 'include'  // Changed from 'same-origin' to 'include'
            });

            console.log('Response status:', response.status);
            const responseText = await response.text();  // Get raw response text first
            console.log('Raw response:', responseText);
            
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                throw new Error('Invalid response from server');
            }
            
            console.log('Parsed response data:', data);

            if (!response.ok) {
                throw new Error(data.message || '{% trans "Failed to save category" %}');
            }

            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to save category" %}');
            }

            // Success - close modal and reload page
            console.log('Category saved successfully');
            closeCategoryModal();
            window.location.reload();
        } catch (error) {
            console.error('Error saving category:', error);
            alert(error.message || '{% trans "An error occurred while saving the category" %}');
        }
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script> 