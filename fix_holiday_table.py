import sqlite3
import os
import sys

# Get the database path - update this if needed
DB_PATH = 'db.sqlite3'

if not os.path.exists(DB_PATH):
    print(f"Error: Database file {DB_PATH} not found. Please update the path in this script.")
    sys.exit(1)

print(f"Connecting to database: {DB_PATH}")

try:
    # Connect to the database
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    print("Connected to database successfully")
    
    # First, drop both tables if they exist to start clean
    cursor.execute("DROP TABLE IF EXISTS website_holiday_new")
    print("Dropped website_holiday_new if it existed")
    
    cursor.execute("DROP TABLE IF EXISTS website_holiday")
    print("Dropped website_holiday if it existed")
    
    # Create a fresh holiday table with the correct schema
    cursor.execute("""
    CREATE TABLE "website_holiday" (
      "holiday_id" integer NOT NULL PRIMARY KEY AUTOINCREMENT,
      "name" varchar(200) NOT NULL,
      "description" text NOT NULL,
      "date" date NOT NULL,
      "created_at" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "updated_at" datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
      "is_active" boolean NOT NULL DEFAULT 1
    )
    """)
    conn.commit()
    print("Created holiday table with correct schema")
    
    # Verify the table structure
    cursor.execute("PRAGMA table_info(website_holiday)")
    columns = cursor.fetchall()
    print("\nVerifying table structure:")
    for column in columns:
        print(f"  - {column[1]}: {column[2]}")
    
    print("\nDatabase update completed successfully!")
    
    conn.close()
    
    print("\nNEXT STEPS:")
    print("1. Run this command to mark migrations as applied:")
    print("   python manage.py migrate website --fake")
    print("2. Restart your Django server")
    print("3. Try accessing the holiday admin page again")
    
except Exception as e:
    print(f"Error: {e}")
    sys.exit(1) 