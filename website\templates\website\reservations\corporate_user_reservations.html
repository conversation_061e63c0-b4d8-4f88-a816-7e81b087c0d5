{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Reservations" %} | GB Academy{% endblock %}

{% block content %}
<div class="mx-auto py-5">
    <h1 class="text-3xl font-bold text-white mb-4 ml-4 flex items-center">
        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
        </svg>
        {% trans "My Reservations" %}
    </h1>

    {% if messages %}
    <div class="mb-4 mx-0">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Corporate Info Card -->
    <div id="corporate-info-card" class="mb-4 bg-blue-900/20 backdrop-blur-md rounded-lg border border-blue-800/30 overflow-hidden shadow-lg mx-0">
        <div class="px-6 py-4 border-b border-blue-800/30">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                </svg>
                {% trans "Corporate Information" %}
            </h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="corporate-info-content">
                <!-- Loading state -->
                <div class="col-span-3 flex justify-center py-4">
                    <div class="animate-pulse flex space-x-4">
                        <div class="flex-1 space-y-4 py-1">
                            <div class="h-4 bg-blue-700/40 rounded w-3/4"></div>
                            <div class="space-y-2">
                                <div class="h-4 bg-blue-700/40 rounded"></div>
                                <div class="h-4 bg-blue-700/40 rounded w-5/6"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Surveys Section -->
    <div class="mb-4 bg-indigo-900/20 backdrop-blur-md rounded-lg border border-indigo-800/30 overflow-hidden shadow-lg mx-0">
        <div class="px-6 py-4 border-b border-indigo-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-indigo-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% trans "Course Surveys" %}
                <span id="pending-surveys-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-indigo-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:user_surveys' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-indigo-600 text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                {% trans "View All Surveys" %}
            </a>
        </div>

        <div id="pending-surveys-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-indigo-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Pending Questionnaires Section -->
    <div class="mb-4 bg-green-900/20 backdrop-blur-md rounded-lg border border-green-800/30 overflow-hidden shadow-lg mx-0">
        <div class="px-6 py-4 border-b border-green-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                {% trans "Course Questionnaires" %}
                <span id="pending-questionnaires-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-green-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:user_questionnaires' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                {% trans "View All Questionnaires" %}
            </a>
        </div>

        <div id="pending-questionnaires-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-green-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg mx-0">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% trans "My Course Reservations" %}</h2>
            <div class="flex space-x-2">
                <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Enroll in New Course" %}
                </a>
            </div>
        </div>

        <div id="reservations-container">
            <!-- Loading state -->
            <div id="loading-indicator" class="p-8">
                <div class="flex flex-col items-center justify-center">
                    <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
                    <p class="mt-4 text-white">{% trans "Loading your reservations..." %}</p>
                </div>
            </div>

            <!-- Reservations table will be populated by JavaScript -->
            <div id="reservations-table-container" class="hidden">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-gray-800/50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "Course" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "Start Date" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "End Date" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "Sessions" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "Reserved On" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "Status" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                                    {% trans "Actions" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody id="reservations-table-body" class="bg-transparent divide-y divide-white/10">
                            <!-- Table rows will be populated by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Empty state -->
            <div id="empty-state" class="hidden px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Reservations" %}</h3>
                <p class="mt-1 text-sm text-gray-400">{% trans "You haven't enrolled in any courses yet." %}</p>
                <div class="mt-6">
                    <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {% trans "Browse Available Courses" %}
                    </a>
                </div>
            </div>

            <!-- Error state -->
            <div id="error-state" class="hidden px-6 py-12 text-center">
                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error Loading Reservations" %}</h3>
                <p id="error-message" class="mt-1 text-sm text-red-400">{% trans "An error occurred while loading your reservations." %}</p>
                <div class="mt-6">
                    <button id="retry-button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"/>
                        </svg>
                        {% trans "Retry" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add a hidden form for reservation cancellation -->
<form id="cancelReservationForm" method="POST" action="" style="display: none;">
    {% csrf_token %}
    <input type="hidden" name="cancel_reservation" value="true">
</form>

<!-- Cancellation Request Modal -->
<div id="cancellationRequestModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-middle bg-blue-950 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-2xl sm:w-full relative mx-auto">
            <form id="cancellationRequestForm" method="POST">
                {% csrf_token %}
                <input type="hidden" id="request_reservation_id" name="reservation_id" value="">
                
                <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-white" id="modalTitle">
                                {% trans "Request Reservation Cancellation" %}
                            </h3>
                            <div class="mt-6 space-y-4">
                                <div>
                                    <label for="reason" class="block text-sm font-medium text-gray-300">{% trans "Reason for cancellation" %} *</label>
                                    <textarea id="reason" name="reason" rows="4" class="mt-1 block w-full bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary" required></textarea>
                                    <p class="mt-1 text-sm text-gray-400">{% trans "Please explain why you need to cancel this reservation. Your request will be reviewed by your corporate admin." %}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-900/80 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="button" id="submitCancellationBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                        {% trans "Submit Request" %}
                    </button>
                    <button type="button" onclick="closeRequestModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-transparent text-base font-medium text-gray-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        {% trans "Cancel" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Reservation Details Modal -->
<div id="reservationDetailsModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-16 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-middle bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:max-w-lg sm:w-full relative mx-auto">
            <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="modalCourseTitle">
                            {% trans "Course Details" %}
                        </h3>
                        <div class="mt-6 space-y-4">
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <p class="text-sm text-gray-400">{% trans "Date Range" %}</p>
                                    <p class="text-white font-medium" id="modalDateRange">-</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">{% trans "Time" %}</p>
                                    <p class="text-white font-medium" id="modalTimeRange">-</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">{% trans "Sessions" %}</p>
                                    <p class="text-white font-medium" id="modalSessions">-</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">{% trans "Status" %}</p>
                                    <p class="text-white font-medium" id="modalStatus">-</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">{% trans "Category" %}</p>
                                    <p class="text-white font-medium" id="modalCategory">-</p>
                                </div>
                                <div>
                                    <p class="text-sm text-gray-400">{% trans "Reserved On" %}</p>
                                    <p class="text-white font-medium" id="modalReservationDate">-</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-blue-800/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="closeViewModal()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <div class="bg-gray-900 px-6 pt-6 pb-5 sm:p-8 sm:pb-6">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-white" id="attendanceModalTitle">
                                {% trans "Course Attendance" %}
                            </h3>
                            <button type="button" id="closeAttendanceModal" class="text-gray-400 hover:text-white">
                                <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div id="attendanceModalContent">
                            <div class="flex items-center justify-center h-32">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Helper function to escape HTML
        function escapeHtml(unsafe) {
            return unsafe
                .replace(/&/g, "&amp;")
                .replace(/</g, "&lt;")
                .replace(/>/g, "&gt;")
                .replace(/"/g, "&quot;")
                .replace(/'/g, "&#039;");
        }
        
        // Variables to store reservation and corporate data
        let reservations = [];
        let corporateData = null;
        let availableCourses = [];
        
        // Get reservation data on page load
        getReservationData();
        
        // Setup event handlers
        const cancelRequestBtn = document.getElementById('cancelRequestButton');
        if (cancelRequestBtn) {
            cancelRequestBtn.addEventListener('click', submitCancellationRequest);
        }
        
        const closeCancelModalBtn = document.getElementById('closeCancellationModal');
        if (closeCancelModalBtn) {
            closeCancelModalBtn.addEventListener('click', closeRequestModal);
        }
        
        // Setup event listener for the submission button
        const submitBtn = document.getElementById('submitCancellationBtn');
        if (submitBtn) {
            submitBtn.addEventListener('click', submitCancellationRequest);
        }
        
        const cancelRequestForm = document.getElementById('cancellationRequestForm');
        if (cancelRequestForm) {
            cancelRequestForm.addEventListener('submit', function(e) {
                // Prevent default form submission to avoid duplicate submissions
                e.preventDefault();
                submitCancellationRequest();
            });
        }
        
        // Function to fetch reservation data using the API
        function getReservationData() {
            fetch('/api/corporate-user-reservations/')
                .then(response => response.json())
            .then(data => {
                    if (data.status === 'success') {
                        reservations = data.reservations;
                        corporateData = data.corporate;
                        availableCourses = data.available_courses || [];
                
                        // Update UI with the fetched data
                        updateReservationsTable();
                        updateCorporateInfo();
                } else {
                        // Handle error
                        console.error('Error fetching reservation data:', data.message);
                }
            })
            .catch(error => {
                    console.error('Error fetching reservation data:', error);
                });
        }
                
        // Function to update the reservations table
        function updateReservationsTable() {
                // Hide loading indicator
            const loadingIndicator = document.getElementById('loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.classList.add('hidden');
            }
                
            // Get table container and empty state elements
            const tableContainer = document.getElementById('reservations-table-container');
            const emptyState = document.getElementById('empty-state');
            const tableBody = document.getElementById('reservations-table-body');
            
            if (!tableBody) {
                console.error('Reservations table body element not found');
                return;
            }
            
            tableBody.innerHTML = '';
            
            if (reservations.length === 0) {
                // Show empty state and hide table
                if (emptyState) emptyState.classList.remove('hidden');
                if (tableContainer) tableContainer.classList.add('hidden');
                return;
            }
            
            // Show table and hide empty state
            if (tableContainer) tableContainer.classList.remove('hidden');
            if (emptyState) emptyState.classList.add('hidden');
            
            // Create rows for each reservation
            reservations.forEach(reservation => {
                const row = document.createElement('tr');
                // Match screenshot row style: Darker blue, less opacity
                row.className = 'bg-blue-900/70 border-b border-blue-800/50 hover:bg-blue-800/60 transition-colors duration-150'; 
                
                // Format dates and times to match screenshot (e.g., May 13, 2025 09:00 AM)
                const startDateFormatted = new Date(reservation.course_instance.start_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                const startTimeFormatted = new Date(reservation.course_instance.start_date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });
                const endDateFormatted = new Date(reservation.course_instance.end_date).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                const endTimeFormatted = new Date(reservation.course_instance.end_date).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });
                const createdAtFormatted = new Date(reservation.created_at).toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
                const createdAtTimeFormatted = new Date(reservation.created_at).toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });

                row.innerHTML = `
                    <td class="px-6 py-3 whitespace-nowrap text-sm text-white">
                        <div class="font-medium">${reservation.course.name}</div>
                        <div class="text-xs text-gray-400">${reservation.course.category}</div>
                    </td>
                    <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-300">
                        <div>${startDateFormatted}</div>
                        <div class="text-xs">${startTimeFormatted}</div>
                    </td>
                    <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-300">
                        <div>${endDateFormatted}</div>
                        <div class="text-xs">${endTimeFormatted}</div>
                    </td>
                    <td class="px-6 py-3 whitespace-nowrap text-sm text-center text-gray-300">${reservation.course_instance.sessions_count || '1'}</td>
                    <td class="px-6 py-3 whitespace-nowrap text-sm text-gray-300">
                        <div>${createdAtFormatted}</div>
                        <div class="text-xs">${createdAtTimeFormatted}</div>
                    </td>
                    <td class="px-6 py-3 whitespace-nowrap text-sm">
                        <span class="px-3 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusClasses(reservation.status)}">
                            ${reservation.status_display}
                        </span>
                    </td>
                    <td class="px-6 py-3 whitespace-nowrap text-right text-sm font-medium space-x-3">
                        <a href="#" onclick="viewReservationDetails('${reservation.reservation_id}'); return false;" class="text-indigo-400 hover:text-indigo-300">{% trans 'View Details' %}</a>
                        <a href="#" onclick="viewAttendance(${reservation.course_instance.id}, '${escapeHtml(reservation.course.name)}'); return false;" class="text-blue-400 hover:text-blue-300">{% trans 'Attendance' %}</a>
                        ${reservation.can_cancel ? `
                            <a href="#" onclick="confirmDeleteReservation('${reservation.reservation_id}'); return false;" class="text-red-400 hover:text-red-300">{% trans 'Cancel' %}</a>
                        ` : ''}
                    </td>
            `;
                
                tableBody.appendChild(row);
            });
        }
        
        // Helper function to get status color classes matching screenshot
        function getStatusClasses(status) {
            switch(status) {
                case 'UPCOMING':
                    return 'bg-green-600/30 text-green-300'; // Green like screenshot
                case 'IN_PROGRESS':
                    return 'bg-blue-600/30 text-blue-300';
                case 'COMPLETED':
                    return 'bg-gray-600/30 text-gray-300';
                case 'CANCELLED':
                    return 'bg-red-600/30 text-red-300'; // Red like screenshot
                case 'WAITING_LIST':
                    return 'bg-yellow-600/30 text-yellow-300';
                case 'WAITING_TO_PAY':
                    // Match screenshot style for Waiting to Pay (brighter text, no strong background)
                    return 'text-purple-300 font-medium'; 
                default:
                    return 'bg-gray-700/30 text-gray-300';
        }
        }
        
        // Function to update corporate information
        function updateCorporateInfo() {
            if (corporateData) {
                const corporateInfoElement = document.getElementById('corporate-info-content');
                if (corporateInfoElement) {
                    corporateInfoElement.innerHTML = `
            <div>
                <p class="text-gray-400 text-sm">{% trans "Corporate Admin" %}</p>
                            <p class="text-white">${corporateData.admin_name || '{% trans "Not specified" %}'}</p>
                            <p class="text-blue-400 text-sm">${corporateData.admin_email || '{% trans "No email available" %}'}</p>
            </div>
            <div>
                <p class="text-gray-400 text-sm">{% trans "Contact" %}</p>
                            <p class="text-white">${corporateData.phone_number || '{% trans "Not specified" %}'}</p>
                            <p class="text-blue-400 text-sm">${corporateData.category_display || '{% trans "No category" %}'}</p>
            </div>
            <div>
                <p class="text-gray-400 text-sm">{% trans "Capacity" %}</p>
                            <p class="text-white">${corporateData.capacity || '—'} {% trans "users" %}</p>
                            <p class="text-blue-400 text-sm">${corporateData.corporate_id ? '{% trans "Corporate ID" %}: ' + corporateData.corporate_id : '{% trans "No ID" %}'}</p>
            </div>
        `;
    }
            }
        }
        
        // Function to view reservation details
        window.viewReservationDetails = function(reservationId) {
            // Find the reservation in our data
            const reservation = reservations.find(res => res.reservation_id === parseInt(reservationId));
            
            if (!reservation) {
                console.error('Reservation not found:', reservationId);
                return;
            }
            
            // Get modal elements - add checks to prevent null references
            const modalEl = document.getElementById('reservationDetailsModal');
            if (!modalEl) {
                console.error('Reservation details modal not found');
                return;
            }
            
            // Populate the modal with reservation details - check each element
            const elements = {
                'modalCourseTitle': reservation.course.name,
                'modalDateRange': `${reservation.course_instance.start_date_formatted} - ${reservation.course_instance.end_date_formatted}`,
                'modalTimeRange': `${reservation.course_instance.start_time_formatted} - ${reservation.course_instance.end_time_formatted}`,
                'modalSessions': reservation.course_instance.sessions_count,
                'modalStatus': reservation.status_display,
                'modalCategory': reservation.course.category,
                'modalReservationDate': reservation.created_at_formatted
            };
            
            // Safely set text content only if elements exist
            for (const [id, value] of Object.entries(elements)) {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = value;
                }
            }
            
            // Show the modal
            modalEl.classList.remove('hidden');
        };
        
        // Function to close the reservation details modal
        window.closeViewModal = function() {
            const modalEl = document.getElementById('reservationDetailsModal');
            if (modalEl) {
                modalEl.classList.add('hidden');
            }
        };
        
        // Function to confirm reservation deletion/cancellation
        window.confirmDeleteReservation = function(reservationId) {
            // Find the reservation
            const reservation = reservations.find(res => res.reservation_id === parseInt(reservationId));
            
            if (!reservation) {
                console.error('Reservation not found:', reservationId);
                return;
            }
            
            // Set the reservation ID in the hidden field
            const hiddenField = document.getElementById('request_reservation_id');
            if (!hiddenField) {
                console.error('Hidden field for reservation ID not found');
                return;
            }
            hiddenField.value = reservationId;
            
            // Set course name in the modal
            const modalTitleEl = document.getElementById('modalTitle');
            if (modalTitleEl) {
                modalTitleEl.textContent = `{% trans "Request Cancellation" %}: ${reservation.course.name}`;
            }
            
            // Show the cancellation request modal
            const modalEl = document.getElementById('cancellationRequestModal');
            if (modalEl) {
                modalEl.classList.remove('hidden');
            }
        };
        
        // Function to close the cancellation request modal
        window.closeRequestModal = function() {
            const modalEl = document.getElementById('cancellationRequestModal');
            if (modalEl) {
                modalEl.classList.add('hidden');
            }
            
            const formEl = document.getElementById('cancellationRequestForm');
            if (formEl) {
                formEl.reset();
            }
        };
        
        // Function to submit the cancellation request
        function submitCancellationRequest() {
            const reasonEl = document.getElementById('reason');
            const reservationIdEl = document.getElementById('request_reservation_id');
            
            if (!reasonEl || !reservationIdEl) {
                console.error('Required form elements not found');
                return;
            }
            
            const reason = reasonEl.value.trim();
            const reservationId = reservationIdEl.value;
            
            if (!reason) {
                alert('{% trans "Please provide a reason for cancellation" %}');
                return;
            }

            // Disable the submit button to prevent double submission
            const submitButton = document.querySelector('#cancellationRequestForm button[type="submit"]');
            if (submitButton) {
                submitButton.disabled = true;
                submitButton.classList.add('opacity-50', 'cursor-not-allowed');
                submitButton.textContent = '{% trans "Submitting..." %}';
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('reservation_id', reservationId);
            formData.append('reason', reason);
            
            // Submit the cancellation request
            fetch('{% url "website:request_cancellation_api" %}', {
                method: 'POST',
                body: formData,
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success message
                    alert(data.message);
                    
                    // Close the modal and refresh data
                    closeRequestModal();
                    getReservationData();
                } else {
                    // Show error message
                    alert(data.message || '{% trans "An error occurred while processing your request" %}');
                    
                    // Re-enable the submit button if there was an error
                    if (submitButton) {
                        submitButton.disabled = false;
                        submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                        submitButton.textContent = '{% trans "Submit Request" %}';
                    }
                }
            })
            .catch(error => {
                console.error('Error submitting cancellation request:', error);
                alert('{% trans "An error occurred while processing your request" %}');
                
                // Re-enable the submit button if there was an error
                if (submitButton) {
                    submitButton.disabled = false;
                    submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
                    submitButton.textContent = '{% trans "Submit Request" %}';
                }
            });
        }
        
        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
        }
    }
            }
            return cookieValue;
        }
        
        // Attendance Modal Functions
        window.viewAttendance = function(courseInstanceId, courseName) {
            const attendanceModal = document.getElementById('attendanceModal');
            const attendanceModalTitle = document.getElementById('attendanceModalTitle');
            const attendanceModalContent = document.getElementById('attendanceModalContent');
            
            if (!attendanceModal || !attendanceModalTitle || !attendanceModalContent) {
                console.error('Attendance modal elements not found');
                return;
            }
            
            // Update modal title
            attendanceModalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - {% trans "Attendance" %}`;
            
            // Show loading state
            attendanceModalContent.innerHTML = `
                <div class="flex items-center justify-center h-32">
                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                </div>
            `;
            
            // Show modal
            attendanceModal.classList.remove('hidden');
            
            // Fetch attendance data
            fetch(`{% url 'website:corporate_user_attendance_api' %}?course_instance_id=${courseInstanceId}`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! Status: ${response.status}`);
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        if (data.sessions.length === 0) {
                            attendanceModalContent.innerHTML = `
                                <div class="text-center py-10">
                                    <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                                    </svg>
                                    <h3 class="mt-2 text-sm font-medium text-gray-300">{% trans "No Sessions Found" %}</h3>
                                    <p class="mt-1 text-sm text-gray-400">{% trans "There are no sessions associated with this course instance." %}</p>
                                </div>
                            `;
                            return;
                        }
                        
                        // Process sessions to ensure they have first_half and second_half properties
                        data.sessions.forEach(session => {
                            // Ensure session has the first_half and second_half properties
                            // If session has an 'attendance' object, extract first_half and second_half from there
                            if (session.attendance) {
                                session.first_half = session.attendance.first_half || false;
                                session.second_half = session.attendance.second_half || false;
                            } else {
                                // Default values if not available
                                session.first_half = session.first_half || false;
                                session.second_half = session.second_half || false;
                            }
                        });
                        
                        // Create table with sessions
                        let tableHtml = `
                            <div class="overflow-x-auto rounded-lg border border-gray-700" style="max-height: 65vh;">
                                <table class="min-w-full divide-y divide-gray-700" style="table-layout: fixed;">
                                    <thead class="bg-gray-800">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Session ID" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Date" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                {% trans "Time" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Room" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 15%;">
                                                {% trans "Status" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider" style="width: 20%;">
                                                {% trans "Attendance" %}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="bg-gray-900 divide-y divide-gray-800">
                        `;
                        
                        data.sessions.forEach(session => {
                            // Status display logic
                            let statusClass, statusLabel;
                            
                            switch(session.status) {
                                case 'upcoming':
                                    statusClass = 'bg-blue-900/30 text-blue-400 border border-blue-800/50';
                                    statusLabel = '{% trans "Upcoming" %}';
                                    break;
                                case 'in_progress':
                                    statusClass = 'bg-green-900/30 text-green-400 border border-green-800/50';
                                    statusLabel = '{% trans "In Progress" %}';
                                    break;
                                case 'completed':
                                    statusClass = 'bg-gray-800/30 text-gray-400 border border-gray-700/50';
                                    statusLabel = '{% trans "Completed" %}';
                                    break;
                                default:
                                    statusClass = 'bg-gray-800 text-white';
                                    statusLabel = session.status;
                            }
                            
                            // Attendance logic 
                            const firstHalfAttended = session.first_half || false;
                            const secondHalfAttended = session.second_half || false;
                            const fullyAttended = firstHalfAttended && secondHalfAttended;
                            const partiallyAttended = firstHalfAttended || secondHalfAttended;
                            
                            let attendanceStatus;
                            if (fullyAttended) {
                                attendanceStatus = `
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-900/30 text-green-400 border border-green-800/50">
                                        <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        {% trans "Attended" %}
                                    </span>
                                `;
                            } else {
                                if (session.status === 'completed') {
                                    if (partiallyAttended) {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-900/30 text-yellow-400 border border-yellow-800/50">
                                                <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01" />
                                                </svg>
                                                {% trans "Partial" %} (${firstHalfAttended ? '1st' : '2nd'})
                                            </span>
                                        `;
                                    } else {
                                        attendanceStatus = `
                                            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-900/30 text-red-400 border border-red-800/50">
                                                <svg class="mr-1 h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                                </svg>
                                                {% trans "Absent" %}
                                            </span>
                                        `;
                                    }
                                } else {
                                    attendanceStatus = `
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-800/50 text-gray-400 border border-gray-700/50">
                                            {% trans "Not Started" %}
                                        </span>
                                    `;
                                }
                            }
                            
                            tableHtml += `
                                <tr class="hover:bg-gray-800/50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        ${session.session_id}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        ${session.date}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        ${session.time}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                        ${session.room}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2.5 py-0.5 inline-flex text-xs leading-5 font-medium rounded-full ${statusClass}">
                                            ${statusLabel}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        ${attendanceStatus}
                                    </td>
                                </tr>
                            `;
                        });
                        
                        tableHtml += `
                                    </tbody>
                                </table>
                            </div>
                            <div class="mt-5 bg-blue-900/20 border border-blue-800/50 rounded-md p-4 text-sm text-blue-300">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                    <div class="ml-3">
                                        <p class="font-medium text-blue-400 mb-1">{% trans "Attendance Information" %}</p>
                                        <p>{% trans "Attendance is marked by trainers during each session. Both first and second half attendance are required to be considered fully attended. If you believe there's an error in your attendance record, please contact administration." %}</p>
                                    </div>
                                </div>
                            </div>
                        `;
                        
                        attendanceModalContent.innerHTML = tableHtml;
                    } else {
                        attendanceModalContent.innerHTML = `
                            <div class="text-center py-10">
                                <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">${data.message || '{% trans "An error occurred while fetching attendance data." %}'}</p>
                            </div>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error fetching attendance data:', error);
                    attendanceModalContent.innerHTML = `
                        <div class="text-center py-10">
                            <svg class="mx-auto h-12 w-12 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                            <p class="mt-1 text-sm text-gray-400">{% trans "An error occurred while fetching attendance data." %}</p>
                        </div>
                    `;
                });
        };
        
        // Close attendance modal
        document.getElementById('closeAttendanceModal').addEventListener('click', function() {
            const modalEl = document.getElementById('attendanceModal');
            if (modalEl) {
                modalEl.classList.add('hidden');
            }
        });

        // Fetch pending surveys
        fetchPendingSurveys();
        
        // Function to fetch and display pending surveys
        function fetchPendingSurveys() {
            fetch('{% url "website:get_pending_surveys_count" %}')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status !== 'success') {
                        throw new Error(data.message || 'Failed to fetch pending surveys');
                    }
                    
                    const pendingCount = data.count;
                    const pendingSurveys = data.surveys;
                    
                    // Update count badge
                    document.getElementById('pending-surveys-count').textContent = pendingCount;
                    
                    // Get container
                    const container = document.getElementById('pending-surveys-container');
                    
                    // Clear loading indicator
                    container.innerHTML = '';
                    
                    if (pendingCount === 0) {
                        // Show no surveys message
                        container.innerHTML = `
                            <div class="text-center py-4">
                                <svg class="mx-auto h-10 w-10 text-indigo-500/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Surveys" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "You have completed all available surveys." %}</p>
                            </div>
                        `;
                    } else {
                        // Create a table to display the surveys
                        const table = document.createElement('div');
                        table.className = 'overflow-x-auto';
                        table.innerHTML = `
                            <table class="min-w-full divide-y divide-indigo-800/30">
                                <thead class="bg-indigo-900/30">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Course" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Completion Date" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Survey" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Actions" %}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-transparent divide-y divide-indigo-800/30" id="surveys-table-body">
                                </tbody>
                            </table>
                        `;
                        
                        container.appendChild(table);
                        const tableBody = document.getElementById('surveys-table-body');
                        
                        // Add up to 3 most recent surveys
                        const surveysToShow = pendingSurveys.slice(0, 3);
                        surveysToShow.forEach(survey => {
                            // Format dates
                            const completionDate = new Date(survey.reservation.completed_at);
                            
                            const row = document.createElement('tr');
                            row.className = 'hover:bg-indigo-900/20';
                            row.innerHTML = `
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">${survey.course.name}</div>
                                    <div class="text-xs text-gray-400">${survey.course.category}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">${completionDate.toLocaleDateString()}</div>
                                    <div class="text-xs text-gray-400">${completionDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-white">${survey.survey.title}</div>
                                    <div class="text-xs text-indigo-400 mt-1">{% trans "Questions" %}: ${survey.survey.question_count}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right">
                                    <a href="${window.location.pathname.includes('/en/') ? '/en' : ''}${window.location.pathname.includes('/ar/') ? '/ar' : ''}/my-surveys/take/${survey.survey.survey_id}/${survey.reservation.reservation_id}/" class="text-indigo-500 hover:text-indigo-400">
                                        {% trans "Take Survey" %}
                                    </a>
                                </td>
                            `;
                            
                            tableBody.appendChild(row);
                        });
                        
                        // If there are more surveys than shown, add a note
                        if (pendingCount > 3) {
                            const moreMsg = document.createElement('div');
                            moreMsg.className = 'text-center py-2 text-indigo-500 text-sm';
                            moreMsg.textContent = `{% trans "and" %} ${pendingCount - 3} {% trans "more pending surveys" %}`;
                            container.appendChild(moreMsg);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching pending surveys:', error);
                    const container = document.getElementById('pending-surveys-container');
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <svg class="mx-auto h-10 w-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error Loading Surveys" %}</h3>
                            <p class="mt-1 text-sm text-red-400">{% trans "Unable to load pending surveys." %}</p>
                        </div>
                    `;
                    document.getElementById('pending-surveys-count').textContent = "!";
                });
        }
        
        // Fetch pending questionnaires
        fetchPendingQuestionnaires();
        
        // Function to fetch and display pending questionnaires
        function fetchPendingQuestionnaires() {
            fetch('{% url "website:get_pending_questionnaires_count" %}?detailed=true')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status !== 'success') {
                        throw new Error(data.message || 'Failed to fetch pending questionnaires');
                    }
                    
                    const pendingCount = data.count;
                    const pendingQuestionnaires = data.questionnaires || [];
                    
                    // Update count badge
                    document.getElementById('pending-questionnaires-count').textContent = pendingCount;
                    
                    // Get container
                    const container = document.getElementById('pending-questionnaires-container');
                    
                    // Clear loading indicator
                    container.innerHTML = '';
                    
                    if (pendingCount === 0 || !pendingQuestionnaires.length) {
                        // Show no questionnaires message
                        container.innerHTML = `
                            <div class="text-center py-4">
                                <svg class="mx-auto h-10 w-10 text-green-500/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Questionnaires" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "You have completed all available questionnaires or don't have any in-progress courses." %}</p>
                            </div>
                        `;
                    } else {
                        // Create a table to display the questionnaires
                        const table = document.createElement('div');
                        table.className = 'overflow-x-auto';
                        table.innerHTML = `
                            <table class="min-w-full divide-y divide-green-800/30">
                                <thead class="bg-green-900/30">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Course" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Session" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Questionnaire" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Actions" %}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-transparent divide-y divide-green-800/30" id="questionnaires-table-body">
                                </tbody>
                            </table>
                        `;
                        
                        container.appendChild(table);
                        const tableBody = document.getElementById('questionnaires-table-body');
                        
                        // Add up to 3 most recent questionnaires
                        const questionnairesToShow = pendingQuestionnaires.slice(0, 3);
                        questionnairesToShow.forEach(questionnaire => {
                            const row = document.createElement('tr');
                            row.className = 'hover:bg-green-900/20';
                            row.innerHTML = `
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">${questionnaire.course.name}</div>
                                    <div class="text-xs text-gray-400">${questionnaire.course.category}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{% trans "Session" %} ${questionnaire.questionnaire.session_number}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-white">${questionnaire.questionnaire.title}</div>
                                    <div class="text-xs text-green-400 mt-1">{% trans "Questions" %}: ${questionnaire.questionnaire.question_count}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right">
                                    <a href="${window.location.pathname.includes('/en/') ? '/en' : ''}${window.location.pathname.includes('/ar/') ? '/ar' : ''}/my-questionnaires/take/${questionnaire.questionnaire.questionnaire_id}/${questionnaire.reservation.reservation_id ? questionnaire.reservation.reservation_id : ''}" class="text-green-500 hover:text-green-400">
                                        {% trans "Take Questionnaire" %}
                                    </a>
                                </td>
                            `;
                            
                            tableBody.appendChild(row);
                        });
                        
                        // If there are more questionnaires than shown, add a note
                        if (pendingCount > 3) {
                            const moreMsg = document.createElement('div');
                            moreMsg.className = 'text-center py-2 text-green-500 text-sm';
                            moreMsg.textContent = `{% trans "and" %} ${pendingCount - 3} {% trans "more pending questionnaires" %}`;
                            container.appendChild(moreMsg);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching pending questionnaires:', error);
                    const container = document.getElementById('pending-questionnaires-container');
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <svg class="mx-auto h-10 w-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error Loading Questionnaires" %}</h3>
                            <p class="mt-1 text-sm text-red-400">{% trans "Unable to load pending questionnaires." %}</p>
                        </div>
                    `;
                    document.getElementById('pending-questionnaires-count').textContent = "!";
                });
        }
    });
</script>
{% endblock %} 