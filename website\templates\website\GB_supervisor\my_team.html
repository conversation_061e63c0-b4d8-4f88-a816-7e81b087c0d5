{% extends "website/base.html" %}
{% load i18n %}
{% load static %}
{% load website_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
            </svg>
            {{ page_title }}
        </h1>
        <a href="{% url 'website:supervisor_pending_requests' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            {% trans "Pending Requests" %}
        </a>
    </div>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Team Overview Stats -->
    {% comment %} <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div class="bg-blue-900/20 backdrop-blur-md rounded-lg border border-blue-800/30 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">{% trans "Total Team Members" %}</dt>
                        <dd class="text-lg font-medium text-white">{{ total_employees }}</dd>
                    </dl>
                </div>
            </div>
        </div> {% endcomment %}

        {% comment %} <div class="bg-green-900/20 backdrop-blur-md rounded-lg border border-green-800/30 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">{% trans "With System Accounts" %}</dt>
                        <dd class="text-lg font-medium text-white">{{ employees_with_accounts|length }}</dd>
                    </dl>
                </div>
            </div>
        </div>

        <div class="bg-orange-900/20 backdrop-blur-md rounded-lg border border-orange-800/30 p-6">
            <div class="flex items-center">
                <div class="flex-shrink-0">
                    <svg class="h-8 w-8 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                    </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                    <dl>
                        <dt class="text-sm font-medium text-gray-400 truncate">{% trans "Without Accounts" %}</dt>
                        <dd class="text-lg font-medium text-white">{{ employees_without_accounts|length }}</dd>
                    </dl>
                </div>
            </div>
        </div> {% endcomment %}
    </div>

    <!-- Team Members with System Accounts -->
    {% if employees_with_accounts %}
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg mb-8">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% trans "Team Members with System Accounts" %}
                <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-green-500 text-white">
                    {{ employees_with_accounts|length }}
                </span>
            </h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Employee" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Department" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Position" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Account Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Recent Requests" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for item in employees_with_accounts %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">
                                            {{ item.employee.english_name|first|default:item.employee.full_name|first|default:"?" }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-white">{{ item.employee.english_name|default:item.employee.full_name }}</div>
                                    <div class="text-xs text-gray-400">{{ item.employee.employee_number }}</div>
                                    <div class="text-xs text-gray-400">{{ item.employee.oracle_email_address }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ item.employee.department|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ item.employee.position_name|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if item.user.is_active %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-900/30 text-green-400 border border-green-800/50">
                                {% trans "Active" %}
                            </span>
                            {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-900/30 text-red-400 border border-red-800/50">
                                {% trans "Inactive" %}
                            </span>
                            {% endif %}
                            {% if item.user.force_password_reset %}
                            <div class="text-xs text-yellow-400 mt-1">{% trans "Password reset required" %}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4">
                            {% if item.recent_requests %}
                            <div class="text-sm text-gray-300">
                                {% for request in item.recent_requests %}
                                <div class="mb-1">
                                    <span class="text-xs {% if request.status == 'PENDING' %}text-orange-400{% elif request.status == 'APPROVED' %}text-green-400{% elif request.status == 'REJECTED' %}text-red-400{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                    <span class="text-xs text-gray-400">- {{ request.created_at|date:"M d" }}</span>
                                </div>
                                {% endfor %}
                            </div>
                            {% else %}
                            <div class="text-xs text-gray-500 italic">{% trans "No recent requests" %}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <a href="mailto:{{ item.employee.oracle_email_address }}" class="text-blue-500 hover:text-blue-400 mr-4">
                                {% trans "Contact" %}
                            </a>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- Team Members without System Accounts -->
    {% if employees_without_accounts %}
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                {% trans "Team Members without System Accounts" %}
                <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-orange-500 text-white">
                    {{ employees_without_accounts|length }}
                </span>
            </h2>
            <p class="mt-2 text-sm text-gray-400">
                {% trans "These team members don't have system accounts yet. Accounts will be created automatically when they request course enrollment." %}
            </p>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Employee" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Department" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Position" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Hire Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Contact" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for employee in employees_without_accounts %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="flex-shrink-0 h-10 w-10">
                                    <div class="h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center">
                                        <span class="text-sm font-medium text-white">
                                            {{ employee.english_name|first|default:employee.full_name|first|default:"?" }}
                                        </span>
                                    </div>
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-white">{{ employee.english_name|default:employee.full_name }}</div>
                                    <div class="text-xs text-gray-400">{{ employee.employee_number }}</div>
                                    <div class="text-xs text-gray-400">{{ employee.oracle_email_address }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ employee.department|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ employee.position_name|default:"-" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">
                                {% if employee.hire_date %}
                                {{ employee.hire_date|date:"M d, Y" }}
                                {% else %}
                                -
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            {% if employee.oracle_email_address %}
                            <a href="mailto:{{ employee.oracle_email_address }}" class="text-blue-500 hover:text-blue-400">
                                {% trans "Email" %}
                            </a>
                            {% else %}
                            <span class="text-gray-500">{% trans "No email" %}</span>
                            {% endif %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}

    <!-- No Team Members Message -->
    {% if not employees_with_accounts and not employees_without_accounts %}
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Team Members Found" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% trans "No employees are currently assigned to you as their supervisor." %}</p>
        </div>
    </div>
    {% endif %}

    <!-- Information Panel -->
    <div class="mt-6 bg-blue-900/20 backdrop-blur-md rounded-lg border border-blue-800/30 p-4 text-sm text-blue-300">
        <h3 class="font-medium mb-2 flex items-center">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            {% trans "Team Management Information" %}
        </h3>
        <ul class="list-disc list-inside space-y-1 text-blue-300/80">
            <li>{% trans "This page shows all employees who report to you according to the Oracle HR system." %}</li>
            <li>{% trans "System accounts are created automatically when employees request course enrollment for the first time." %}</li>
            <li>{% trans "You will receive email notifications when team members submit course enrollment requests." %}</li>
            <li>{% trans "Recent requests show the last 3 approval requests from each team member." %}</li>
        </ul>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add any team management specific JavaScript here
        console.log('Team management page loaded');
    });
</script>
{% endblock %} 