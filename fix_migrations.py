#!/usr/bin/env python
"""Script to fix Django migration records in the database."""
import os
import django

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Now we can import Django models
from django.db import connection

def fix_migrations():
    """Fix the migration records in the database."""
    with connection.cursor() as cursor:
        # Check if the problematic record exists
        cursor.execute(
            "SELECT * FROM django_migrations WHERE app = 'website' AND name = '0019_equipment_equipmentmaintenance_equipmentavailability_and_more';"
        )
        record = cursor.fetchone()
        
        if record:
            print("Found problematic migration record, updating...")
            # Update the record to use our new migration name
            cursor.execute(
                "UPDATE django_migrations SET name = '0019_equipment_fix' WHERE app = 'website' AND name = '0019_equipment_equipmentmaintenance_equipmentavailability_and_more';"
            )
            print("Updated migration record successfully.")
        else:
            print("Problematic migration record not found, checking if we need to insert a new record...")
            
            # Check if the fixed record exists
            cursor.execute(
                "SELECT * FROM django_migrations WHERE app = 'website' AND name = '0019_equipment_fix';"
            )
            fixed_record = cursor.fetchone()
            
            if not fixed_record:
                print("Inserting new migration record...")
                # Insert a record for our fixed migration
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES ('website', '0019_equipment_fix', datetime('now'));"
                )
                print("Inserted new migration record successfully.")
            else:
                print("Fixed migration record already exists, no action needed.")

if __name__ == "__main__":
    fix_migrations()
    print("Migration fix operation completed. Run 'python manage.py migrate' to check if it worked.") 