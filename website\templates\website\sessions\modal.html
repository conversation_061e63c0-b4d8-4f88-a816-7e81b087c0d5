{% load i18n %}

<!-- Session Modal -->
<div id="sessionModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-10 px-4 pb-10 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-6xl sm:w-full mt-10">
            <div class="bg-gray-900 px-4 pt-12 pb-4 sm:p-6 sm:pt-12 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:text-left w-full">
                        <h3 class="text-xl font-bold text-white mb-4 text-center" id="modalTitle">
                            {% trans "Create Session" %}
                        </h3>
                        <!-- Error and Success Messages -->
                        <div id="sessionModalError" class="mt-2 p-2 bg-red-500 text-white rounded hidden"></div>
                        <div id="sessionModalSuccess" class="mt-2 p-2 bg-green-500 text-white rounded hidden"></div>
                        
                        <!-- Note about conflicts -->
                        <div class="p-3 bg-blue-500/20 border border-blue-500 rounded-md mb-4">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <!-- Info Icon -->
                                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="text-sm text-blue-400">{% trans "Sessions cannot be created if there are scheduling conflicts with the trainer or room. Please select dates and times that are available." %}</p>
                                </div>
                            </div>
                        </div>
                        
                        <form id="sessionForm" class="flex flex-wrap -mx-2">
                            <input type="hidden" id="session_id" name="session_id" value="">
                            
                            <!-- Left Side - Form Fields -->
                            <div class="w-full md:w-1/2 px-2 pb-2">
                                <div class="bg-gray-800/30 rounded-lg border border-gray-700/50 h-full">
                                    <div class="flex items-center justify-between bg-gray-800 p-3 rounded-t-lg sticky top-0 z-10">
                                        <h4 class="text-sm font-medium text-gray-300">{% trans "Session Details" %}</h4>
                                    </div>
                                    <div class="p-4 pt-3">
                                    <!-- Conflict Summary -->
                                    <div id="conflictSummary" class="p-3 bg-red-500/20 border border-red-500 rounded-md mb-4 hidden">
                                        <div class="flex items-start">
                                            <div class="flex-shrink-0">
                                                <!-- Warning Icon -->
                                                <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                </svg>
                                            </div>
                                            <div class="ml-3">
                                                <h4 class="text-sm font-medium text-red-400 mb-2">{% trans "Scheduling Conflicts Detected" %}</h4>
                                                <ul id="conflictList" class="text-sm text-red-400 list-disc list-inside">
                                                    <!-- Conflicts will be listed here -->
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="space-y-4 overflow-y-auto max-h-[65vh] pr-2">
                                    <!-- Course field -->
                                    <div>
                                        <label for="course" class="block text-sm font-medium text-gray-300">{% trans "Course" %}</label>
                                        <div class="mb-2 text-xs text-blue-400">
                                            <svg class="inline-block h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                            {% trans "Only courses that have been assigned to trainers are shown." %}
                                        </div>
                                        <select id="course" name="course" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                            <option value="">{% trans "Select a course" %}</option>
                                            {% for course in courses %}
                                            <option value="{{ course.course_id }}" 
                                                data-sessions="{{ course.num_of_sessions }}" 
                                                data-equipment-categories="{{ course.equipment_categories_json|default:'{}' }}"
                                                data-session-room-types="{{ course.session_room_types_json|default:'{}' }}">
                                                {{ course.name_en }}
                                            </option>
                                            {% empty %}
                                            <option value="" disabled>{% trans "No courses with trainers available" %}</option>
                                            {% endfor %}
                                        </select>
                                        <!-- Equipment Categories Info -->
                                        <div id="equipment-categories-info" class="mt-2 hidden p-2 bg-blue-500/20 border border-blue-500/30 rounded-md">
                                            <div class="flex items-start">
                                                <div class="flex-shrink-0">
                                                    <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                    </svg>
                                                </div>
                                                <div class="ml-3">
                                                    <p class="text-sm font-medium text-blue-400 mb-1">{% trans "Required Equipment:" %}</p>
                                                    <div id="equipment-categories-list" class="text-sm text-blue-300"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Capacity field -->
                                    <div>
                                        <label for="capacity" class="block text-sm font-medium text-gray-300">{% trans "Capacity" %}</label>
                                        <div class="mb-2 text-xs text-blue-400">
                                            <svg class="inline-block h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                            {% trans "Enter the number of students that can enroll in this course instance." %}
                                        </div>
                                        <input type="number" id="capacity" name="capacity" value="0" min="0" inputmode="numeric" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    </div>

                                    <!-- Location field -->
                                    <div>
                                        <label for="location" class="block text-sm font-medium text-gray-300">{% trans "Location" %}</label>
                                        <select id="location" name="location" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                            <option value="PHYSICAL">{% trans "Physical" %}</option>
                                            <option value="ONLINE">{% trans "Online" %}</option>
                                        </select>
                                    </div>

                                    <!-- Slot Type field -->
                                    <div>
                                        <label for="slot_type" class="block text-sm font-medium text-gray-300">{% trans "Slot Type" %}</label>
                                        <select id="slot_type" name="slot_type" onchange="handleSlotTypeChange()" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                            <option value="FULL_DAY">{% trans "Full Day (9:00 AM - 4:00 PM)" %}</option>
                                            <option value="MORNING">{% trans "Morning (9:00 AM - 12:00 PM)" %}</option>
                                            <option value="AFTERNOON">{% trans "Afternoon (1:00 PM - 4:00 PM)" %}</option>
                                            <option value="CUSTOM">{% trans "Custom Time" %}</option>
                                        </select>
                                    </div>
                                    
                                    <!-- Cancellation Deadline field -->
                                    <div>
                                        <label for="cancellation_deadline" class="block text-sm font-medium text-gray-300">{% trans "Cancellation Deadline (hours)" %}</label>
                                        <div class="mb-2 text-xs text-blue-400">
                                            <svg class="inline-block h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                            {% trans "Number of hours before course start when students can no longer cancel their reservations." %}
                                        </div>
                                        <input type="number" id="cancellation_deadline" name="cancellation_deadline" value="72" min="0" inputmode="numeric" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                    </div>
                                    
                                    <!-- Published Checkbox field -->
                                    <div>
                                        <div class="flex items-center">
                                            <input type="checkbox" id="published" name="published" class="h-4 w-4 text-primary bg-gray-800 border-gray-600 rounded focus:ring-primary focus:ring-2">
                                            <label for="published" class="ml-2 block text-sm font-medium text-gray-300">{% trans "Published" %}</label>
                                        </div>
                                        <div class="mt-1 text-xs text-blue-400">
                                            <svg class="inline-block h-4 w-4 mr-1" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                            {% trans "Check to make this course instance visible and available for enrollment. Unchecked instances still reserve trainer and room slots." %}
                                        </div>
                                    </div>
                                    
                                    <!-- Course Type and Corporate fields - only shown when creating new session -->
                                    <div id="course_type_container">
                                        <!-- Course Type -->
                                        <div class="mb-4">
                                            <label for="course_type" class="block text-sm font-medium text-gray-300">{% trans "Course Type" %}</label>
                                            <select id="course_type" name="course_type" onchange="handleCourseTypeChange()" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                                <option value="INDIVIDUAL">{% trans "Individual" %}</option>
                                                <option value="CORPORATE">{% trans "Corporate" %}</option>
                                                <option value="GB">{% trans "GB" %}</option>
                                            </select>
                                        </div>
                                        
                                        <!-- Corporate ID - only shown when Corporate is selected -->
                                        <div id="corporate_container" class="mb-4 hidden">
                                            <label for="corporate_id" class="block text-sm font-medium text-gray-300">{% trans "Corporate" %}</label>
                                            <select id="corporate_id" name="corporate_id" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                                <option value="">{% trans "Select a corporate" %}</option>
                                                {% for corporate in corporates %}
                                                <option value="{{ corporate.corporate_id }}">{{ corporate.legal_name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <!-- Hidden elements for maintaining the original behavior -->
                                    <!-- Hidden trainer select - only used for cloning options to per-session selects -->
                                    <div id="main_trainer_container" class="hidden">
                                        <select id="trainer" name="trainer" class="hidden">
                                            <option value="">{% trans "Select a trainer" %}</option>
                                            {% for trainer in trainers %}
                                            <option value="{{ trainer.trainer_id }}">{{ trainer.user.get_full_name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <!-- Hidden room select - only used for cloning options to per-session selects -->
                                    <div id="main_room_container" class="hidden">
                                        <select id="room" name="room" class="hidden">
                                            <option value="">{% trans "Select a room" %}</option>
                                            {% for room in rooms %}
                                            <option value="{{ room.room_id }}" 
                                                data-room-type="{{ room.room_type.room_type_id }}"
                                                data-fixed-equipment="{% for equipment in room.fixed_equipment.all %}{{ equipment.equipment_id }}{% if not forloop.last %},{% endif %}{% endfor %}"
                                                data-fixed-equipment-names="{% for equipment in room.fixed_equipment.all %}{{ equipment.name }}{% if not forloop.last %},{% endif %}{% endfor %}">
                                                {{ room.name }} ({% trans "Capacity" %}: {{ room.capacity }})
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <!-- Equipment section is now per-session, so we hide this -->
                                    <div id="main_equipment_container" class="hidden">
                                    </div>
                                </div>
                                </div>
                                </div>
                            </div>
                            
                            <!-- Right Side - Session Dates -->
                            <div class="w-full md:w-1/2 px-2 md:border-l border-gray-700 md:pl-4 h-full">
                                <!-- Session Dates Container -->
                                <div id="sessionDatesContainer" class="space-y-4 h-full">
                                    <div class="flex items-center justify-between bg-gray-800 p-3 rounded-t-lg sticky top-0 z-10">
                                        <h4 class="text-sm font-medium text-gray-300">{% trans "Session Dates" %}</h4>
                                        <span id="sessionCount" class="text-sm text-gray-400 bg-gray-700 px-2 py-1 rounded-md"></span>
                                    </div>
                                    <div id="sessionDates" class="space-y-4 max-h-[65vh] overflow-y-auto pr-2 bg-gray-800/30 p-3 rounded-b-lg">
                                        <!-- Session dates will be added here dynamically -->
                                    </div>
                                </div>
                            </div>
                        </form>
                        <div class="bg-gray-800 px-4 py-3 sm:px-6 flex justify-end mt-4">
                            <button type="button" data-action="close-session-modal" class="inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:w-auto sm:text-sm mr-3">
                                {% trans "Cancel" %}
                            </button>
                            <button type="button" id="saveSessionBtn" class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:w-auto sm:text-sm">
                                {% trans "Save" %}
                            </button>
                        </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Global variables
let busySlots = [];
let selectedDates = [];
let requiredSessions = 0;
let isSubmitting = false;
let isEditing = false;
let equipmentConflicts = [];
let holidaysData = [];
let holidaysLoaded = false;

// Function to check if a date is a weekend
function isWeekend(date) {
    // Get day of week (5 is Friday, 6 is Saturday)
    const day = date.getDay();
    // Return true if day is Saturday (6) or Friday (5)
    return day === 5 || day === 6;
}

// Function to load holidays from the server
function loadHolidays() {
    if (window.holidaysLoaded) return Promise.resolve(window.holidaysData);
    
    return fetch('/api/holidays/get/')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                window.holidaysData = data.holidays.map(h => h.date.split('T')[0]); // Extract just the date part
                window.holidaysLoaded = true;
                return window.holidaysData;
            } else {
                console.error('Error loading holidays:', data.message);
                return [];
            }
        })
        .catch(error => {
            console.error('Error fetching holidays:', error);
            return [];
        });
}

// Function to check if a date is a holiday
function isHoliday(dateObj) {
    if (!window.holidaysLoaded) return false;
    
    const dateStr = dateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    return window.holidaysData.includes(dateStr);
}

// Function to display warning for weekend/holiday dates
function showDateWarning(dateInput, dateObj, index) {
    // Check if date warning container already exists
    let warningContainer = document.getElementById(`date_warning_${index}`);
    
    // If no warning container exists, create one
    if (!warningContainer) {
        warningContainer = document.createElement('div');
        warningContainer.id = `date_warning_${index}`;
        warningContainer.className = 'mt-1 p-2 bg-yellow-500/20 border border-yellow-500 rounded-md text-sm text-yellow-600';
        
        // Insert after the date input container
        const dateInputContainer = dateInput.parentElement;
        if (dateInputContainer && dateInputContainer.parentElement) {
            const parentDiv = dateInputContainer.parentElement;
            parentDiv.insertBefore(warningContainer, dateInputContainer.nextSibling);
        }
    }
    
    // Set warning text based on what's detected
    if (isWeekend(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a weekend day (Friday or Saturday)" %}';
        warningContainer.classList.remove('hidden');
    } else if (isHoliday(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a holiday" %}';
        warningContainer.classList.remove('hidden');
    } else {
        warningContainer.classList.add('hidden');
    }
}

// Load holidays when the page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize global holiday variables if they don't exist
    if (typeof window.holidaysData === 'undefined') {
        window.holidaysData = [];
    }
    if (typeof window.holidaysLoaded === 'undefined') {
        window.holidaysLoaded = false;
    }
    
    loadHolidays();
});

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Add event listeners for modal controls
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-action="open-session-modal"]')) {
            modalOpenSession();
        } else if (e.target.matches('[data-action="close-session-modal"]')) {
            closeSessionModal();
        }
    });

    // Listen for edit-session events
    document.addEventListener('edit-session', function(e) {
        if (e.detail && e.detail.sessionId) {
            modalOpenSession(e.detail.sessionId);
        }
    });

    // Initialize form event listeners
    const sessionForm = document.getElementById('sessionForm');
    if (sessionForm) {
        const courseSelect = document.getElementById('course');
        const locationSelect = document.getElementById('location');
        const slotTypeSelect = document.getElementById('slot_type');
        const trainerSelect = document.getElementById('trainer');
        const roomSelect = document.getElementById('room');

        // Course change handler
        if (courseSelect) {
            courseSelect.addEventListener('change', function() {
                handleCourseChange();
                // Check availability after course change if we have trainer and room
                if (trainerSelect && trainerSelect.value) {
                    checkAvailability();
                }
            });
        }
        
        // Location change handler
        if (locationSelect) {
            locationSelect.addEventListener('change', function() {
                handleLocationChange();
                // Check availability after location change if we have trainer and room
                if (trainerSelect && trainerSelect.value) {
                    checkAvailability();
                }
            });
        }
        
        // Slot type change handler
        if (slotTypeSelect) {
            slotTypeSelect.addEventListener('change', function() {
                updateSessionTimes();
                // Check availability after slot type change if we have trainer and room
                if (trainerSelect && trainerSelect.value) {
                    checkAvailability();
                }
            });
        }
        
        // Trainer change handler
        if (trainerSelect) {
            trainerSelect.addEventListener('change', function() {
                // Clear conflict warnings when trainer changes
                for (let i = 0; i < requiredSessions; i++) {
                    const warningElement = document.getElementById(`conflict_warning_${i}`);
                    if (warningElement) {
                        warningElement.classList.add('hidden');
                    }
                    
                    // Reset input field styles
                    const dateInput = document.querySelector(`[name="session_date_${i}"]`);
                    if (dateInput) {
                        dateInput.classList.remove('border-red-500');
                        dateInput.classList.add('border-gray-600');
                    }
                }
                
                // Check availability if we have a trainer selected
                if (trainerSelect.value) {
                    checkAvailability();
                } else {
                    // Reset busy slots if no trainer selected
                    busySlots = [];
                    updateConflictSummary();
                }
            });
        }
        
        // Room change handler
        if (roomSelect) {
            roomSelect.addEventListener('change', function() {
                // Clear conflict warnings when room changes
                for (let i = 0; i < requiredSessions; i++) {
                    const warningElement = document.getElementById(`conflict_warning_${i}`);
                    if (warningElement) {
                        warningElement.classList.add('hidden');
                    }
                    
                    // Reset input field styles
                    const dateInput = document.querySelector(`[name="session_date_${i}"]`);
                    if (dateInput) {
                        dateInput.classList.remove('border-red-500');
                        dateInput.classList.add('border-gray-600');
                    }
                }
                
                // Check availability if we have a room selected for physical sessions
                const locationSelect = document.getElementById('location');
                const isPhysical = locationSelect && locationSelect.value === 'PHYSICAL';
                
                if (isPhysical && roomSelect.value && trainerSelect && trainerSelect.value) {
                    checkAvailability();
                } else if (!isPhysical && trainerSelect && trainerSelect.value) {
                    // For online sessions, we only need the trainer
                    checkAvailability();
                }
            });
        }
    }
    
    // Add save button event listener
    const saveButton = document.getElementById('saveSessionBtn');
    if (saveButton) {
        saveButton.addEventListener('click', handleSave);
    }
});

function modalOpenSession(sessionId = null) {
    try {
        // Reset all state
        busySlots = [];
        selectedDates = [];
        requiredSessions = 0;
        isSubmitting = false;
        isEditing = false; // Reset editing flag
        
        // Reset form
        const form = document.getElementById('sessionForm');
        if (form) {
            form.reset();
        }
        
        // Clear session dates
        const container = document.getElementById('sessionDates');
        if (container) {
            container.innerHTML = '';
        }
        
        // Reset session count
        const sessionCount = document.getElementById('sessionCount');
        if (sessionCount) {
            sessionCount.textContent = '0 / 0 sessions selected';
        }
        
        // Hide conflict summary
        const conflictSummary = document.getElementById('conflictSummary');
        if (conflictSummary) {
            conflictSummary.classList.add('hidden');
        }
        
        // Reset save button
        const saveButton = document.getElementById('saveSessionBtn');
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.textContent = '{% trans "Save" %}';
        }
        
        // Hide error and success messages
        hideMessages();
        
        // Show modal
        const modal = document.getElementById('sessionModal');
        if (modal) {
            modal.classList.remove('hidden');
            
            // Setup initial room state
            handleLocationChange();
            
            // Load corporates for dropdown
            const courseTypeSelect = document.getElementById('course_type');
            if (courseTypeSelect && courseTypeSelect.value === 'CORPORATE') {
                handleCourseTypeChange();
            }
            
            // Set modal title
            const modalTitle = document.getElementById('modalTitle');
            if (modalTitle) {
                modalTitle.textContent = sessionId ? '{% trans "Edit Session" %}' : '{% trans "Create Session" %}';
            }
            
            // Show/hide course type container based on edit mode
            const courseTypeContainer = document.getElementById('course_type_container');
            if (courseTypeContainer) {
                if (sessionId) {
                    // Hide in edit mode
                    courseTypeContainer.classList.add('hidden');
                } else {
                    // Show in create mode
                    courseTypeContainer.classList.remove('hidden');
                }
            }
            
            // Reset equipment categories info
            const equipmentCategoriesInfoDiv = document.getElementById('equipment-categories-info');
            if (equipmentCategoriesInfoDiv) {
                equipmentCategoriesInfoDiv.classList.add('hidden');
            }
            
            // If not editing, show all equipment initially
            if (!sessionId) {
                // Get the equipment container
                const equipmentContainer = document.querySelector('.equipment-container .space-y-2, .max-h-48 .space-y-2');
                if (equipmentContainer) {
                    equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Select date to see available equipment" %}</span></div>';
                }
            }
        }

        // If we have a session ID, load the session data
        if (sessionId) {
            isEditing = true; // Set editing mode
            loadSessionData(sessionId);
            
            // Add event listener for course change in edit mode
            const courseSelect = document.getElementById('course');
            if (courseSelect) {
                courseSelect.addEventListener('change', function() {
                    handleCourseChange();
                    updateEquipmentCategoriesDisplay();
                });
            }
        }
    } catch (error) {
        console.error('Error opening modal:', error);
    }
}

// New function to update equipment categories display
function updateEquipmentCategoriesDisplay() {
    const courseSelect = document.getElementById('course');
    const equipmentCategoriesInfoDiv = document.getElementById('equipment-categories-info');
    const equipmentCategoriesList = document.getElementById('equipment-categories-list');
    
    if (!courseSelect || !equipmentCategoriesInfoDiv || !equipmentCategoriesList) {
        return;
    }
    
    // Hide by default
    equipmentCategoriesInfoDiv.classList.add('hidden');
    
    // If no course is selected, return
    if (!courseSelect.value || courseSelect.selectedIndex <= 0) {
        return;
    }
    
    // Get the selected option
    const selectedOption = courseSelect.options[courseSelect.selectedIndex];
    if (!selectedOption) {
        return;
    }
    
    try {
        // Parse the equipment categories JSON data
        const equipmentCategoriesStr = selectedOption.dataset.equipmentCategories || '{}';
        const equipmentCategories = JSON.parse(equipmentCategoriesStr);
        
        // Check if there are any equipment categories
        if (Object.keys(equipmentCategories).length > 0) {
            // Create HTML list of equipment categories
            let categoriesList = '<ul class="list-disc list-inside pl-2">';
            for (const catId in equipmentCategories) {
                const category = equipmentCategories[catId];
                categoriesList += `<li>${category.name} (${category.quantity} ${category.quantity === 1 ? 'item' : 'items'})</li>`;
            }
            categoriesList += '</ul>';
            
            // Display the equipment categories
            equipmentCategoriesList.innerHTML = categoriesList;
            equipmentCategoriesInfoDiv.classList.remove('hidden');
        } else {
            // Show no equipment message
            equipmentCategoriesList.innerHTML = '<p>{% trans "No specific equipment categories required" %}</p>';
            equipmentCategoriesInfoDiv.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error parsing equipment categories:', error);
        equipmentCategoriesInfoDiv.classList.add('hidden');
    }
}

function closeSessionModal() {
    const modal = document.getElementById('sessionModal');
    if (modal) {
        modal.classList.add('hidden');
    }
    hideMessages();
}

function hideMessages() {
    const errorDiv = document.getElementById('sessionModalError');
    const successDiv = document.getElementById('sessionModalSuccess');
    
    if (errorDiv) {
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
    }
    
    if (successDiv) {
        successDiv.textContent = '';
        successDiv.classList.add('hidden');
    }
}

function handleCourseChange() {
    try {
        const courseSelect = document.getElementById('course');
        if (!courseSelect) return;
        
        // Get equipment categories info elements
        const equipmentCategoriesInfoDiv = document.getElementById('equipment-categories-info');
        const equipmentCategoriesList = document.getElementById('equipment-categories-list');
        
        // Hide equipment info by default
        if (equipmentCategoriesInfoDiv) {
            equipmentCategoriesInfoDiv.classList.add('hidden');
        }
        
        // If no course is selected, hide equipment info and return
        if (!courseSelect.value) {
            if (equipmentCategoriesInfoDiv) {
                equipmentCategoriesInfoDiv.classList.add('hidden');
            }
            requiredSessions = 0;
            const sessionCount = document.getElementById('sessionCount');
            if (sessionCount) {
                sessionCount.textContent = '';
            }
            return;
        }
        
        // If we're in edit mode, keep required sessions as 1
        if (isEditing) {
            requiredSessions = 1;
        } else {
            // Normal course session handling for creation mode
            const selectedOption = courseSelect.options[courseSelect.selectedIndex];
            if (!selectedOption) {
                requiredSessions = 0;
            } else {
                requiredSessions = parseInt(selectedOption.dataset.sessions || '0');
                
                // Get session room types
                const sessionRoomTypesStr = selectedOption.dataset.sessionRoomTypes || '{}';
                console.log('Session room types:', sessionRoomTypesStr);
                
                try {
                    // Parse the session room types JSON
                    let sessionRoomTypes = {};
                    if (sessionRoomTypesStr.includes("'")) {
                        // Fix Python-style dictionaries
                        const jsonValue = sessionRoomTypesStr.replace(/'/g, '"');
                        sessionRoomTypes = JSON.parse(jsonValue);
                    } else {
                        sessionRoomTypes = JSON.parse(sessionRoomTypesStr);
                    }
                    console.log('Parsed session room types:', sessionRoomTypes);
                    
                    // Filter rooms for each session based on the session-specific room type
                    for (let i = 1; i <= requiredSessions; i++) {
                        // Try different ways to get the room type ID
                        let roomTypeId = sessionRoomTypes[i.toString()];
                        
                        // If not found, try as number key
                        if (roomTypeId === undefined) {
                            roomTypeId = sessionRoomTypes[i];
                        }
                        
                        // If still not found and this is session 1, try "1" key
                        if (roomTypeId === undefined && i === 1) {
                            roomTypeId = sessionRoomTypes["1"];
                        }
                        
                        if (roomTypeId) {
                            console.log(`Session ${i} requires room type ID:`, roomTypeId);
                        } else {
                            console.log(`No room type requirement found for session ${i}`);
                        }
                    }
                } catch (error) {
                    console.error('Error parsing session room types:', error);
                }
            
                // Show equipment categories information
                if (equipmentCategoriesInfoDiv && equipmentCategoriesList) {
                    try {
                        // Parse the equipment categories JSON data
                        const equipmentCategoriesStr = selectedOption.dataset.equipmentCategories || '{}';
                        const equipmentCategories = JSON.parse(equipmentCategoriesStr);
                        
                        // Check if there are any equipment categories
                        if (Object.keys(equipmentCategories).length > 0) {
                            // Create HTML list of equipment categories
                            let categoriesList = '<ul class="list-disc list-inside pl-2">';
                            for (const catId in equipmentCategories) {
                                const category = equipmentCategories[catId];
                                categoriesList += `<li>${category.name} (${category.quantity} ${category.quantity === 1 ? 'item' : 'items'})</li>`;
                            }
                            categoriesList += '</ul>';
                            
                            // Display the equipment categories
                            equipmentCategoriesList.innerHTML = categoriesList;
                            equipmentCategoriesInfoDiv.classList.remove('hidden');
                        } else {
                            // Check if the course has traditional required_equipment items
                            equipmentCategoriesList.innerHTML = '<p>{% trans "No specific equipment categories required" %}</p>';
                            equipmentCategoriesInfoDiv.classList.remove('hidden');
                        }
                    } catch (error) {
                        console.error('Error parsing equipment categories:', error);
                        equipmentCategoriesInfoDiv.classList.add('hidden');
                    }
                }
            }
        }
        
        const sessionCount = document.getElementById('sessionCount');
        if (sessionCount) {
            if (isEditing) {
                sessionCount.textContent = "Editing Session";
            } else {
                sessionCount.textContent = `0 / ${requiredSessions} sessions selected`;
            }
        }
        
        // Reset dates
        selectedDates = [];
        updateSessionDates();
        
        // Only check availability in creation mode
        if (!isEditing) {
            checkAvailability();
        }
    } catch (error) {
        console.error('Error in handleCourseChange:', error);
    }
}

function handleLocationChange() {
    try {
        const locationSelect = document.getElementById('location');
        
        if (!locationSelect) return;
        
        const location = locationSelect.value;
        const isOnline = location === 'ONLINE';
        
        // Update all per-session room fields based on location
        for (let i = 0; i < requiredSessions; i++) {
            const roomSelect = document.getElementById(`room_${i}`);
            if (roomSelect) {
                if (isOnline) {
                    // For online sessions, disable and hide room selection
                    roomSelect.disabled = true;
                    roomSelect.value = '';
                    roomSelect.closest('div').classList.add('opacity-50');
                } else {
                    // For physical sessions, enable room selection
                    roomSelect.disabled = false;
                    roomSelect.closest('div').classList.remove('opacity-50');
                    
                    // Apply room type filtering if course is selected
                    const courseSelect = document.getElementById('course');
                    if (courseSelect && courseSelect.selectedIndex > 0) {
                        const selectedOption = courseSelect.options[courseSelect.selectedIndex];
                        const sessionRoomTypesStr = selectedOption.dataset.sessionRoomTypes || '{}';
                        
                        try {
                            // Parse the session room types JSON
                            let sessionRoomTypes = {};
                            if (sessionRoomTypesStr.includes("'")) {
                                // Fix Python-style dictionaries
                                const jsonValue = sessionRoomTypesStr.replace(/'/g, '"');
                                sessionRoomTypes = JSON.parse(jsonValue);
                            } else {
                                sessionRoomTypes = JSON.parse(sessionRoomTypesStr);
                            }
                            
                            // Try different ways to get the room type ID
                            let requiredRoomTypeId = sessionRoomTypes[(i + 1).toString()];
                            
                            // If not found, try number key
                            if (requiredRoomTypeId === undefined) {
                                requiredRoomTypeId = sessionRoomTypes[i + 1];
                            }
                            
                            // If still not found, try "1" key as fallback
                            if (requiredRoomTypeId === undefined) {
                                requiredRoomTypeId = sessionRoomTypes["1"];
                            }
                            
                            if (requiredRoomTypeId) {
                                console.log(`Session ${i+1} requires room type ID:`, requiredRoomTypeId);
                                requiredRoomTypeId = requiredRoomTypeId.toString();
                                
                                // Reset selection first
                                roomSelect.value = "";
                                
                                // Show or hide room options based on matching room type
                                Array.from(roomSelect.options).forEach(option => {
                                    if (option.value === "") return; // Skip the placeholder option
                                    
                                    const optionRoomType = option.dataset.roomType;
                                    if (optionRoomType && optionRoomType.toString() === requiredRoomTypeId.toString()) {
                                        option.style.display = "";
                                    } else {
                                        option.style.display = "none";
                                    }
                                });
                            } else {
                                console.log(`No room type requirement found for session ${i+1}, showing all rooms`);
                                // Show all rooms if no specific requirement
                                Array.from(roomSelect.options).forEach(option => {
                                    option.style.display = "";
                                });
                            }
                        } catch (error) {
                            console.error('Error parsing session room types:', error);
                            // Default to showing all rooms on error
                            requiredRoomTypeId = null;
                        }
                    }
                }
            }
            
            // Update equipment container based on location
            const equipmentContainer = document.getElementById(`equipment_container_${i}`);
            if (equipmentContainer) {
                if (isOnline) {
                    // For online sessions, clear and disable equipment selection
                    equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Equipment not needed for online sessions" %}</span></div>';
                    equipmentContainer.closest('div').classList.add('opacity-50');
                } else {
                    // For physical sessions, enable equipment selection
                    equipmentContainer.closest('div').classList.remove('opacity-50');
                    
                    // Only update equipment if we have date and room selected
                    if (selectedDates[i] && selectedDates[i].start_time && roomSelect && roomSelect.value) {
                        updateAvailableEquipmentForSession(i);
                    } else {
                        equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select date and room first" %}</span></div>';
                    }
                }
            }
        }
        
        // Check availability if needed
        checkAvailability();
    } catch (error) {
        console.error('Error in handleLocationChange:', error);
    }
}

// Add formatDate function
function formatDate(date) {
    if (!(date instanceof Date)) {
        date = new Date(date);
    }
    return date.toISOString().split('T')[0];
}

// Update checkAvailability function
async function checkAvailability() {
    try {
        // Get form data
        const courseSelect = document.getElementById('course');
        const locationSelect = document.getElementById('location');
        const slotTypeSelect = document.getElementById('slot_type');
        
        if (!courseSelect || !locationSelect) {
            console.error('Required selects not found');
            return;
        }
        
        // Check if we have the necessary data to check availability
        const courseId = courseSelect.value;
        const location = locationSelect.value;
        const slotType = slotTypeSelect ? slotTypeSelect.value : 'FULL_DAY';
        
        if (!courseId) {
            console.log('Course not selected yet');
            return;
        }

        // Disable save button during check
        const saveButton = document.getElementById('saveSessionBtn');
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.textContent = '{% trans "Checking availability..." %}';
        }
        
        // Check availability for each session
        for (let i = 0; i < requiredSessions; i++) {
            const trainerSelect = document.getElementById(`trainer_${i}`);
            const roomSelect = document.getElementById(`room_${i}`);
            
            const trainerId = trainerSelect ? trainerSelect.value : '';
            const roomId = (location === 'PHYSICAL' && roomSelect) ? roomSelect.value : '';
            
            if (!trainerId || (location === 'PHYSICAL' && !roomId)) {
                console.log(`Trainer or room not selected for session ${i+1}`);
                continue;
            }
            
            // Get the session dates
            if (!selectedDates[i] || !selectedDates[i].start_time || !selectedDates[i].end_time) {
                console.log(`No dates selected for session ${i+1}`);
                continue;
            }
            
            const startDate = new Date(selectedDates[i].start_time);
            const endDate = new Date(selectedDates[i].end_time);
        
        // Build the URL with necessary parameters
        let url = `{% url 'website:check_availability' %}?trainer_id=${trainerId}&start_date=${formatDate(startDate)}&end_date=${formatDate(endDate)}`;
        
        // Add the session_id parameter when in editing mode
        if (isEditing && document.getElementById('session_id')?.value) {
            url += `&session_id=${document.getElementById('session_id').value}`;
        }
        
        // Add additional parameters
        if (courseId) {
            url += `&course_id=${courseId}`;
        }
        
        if (slotType) {
            url += `&slot_type=${slotType}`;
        }
        
            if (location) {
                url += `&location=${location}`;
        }
        
        // Add room ID for physical sessions
            if (location === 'PHYSICAL' && roomId) {
            url += `&room_id=${roomId}`;
        }
        
            console.log(`Checking availability for session ${i+1} with URL:`, url);
        
        // Send request to check availability
        const response = await fetch(url);
        const responseData = await response.json();
        
        if (responseData.status === 'success') {
                // Reset busy slots for this session
                if (!busySlots[i]) busySlots[i] = [];
                busySlots[i] = responseData.busy_slots || [];
            
                // Check for conflicts in this session's selected dates
                if (selectedDates[i] && selectedDates[i].start_time && selectedDates[i].end_time) {
                            const conflicts = checkForConflicts(selectedDates[i], i);
                            if (conflicts) {
                                const warningElement = document.getElementById(`conflict_warning_${i}`);
                                if (warningElement) {
                                    // Find the text span within the warning element
                                    const warningText = warningElement.querySelector('span:last-child');
                                    if (warningText) {
                                        warningText.textContent = conflicts;
                                    }
                                    warningElement.classList.remove('hidden');
                                }
                            }
                }
            } else {
                console.error(`Error in availability response for session ${i+1}:`, responseData);
                showError(responseData.message || 'Failed to check availability');
                        }
                    }
                    
                    // Update conflict summary
                    updateConflictSummary();
                    
                    // Update save button state
                    if (saveButton) {
            const hasConflicts = selectedDates.some((date, index) => 
                date && date.start_time && date.end_time && checkForConflicts(date, index)
            );
                        saveButton.disabled = hasConflicts;
                        saveButton.textContent = hasConflicts ? '{% trans "Cannot Save - Conflicts Found" %}' : '{% trans "Save" %}';
                }
    } catch (error) {
        console.error('Error in checkAvailability:', error);
        // Re-enable save button on error
        const saveButton = document.getElementById('saveSessionBtn');
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.textContent = '{% trans "Save" %}';
        }
    }
}

function updateSessionDates() {
    try {
        // Get container
        const container = document.getElementById('sessionDates');
        if (!container) {
            console.error('Session dates container not found');
            return;
        }
        
        // Clear existing content
        container.innerHTML = '';
        
        // Get current slot type
        const slotTypeSelect = document.getElementById('slot_type');
        const isCustomTime = slotTypeSelect && slotTypeSelect.value === 'CUSTOM';
        
        // Add today's date as min date for all date inputs
        const today = new Date().toISOString().split('T')[0]; // Format YYYY-MM-DD
        
        for (let i = 0; i < requiredSessions; i++) {
            const dateDiv = document.createElement('div');
            dateDiv.className = 'bg-gray-800 p-4 rounded-lg shadow-md';
            
            const dateTitle = document.createElement('h3');
            dateTitle.className = 'text-md font-semibold text-white mb-2';
            dateTitle.textContent = `${isEditing ? '' : `{% trans "Session" %} ${i + 1} `}{% trans "Date" %}`;
            dateDiv.appendChild(dateTitle);
            
            const dateInputContainer = document.createElement('div');
            dateInputContainer.className = 'mb-2';
            
            const dateInput = document.createElement('input');
            dateInput.type = 'date';
            dateInput.name = `session_date_${i}`;
            dateInput.id = `session_date_${i}`;
            dateInput.required = true;
            dateInput.min = today; // Set minimum date to today
            dateInput.className = 'mt-1 block w-full border border-gray-600 bg-gray-800 text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm';
            
            // Add change event to update session data
            dateInput.addEventListener('change', function() {
                if (isCustomTime) {
                    const startTimeInput = document.getElementById(`start_time_${i}`);
                    const endTimeInput = document.getElementById(`end_time_${i}`);
                    const roomSelect = document.getElementById(`room_${i}`);
                    const roomTypeId = selectedDates[i] ? selectedDates[i].room_type_id : null;
                    
                    if (startTimeInput && endTimeInput) {
                        handleDateChange(i, dateInput, startTimeInput, endTimeInput, roomSelect, roomTypeId);
                    }
                } else {
                    // For fixed time, use default times
                    const startTimeValue = getStartTimeForSlotType(slotTypeSelect.value);
                    const endTimeValue = getEndTimeForSlotType(slotTypeSelect.value);
                    const roomSelect = document.getElementById(`room_${i}`);
                    const roomTypeId = selectedDates[i] ? selectedDates[i].room_type_id : null;
                    
                    handleDateChange(i, dateInput, { value: startTimeValue }, { value: endTimeValue }, roomSelect, roomTypeId);
                    
                    const timeDisplay = document.getElementById(`time_display_${i}`);
                    if (timeDisplay) {
                        updateTimeDisplay(timeDisplay);
                    }
                }
            });
            
            dateInputContainer.appendChild(dateInput);
            dateDiv.appendChild(dateInputContainer);
            
            // Conflict warning for this session
            const conflictWarning = document.createElement('div');
            conflictWarning.id = `conflict_warning_${i}`;
            conflictWarning.className = 'p-2 bg-red-500/20 border border-red-500 rounded-md mb-2 hidden';
            conflictWarning.innerHTML = `
                <span class="text-sm text-red-400 font-medium">{% trans "Warning:" %}</span>
                <span class="text-sm text-red-400 ml-1"></span>
            `;
            dateDiv.appendChild(conflictWarning);
            
            // Time display for fixed time slots
            if (!isCustomTime) {
                const timeDisplay = document.createElement('div');
                timeDisplay.id = `time_display_${i}`;
                timeDisplay.className = 'text-sm text-gray-400 mb-2';
                updateTimeDisplay(timeDisplay);
                dateDiv.appendChild(timeDisplay);
            }
            
            // Custom time inputs for custom slots
            if (isCustomTime) {
                const timeContainer = document.createElement('div');
                timeContainer.className = 'flex space-x-2 mb-2';
                
                const startTimeDiv = document.createElement('div');
                startTimeDiv.className = 'w-1/2';
                
                const startTimeLabel = document.createElement('label');
                startTimeLabel.htmlFor = `start_time_${i}`;
                startTimeLabel.className = 'block text-sm font-medium text-gray-300';
                startTimeLabel.textContent = '{% trans "Start Time" %}';
                
                const startTimeInput = document.createElement('input');
                startTimeInput.type = 'time';
                startTimeInput.name = `start_time_${i}`;
                startTimeInput.id = `start_time_${i}`;
                startTimeInput.required = true;
                startTimeInput.className = 'mt-1 block w-full border border-gray-600 bg-gray-800 text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm';
                startTimeInput.value = '09:00'; // Default value
                
                startTimeDiv.appendChild(startTimeLabel);
                startTimeDiv.appendChild(startTimeInput);
                
                const endTimeDiv = document.createElement('div');
                endTimeDiv.className = 'w-1/2';
                
                const endTimeLabel = document.createElement('label');
                endTimeLabel.htmlFor = `end_time_${i}`;
                endTimeLabel.className = 'block text-sm font-medium text-gray-300';
                endTimeLabel.textContent = '{% trans "End Time" %}';
                
                const endTimeInput = document.createElement('input');
                endTimeInput.type = 'time';
                endTimeInput.name = `end_time_${i}`;
                endTimeInput.id = `end_time_${i}`;
                endTimeInput.required = true;
                endTimeInput.className = 'mt-1 block w-full border border-gray-600 bg-gray-800 text-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm';
                endTimeInput.value = '16:00'; // Default value
                
                endTimeDiv.appendChild(endTimeLabel);
                endTimeDiv.appendChild(endTimeInput);
                
                timeContainer.appendChild(startTimeDiv);
                timeContainer.appendChild(endTimeDiv);
                dateDiv.appendChild(timeContainer);
                
                // Add change event to time inputs to update session data
                startTimeInput.addEventListener('change', function() {
                        updateSessionTimeData(i, dateInput, startTimeInput, endTimeInput);
                        updateAvailableRoomsForSession(i);
                });
                
                endTimeInput.addEventListener('change', function() {
                        updateSessionTimeData(i, dateInput, startTimeInput, endTimeInput);
                        updateAvailableRoomsForSession(i);
                });
            }
            
            // Room selection for this session
            const roomContainer = document.createElement('div');
            roomContainer.className = 'mb-2';
            roomContainer.id = 'roomFieldContainer';
            
            const roomLabel = document.createElement('label');
            roomLabel.htmlFor = `room_${i}`;
            roomLabel.className = 'block text-sm font-medium text-gray-300';
            roomLabel.textContent = '{% trans "Room" %}';
            
            const roomSelect = document.createElement('select');
            roomSelect.name = `room_${i}`;
            roomSelect.id = `room_${i}`;
            roomSelect.className = 'mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md';
            
            // Clone options from main room select
            const mainRoomSelect = document.getElementById('room');
            if (mainRoomSelect) {
                Array.from(mainRoomSelect.options).forEach(option => {
                    roomSelect.add(option.cloneNode(true));
                });
            }
            
            // Add change event to update session data
            roomSelect.addEventListener('change', function() {
                    if (!selectedDates[i]) selectedDates[i] = {};
                selectedDates[i].room = this.value;
                
                // Check for conflicts
                        const hasConflict = checkForConflicts(selectedDates[i], i);
                const conflictWarning = document.getElementById(`conflict_warning_${i}`);
                
                if (conflictWarning) {
                    const warningText = conflictWarning.querySelector('span:nth-child(2)');
                    
                    if (hasConflict && warningText) {
                        warningText.textContent = hasConflict;
                        conflictWarning.classList.remove('hidden');
                        
                        // Update input field style to indicate conflict
                        dateInput.classList.add('border-red-500');
                        dateInput.classList.remove('border-gray-600');
                    } else {
                        conflictWarning.classList.add('hidden');
                        
                        // Reset input field styles
                        dateInput.classList.remove('border-red-500');
                        dateInput.classList.add('border-gray-600');
                    }
                }
                
                // Update conflict summary
                updateConflictSummary();
                
                // Display fixed equipment info for the selected room
                displayFixedEquipmentInfo(i);
                
                // Update available equipment for this session
                updateAvailableEquipmentForSession(i);
            });
            
            roomContainer.appendChild(roomLabel);
            roomContainer.appendChild(roomSelect);
            dateDiv.appendChild(roomContainer);
            
            // Add fixed equipment info container
            const fixedEquipmentInfoContainer = document.createElement('div');
            fixedEquipmentInfoContainer.id = `fixed_equipment_info_${i}`;
            fixedEquipmentInfoContainer.className = 'mt-2 mb-2 hidden';
            dateDiv.appendChild(fixedEquipmentInfoContainer);
            
            // Trainer selection for this session
            const trainerContainer = document.createElement('div');
            trainerContainer.className = 'mb-3';
            
            const trainerLabel = document.createElement('label');
            trainerLabel.className = 'block text-sm font-medium text-gray-300 mb-1';
            trainerLabel.textContent = '{% trans "Trainers" %}';
            
            const trainerCheckboxContainer = document.createElement('div');
            trainerCheckboxContainer.className = 'mt-1 max-h-32 overflow-y-auto border border-gray-600 rounded-md bg-gray-900 p-2';
            trainerCheckboxContainer.id = `trainer_container_${i}`;
            
            // Initial message
            trainerCheckboxContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select date first" %}</span></div>';
            
            trainerContainer.appendChild(trainerLabel);
            trainerContainer.appendChild(trainerCheckboxContainer);
            dateDiv.appendChild(trainerContainer);
            
            // Equipment selection for this session
            const equipmentContainer = document.createElement('div');
            equipmentContainer.className = 'mt-3';
            
            const equipmentLabel = document.createElement('label');
            equipmentLabel.className = 'block text-sm font-medium text-gray-300 mb-1';
            equipmentLabel.textContent = '{% trans "Required Equipment" %}';
            
            const equipmentSelectContainer = document.createElement('div');
            equipmentSelectContainer.className = 'mt-1 max-h-32 overflow-y-auto border border-gray-600 rounded-md bg-gray-900 p-2';
            equipmentSelectContainer.id = `equipment_container_${i}`;
            
            // Initial empty message
            equipmentSelectContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select date and room first" %}</span></div>';
            
            equipmentContainer.appendChild(equipmentLabel);
            equipmentContainer.appendChild(equipmentSelectContainer);
            dateDiv.appendChild(equipmentContainer);
            
            container.appendChild(dateDiv);
            
            // Set existing date and times if available
            if (selectedDates[i] && selectedDates[i].start_time) {
                const dateStr = selectedDates[i].start_time.split('T')[0];
                dateInput.value = dateStr;
                
                // If custom time, set the time inputs
                if (isCustomTime && startTimeInput && endTimeInput) {
                    const startTimeStr = selectedDates[i].start_time.split('T')[1].substring(0, 5); // HH:MM
                    const endTimeStr = selectedDates[i].end_time.split('T')[1].substring(0, 5); // HH:MM
                    
                    startTimeInput.value = startTimeStr;
                    endTimeInput.value = endTimeStr;
                }
                
                // Set room if available
                if (selectedDates[i].room && roomSelect) {
                    roomSelect.value = selectedDates[i].room;
                }
                
                // Check for conflicts with busy slots for existing dates (only if not editing)
                if (!isEditing) {
                    const hasConflict = checkForConflicts(selectedDates[i], i);
                    
                    // Show conflict warning if needed
                    if (hasConflict) {
                        warningText.textContent = hasConflict;
                        conflictWarning.classList.remove('hidden');
                    }
                }
                
                // Load equipment for this session
                updateAvailableEquipmentForSession(i);
                
                // Load trainers for this session
                updateAvailableTrainersForSession(i);
                
                // Load rooms for this session
                updateAvailableRoomsForSession(i);
            }
        }
        
        // Update conflict summary after all dates are set up (only if not editing)
        if (!isEditing) {
            updateConflictSummary();
        }
    } catch (error) {
        console.error('Error in updateSessionDates:', error);
    }
}

// Add function to update available trainers for a specific session
async function updateAvailableTrainersForSession(sessionIndex) {
    const sessionDate = selectedDates[sessionIndex];
    if (!sessionDate || !sessionDate.start_time || !sessionDate.end_time) {
        console.log('No session date or time available');
        return;
    }

    const trainerContainer = document.getElementById(`trainer_container_${sessionIndex}`);
    if (!trainerContainer) {
        console.error(`Trainer container not found for session ${sessionIndex}`);
        return;
    }

    try {
        // Get all trainers first
        const allTrainers = window.trainers || [];
        if (!allTrainers.length) {
            console.error('No trainers available');
            trainerContainer.innerHTML = '<div class="text-red-500">No trainers available</div>';
            return;
        }

        // Get currently selected trainers for this session
        const selectedTrainerIds = sessionDate.trainers || [];

        // Check availability for the time slot
        const url = new URL(`${window.location.origin}/en/sessions/check-availability/`);
        url.searchParams.append('start_time', sessionDate.start_time);
        url.searchParams.append('end_time', sessionDate.end_time);
        if (sessionDate.session_id) {
            url.searchParams.append('session_id', sessionDate.session_id);
        }

        // Show loading message
        trainerContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Loading available trainers..." %}</span></div>';

        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();

        // Get the list of available trainer IDs
        const availableTrainerIds = data.available_resources.trainers || [];

        // Clear existing content
        trainerContainer.innerHTML = '';
        
        // Filter trainers to only show available ones or those already selected
        const filteredTrainers = allTrainers.filter(trainer => 
            availableTrainerIds.includes(trainer.trainer_id) || 
            selectedTrainerIds.includes(trainer.trainer_id)
        );
        
        if (filteredTrainers.length === 0) {
            trainerContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No trainers available for this time slot" %}</span></div>';
            return;
        }

        // Create and append trainer checkboxes for available or selected trainers
        filteredTrainers.forEach(trainer => {
            const isAvailable = availableTrainerIds.includes(trainer.trainer_id);
            const isSelected = selectedTrainerIds.includes(trainer.trainer_id);

            const trainerDiv = document.createElement('div');
            trainerDiv.className = 'py-2';

            const label = document.createElement('label');
            label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
            
            if (isSelected) {
                label.className += ' bg-blue-900/30';
            }

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.id = `trainer-${sessionIndex}-${trainer.trainer_id}`;
            checkbox.name = `trainer_${sessionIndex}`;
            checkbox.value = trainer.trainer_id;
            checkbox.checked = isSelected;
            checkbox.className = 'form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out';
            checkbox.dataset.trainerIndex = trainer.trainer_id;
            // Disable checkbox for unavailable trainers that aren't already selected
            checkbox.disabled = !isAvailable && !isSelected;

            const span = document.createElement('span');
            span.className = 'ml-2 text-sm text-white';
            span.textContent = trainer.name;
            
            // Add opacity to unavailable trainers
            if (!isAvailable && !isSelected) {
                label.classList.add('opacity-50');
            }

            // Add "Selected" badge for selected trainers
            if (isSelected) {
                const selectedBadge = document.createElement('span');
                selectedBadge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                selectedBadge.textContent = '{% trans "Selected" %}';
                span.appendChild(selectedBadge);
            }

            label.appendChild(checkbox);
            label.appendChild(span);
            trainerDiv.appendChild(label);
            trainerContainer.appendChild(trainerDiv);

            // Add change event listener to update selectedDates when checkbox changes
            checkbox.addEventListener('change', function() {
                if (!selectedDates[sessionIndex]) selectedDates[sessionIndex] = {};
                if (!selectedDates[sessionIndex].trainers) selectedDates[sessionIndex].trainers = [];
                
                const trainerId = parseInt(this.value);
                
                if (this.checked) {
                    // Add to trainers list if not already there
                    if (!selectedDates[sessionIndex].trainers.includes(trainerId)) {
                        selectedDates[sessionIndex].trainers.push(trainerId);
                    }
                    
                    // Update visual style
                    label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md bg-blue-900/30';
                    
                    // Add badge if not already present
                    if (!span.querySelector('span')) {
                        const badge = document.createElement('span');
                        badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                        badge.textContent = '{% trans "Selected" %}';
                        span.appendChild(badge);
                    }
                } else {
                    // Remove from trainers list
                    selectedDates[sessionIndex].trainers = selectedDates[sessionIndex].trainers.filter(id => 
                        id !== trainerId
                    );
                    
                    // Update visual style
                    label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                    
                    // Remove badge if present
                    const badge = span.querySelector('span');
                    if (badge) {
                        span.removeChild(badge);
                    }
                    
                    // Add opacity if this trainer is unavailable
                    if (!isAvailable) {
                        label.classList.add('opacity-50');
                    }
                    
                    // Delete trainer availability record when unselected during editing
                    const sessionId = document.getElementById('session_id')?.value;
                    if (sessionId && isEditing) {
                        deleteTrainerAvailability(trainerId, sessionId);
                    }
                }
            });
        });

    } catch (error) {
        console.error('Error fetching trainer availability:', error);
        trainerContainer.innerHTML = `<div class="text-red-500">Error fetching trainer availability: ${error.message}</div>`;
    }
}

// New function to update session data with custom times
function updateSessionTimeData(index, dateInput, startTimeInput, endTimeInput) {
    if (!dateInput || !dateInput.value || !startTimeInput || !endTimeInput) return;
    
    const date = dateInput.value; // YYYY-MM-DD
    const startTime = startTimeInput.value; // HH:MM
    const endTime = endTimeInput.value; // HH:MM
    
    // Preserve existing properties like trainers and equipment if they exist
    const existingData = selectedDates[index] || {};
    
    // Make sure end time is after start time
    if (startTime >= endTime) {
        alert('{% trans "End time must be after start time" %}');
        // Set end time to be at least 1 hour after start time
        const [startHours, startMins] = startTime.split(':').map(Number);
        let newHours = startHours + 1;
        if (newHours > 23) newHours = 23;
        endTimeInput.value = `${String(newHours).padStart(2, '0')}:${String(startMins).padStart(2, '0')}`;
        
        // Update with new values while preserving existing data
        selectedDates[index] = {
            ...existingData,
            start_time: `${date}T${startTime}`,
            end_time: `${date}T${endTimeInput.value}`
        };
    } else {
        selectedDates[index] = {
            ...existingData,
            start_time: `${date}T${startTime}`,
            end_time: `${date}T${endTime}`
        };
    }
    
    // Check for conflicts if not editing or if specifically configured to check for conflicts in edit mode
    const hasConflict = checkForConflicts(selectedDates[index], index);
    const conflictWarning = document.getElementById(`conflict_warning_${index}`);
    
    if (conflictWarning) {
        const warningText = conflictWarning.querySelector('span:nth-child(2)');
        
        // Show or hide conflict warning
        if (hasConflict && warningText) {
            warningText.textContent = hasConflict;
            conflictWarning.classList.remove('hidden');
            
            // Update input field style to indicate conflict
            dateInput.classList.add('border-red-500');
            dateInput.classList.remove('border-gray-600');
            
            // Also add red border to time inputs to highlight the conflict
            if (startTimeInput) {
                startTimeInput.classList.add('border-red-500');
                startTimeInput.classList.remove('border-gray-600');
            }
            if (endTimeInput) {
                endTimeInput.classList.add('border-red-500');
                endTimeInput.classList.remove('border-gray-600');
            }
        } else {
            conflictWarning.classList.add('hidden');
            
            // Reset input field styles
            dateInput.classList.remove('border-red-500');
            dateInput.classList.add('border-gray-600');
            
            // Reset time input styles
            if (startTimeInput) {
                startTimeInput.classList.remove('border-red-500');
                startTimeInput.classList.add('border-gray-600');
            }
            if (endTimeInput) {
                endTimeInput.classList.remove('border-red-500');
                endTimeInput.classList.add('border-gray-600');
            }
        }
    }
    
    // Always update conflict summary
    updateConflictSummary();
    
    // Update session count and equipment availability
    updateSessionCount();
    checkSessionEquipmentAvailability();
}

// Renamed function to handle fixed time slots
function updateFixedTimeSessionData(index, dateInput) {
    if (!dateInput || !dateInput.value) return;
    
    // Get the slot type and set times accordingly
    const slotTypeSelect = document.getElementById('slot_type');
    const slotType = slotTypeSelect ? slotTypeSelect.value : 'FULL_DAY';
    const date = dateInput.value; // YYYY-MM-DD
    
    let startTime, endTime;
    switch(slotType) {
        case 'MORNING':
            startTime = '09:00';
            endTime = '12:00';
            break;
        case 'AFTERNOON':
            startTime = '13:00';
            endTime = '16:00';
            break;
        case 'CUSTOM':
            // This shouldn't happen as custom is handled separately
            return;
        default: // FULL_DAY
            startTime = '09:00';
            endTime = '16:00';
            break;
    }
    
    // Preserve existing properties like trainers and equipment if they exist
    const existingData = selectedDates[index] || {};
    selectedDates[index] = {
        ...existingData,
        start_time: `${date}T${startTime}`,
        end_time: `${date}T${endTime}`
    };
    
    // Update available rooms for this session based on the fixed time slot
    updateAvailableRoomsForSession(index);
}

// Update the updateTimeDisplay function to handle custom time slots
function updateTimeDisplay(displayElement) {
    if (!displayElement) return;
    
    const slotTypeSelect = document.getElementById('slot_type');
    if (!slotTypeSelect) return;
    
    const slotType = slotTypeSelect.value || 'FULL_DAY';
    let timeText;
    
    switch(slotType) {
        case 'MORNING':
            timeText = '9:00 AM - 12:00 PM';
            break;
        case 'AFTERNOON':
            timeText = '1:00 PM - 4:00 PM';
            break;
        case 'CUSTOM':
            // No text for custom as we show time inputs
            displayElement.classList.add('hidden');
            return;
        default: // FULL_DAY
            timeText = '9:00 AM - 4:00 PM';
            break;
    }
    
    displayElement.classList.remove('hidden');
    displayElement.textContent = `Time: ${timeText}`;
}

// Update the handleSlotTypeChange function to show/hide time inputs
function handleSlotTypeChange() {
    try {
        const slotTypeSelect = document.getElementById('slot_type');
        if (!slotTypeSelect) return;
        
        const slotType = slotTypeSelect.value || 'FULL_DAY';
        const isCustomTime = slotType === 'CUSTOM';
        
        // If changing to/from custom, regenerate the date fields
        // This ensures time inputs are added/removed
        updateSessionDates();
        
        // If not custom, update the time displays
        if (!isCustomTime) {
            // Update time displays for all date inputs
            for (let i = 0; i < requiredSessions; i++) {
                const timeDisplay = document.getElementById(`time_display_${i}`);
                if (timeDisplay) {
                    updateTimeDisplay(timeDisplay);
                }
            }
        }
        
        // Update session times
        updateSessionTimes();
        
        // Check equipment availability with new times
        checkSessionEquipmentAvailability();
    } catch (error) {
        console.error('Error in handleSlotTypeChange:', error);
    }
}

// Update the updateSessionTimes function to handle custom time slots
function updateSessionTimes() {
    try {
        // Get the slot type
        const slotTypeSelect = document.getElementById('slot_type');
        if (!slotTypeSelect) return;
        
        const slotType = slotTypeSelect.value || 'FULL_DAY';
        let hasChanges = false;
        
        // For custom times, we don't update automatically - 
        // the user sets values directly in the time inputs
        if (slotType === 'CUSTOM') {
            for (let i = 0; i < requiredSessions; i++) {
                // If using custom times, update from inputs
                const dateInput = document.querySelector(`[name="session_date_${i}"]`);
                const startTimeInput = document.getElementById(`start_time_${i}`);
                const endTimeInput = document.getElementById(`end_time_${i}`);
                
                if (dateInput && dateInput.value && startTimeInput && endTimeInput) {
                    updateSessionTimeData(i, dateInput, startTimeInput, endTimeInput);
                    hasChanges = true;
                }
            }
            return hasChanges;
        }
        
        // For fixed time slots, update using predefined times
        for (let i = 0; i < requiredSessions; i++) {
            const timeDisplay = document.getElementById(`time_display_${i}`);
            if (timeDisplay) {
                updateTimeDisplay(timeDisplay);
            }
            
            // Update the actual session data if it exists
            if (selectedDates[i] && selectedDates[i].start_time) {
                const date = selectedDates[i].start_time.split('T')[0];
                const oldStartTime = selectedDates[i].start_time;
                const oldEndTime = selectedDates[i].end_time;
                
                let startTime, endTime;
                switch(slotType) {
                    case 'MORNING':
                        startTime = '09:00';
                        endTime = '12:00';
                        break;
                    case 'AFTERNOON':
                        startTime = '13:00';
                        endTime = '16:00';
                        break;
                    default: // FULL_DAY
                        startTime = '09:00';
                        endTime = '16:00';
                        break;
                }
                
                // Create new dates
                const newStartTime = `${date}T${startTime}`;
                const newEndTime = `${date}T${endTime}`;
                
                // Check if times have changed
                if (newStartTime !== oldStartTime || newEndTime !== oldEndTime) {
                    selectedDates[i].start_time = newStartTime;
                    selectedDates[i].end_time = newEndTime;
                    hasChanges = true;
                    
                    // Check for conflicts with busy slots when time changes
                    if (!isEditing) {
                    const conflicts = checkForConflicts(selectedDates[i], i);
                    const warningElement = document.getElementById(`conflict_warning_${i}`);
                    
                    if (warningElement) {
                            const warningText = warningElement.querySelector('span:nth-child(2)');
                            
                        if (conflicts) {
                            if (warningText) {
                                warningText.textContent = conflicts;
                            }
                            warningElement.classList.remove('hidden');
                        } else {
                            warningElement.classList.add('hidden');
                            }
                        }
                    }
                    
                    // Update input field style
                    const dateInput = document.querySelector(`[name="session_date_${i}"]`);
                    if (dateInput) {
                        if (conflicts) {
                            dateInput.classList.add('border-red-500');
                            dateInput.classList.remove('border-gray-600');
                        } else {
                            dateInput.classList.remove('border-red-500');
                            dateInput.classList.add('border-gray-600');
                        }
                        }
                    }
                }
            }
            
            // Update conflict summary
        if (!isEditing) {
            updateConflictSummary();
        }
        
        return hasChanges;
    } catch (error) {
        console.error('Error in updateSessionTimes:', error);
        return false;
    }
}

// Add function to load session data with better custom time handling
function loadSessionData(sessionId) {
    fetch(`{% url 'website:create_session' %}?session_id=${sessionId}`)
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                const session = data.session;
                const form = document.getElementById('sessionForm');
                
                // Set editing mode
                isEditing = true;
                
                // Store the session ID
                const sessionIdInput = document.getElementById('session_id');
                if (sessionIdInput) {
                    sessionIdInput.value = sessionId;
                }
                
                // Set form values
                const courseSelect = document.getElementById('course');
                const locationSelect = document.getElementById('location');
                const slotTypeSelect = document.getElementById('slot_type');
                
                if (courseSelect) courseSelect.value = session.course;
                if (locationSelect) locationSelect.value = session.location;
                
                // Reset equipment categories info
                const equipmentCategoriesInfoDiv = document.getElementById('equipment-categories-info');
                if (equipmentCategoriesInfoDiv) {
                    equipmentCategoriesInfoDiv.classList.add('hidden');
                }
                
                // Show equipment categories for the selected course when editing
                updateEquipmentCategoriesDisplay();
                
                // Check if we need to use custom time slot
                const startDate = new Date(session.start_date);
                const endDate = new Date(session.end_date);
                
                const startHour = startDate.getHours();
                const startMinute = startDate.getMinutes();
                const endHour = endDate.getHours();
                const endMinute = endDate.getMinutes();
                
                const startTimeStr = `${startHour.toString().padStart(2, '0')}:${startMinute.toString().padStart(2, '0')}`;
                const endTimeStr = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
                
                // Determine if this is a standard time slot or custom
                let useCustomTime = true;
                
                // Check if it's a full day slot (9:00-16:00)
                if (startTimeStr === '09:00' && endTimeStr === '16:00') {
                    useCustomTime = false;
                    if (slotTypeSelect) slotTypeSelect.value = 'FULL_DAY';
                }
                // Check if it's a morning slot (9:00-12:00)
                else if (startTimeStr === '09:00' && endTimeStr === '12:00') {
                    useCustomTime = false;
                    if (slotTypeSelect) slotTypeSelect.value = 'MORNING';
                }
                // Check if it's an afternoon slot (13:00-16:00)
                else if (startTimeStr === '13:00' && endTimeStr === '16:00') {
                    useCustomTime = false;
                    if (slotTypeSelect) slotTypeSelect.value = 'AFTERNOON';
                }
                // Otherwise it's a custom time
                else {
                    if (slotTypeSelect) slotTypeSelect.value = 'CUSTOM';
                }
                
                // For editing, we don't want to enforce the usual session count
                // Just add a single date field for this specific session
                requiredSessions = 1;
                
                // Handle the course change to update session counts
                handleCourseChange();
                
                // Trigger session dates update
                updateSessionDates();
                
                // Set the single date for the session
                const dateStr = startDate.toISOString().split('T')[0];
                
                // Initialize the selected dates array for the first session
                selectedDates[0] = {
                    start_time: session.start_date,
                    end_time: session.end_date,
                    equipment: session.equipment || [],
                    trainers: session.trainers || [] // Add trainers to the selectedDates array
                };
                
                // Fetch availability data to check for conflicts
                setTimeout(() => {
                    // Apply location change to setup room dropdowns
                    handleLocationChange();
                    
                    // Set the first date input's value
                    const dateInput = document.querySelector('[name="session_date_0"]');
                    if (dateInput) {
                        dateInput.value = dateStr;
                        
                        // Wait for the room dropdowns to be created
                        setTimeout(() => {
                            // Set the room value for the first session
                            const roomSelect = document.getElementById('room_0');
                            if (roomSelect && session.room) {
                                roomSelect.value = session.room;
                                
                                // Trigger change event on room select to update related UI
                                const event = new Event('change');
                                roomSelect.dispatchEvent(event);
                            }
                            
                            // If custom time, set the time inputs
                            if (useCustomTime) {
                                const startTimeInput = document.getElementById('start_time_0');
                                const endTimeInput = document.getElementById('end_time_0');
                                
                                if (startTimeInput) startTimeInput.value = startTimeStr;
                                if (endTimeInput) endTimeInput.value = endTimeStr;
                            }
                            
                            // Check availability after room is set
                            checkAvailability();
                            
                            // Check for conflicts
                            const conflicts = checkForConflicts(selectedDates[0], 0);
                            if (conflicts) {
                                const conflictWarning = document.getElementById('conflict_warning_0');
                                if (conflictWarning) {
                                    const warningText = conflictWarning.querySelector('span:nth-child(2)');
                                    if (warningText) {
                                        warningText.textContent = conflicts;
                                        conflictWarning.classList.remove('hidden');
                                    }
                                }
                                
                                // Update input styles to reflect conflict
                                if (dateInput) {
                                    dateInput.classList.add('border-red-500');
                                    dateInput.classList.remove('border-gray-600');
                                }
                                
                                const startTimeInput = document.getElementById('start_time_0');
                                const endTimeInput = document.getElementById('end_time_0');
                                
                                if (startTimeInput) {
                                    startTimeInput.classList.add('border-red-500');
                                    startTimeInput.classList.remove('border-gray-600');
                                }
                                if (endTimeInput) {
                                    endTimeInput.classList.add('border-red-500');
                                    endTimeInput.classList.remove('border-gray-600');
                                }
                                
                                // Disable save button if conflicts exist
                                const saveButton = document.getElementById('saveSessionBtn');
                                if (saveButton) {
                                    saveButton.disabled = true;
                                    saveButton.textContent = '{% trans "Cannot Save - Conflicts Found" %}';
                                }
                            }
                            
                            // Now fetch available equipment for this session time
                            fetchAvailableEquipmentForEditing(session.start_date, session.end_date, session.equipment || []);
                            
                            // Also fetch available trainers for this session
                            fetchAvailableTrainersForEditing(session.start_date, session.end_date, session.trainers || []);
                            
                            // Update available rooms for this session
                            updateAvailableRoomsForSession(0);
                        }, 200);
                    }
                    
                    // Update the session count display for editing mode
                    const sessionCount = document.getElementById('sessionCount');
                    if (sessionCount) {
                        sessionCount.textContent = "Editing Session";
                    }
                    
                    // Update conflict summary
                    updateConflictSummary();
                }, 100);
            } else {
                showError(data.message || 'Failed to load session data');
            }
        })
        .catch(error => {
            console.error('Error loading session data:', error);
            showError('Error loading session data: ' + error.message);
        });
}

function updateSessionCount() {
    const sessionCount = document.getElementById('sessionCount');
    if (sessionCount) {
        if (isEditing) {
            sessionCount.textContent = "Editing Session";
        } else {
        const validSessions = selectedDates.filter(date => date && date.start_time && date.end_time).length;
        sessionCount.textContent = `${validSessions} / ${requiredSessions} sessions selected`;
        }
    }
}

// Add new function to update available equipment for a specific session
function updateAvailableEquipmentForSession(sessionIndex) {
    try {
        // Get equipment container for this session
        const equipmentContainer = document.getElementById(`equipment_container_${sessionIndex}`);
        if (!equipmentContainer) {
            console.error(`Equipment container not found for session ${sessionIndex}`);
            return;
        }
        
        // Check if we have valid session data
        if (!selectedDates[sessionIndex] || !selectedDates[sessionIndex].start_time || !selectedDates[sessionIndex].end_time) {
            equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select date and time first" %}</span></div>';
            return;
        }
        
        // Check if we have a room selected
        const roomSelect = document.getElementById(`room_${sessionIndex}`);
        if (!roomSelect || !roomSelect.value) {
            equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select a room first" %}</span></div>';
            return;
        }
        
        // Show loading indicator
        equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Loading available equipment..." %}</span></div>';
        
        // Get current session_id if editing
        const sessionId = document.getElementById('session_id')?.value || '';
        
        // Get the time range for this session
        const startTime = selectedDates[sessionIndex].start_time;
        const endTime = selectedDates[sessionIndex].end_time;
        
        // Fetch available equipment
        fetch('{% url "website:check_equipment_availability" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                start_time: startTime,
                end_time: endTime,
                session_id: sessionId
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status !== 'success') {
                throw new Error(data.message || 'Failed to fetch equipment availability');
            }
            
            // Clear current equipment list
            equipmentContainer.innerHTML = '';
            
            // Get available equipment
            const availableEquipment = data.available_equipment || [];
            
            if (availableEquipment.length === 0) {
                equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No equipment available at selected time" %}</span></div>';
                return;
            }
            
            // Get fixed equipment from room
            const roomSelect = document.getElementById(`room_${sessionIndex}`);
            let fixedEquipmentIds = [];
            
            if (roomSelect && roomSelect.value) {
                const selectedRoomOption = roomSelect.options[roomSelect.selectedIndex];
                if (selectedRoomOption && selectedRoomOption.dataset.fixedEquipment) {
                    fixedEquipmentIds = selectedRoomOption.dataset.fixedEquipment.split(',').filter(id => id.trim() !== '');
                }
            }
            
            // Get previously selected equipment for this session
            const selectedEquipment = selectedDates[sessionIndex].equipment || [];
            
            // Create checkbox for each equipment
            availableEquipment.forEach(item => {
                const isSelected = selectedEquipment.includes(item.id.toString()) || 
                                   selectedEquipment.includes(parseInt(item.id));
                
                const isFixedInRoom = fixedEquipmentIds.includes(item.id.toString()) || 
                                      fixedEquipmentIds.includes(item.id);
                
                const label = document.createElement('label');
                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                
                if (isSelected) {
                    label.className += ' bg-blue-900/30';
                }
                
                if (isFixedInRoom) {
                    label.className += ' border-l-4 border-green-500';
                }
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = `equipment_${sessionIndex}`;
                checkbox.value = item.id;
                checkbox.className = 'form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary';
                checkbox.checked = isSelected;
                
                if (isFixedInRoom) {
                    // Auto-select fixed equipment and disable checkbox
                    checkbox.checked = true;
                    checkbox.disabled = true;
                    
                    // Make sure it's in the selected equipment list
                    if (!selectedEquipment.includes(item.id.toString()) && !selectedEquipment.includes(parseInt(item.id))) {
                        if (!selectedDates[sessionIndex]) selectedDates[sessionIndex] = {};
                        if (!selectedDates[sessionIndex].equipment) selectedDates[sessionIndex].equipment = [];
                        selectedDates[sessionIndex].equipment.push(item.id.toString());
                    }
                }
                
                const span = document.createElement('span');
                span.className = 'ml-2 text-sm text-white';
                span.textContent = `${item.name} (${item.code})`;
                
                if (isSelected) {
                    const badge = document.createElement('span');
                    badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                    badge.textContent = '{% trans "Selected" %}';
                    span.appendChild(badge);
                }
                
                if (isFixedInRoom) {
                    const badge = document.createElement('span');
                    badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800';
                    badge.textContent = '{% trans "Available in Room" %}';
                    span.appendChild(badge);
                }
                
                label.appendChild(checkbox);
                label.appendChild(span);
                equipmentContainer.appendChild(label);
                
                // Add change event to update session data
                checkbox.addEventListener('change', function() {
                    if (!selectedDates[sessionIndex]) selectedDates[sessionIndex] = {};
                    if (!selectedDates[sessionIndex].equipment) selectedDates[sessionIndex].equipment = [];
                    
                    const equipId = item.id.toString();
                    
                    if (this.checked) {
                        // Add to equipment list if not already there
                        if (!selectedDates[sessionIndex].equipment.includes(equipId)) {
                            selectedDates[sessionIndex].equipment.push(equipId);
                        }
                        
                        // Update visual style
                        label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md bg-blue-900/30';
                        
                        // Add badge if not already present
                        if (!span.querySelector('span')) {
                            const badge = document.createElement('span');
                            badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                            badge.textContent = '{% trans "Selected" %}';
                            span.appendChild(badge);
                        }
                    } else {
                        // Remove from equipment list
                        selectedDates[sessionIndex].equipment = selectedDates[sessionIndex].equipment.filter(id => 
                            id !== equipId && parseInt(id) !== parseInt(equipId)
                        );
                        
                        // Update visual style
                        label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                        
                        // Remove badge if present
                        const badge = span.querySelector('span');
                        if (badge) {
                            span.removeChild(badge);
                        }
                    }
                });
            });
        })
        .catch(error => {
            console.error('Error fetching equipment availability:', error);
            equipmentContainer.innerHTML = `<div class="text-center py-4 text-red-400">{% trans "Error loading equipment" %}: ${error.message}</div>`;
        });
    } catch (error) {
        console.error('Error in updateAvailableEquipmentForSession:', error);
    }
}

function handleSave() {
    if (isSubmitting) return;
    isSubmitting = true;
    
    try {
        // Collect base data from the form
        const baseData = {
            course: document.getElementById('course').value,
            location: document.getElementById('location').value,
            slot_type: document.getElementById('slot_type').value,
            cancellation_deadline: document.getElementById('cancellation_deadline').value,
            published: document.getElementById('published').checked,
            course_type: document.getElementById('course_type').value,
            corporate_id: document.getElementById('corporate_id').value,
            capacity: document.getElementById('capacity').value
        };

        // Validate base data
        const requiredFields = ['course', 'location', 'slot_type', 'cancellation_deadline', 'capacity'];
        for (const field of requiredFields) {
            if (!baseData[field]) {
                throw new Error(`{% trans "Please fill out all required fields" %}`);
            }
        }

        // Update session times based on current inputs
        updateSessionTimes();
        
        // Ensure we have valid dates for the required number of sessions
        const individualSessions = [];
        const conflicts = [];
        
        // Generate a unique batch ID for grouping sessions
        const batchId = 'batch-' + Date.now() + '-' + Math.random().toString(36).substring(2, 9);
        
        for (let i = 0; i < requiredSessions; i++) {
            // Check if this session has valid data
            if (!selectedDates[i] || !selectedDates[i].start_time || !selectedDates[i].end_time) {
                throw new Error(`{% trans "Please select a date for Session" %} ${i + 1}`);
            }
            
            // Get trainer ID for this session
            const trainerCheckboxes = document.querySelectorAll(`input[name="trainer_${i}"]:checked`);
            const selectedTrainers = Array.from(trainerCheckboxes).map(cb => cb.value);
            
            // Validate trainer selection
            if (selectedTrainers.length === 0) {
                throw new Error(`{% trans "Please select at least one trainer for Session" %} ${i + 1}`);
            }
            
            // Get room ID for this session
            const roomSelect = document.getElementById(`room_${i}`);
            const roomId = roomSelect ? roomSelect.value : null;
            
            // Make sure room is selected for physical location
            if (baseData.location === 'PHYSICAL' && (!roomId || roomId === "")) {
                throw new Error(`{% trans "Please select a room for Session" %} ${i + 1}`);
            }
            
            // Get selected equipment for this session
            const equipmentCheckboxes = document.querySelectorAll(`input[name="equipment_${i}"]:checked`);
            const selectedEquipment = Array.from(equipmentCheckboxes).map(cb => cb.value);
            
            // Check for scheduling conflicts
            const conflict = checkForConflicts(selectedDates[i], i);
            if (conflict) {
                conflicts.push(`{% trans "Session" %} ${i + 1}: ${conflict}`);
            }
            
            // Create individual session data object
            const sessionData = {
                course: baseData.course,
                trainers: selectedTrainers,
                location: baseData.location,
                slot_type: baseData.slot_type,
                room: roomId,
                equipment: selectedEquipment,
                start_time: selectedDates[i].start_time,
                end_time: selectedDates[i].end_time,
                batch_id: batchId,  // Add the batch ID for grouping sessions
                course_type: baseData.course_type,
                corporate_id: baseData.corporate_id,
                published: baseData.published,
                cancellation_deadline: baseData.cancellation_deadline,
                capacity: baseData.capacity
            };
            
            individualSessions.push(sessionData);
        }
        
        // If not editing, throw an error if conflicts are found
        if (!isEditing && conflicts.length > 0) {
            throw new Error('{% trans "Cannot save sessions with conflicts:" %}\n' + conflicts.join('\n'));
        }
        
        // Add session_id if we're editing
        if (isEditing && document.getElementById('session_id')?.value) {
            // When editing, we only support editing one session at a time
            individualSessions[0].session_id = document.getElementById('session_id').value;
        }
        
        console.log('Sessions to create/update:', individualSessions);
        
        // Update save button
        const saveButton = document.getElementById('saveSessionBtn');
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.textContent = '{% trans "Saving..." %}';
        }
        
        // Process sessions sequentially to avoid database locks
        async function createSessionsSequentially() {
            const results = [];
            let errorOccurred = false;
            let errorMessage = '';
            
            for (let i = 0; i < individualSessions.length; i++) {
                if (errorOccurred) break;
                
                const sessionData = individualSessions[i];
                
                // Make sure all equipment data is included and unique
                if (isEditing && sessionData.equipment) {
                    // Try to get equipment from selectedDates array
                    if (selectedDates[i] && selectedDates[i].equipment && selectedDates[i].equipment.length > 0) {
                        // Use a Set to ensure unique equipment IDs
                        const uniqueEquipment = new Set([
                            ...sessionData.equipment.map(id => String(id)),
                            ...selectedDates[i].equipment.map(id => String(id))
                        ]);
                        
                        sessionData.equipment = Array.from(uniqueEquipment);
                    } else if (sessionData.equipment.length === 0) {
                        // If equipment array is empty, check selectedDates
                        if (selectedDates[i] && selectedDates[i].equipment && selectedDates[i].equipment.length > 0) {
                            sessionData.equipment = [...selectedDates[i].equipment];
                        }
                    }
                }
                
                // Add session_id if we're editing
                if (isEditing) {
                    const sessionIdInput = document.getElementById('session_id');
                    if (sessionIdInput && sessionIdInput.value) {
                        sessionData.session_id = sessionIdInput.value;
                    }
                }
                
                try {
                    console.log('Sending session data:', sessionData);
                    
                    const response = await fetch('{% url "website:create_session" %}', {
                        method: isEditing ? 'PUT' : 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': getCsrfToken()
                        },
                        body: JSON.stringify({
                            ...baseData,
                            session_id: sessionData.session_id,
                            start_time: sessionData.start_time,
                            end_time: sessionData.end_time,
                            room: sessionData.room,
                            trainers: sessionData.trainers,
                            equipment: sessionData.equipment,
                            capacity: baseData.capacity,
                        })
                    });
                    
                    console.log('Response status:', response.status);
                    const responseData = await response.json();
                    
                    if (responseData.status !== 'success') {
                        errorOccurred = true;
                        errorMessage = responseData.message || 'Unknown error';
                        console.error(`Error creating session ${i+1}:`, errorMessage);
                    } else {
                        results.push(responseData);
                    }
                } catch (error) {
                    errorOccurred = true;
                    errorMessage = error.message || 'Failed to create session';
                    console.error(`Error creating session ${i+1}:`, error);
                }
            }
            
            return { results, errorOccurred, errorMessage };
        }
        
        // Create sessions sequentially
        createSessionsSequentially()
            .then(({ results, errorOccurred, errorMessage }) => {
                if (errorOccurred) {
                    throw new Error(errorMessage);
                }
                
                // All successful
                const successMessage = isEditing ? 
                    'Session updated successfully' : 
                    `Successfully created ${results.length} sessions`;
                
                showSuccess(successMessage);
                
                // Close modal and reload after a short delay
                setTimeout(() => {
                    closeSessionModal();
                    window.location.reload();
                }, 1500);
            })
            .catch(error => {
                console.error('Error saving sessions:', error);
                showError(error.message || 'Failed to save sessions');
            })
            .finally(() => {
                isSubmitting = false;
                if (saveButton) {
                    saveButton.disabled = false;
                    saveButton.textContent = '{% trans "Save" %}';
                }
            });
        
    } catch (error) {
        console.error('Error in handleSave:', error);
        showError(error.message);
        isSubmitting = false;
        const saveButton = document.getElementById('saveSessionBtn');
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.textContent = '{% trans "Save" %}';
        }
    }
}

function showError(message) {
    const errorDiv = document.getElementById('sessionModalError');
    if (errorDiv) {
        // Replace newlines with HTML line breaks
        errorDiv.innerHTML = message.replace(/\n/g, '<br>');
        errorDiv.classList.remove('hidden');
    }
}

function showSuccess(message) {
    const successDiv = document.getElementById('sessionModalSuccess');
    if (successDiv) {
        successDiv.textContent = message;
        successDiv.classList.remove('hidden');
    }
}

function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    return csrfInput ? csrfInput.value : '';
}

// Add a new function to check equipment availability
function checkSessionEquipmentAvailability() {
    try {
        // Get the equipment container
        const equipmentContainer = document.querySelector('.equipment-container .space-y-2, .max-h-48 .space-y-2');
        
        if (!equipmentContainer) {
            console.error("Equipment container not found");
            return;
        }
        
        // Get any selected dates
        let validSessions = selectedDates.filter(date => date && date.start_time && date.end_time);
        
        // If no valid sessions selected, keep all equipment visible
        if (validSessions.length === 0) {
            return;
        }
        
        // Show loading indicator
        equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Loading available equipment..." %}</span></div>';
        
        // Use the first selected date for checking availability
        const startTime = validSessions[0].start_time;
        const endTime = validSessions[0].end_time;
        
        // Get current session_id if editing
        const sessionId = document.getElementById('session_id')?.value || '';
        
        // Fetch available equipment
        fetch('{% url "website:check_equipment_availability" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                start_time: startTime,
                end_time: endTime,
                session_id: sessionId // Include session ID to exclude current session's bookings
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status !== 'success') {
                throw new Error(data.message || 'Failed to fetch equipment availability');
            }
            
            // Clear current equipment list
            equipmentContainer.innerHTML = '';
            
            // Get available equipment
            const availableEquipment = data.available_equipment || [];
            
            // Check if we're editing a session
            const isEditing = document.getElementById('session_id')?.value ? true : false;
            
            if (availableEquipment.length === 0) {
                equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No equipment available at selected time" %}</span></div>';
                return;
            }
            
            // Sort equipment by name
            availableEquipment.forEach(item => {
                const label = document.createElement('label');
                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.name = 'equipment';
                checkbox.value = item.id;
                checkbox.className = 'form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary';
                
                const span = document.createElement('span');
                span.className = 'ml-2 text-sm text-white';
                span.textContent = `${item.name} (${item.code})`;
                
                label.appendChild(checkbox);
                label.appendChild(span);
                equipmentContainer.appendChild(label);
            });
        })
        .catch(error => {
            console.error('Error fetching equipment availability:', error);
            equipmentContainer.innerHTML = `<div class="text-center py-4 text-red-400">{% trans "Error loading equipment" %}: ${error.message}</div>`;
        });
    } catch (error) {
        console.error('Error in checkSessionEquipmentAvailability:', error);
    }
}

// Add new function to fetch available equipment for editing
function fetchAvailableEquipmentForEditing(startTime, endTime, assignedEquipment) {
    try {
        // For editing, we use the first equipment container (index 0)
        // because editing only supports a single session at a time
        const equipmentContainer = document.getElementById('equipment_container_0');
        
        if (!equipmentContainer) {
            console.error("Equipment container not found");
            // Create the equipment container if it doesn't exist yet
            updateSessionDates();
            // Try again after updateSessionDates
            setTimeout(() => {
                const newContainer = document.getElementById('equipment_container_0');
                if (newContainer) {
                    fetchAvailableEquipmentForEditing(startTime, endTime, assignedEquipment);
                } else {
                    console.error("Equipment container still not found after updateSessionDates");
                }
            }, 100);
            return;
        }
        
        // Show loading indicator
        equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Loading available equipment..." %}</span></div>';
        
        // Ensure assigned equipment is an array of strings and is unique
        const uniqueAssignedEquipment = Array.from(new Set(
            (assignedEquipment || []).map(id => String(id))
        ));
        
        console.log('Fetching equipment for session with assigned equipment:', uniqueAssignedEquipment);
        
        // Store the assigned equipment in selectedDates for this session
        if (!selectedDates[0]) selectedDates[0] = {};
        selectedDates[0].equipment = uniqueAssignedEquipment;
        
        // Fetch available equipment
        fetch('{% url "website:check_equipment_availability" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                start_time: startTime,
                end_time: endTime,
                session_id: document.getElementById('session_id').value // Include session ID to exclude current session's bookings
            })
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status !== 'success') {
                throw new Error(data.message || 'Failed to fetch equipment availability');
            }
            
            // Get available equipment, including those already assigned to this session
            let availableEquipment = data.available_equipment || [];
            
            // Create a map of assigned equipment IDs for quick lookup
            const assignedEquipmentMap = {};
            uniqueAssignedEquipment.forEach(id => {
                assignedEquipmentMap[id] = true;
            });
            
            // Add equipment that's assigned to the session but might not be in the available list
            if (uniqueAssignedEquipment.length > 0) {
                // Filter out any assigned equipment that's already in the available list
                const availableIds = availableEquipment.map(eq => String(eq.id));
                const missingEquipment = uniqueAssignedEquipment.filter(id => 
                    !availableIds.includes(String(id))
                );
                
                if (missingEquipment.length > 0) {
                    console.log('Fetching details for missing equipment:', missingEquipment);
                    
                    // Fetch details for missing equipment
                    Promise.all(missingEquipment.map(id => 
                        fetch(`{% url "website:equipment_detail" equipment_id=0 %}`.replace('0', id))
                        .then(resp => resp.json())
                        .catch(() => null) // Ignore errors for specific equipment
                    ))
                    .then(equipmentDetails => {
                        // Add valid equipment details to available list
                        equipmentDetails.forEach(detail => {
                            if (detail && detail.status === 'success' && detail.equipment) {
                                // Check if this equipment is already in the list
                                const isDuplicate = availableEquipment.some(eq => 
                                    String(eq.id) === String(detail.equipment.equipment_id)
                                );
                                
                                if (!isDuplicate) {
                                    availableEquipment.push({
                                        id: detail.equipment.equipment_id,
                                        name: detail.equipment.name,
                                        code: detail.equipment.code,
                                        is_assigned: true
                                    });
                                }
                            }
                        });
                        
                        // Render the combined equipment list
                        renderEquipmentListForSession(equipmentContainer, availableEquipment, assignedEquipmentMap, 0);
                    });
                } else {
                    // Render with just the available equipment
                    renderEquipmentListForSession(equipmentContainer, availableEquipment, assignedEquipmentMap, 0);
                }
            } else {
                // No assigned equipment, just render available equipment
                renderEquipmentListForSession(equipmentContainer, availableEquipment, assignedEquipmentMap, 0);
            }
        })
        .catch(error => {
            console.error('Error fetching equipment availability:', error);
            equipmentContainer.innerHTML = `<div class="text-center py-4 text-red-400">{% trans "Error loading equipment" %}: ${error.message}</div>`;
        });
    } catch (error) {
        console.error('Error in fetchAvailableEquipmentForEditing:', error);
    }
}

// Create a new function specifically for rendering equipment list for a session
function renderEquipmentListForSession(container, equipment, assignedEquipmentMap, sessionIndex) {
    if (!equipment || equipment.length === 0) {
        container.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No equipment available" %}</span></div>';
        return;
    }
    
    // Clear the container first to prevent duplicates
    container.innerHTML = '';
    
    // Sort equipment: assigned first, then by name
    equipment.sort((a, b) => {
        const aAssigned = assignedEquipmentMap[a.id] || assignedEquipmentMap[String(a.id)] || a.is_assigned;
        const bAssigned = assignedEquipmentMap[b.id] || assignedEquipmentMap[String(b.id)] || b.is_assigned;
        
        if (aAssigned && !bAssigned) return -1;
        if (!aAssigned && bAssigned) return 1;
        return a.name.localeCompare(b.name);
    });
    
    // Create checkbox for each equipment
    equipment.forEach(item => {
        // Ensure we're dealing with string IDs consistently
        const itemId = String(item.id);
        const isAssigned = assignedEquipmentMap[itemId] || assignedEquipmentMap[item.id] || item.is_assigned;
        
        const label = document.createElement('label');
        label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
        if (isAssigned) {
            label.className += ' bg-blue-900/30';
        }
        
        const checkbox = document.createElement('input');
        checkbox.type = 'checkbox';
        checkbox.name = `equipment_${sessionIndex}`;
        checkbox.value = itemId;
        checkbox.className = 'form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary';
        checkbox.checked = isAssigned;
        
        const span = document.createElement('span');
        span.className = 'ml-2 text-sm text-white';
        span.textContent = `${item.name} (${item.code})`;
        
        if (isAssigned) {
            const badge = document.createElement('span');
            badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
            badge.textContent = '{% trans "Selected" %}';
            span.appendChild(badge);
        }
        
        label.appendChild(checkbox);
        label.appendChild(span);
        container.appendChild(label);
        
        // Add change event to update session data
        checkbox.addEventListener('change', function() {
            // Initialize the equipment array if needed
            if (!selectedDates[sessionIndex]) selectedDates[sessionIndex] = {};
            if (!selectedDates[sessionIndex].equipment) selectedDates[sessionIndex].equipment = [];
            
            // Always work with string IDs for consistency
            const equipId = String(item.id);
            
            if (this.checked) {
                // Add to equipment list if not already there
                if (!selectedDates[sessionIndex].equipment.includes(equipId)) {
                    selectedDates[sessionIndex].equipment.push(equipId);
                }
                
                // Update visual style
                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md bg-blue-900/30';
                
                // Add badge if not already present
                if (!span.querySelector('span')) {
                    const badge = document.createElement('span');
                    badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                    badge.textContent = '{% trans "Selected" %}';
                    span.appendChild(badge);
                }
            } else {
                // Remove from equipment list
                selectedDates[sessionIndex].equipment = selectedDates[sessionIndex].equipment.filter(id => 
                    id !== equipId && String(id) !== equipId
                );
                
                // Update visual style
                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                
                // Remove badge if present
                const badge = span.querySelector('span');
                if (badge) {
                    span.removeChild(badge);
                }
            }
        });
    });
}

// Expose the function to the global scope so it can be called from index.html
window.modalOpenSession = modalOpenSession;

// Add this function after updateSessionDates
function checkForConflicts(sessionDate, sessionIndex) {
    if (!sessionDate || !sessionDate.start_time || !sessionDate.end_time) {
        return false;
    }
    
    try {
        // Get selected trainers and room
        const locationSelect = document.getElementById('location');
        const trainerCheckboxes = document.querySelectorAll(`input[name="trainer_${sessionIndex}"]:checked`);
        const roomSelect = document.getElementById(`room_${sessionIndex}`);
        
        if (!locationSelect) {
            return false;
        }
        
        const selectedTrainers = Array.from(trainerCheckboxes).map(cb => cb.value);
        const location = locationSelect.value;
        const roomId = (location === 'PHYSICAL' && roomSelect) ? roomSelect.value : '';
        
        if (selectedTrainers.length === 0 || (location === 'PHYSICAL' && !roomId)) {
            return false;
        }
        
        // Convert session date to Date objects for comparison
        const sessionStart = new Date(sessionDate.start_time);
        const sessionEnd = new Date(sessionDate.end_time);
        
        // Get the date input field
        const dateInput = document.querySelector(`[name="session_date_${sessionIndex}"]`);
        
        let conflicts = [];
        
        // Get the current session ID if in edit mode
        const currentSessionId = isEditing ? document.getElementById('session_id')?.value : null;
        
        // Get up-to-date trainer availability using the availability endpoint
        const checkTrainerAvailability = async () => {
            try {
                // Create URL with query parameters
                const url = new URL('{% url "website:check_availability" %}', window.location.origin);
                url.searchParams.append('start_time', sessionDate.start_time);
                url.searchParams.append('end_time', sessionDate.end_time);
                if (currentSessionId) {
                    url.searchParams.append('session_id', currentSessionId);
                }
                
                const response = await fetch(url);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const data = await response.json();
                if (data.status !== 'success') {
                    throw new Error(data.message || 'Failed to fetch trainer availability');
                }
                
                // Get available trainer IDs
                const availableTrainerIds = data.available_resources?.trainers || [];
                
                // Find unavailable trainers among selected trainers
                const unavailableTrainers = selectedTrainers.filter(trainerId => 
                    !availableTrainerIds.includes(parseInt(trainerId)) && 
                    !availableTrainerIds.includes(trainerId)
                );
                
                if (unavailableTrainers.length > 0) {
                    // Get trainer names for better error messages
                    const trainerNames = [];
                    for (const trainerId of unavailableTrainers) {
                        const trainerLabel = document.querySelector(`label input[value="${trainerId}"]`)?.closest('label')?.textContent.trim();
                        if (trainerLabel) {
                            trainerNames.push(trainerLabel);
                        } else {
                            trainerNames.push(`Trainer #${trainerId}`);
                        }
                    }
                    
                    conflicts.push(`Trainers not available: ${trainerNames.join(', ')}`);
                }
                
                return conflicts.length > 0;
            } catch (error) {
                console.error('Error checking trainer availability:', error);
                conflicts.push('Error checking trainer availability');
                return true;
            }
        };
        
        // Check room availability
        if (location === 'PHYSICAL' && roomId) {
            // Check for overlapping room availability records
            const roomConflicts = busySlots.filter(slot => 
                slot.room_id && 
                slot.room_id.toString() === roomId &&
                new Date(slot.start_time) < sessionEnd && 
                new Date(slot.end_time) > sessionStart &&
                (!currentSessionId || slot.session_id?.toString() !== currentSessionId.toString())
            );
            
            if (roomConflicts.length > 0) {
                conflicts.push(`Room is busy from ${formatDateTimeRange(roomConflicts[0].start_time, roomConflicts[0].end_time)}`);
            }
        }
        
        // First check using busy slots in memory
        for (const slot of busySlots) {
            try {
                // Skip invalid slots
                if (!slot.start_time || !slot.end_time) {
                    console.warn("Skipping invalid busy slot:", slot);
                    continue;
                }
                
                // Skip slots that match the current session ID when in edit mode
                if (isEditing && currentSessionId && slot.session_id && slot.session_id.toString() === currentSessionId.toString()) {
                    console.log("Skipping conflict check with current session:", slot);
                    continue;
                }
                
                const slotStart = new Date(slot.start_time);
                const slotEnd = new Date(slot.end_time);
                
                // Check for time overlap
                if (sessionStart < slotEnd && sessionEnd > slotStart) {
                    // Check if this conflicts with any selected trainer or room
                    if (slot.trainer_id && selectedTrainers.includes(slot.trainer_id.toString())) {
                        // Find the trainer name for the conflict message
                        const trainerLabel = document.querySelector(`label input[value="${slot.trainer_id}"]`)?.closest('label')?.textContent.trim();
                        conflicts.push(`Trainer ${trainerLabel || 'Unknown'} is busy from ${formatDateTimeRange(slot.start_time, slot.end_time)}`);
                    }
                }
            } catch (e) {
                console.error("Error checking slot conflict:", e);
            }
        }
        
        // Also check the server for up-to-date availability
        checkTrainerAvailability();
        
        if (conflicts.length > 0) {
            // If there are conflicts, highlight the inputs
            if (dateInput) {
                dateInput.classList.add('border-red-500');
                dateInput.classList.remove('border-gray-600');
            }
            
            return conflicts.join('. ');
        }
        
        // No conflicts found
        if (dateInput) {
            dateInput.classList.remove('border-red-500');
            dateInput.classList.add('border-gray-600');
        }
        
        return false;
    } catch (error) {
        console.error('Error in checkForConflicts:', error);
        return false;
    }
}

function formatDateTime(date) {
    if (!date) return '';
    
    const options = { 
        year: 'numeric', 
        month: 'short', 
        day: 'numeric',
        hour: '2-digit', 
        minute: '2-digit'
    };
    
    return date.toLocaleDateString(undefined, options);
}

// Add updateConflictSummary function
function updateConflictSummary() {
    try {
        const summaryDiv = document.getElementById('conflictSummary');
        const conflictList = document.getElementById('conflictList');
        
        if (!summaryDiv || !conflictList) return;
        
        // Collect all conflicts
        let conflicts = [];
        
        // Only check dates that have been selected
        for (let i = 0; i < selectedDates.length; i++) {
            if (selectedDates[i] && selectedDates[i].start_time && selectedDates[i].end_time) {
                const conflict = checkForConflicts(selectedDates[i], i);
                if (conflict) {
                    conflicts.push({
                        sessionIndex: i,
                        message: conflict
                    });
                }
            }
        }
        
        // Update the summary
        if (conflicts.length > 0) {
            // Clear the list
            conflictList.innerHTML = '';
            
            // Add each conflict
            conflicts.forEach(conflict => {
                const li = document.createElement('li');
                li.textContent = isEditing ? 
                    `${conflict.message}` : 
                    `Session ${conflict.sessionIndex + 1}: ${conflict.message}`;
                conflictList.appendChild(li);
            });
            
            // Show the summary
            summaryDiv.classList.remove('hidden');
            
            // Disable save button
            const saveButton = document.getElementById('saveSessionBtn');
            if (saveButton) {
                saveButton.disabled = true;
                saveButton.textContent = '{% trans "Cannot Save - Conflicts Found" %}';
            }
        } else {
            // Hide the summary if no conflicts
            summaryDiv.classList.add('hidden');
            
            // Enable save button if we have the required number of sessions
            const validSessions = selectedDates.filter(date => date && date.start_time && date.end_time).length;
            const courseSelect = document.getElementById('course');
            
            if (courseSelect) {
                const selectedOption = courseSelect.options[courseSelect.selectedIndex];
                if (selectedOption) {
                    const expectedSessions = parseInt(selectedOption.getAttribute('data-sessions') || '0');
                    
                    const saveButton = document.getElementById('saveSessionBtn');
                    if (saveButton) {
                        if (isEditing) {
                            // In edit mode, just ensure we have at least one valid date
                            const hasRequiredSessions = validSessions > 0;
                            saveButton.disabled = !hasRequiredSessions;
                        } else {
                            // In create mode, ensure we have the expected number of sessions
                        const hasRequiredSessions = validSessions === expectedSessions;
                        saveButton.disabled = !hasRequiredSessions;
                        }
                        
                        saveButton.textContent = '{% trans "Save" %}';
                    }
                }
            }
        }
    } catch (error) {
        console.error('Error updating conflict summary:', error);
    }
}

// Function to check equipment availability
async function checkEquipmentAvailability() {
    const dateInputs = document.querySelectorAll('[name^="session_date_"]');
    const warningDiv = document.getElementById('equipment-availability-warning');
    const conflictsDiv = document.getElementById('equipment-conflicts');
    const equipmentContainer = document.querySelector('.equipment-container .space-y-2');
    
    if (!dateInputs.length || !equipmentContainer) {
        return;
    }

    // Get all valid session dates and times
    const sessions = [];
    for (let i = 0; i < requiredSessions; i++) {
        if (selectedDates[i] && selectedDates[i].start_time && selectedDates[i].end_time) {
            sessions.push({
                start_time: selectedDates[i].start_time,
                end_time: selectedDates[i].end_time
            });
        }
    }

    if (!sessions.length) {
        if (warningDiv) warningDiv.classList.add('hidden');
        return;
    }

    try {
        // Store current selections
        const currentSelections = Array.from(document.querySelectorAll('input[name="equipment"]:checked')).map(cb => cb.value);
        
        // Disable equipment checkboxes while checking
        document.querySelectorAll('input[name="equipment"]').forEach(checkbox => {
            checkbox.disabled = true;
        });

        // Check availability for first session (we'll use this to determine initially available equipment)
        const response = await fetch('{% url "website:check_equipment_availability" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                start_time: sessions[0].start_time,
                end_time: sessions[0].end_time,
                session_id: document.getElementById('session_id')?.value || '' // Include session ID to exclude current session's bookings
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        
        if (data.status === 'success') {
            // Clear current equipment options
            equipmentContainer.innerHTML = '';
            
            // Add available equipment options
            if (data.available_equipment && data.available_equipment.length > 0) {
                data.available_equipment.forEach(equipment => {
                    const label = document.createElement('label');
                    label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                    
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.name = 'equipment';
                    checkbox.value = equipment.id;
                    checkbox.className = 'form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary';
                    // Restore previous selection if equipment was selected
                    checkbox.checked = currentSelections.includes(equipment.id);
                    
                    const span = document.createElement('span');
                    span.className = 'ml-2 text-sm text-white';
                    span.textContent = `${equipment.name} (${equipment.code})`;
                    
                    label.appendChild(checkbox);
                    label.appendChild(span);
                    equipmentContainer.appendChild(label);
                });
            } else {
                equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No equipment available at selected time" %}</span></div>';
            }
            
            // Hide warning if no conflicts
            if (warningDiv) warningDiv.classList.add('hidden');
            
            // Show warning if there are conflicts
            if (data.conflicts && data.conflicts.length > 0 && warningDiv && conflictsDiv) {
                warningDiv.classList.remove('hidden');
                conflictsDiv.innerHTML = '';
                
                const conflictsList = document.createElement('ul');
                conflictsList.className = 'list-disc list-inside mt-2';
                
                data.conflicts.forEach(conflict => {
                    const li = document.createElement('li');
                    li.textContent = `${conflict.equipment_name} is booked for ${formatTimeRange(conflict.start_time, conflict.end_time)}`;
                    conflictsList.appendChild(li);
                });
                
                conflictsDiv.appendChild(conflictsList);
            }
        }
    } catch (error) {
        console.error('Error checking equipment availability:', error);
        if (conflictsDiv) {
            conflictsDiv.textContent = '{% trans "Error checking equipment availability" %}';
        }
        if (warningDiv) {
            warningDiv.classList.remove('hidden');
        }
    } finally {
        // Re-enable equipment checkboxes
        document.querySelectorAll('input[name="equipment"]').forEach(checkbox => {
            checkbox.disabled = false;
        });
    }
}

// Add event listeners for date changes to trigger equipment availability check
function addDateChangeListeners() {
    document.querySelectorAll('[name^="session_date_"]').forEach(dateInput => {
        dateInput.addEventListener('change', checkEquipmentAvailability);
    });
}

// Add handler for location change
function handleLocationChange() {
    try {
        const locationSelect = document.getElementById('location');
        const roomFieldContainer = document.getElementById('roomFieldContainer');
        
        if (!locationSelect || !roomFieldContainer) {
            return;
        }
        
        const isPhysical = locationSelect.value === 'PHYSICAL';
        
        // Show/hide room field based on location
        roomFieldContainer.classList.toggle('hidden', !isPhysical);
        
        // Update required attribute on room select
        const roomSelect = document.getElementById('room');
        if (roomSelect) {
            roomSelect.required = isPhysical;
        }
        
        // Check availability after location changes
        checkAvailability();
    } catch (error) {
        console.error('Error in handleLocationChange:', error);
    }
}

// Add this to initialize event listeners 
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for location change
    const locationSelect = document.getElementById('location');
    if (locationSelect) {
        locationSelect.addEventListener('change', handleLocationChange);
    }
    
    // Add event listener for slot type change
    const slotTypeSelect = document.getElementById('slot_type');
    if (slotTypeSelect) {
        slotTypeSelect.addEventListener('change', handleSlotTypeChange);
    }
});

// Format a date range for display
function formatDateTimeRange(startIso, endIso) {
    try {
        const startDate = new Date(startIso);
        const endDate = new Date(endIso);
        
        if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
            console.error(`Invalid date format: start=${startIso}, end=${endIso}`);
            return `${startIso} to ${endIso}`;
        }
        
        // Check if both dates are on the same day
        const sameDay = startDate.toDateString() === endDate.toDateString();
        
        const dateOptions = { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric'
        };
        
        const timeOptions = {
            hour: '2-digit', 
            minute: '2-digit'
        };
        
        if (sameDay) {
            // If same day, show "Jan 1, 2023 from 9:00 AM to 5:00 PM"
            return `${startDate.toLocaleDateString(undefined, dateOptions)} from ${startDate.toLocaleTimeString(undefined, timeOptions)} to ${endDate.toLocaleTimeString(undefined, timeOptions)}`;
        } else {
            // If different days, show "Jan 1, 2023 9:00 AM to Jan 2, 2023 5:00 PM"
            return `${startDate.toLocaleDateString(undefined, dateOptions)} ${startDate.toLocaleTimeString(undefined, timeOptions)} to ${endDate.toLocaleDateString(undefined, dateOptions)} ${endDate.toLocaleTimeString(undefined, timeOptions)}`;
        }
    } catch (error) {
        console.error("Error formatting date range:", error);
        return `${startIso} to ${endIso}`;
    }
}

// Initialize trainers data
window.trainers = [
    {% for trainer in trainers %}
    {
        trainer_id: {{ trainer.trainer_id }},
        name: "{{ trainer.user.get_full_name|default:trainer.user.username|escapejs }}",
        status: "{{ trainer.status }}"
    },
    {% endfor %}
];

// Add new function to fetch available trainers for editing
function fetchAvailableTrainersForEditing(startTime, endTime, assignedTrainers) {
    try {
        // For editing, we use the first trainer container (index 0)
        // because editing only supports a single session at a time
        const trainerContainer = document.getElementById('trainer_container_0');
        
        if (!trainerContainer) {
            console.error("Trainer container not found");
            // Create the trainer container if it doesn't exist yet
            updateSessionDates();
            // Try again after updateSessionDates
            setTimeout(() => {
                const newContainer = document.getElementById('trainer_container_0');
                if (newContainer) {
                    fetchAvailableTrainersForEditing(startTime, endTime, assignedTrainers);
                } else {
                    console.error("Trainer container still not found after updateSessionDates");
                }
            }, 100);
            return;
        }
        
        // Show loading indicator
        trainerContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Loading available trainers..." %}</span></div>';
        
        // Ensure assigned trainers is an array of strings and is unique
        const uniqueAssignedTrainers = Array.from(new Set(
            (assignedTrainers || []).map(id => String(id))
        ));
        
        console.log('Fetching trainers for session with assigned trainers:', uniqueAssignedTrainers);
        
        // Store the assigned trainers in selectedDates for this session
        if (!selectedDates[0]) selectedDates[0] = {};
        selectedDates[0].trainers = uniqueAssignedTrainers;
        
        // Fetch available trainers
        const url = new URL(`${window.location.origin}/en/sessions/check-availability/`);
        url.searchParams.append('start_time', startTime);
        url.searchParams.append('end_time', endTime);
        
        // Include session ID to exclude current session's bookings
        const sessionId = document.getElementById('session_id')?.value;
        if (sessionId) {
            url.searchParams.append('session_id', sessionId);
        }
        
        fetch(url)
            .then(response => {
                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                return response.json();
            })
            .then(data => {
                // Get all trainers
                const allTrainers = window.trainers || [];
                if (!allTrainers.length) {
                    console.error('No trainers available');
                    trainerContainer.innerHTML = '<div class="text-red-500">No trainers available</div>';
                    return;
                }
                
                // Get the list of available trainer IDs
                const availableTrainerIds = data.available_resources.trainers || [];
                
                // Clear existing content
                trainerContainer.innerHTML = '';
                
                // Create and append trainer checkboxes for all trainers
                allTrainers.forEach(trainer => {
                    const isAvailable = availableTrainerIds.includes(trainer.trainer_id);
                    const isSelected = uniqueAssignedTrainers.includes(String(trainer.trainer_id));
                    
                    // Skip trainers that are not available and not selected
                    if (!isAvailable && !isSelected) {
                        return;
                    }
                    
                    const trainerDiv = document.createElement('div');
                    trainerDiv.className = 'py-2';
                    
                    const label = document.createElement('label');
                    label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                    
                    if (isSelected) {
                        label.className += ' bg-blue-900/30';
                    }
                    
                    const checkbox = document.createElement('input');
                    checkbox.type = 'checkbox';
                    checkbox.id = `trainer-0-${trainer.trainer_id}`;
                    checkbox.name = `trainer_0`;
                    checkbox.value = trainer.trainer_id;
                    checkbox.checked = isSelected;
                    // Disable checkbox for unavailable trainers that aren't already selected
                    checkbox.disabled = !isAvailable && !isSelected;
                    checkbox.className = 'form-checkbox h-4 w-4 text-blue-600 transition duration-150 ease-in-out';
                    checkbox.dataset.trainerIndex = trainer.trainer_id;
                    
                    const span = document.createElement('span');
                    span.className = 'ml-2 text-sm text-white';
                    span.textContent = trainer.name;
                    
                    // Add opacity to unavailable trainers
                    if (!isAvailable && !isSelected) {
                        label.classList.add('opacity-50');
                    }
                    
                    // Add "Selected" badge for selected trainers
                    if (isSelected) {
                        const selectedBadge = document.createElement('span');
                        selectedBadge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                        selectedBadge.textContent = '{% trans "Selected" %}';
                        span.appendChild(selectedBadge);
                    }
                    
                    label.appendChild(checkbox);
                    label.appendChild(span);
                    trainerDiv.appendChild(label);
                    trainerContainer.appendChild(trainerDiv);
                    
                    // Add change event listener to update selectedDates when checkbox changes
                    checkbox.addEventListener('change', function() {
                        if (!selectedDates[0]) selectedDates[0] = {};
                        if (!selectedDates[0].trainers) selectedDates[0].trainers = [];
                        
                        const trainerId = parseInt(this.value);
                        
                        if (this.checked) {
                            // Add to trainers list if not already there
                            if (!selectedDates[0].trainers.includes(trainerId)) {
                                selectedDates[0].trainers.push(trainerId);
                            }
                            
                            // Update visual style
                            label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md bg-blue-900/30';
                            
                            // Add badge if not already present
                            if (!span.querySelector('span')) {
                                const badge = document.createElement('span');
                                badge.className = 'ml-2 inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800';
                                badge.textContent = '{% trans "Selected" %}';
                                span.appendChild(badge);
                            }
                        } else {
                            // Remove from trainers list
                            selectedDates[0].trainers = selectedDates[0].trainers.filter(id => 
                                id !== trainerId
                            );
                            
                            // Update visual style
                            label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                            
                            // Remove badge if present
                            const badge = span.querySelector('span');
                            if (badge) {
                                span.removeChild(badge);
                            }
                            
                            // Add opacity if this trainer is unavailable
                            if (!isAvailable) {
                                label.classList.add('opacity-50');
                            }
                            
                            // Delete trainer availability record when unselected during editing
                            const sessionId = document.getElementById('session_id')?.value;
                            if (sessionId && isEditing) {
                                deleteTrainerAvailability(trainerId, sessionId);
                            }
                        }
                    });
                });
            })
            .catch(error => {
                console.error('Error fetching trainer availability:', error);
                trainerContainer.innerHTML = `<div class="text-red-500">Error fetching trainer availability: ${error.message}</div>`;
            });
    } catch (error) {
        console.error('Error in fetchAvailableTrainersForEditing:', error);
    }
}

// Add a function to delete trainer availability for a session
function deleteTrainerAvailability(trainerId, sessionId) {
    if (!sessionId) return;
    
    fetch('{% url "website:delete_trainer_availability" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            trainer_id: trainerId,
            session_id: sessionId
        })
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return response.json();
    })
    .then(data => {
        if (data.status === 'success') {
            console.log(`Successfully deleted availability record for trainer ${trainerId}`);
        } else {
            console.error(`Failed to delete availability record: ${data.message}`);
        }
    })
    .catch(error => {
        console.error('Error deleting trainer availability:', error);
    });
}

// Add function to update available rooms for a specific session
async function updateAvailableRoomsForSession(sessionIndex) {
    const sessionDate = selectedDates[sessionIndex];
    if (!sessionDate || !sessionDate.start_time || !sessionDate.end_time) {
        console.log('No session date or time available');
        return;
    }

    const roomSelect = document.getElementById(`room_${sessionIndex}`);
    if (!roomSelect) {
        console.error(`Room select not found for session ${sessionIndex}`);
        return;
    }

    try {
        // Store current selection
        const currentSelection = roomSelect.value;

        // Check availability for the time slot
        const url = new URL(`${window.location.origin}/en/sessions/check-availability/`);
        url.searchParams.append('start_time', sessionDate.start_time);
        url.searchParams.append('end_time', sessionDate.end_time);
        if (sessionDate.session_id) {
            url.searchParams.append('session_id', sessionDate.session_id);
        }

        // First, backup all options from the main room select
        const mainRoomSelect = document.getElementById('room');
        if (!mainRoomSelect) {
            console.error('Main room select not found');
            return;
        }

        // Get all options from the main room select
        const allOptions = Array.from(mainRoomSelect.options).map(option => ({
            value: option.value,
            text: option.text,
            roomType: option.dataset.roomType,
            fixedEquipment: option.dataset.fixedEquipment,
            fixedEquipmentNames: option.dataset.fixedEquipmentNames
        }));

        console.log('All available rooms:', allOptions);

        // Temporarily disable the select while updating
        roomSelect.disabled = true;

        const response = await fetch(url);
        if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
        const data = await response.json();

        // Get the list of available room IDs
        const availableRoomIds = data.available_resources.rooms || [];

        // Get course and required room type for this specific session
        const courseSelect = document.getElementById('course');
        let requiredRoomTypeId = null;
        
        if (courseSelect && courseSelect.value) {
            const selectedOption = courseSelect.options[courseSelect.selectedIndex];
            const sessionRoomTypesStr = selectedOption.dataset.sessionRoomTypes || '{}';
            console.log('Session room types:', sessionRoomTypesStr);
            
            try {
                // Parse the session room types JSON
                let sessionRoomTypes = {};
                if (sessionRoomTypesStr.includes("'")) {
                    // Fix Python-style dictionaries
                    const jsonValue = sessionRoomTypesStr.replace(/'/g, '"');
                    sessionRoomTypes = JSON.parse(jsonValue);
                } else {
                    sessionRoomTypes = JSON.parse(sessionRoomTypesStr);
                }
                console.log('Parsed session room types:', sessionRoomTypes);
                
                // Log all keys in the sessionRoomTypes object to debug
                console.log('Session room types keys:', Object.keys(sessionRoomTypes));
                
                // Get the current session number (1-based)
                const sessionNumber = sessionIndex + 1;
                console.log('Trying to access session:', sessionNumber.toString());
                
                // Try multiple approaches to get the room type ID for this session
                
                // First using string key (most common format from Django)
                requiredRoomTypeId = sessionRoomTypes[sessionNumber.toString()];
                console.log('Room type ID (string key):', requiredRoomTypeId);
                
                // If not found, try using number as key
                if (requiredRoomTypeId === undefined) {
                    requiredRoomTypeId = sessionRoomTypes[sessionNumber];
                    console.log('Room type ID (number key):', requiredRoomTypeId);
                }
                
                // Print the value to debug any issues with empty strings vs null/undefined
                console.log("Required room type ID:", requiredRoomTypeId);
                console.log("Type of required room type ID:", typeof requiredRoomTypeId);
                
                // If requiredRoomTypeId is still undefined, empty string, or null, default to showing all rooms
                if (requiredRoomTypeId === undefined || requiredRoomTypeId === null || requiredRoomTypeId === "") {
                    console.log('No room type requirement found for session ' + sessionNumber);
                    requiredRoomTypeId = null;
                } else {
                    // Ensure we have a consistent type for comparison (string)
                    requiredRoomTypeId = requiredRoomTypeId.toString();
                    console.log(`Session ${sessionNumber} requires room type ID: ${requiredRoomTypeId}`);
                }
            } catch (error) {
                console.error('Error parsing session room types:', error);
                // Default to showing all rooms on error
                requiredRoomTypeId = null;
            }
        }

        // Clear existing options
        roomSelect.innerHTML = '';

        // Add default option
        const defaultOption = document.createElement('option');
        defaultOption.value = '';
        defaultOption.textContent = '{% trans "Select a room" %}';
        roomSelect.appendChild(defaultOption);

        // Add options for available rooms
        let hasOptions = false;
        let filteredByType = 0;
        let filteredByAvailability = 0;
        
        console.log('Required room type ID:', requiredRoomTypeId);
        console.log('Available room IDs:', availableRoomIds);
        
        allOptions.forEach(option => {
            // Skip the default empty option
            if (!option.value) return;
            
            // Only filter rooms by type if we have a valid required room type
            let roomTypeMatch = true;
            if (requiredRoomTypeId !== null) {
                roomTypeMatch = option.roomType && option.roomType.toString() === requiredRoomTypeId.toString();
                if (!roomTypeMatch) {
                    console.log(`Room ${option.text} (ID: ${option.value}, type: ${option.roomType}) does not match required type ${requiredRoomTypeId}`);
                    filteredByType++;
                    return;
                }
            }
            
            // Only include available rooms or the currently selected room
            if (availableRoomIds.includes(parseInt(option.value)) || option.value === currentSelection) {
                const newOption = document.createElement('option');
                newOption.value = option.value;
                newOption.textContent = option.text;
                newOption.dataset.roomType = option.roomType;
                
                // Add fixed equipment data attributes
                if (option.fixedEquipment) {
                    newOption.dataset.fixedEquipment = option.fixedEquipment;
                }
                if (option.fixedEquipmentNames) {
                    newOption.dataset.fixedEquipmentNames = option.fixedEquipmentNames;
                }
                
                roomSelect.appendChild(newOption);
                hasOptions = true;
                console.log(`Added room ${option.text} (ID: ${option.value}, type: ${option.roomType}) to options`);
            } else {
                console.log(`Room ${option.text} (ID: ${option.value}) is not available at this time`);
                filteredByAvailability++;
            }
        });
        
        console.log(`Room filtering stats: ${filteredByType} filtered by type, ${filteredByAvailability} filtered by availability, ${hasOptions ? 'some' : 'no'} options available`);

        // Re-enable the select
        roomSelect.disabled = false;

        // Restore the previous selection if it exists
        if (currentSelection && Array.from(roomSelect.options).some(opt => opt.value === currentSelection)) {
            roomSelect.value = currentSelection;
        } else {
            roomSelect.value = '';
        }

        // Update session data with the selected room
        if (!selectedDates[sessionIndex]) selectedDates[sessionIndex] = {};
        selectedDates[sessionIndex].room = roomSelect.value;

        // Show message if no rooms are available
        if (!hasOptions) {
            console.log('No rooms available for this time slot');
            // Maybe add a message near the select
        }
        
        // If a room is selected, display fixed equipment info for that room
        if (roomSelect.value) {
            console.log(`Room auto-selected after updating options: ${roomSelect.value}`);
            displayFixedEquipmentInfo(sessionIndex);
        }
    } catch (error) {
        console.error('Error fetching room availability:', error);
        roomSelect.disabled = false;
    }
}

// Add new function to display fixed equipment info and compare with course requirements
function displayFixedEquipmentInfo(sessionIndex) {
    try {
        // Get containers
        const fixedEquipmentContainer = document.getElementById(`fixed_equipment_info_${sessionIndex}`);
        if (!fixedEquipmentContainer) {
            console.error(`Fixed equipment container not found for session ${sessionIndex}`);
            return;
        }
        
        // Reset container
        fixedEquipmentContainer.innerHTML = '';
        fixedEquipmentContainer.classList.add('hidden');
        
        // Check if we have a room selected
        const roomSelect = document.getElementById(`room_${sessionIndex}`);
        if (!roomSelect || !roomSelect.value) {
            console.log(`No room selected for session ${sessionIndex}`);
            return;
        }
        
        // Get selected room option
        const selectedRoomOption = roomSelect.options[roomSelect.selectedIndex];
        if (!selectedRoomOption) {
            console.error(`Selected room option not found for session ${sessionIndex}`);
            return;
        }
        
        console.log(`Room selected for session ${sessionIndex}:`, selectedRoomOption.textContent);
        console.log(`Room data attributes:`, {
            value: selectedRoomOption.value,
            roomType: selectedRoomOption.dataset.roomType,
            fixedEquipment: selectedRoomOption.dataset.fixedEquipment,
            fixedEquipmentNames: selectedRoomOption.dataset.fixedEquipmentNames
        });
        
        // Get fixed equipment from the selected room
        const fixedEquipmentIds = selectedRoomOption.dataset.fixedEquipment || '';
        const fixedEquipmentNames = selectedRoomOption.dataset.fixedEquipmentNames || '';
        
        console.log(`Fixed equipment IDs: "${fixedEquipmentIds}"`);
        console.log(`Fixed equipment names: "${fixedEquipmentNames}"`);
        
        if (!fixedEquipmentIds || fixedEquipmentIds.trim() === '') {
            // No fixed equipment in this room
            console.log(`No fixed equipment found for room ${selectedRoomOption.textContent}`);
            fixedEquipmentContainer.innerHTML = `
                <div class="p-2 bg-yellow-500/20 border border-yellow-500/30 rounded-md">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm text-yellow-400">{% trans "This room has no fixed equipment" %}</p>
                        </div>
                    </div>
                </div>
            `;
            fixedEquipmentContainer.classList.remove('hidden');
            return;
        }
        
        // Parse fixed equipment
        const fixedEquipmentIdArray = fixedEquipmentIds.split(',').filter(id => id.trim() !== '');
        const fixedEquipmentNameArray = fixedEquipmentNames.split(',').filter(name => name.trim() !== '');
        
        console.log(`Parsed equipment IDs:`, fixedEquipmentIdArray);
        console.log(`Parsed equipment names:`, fixedEquipmentNameArray);
        
        // Get required equipment categories from the selected course
        const courseSelect = document.getElementById('course');
        if (!courseSelect || !courseSelect.value) {
            return;
        }
        
        const selectedCourseOption = courseSelect.options[courseSelect.selectedIndex];
        if (!selectedCourseOption) {
            return;
        }
        
        // Try to parse equipment categories
        let equipmentCategories = {};
        try {
            const equipmentCategoriesStr = selectedCourseOption.dataset.equipmentCategories || '{}';
            equipmentCategories = JSON.parse(equipmentCategoriesStr);
        } catch (error) {
            console.error('Error parsing equipment categories:', error);
        }
        
        // If we have equipment categories data, check if the fixed equipment meets the requirements
        if (Object.keys(equipmentCategories).length > 0) {
            // Here we'd need to compare categories with actual equipment
            // This is simplified because we don't have category info for each equipment
            // A more complete solution would require additional API calls
            
            // For now, just display the fixed equipment
            let html = `
                <div class="p-2 bg-blue-500/20 border border-blue-500/30 rounded-md">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-blue-400 mb-1">{% trans "Fixed Equipment in Room:" %}</p>
                            <ul class="list-disc list-inside text-sm text-blue-300">
                                ${fixedEquipmentNameArray.map(name => `<li>${name}</li>`).join('')}
                            </ul>
                            <p class="text-sm text-blue-400 mt-2">{% trans "You may need additional equipment based on the course requirements." %}</p>
                        </div>
                    </div>
                </div>
            `;
            
            fixedEquipmentContainer.innerHTML = html;
            fixedEquipmentContainer.classList.remove('hidden');
        } else {
            // No equipment categories, just show fixed equipment
            let html = `
                <div class="p-2 bg-blue-500/20 border border-blue-500/30 rounded-md">
                    <div class="flex items-start">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-sm font-medium text-blue-400 mb-1">{% trans "Fixed Equipment in Room:" %}</p>
                            <ul class="list-disc list-inside text-sm text-blue-300">
                                ${fixedEquipmentNameArray.map(name => `<li>${name}</li>`).join('')}
                            </ul>
                        </div>
                    </div>
                </div>
            `;
            
            fixedEquipmentContainer.innerHTML = html;
            fixedEquipmentContainer.classList.remove('hidden');
        }
    } catch (error) {
        console.error('Error in displayFixedEquipmentInfo:', error);
    }
}

// Function to handle date input change
function handleDateChange(sessionIndex, dateInput, startTimeInput, endTimeInput, roomSelect, roomTypeId) {
    if (!dateInput || !dateInput.value) return;
    
    // Check if the selected date is a weekend or holiday
    const dateObj = new Date(dateInput.value);
    showDateWarning(dateInput, dateObj, sessionIndex);
    
    const date = dateInput.value; // YYYY-MM-DD
    const startTime = startTimeInput ? startTimeInput.value : '09:00'; // HH:MM
    const endTime = endTimeInput ? endTimeInput.value : '16:00'; // HH:MM
    
    // Get selected room ID
    const roomId = roomSelect ? roomSelect.value : null;
    
    // Update or create entry in selectedDates
    if (!selectedDates[sessionIndex]) {
        selectedDates[sessionIndex] = {};
    }
    
    // Add date and times to selectedDates
    selectedDates[sessionIndex].start_time = `${date}T${startTime}`;
    selectedDates[sessionIndex].end_time = `${date}T${endTime}`;
    
    // Update room if selected and location is physical
    if (roomId && document.getElementById('location').value === 'PHYSICAL') {
        selectedDates[sessionIndex].room = roomId;
    }
    
    // Add room type ID if provided
    if (roomTypeId) {
        selectedDates[sessionIndex].room_type_id = roomTypeId;
    }
    
    // Update session count
    updateSessionCount();
    
    // Update available trainers and equipment lists for this session
    updateAvailableTrainersForSession(sessionIndex);
    updateAvailableRoomsForSession(sessionIndex);
    updateAvailableEquipmentForSession(sessionIndex);
    
    // Check for conflicts (only if not in edit mode)
    if (!isEditing) {
        const warningText = document.getElementById(`session_conflict_text_${sessionIndex}`);
        const conflictWarning = document.getElementById(`session_conflict_warning_${sessionIndex}`);
        
        // Check if this date conflicts with existing busy slots
        const conflictMessage = checkForConflicts(selectedDates[sessionIndex], sessionIndex);
        
        if (conflictMessage && warningText && conflictWarning) {
            warningText.textContent = conflictMessage;
            conflictWarning.classList.remove('hidden');
        } else if (warningText && conflictWarning) {
            warningText.textContent = '';
            conflictWarning.classList.add('hidden');
        }
        
        // Update conflict summary after checking all dates
        updateConflictSummary();
    }
}

// Helper functions to get start and end times for different slot types
function getStartTimeForSlotType(slotType) {
    switch(slotType) {
        case 'MORNING':
            return '09:00';
        case 'AFTERNOON':
            return '13:00';
        case 'FULL_DAY':
        default:
            return '09:00';
    }
}

function getEndTimeForSlotType(slotType) {
    switch(slotType) {
        case 'MORNING':
            return '12:00';
        case 'AFTERNOON':
            return '16:00';
        case 'FULL_DAY':
        default:
            return '16:00';
    }
}

// Function to handle course type change and show/hide corporate dropdown
function handleCourseTypeChange() {
    const courseTypeSelect = document.getElementById('course_type');
    const corporateContainer = document.getElementById('corporate_container');
    
    if (!courseTypeSelect || !corporateContainer) return;
    
    if (courseTypeSelect.value === 'CORPORATE') {
        corporateContainer.classList.remove('hidden');
        
        // Fetch corporates from API and populate dropdown
        fetch('/api/get-corporates/')
            .then(response => {
                if (!response.ok) {
                    throw new Error('Network response was not ok');
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    const corporateSelect = document.getElementById('corporate_id');
                    if (corporateSelect) {
                        // Clear existing options except the first one
                        while (corporateSelect.options.length > 1) {
                            corporateSelect.remove(1);
                        }
                        
                        // Add new options
                        data.corporates.forEach(corporate => {
                            const option = document.createElement('option');
                            option.value = corporate.id;
                            option.textContent = corporate.name;
                            corporateSelect.appendChild(option);
                        });
                    }
                } else {
                    console.error('Error fetching corporates:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching corporates:', error);
            });
    } else {
        corporateContainer.classList.add('hidden');
        // Clear corporate ID selection
        const corporateSelect = document.getElementById('corporate_id');
        if (corporateSelect) {
            corporateSelect.value = '';
        }
    }
}
</script> 