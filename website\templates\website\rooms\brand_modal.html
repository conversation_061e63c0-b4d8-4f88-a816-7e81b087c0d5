{% load i18n %}

<!-- Brand Modal -->
<div id="brandModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-md mx-4">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-white/10">
            <h3 id="brandModalTitle" class="text-lg font-medium text-white">{% trans "Add Brand" %}</h3>
            <button onclick="closeBrandModal()" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-4">
            <form id="brandForm" class="space-y-4">
                <div>
                    <label for="brandName" class="block text-sm font-medium text-white/70 mb-1">{% trans "Brand Name" %} *</label>
                    <input type="text" name="name" id="brandName" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end p-4 border-t border-white/10 space-x-3">
            <button onclick="closeBrandModal()" 
                class="px-3 py-1.5 text-sm font-medium text-white/70 hover:text-white bg-white/5 hover:bg-white/10 rounded-md transition-colors">
                {% trans "Cancel" %}
            </button>
            <button onclick="saveBrand()" 
                class="px-3 py-1.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors">
                {% trans "Save Brand" %}
            </button>
        </div>
    </div>
</div>

<script>
    let isEditingBrand = false;
    let currentBrandId = null;

    function openBrandModal(editing = false, brandId = null) {
        try {
            isEditingBrand = editing;
            currentBrandId = brandId;
            document.getElementById('brandModal').classList.remove('hidden');
            
            if (editing) {
                document.getElementById('brandModalTitle').textContent = '{% trans "Edit Brand" %}';
                loadBrandData(brandId);
            } else {
                document.getElementById('brandModalTitle').textContent = '{% trans "Add Brand" %}';
                document.getElementById('brandForm').reset();
            }
        } catch (error) {
            console.error('Error in openBrandModal:', error);
            alert('{% trans "An error occurred while opening the modal" %}');
        }
    }

    function closeBrandModal() {
        document.getElementById('brandModal').classList.add('hidden');
        document.getElementById('brandForm').reset();
        isEditingBrand = false;
        currentBrandId = null;
    }

    async function loadBrandData(brandId) {
        try {
            // Get the current language prefix from the URL or default to '/en'
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const url = `/${languagePrefix}/api/brands/${brandId}/`;
            console.log('Loading brand data from:', url);
            
            const response = await fetch(url, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                credentials: 'include'
            });
            
            if (!response.ok) {
                throw new Error('{% trans "Failed to fetch brand details" %}');
            }
            
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            const data = JSON.parse(responseText);
            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to load brand details" %}');
            }

            const form = document.getElementById('brandForm');
            form.querySelector('[name="name"]').value = data.brand.name;
        } catch (error) {
            console.error('Error:', error);
            alert('{% trans "An error occurred while fetching brand details" %}');
        }
    }

    async function saveBrand() {
        try {
            const form = document.getElementById('brandForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = {
                name: form.querySelector('[name="name"]').value.trim(),
            };
            
            console.log('Sending brand data:', formData);
            const csrfToken = getCookie('csrftoken');
            console.log('CSRF Token:', csrfToken);

            // Get the current language prefix from the URL or default to '/en'
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            
            // Determine URL and method based on whether we're editing or creating
            const method = isEditingBrand ? 'PUT' : 'POST';
            const url = isEditingBrand 
                ? `/${languagePrefix}/api/brands/${currentBrandId}/`
                : `/${languagePrefix}/api/brands/`;
            
            console.log(`Sending ${method} request to:`, url);
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(formData),
                credentials: 'include'
            });

            console.log('Response status:', response.status);
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                throw new Error('Invalid response from server');
            }
            
            console.log('Parsed response data:', data);

            if (!response.ok) {
                throw new Error(data.message || '{% trans "Failed to save brand" %}');
            }

            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to save brand" %}');
            }

            // Success - close modal and reload page
            console.log('Brand saved successfully');
            closeBrandModal();
            window.location.reload();
        } catch (error) {
            console.error('Error saving brand:', error);
            alert(error.message || '{% trans "An error occurred while saving the brand" %}');
        }
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script> 