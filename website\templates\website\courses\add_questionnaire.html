{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="mx-auto p-4 max-w-5xl" style="background-color: #192440;">
    <div class="mb-6 flex justify-between items-center">
        <h1 class="text-2xl md:text-3xl font-bold text-white">{% trans "Add New Questionnaire" %}</h1>
        <button type="button" onclick="window.location.href='{% url 'website:course_content_list' course_id=course.course_id %}'" class="text-gray-300 hover:text-white">
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>

    <form id="addQuestionnaireForm" method="POST">
        {% csrf_token %}
        <div class="space-y-6">
            <!-- Questionnaire Title -->
            <div>
                <label for="title" class="block text-sm font-medium text-white mb-1">{% trans "Questionnaire Title" %}</label>
                <input type="text" name="title" id="title" required class="w-full py-2 px-3 bg-[#1e3a8a] border border-[#2c4a8a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            
            <!-- session_numbers -->
            <div>
                <label for="session_number" class="block text-sm font-medium text-white mb-1">{% trans "Session Number" %}</label>
                <div class="relative">
                    <select name="session_number" id="session_number" required class="w-full py-2 px-3 bg-[#1e3a8a] border border-[#2c4a8a] rounded-md text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                        <option value="">{% trans "Select session number" %}</option>
                        {% for num in session_numbers %}
                            <option value="{{ num }}">{% trans "Session" %} {{ num }}</option>
                        {% endfor %}
                    </select>
                    <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                        <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Comments -->
            <div>
                <label for="comments" class="block text-sm font-medium text-white mb-1">{% trans "Comments" %}</label>
                <textarea name="comments" id="comments" rows="3" class="w-full py-2 px-3 bg-[#1e3a8a] border border-[#2c4a8a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500"></textarea>
            </div>

            <!-- Total Score Display -->
            <div class="bg-[#1e3a8a] p-4 rounded-md">
                <div class="flex justify-between items-center">
                    <label class="text-lg font-semibold text-white">{% trans "Total Score" %}</label>
                    <div id="totalScoreDisplay" class="text-xl font-bold text-white">0</div>
                </div>
            </div>
            
            <!-- Questions Section -->
            <div>
                <div class="flex justify-between items-center mb-2">
                    <label class="block text-sm font-medium text-white">{% trans "Questions" %}</label>
                    <button type="button" id="addQuestionBtn" class="px-4 py-1.5 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                        {% trans "Add Question" %}
                    </button>
                </div>
                
                <div id="questionsContainer" class="space-y-4">
                    <!-- Questions will be added here dynamically -->
                </div>
            </div>
        </div>
        
        <div class="mt-6 flex justify-end space-x-3">
            <button type="button" onclick="window.location.href='{% url 'website:course_content_list' course_id=course.course_id %}'" class="px-4 py-2 bg-gray-700 text-white rounded-md hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-gray-500">
                {% trans "Cancel" %}
            </button>
            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500">
                {% trans "Save Questionnaire" %}
            </button>
        </div>
    </form>
</div>

<!-- Question Template -->
<template id="question-template">
    <div class="question-item bg-[#1e3a8a] p-4 rounded-md">
        <div class="flex justify-between items-start gap-2">
            <div class="flex-grow">
                <input type="text" name="questions[0][text]" placeholder="{% trans 'Enter question text' %}" required class="w-full py-2 px-3 bg-[#2c4a8a] border border-[#3c5a9a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
            </div>
            <button type="button" class="delete-question text-red-400 hover:text-red-300 p-1">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            </button>
        </div>
        
        <!-- Score input -->
        <div class="mt-3">
            <label class="block text-sm font-medium text-white mb-1">{% trans "Question Score" %}</label>
            <input type="number" name="questions[0][score]" min="0" value="1" class="question-score w-full py-2 px-3 bg-[#2c4a8a] border border-[#3c5a9a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
        </div>
        
        <div class="mt-4">
            <label class="block text-sm font-medium text-white mb-1">{% trans "Question Type" %}</label>
            <div class="relative">
                <select name="questions[0][type]" class="question-type w-full py-2 px-3 bg-[#2c4a8a] border border-[#3c5a9a] rounded-md text-white appearance-none focus:outline-none focus:ring-2 focus:ring-blue-500">
                    <option value="text">{% trans "Text" %}</option>
                    <option value="rating">{% trans "Rating (1-5)" %}</option>
                    <option value="multiplechoice">{% trans "Multiple Choice" %}</option>
                    <option value="checkbox">{% trans "Checkbox" %}</option>
                </select>
                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </div>
            </div>
        </div>
        
        <div class="multiple-choice-options hidden mt-4">
            <label class="block text-sm font-medium text-white mb-1">{% trans "Options" %}</label>
            <div class="options-container space-y-2">
                <div class="option-item flex items-center gap-2">
                    <div class="flex-grow flex items-center gap-2">
                        <input type="checkbox" name="questions[0][correct_options][0]" class="correct-option h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-500 rounded">
                        <input type="text" name="questions[0][options][]" placeholder="{% trans 'Option text' %}" class="flex-grow py-2 px-3 bg-[#2c4a8a] border border-[#3c5a9a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    <button type="button" class="delete-option text-red-400 hover:text-red-300 p-1">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <button type="button" class="add-option mt-2 text-sm py-1 px-2 bg-blue-700 text-white rounded hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-blue-500">
                {% trans "Add Option" %}
            </button>
        </div>
    </div>
</template>

{% endblock %}

{% block extra_css %}
<style>
    /* Dark background for the entire page */
    body {
        background-color: #1e3a8a; /* Tailwind indigo-800 */
    }
    
    /* Style for select dropdowns */
    select {
        background-color: #1a2542 !important;
        color: white !important;
        border: 1px solid #374151 !important; /* Tailwind gray-700 */
    }

    select option {
        background-color: #1a2542 !important;
        color: white !important;
        padding: 8px;
    }

    /* Style for dropdown hover/focus state */
    select option:hover,
    select option:focus,
    select option:active,
    select option:checked {
        background-color: #2a3b5e !important;
    }

    /* Adjust dropdown arrow color for better visibility */
    .pointer-events-none.absolute.inset-y-0.right-0.flex.items-center.px-2.text-gray-700 svg {
        fill: white !important;
    }

    /* Form inputs styling */
    input[type="text"],
    input[type="number"],
    textarea,
    select {
        background-color: #273151 !important;
        color: white !important;
        border-color: #4b5563 !important; /* Tailwind gray-600 */
    }
    
    input[type="text"]:focus,
    input[type="number"]:focus,
    textarea:focus,
    select:focus {
        border-color: #60a5fa !important; /* Tailwind blue-400 */
        box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3) !important;
    }
    
    /* Question items background */
    .question-item {
        background-color: #273151 !important;
        border-color: #4b5563 !important; /* Tailwind gray-600 */
    }
    
    /* Option items in multiple choice questions */
    .option-item input[type="text"] {
        background-color: #273151 !important;
        color: white !important;
    }
    
    /* Checkbox styling */
    input[type="checkbox"] {
        border-color: #4b5563 !important;
        background-color: #273151 !important;
    }
    
    input[type="checkbox"]:checked {
        background-color: #3b82f6 !important;
        border-color: #3b82f6 !important;
    }
    
    /* Button styling */
    #addQuestionBtn, 
    button[type="submit"] {
        background-color: #3b82f6 !important;
        color: white !important;
        font-weight: 600 !important;
        transition: all 0.2s ease !important;
    }
    
    #addQuestionBtn:hover, 
    button[type="submit"]:hover {
        background-color: #2563eb !important;
        transform: translateY(-1px) !important;
    }
    
    button[type="button"].cancel {
        background-color: #4b5563 !important;
        color: white !important;
        font-weight: 600 !important;
        border: 1px solid #6b7280 !important;
        transition: all 0.2s ease !important;
    }
    
    button[type="button"].cancel:hover {
        background-color: #6b7280 !important;
        transform: translateY(-1px) !important;
    }
    
    /* Delete buttons styling */
    .delete-question, 
    .delete-option {
        color: #f87171 !important;
        transition: all 0.2s ease !important;
    }
    
    .delete-question:hover, 
    .delete-option:hover {
        color: #ef4444 !important;
        transform: scale(1.05) !important;
    }
    
    /* Add option button styling */
    .add-option {
        background-color: #3b82f6 !important;
        color: white !important;
        font-weight: 500 !important;
        transition: all 0.2s ease !important;
    }
    
    .add-option:hover {
        background-color: #2563eb !important;
        transform: translateY(-1px) !important;
    }
    
    /* Total score display */
    #totalScoreDisplay {
        font-size: 1.5rem;
        font-weight: 700;
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Question functionality
        const addQuestionBtn = document.getElementById('addQuestionBtn');
        const questionsContainer = document.getElementById('questionsContainer');
        const totalScoreDisplay = document.getElementById('totalScoreDisplay');
        
        // Add first question automatically
        addQuestion();
        
        // Add event listener for add question button
        if (addQuestionBtn) {
            addQuestionBtn.addEventListener('click', function() {
                addQuestion();
            });
        }
        
        // Function to add a new question
        function addQuestion() {
            const template = document.getElementById('question-template');
            const questionElement = template.content.cloneNode(true);
            const questionItems = document.querySelectorAll('.question-item');
            const index = questionItems.length;
            
            // Update name attributes with the correct index
            questionElement.querySelector('input[name="questions[0][text]"]').name = `questions[${index}][text]`;
            questionElement.querySelector('select[name="questions[0][type]"]').name = `questions[${index}][type]`;
            questionElement.querySelector('input[name="questions[0][score]"]').name = `questions[${index}][score]`;
            
            const options = questionElement.querySelectorAll('input[name="questions[0][options][]"]');
            options.forEach((option, j) => {
                option.name = `questions[${index}][options][]`;
            });
            
            // Update correct option checkbox names
            const correctOptions = questionElement.querySelectorAll('input[name^="questions[0][correct_options]"]');
            correctOptions.forEach((checkbox, j) => {
                checkbox.name = `questions[${index}][correct_options][${j}]`;
            });
            
            // Add event listeners for question type change
            const questionTypeSelect = questionElement.querySelector('.question-type');
            questionTypeSelect.addEventListener('change', function() {
                toggleMultipleChoiceOptions(this);
            });
            
            // Add event listener for score input to update total
            const scoreInput = questionElement.querySelector('input[name^="questions["][name$="][score]"]');
            scoreInput.addEventListener('input', updateTotalScore);
            
            // Add event listener for delete question button
            const deleteQuestionBtn = questionElement.querySelector('.delete-question');
            deleteQuestionBtn.addEventListener('click', function() {
                const questionItems = document.querySelectorAll('.question-item');
                if (questionItems.length > 1) {
                    this.closest('.question-item').remove();
                    updateQuestionIndices();
                    updateTotalScore();
                } else {
                    alert('{% trans "You must have at least one question" %}');
                }
            });
            
            // Add event listener for add option button
            const addOptionBtn = questionElement.querySelector('.add-option');
            addOptionBtn.addEventListener('click', function() {
                addOption(this.closest('.question-item'), index);
            });
            
            // Add event listeners for delete option buttons
            const deleteOptionBtns = questionElement.querySelectorAll('.delete-option');
            deleteOptionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.closest('.options-container').querySelectorAll('.option-item').length > 1) {
                        this.closest('.option-item').remove();
                        updateOptionIndices(this.closest('.question-item'), index);
                    } else {
                        alert('{% trans "You must have at least one option" %}');
                    }
                });
            });
            
            questionsContainer.appendChild(questionElement);
            updateTotalScore();
        }
        
        // Function to toggle multiple choice options based on question type
        function toggleMultipleChoiceOptions(selectElement) {
            const questionItem = selectElement.closest('.question-item');
            const multipleChoiceOptions = questionItem.querySelector('.multiple-choice-options');
            
            if (selectElement.value === 'multiplechoice' || selectElement.value === 'checkbox') {
                multipleChoiceOptions.classList.remove('hidden');
            } else {
                multipleChoiceOptions.classList.add('hidden');
            }
        }
        
        // Function to add a new option to a multiple choice question
        function addOption(questionItem, questionIndex) {
            const optionsContainer = questionItem.querySelector('.options-container');
            const optionItems = optionsContainer.querySelectorAll('.option-item');
            const newOptionIndex = optionItems.length;
            
            const optionItem = document.createElement('div');
            optionItem.className = 'option-item flex items-center gap-2';
            
            optionItem.innerHTML = `
                <div class="flex-grow flex items-center gap-2">
                    <input type="checkbox" name="questions[${questionIndex}][correct_options][${newOptionIndex}]" class="correct-option h-5 w-5 text-blue-600 focus:ring-blue-500 border-gray-500 rounded">
                    <input type="text" name="questions[${questionIndex}][options][]" placeholder="{% trans 'Option text' %}" class="flex-grow py-2 px-3 bg-[#2c4a8a] border border-[#3c5a9a] rounded-md text-white focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="button" class="delete-option text-red-400 hover:text-red-300 p-1">
                    <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            `;
            
            // Add event listener for delete option button
            const deleteOptionBtn = optionItem.querySelector('.delete-option');
            deleteOptionBtn.addEventListener('click', function() {
                if (optionsContainer.querySelectorAll('.option-item').length > 1) {
                    this.closest('.option-item').remove();
                    updateOptionIndices(questionItem, questionIndex);
                } else {
                    alert('{% trans "You must have at least one option" %}');
                }
            });
            
            optionsContainer.appendChild(optionItem);
        }
        
        // Function to update option indices after deletion
        function updateOptionIndices(questionItem, questionIndex) {
            const optionItems = questionItem.querySelectorAll('.option-item');
            optionItems.forEach((item, j) => {
                const checkbox = item.querySelector('input[type="checkbox"]');
                checkbox.name = `questions[${questionIndex}][correct_options][${j}]`;
            });
        }
        
        // Function to update question indices after deletion
        function updateQuestionIndices() {
            const questionItems = document.querySelectorAll('.question-item');
            questionItems.forEach((item, index) => {
                // Update question text input
                const questionTextInput = item.querySelector('input[name^="questions["][name$="][text]"]');
                questionTextInput.name = `questions[${index}][text]`;
                
                // Update question score input
                const questionScoreInput = item.querySelector('input[name^="questions["][name$="][score]"]');
                questionScoreInput.name = `questions[${index}][score]`;
                
                // Update question type select
                const questionTypeSelect = item.querySelector('select[name^="questions["][name$="][type]"]');
                questionTypeSelect.name = `questions[${index}][type]`;
                
                // Update options inputs
                const optionInputs = item.querySelectorAll('input[name^="questions["][name$="][options][]"]');
                optionInputs.forEach(option => {
                    option.name = `questions[${index}][options][]`;
                });
                
                // Update correct option checkboxes
                const optionItems = item.querySelectorAll('.option-item');
                optionItems.forEach((optionItem, j) => {
                    const checkbox = optionItem.querySelector('input[type="checkbox"]');
                    if (checkbox) {
                        checkbox.name = `questions[${index}][correct_options][${j}]`;
                    }
                });
            });
        }
        
        // Function to update the total score display
        function updateTotalScore() {
            let total = 0;
            const scoreInputs = document.querySelectorAll('input[name^="questions["][name$="][score]"]');
            
            scoreInputs.forEach(input => {
                const score = parseInt(input.value) || 0;
                total += score;
            });
            
            totalScoreDisplay.textContent = total;
        }
    });
</script>
{% endblock %}
