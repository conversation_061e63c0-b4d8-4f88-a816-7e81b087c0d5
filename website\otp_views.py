from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db import transaction
from django.contrib import messages
from django.contrib.auth import login
from django.views.decorators.csrf import csrf_exempt
import logging
import json

from .models import CustomUser, OTPVerification, GB_Employee, UserType
from otp_email_utils import send_otp_verification_email, send_otp_max_attempts_notification

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["POST"])
def request_otp_api(request):
    """
    API endpoint to request OTP for internal employee login.
    
    Flow:
    1. Check if employee exists in database
    2. If exists: use existing email
    3. If not exists: get email from SOAP API
    4. Create OTP and send email
    """
    try:
        data = json.loads(request.body)
        employee_id = data.get('employee_id', '').strip()
        
        if not employee_id:
            return JsonResponse({
                'status': 'error',
                'message': _('Employee ID is required.')
            }, status=400)
        
        logger.info(f"OTP request for employee ID: {employee_id}")
        
        # Clean up old expired OTP records
        OTPVerification.cleanup_expired()
        
        # First, check if user already exists in our database
        existing_user = None
        email = None
        employee_name = None
        
        try:
            # Check for existing CustomUser with this employee_id
            existing_user = CustomUser.objects.get(employee_id=employee_id)
            email = existing_user.email
            employee_name = existing_user.get_full_name()
            logger.info(f"Found existing user for employee {employee_id}: {email}")
        except CustomUser.DoesNotExist:
            # Check for existing GB_Employee record
            try:
                gb_employee = GB_Employee.objects.get(employee_number=employee_id)
                email = gb_employee.oracle_email_address
                employee_name = gb_employee.english_name or gb_employee.full_name
                logger.info(f"Found existing GB_Employee for {employee_id}: {email}")
            except GB_Employee.DoesNotExist:
                # Need to fetch from Oracle SOAP API
                logger.info(f"Employee {employee_id} not found in database, fetching from Oracle SOAP API")
                
                try:
                    from .soap_client import OracleSOAPClient
                    soap_client = OracleSOAPClient()
                    oracle_data = soap_client.get_employee_data(employee_id)
                    
                    if oracle_data and oracle_data.get('oracle_email_address'):
                        email = oracle_data['oracle_email_address']
                        employee_name = oracle_data.get('english_name') or oracle_data.get('full_name')
                        logger.info(f"Retrieved email from Oracle for {employee_id}: {email}")
                        
                        # Optionally create GB_Employee record for future use
                        try:
                            GB_Employee.create_or_update_from_oracle(oracle_data)
                            logger.info(f"Created GB_Employee record for {employee_id}")
                        except Exception as gb_err:
                            logger.warning(f"Could not create GB_Employee record: {str(gb_err)}")
                    else:
                        logger.error(f"Could not retrieve email for employee {employee_id} from Oracle")
                        return JsonResponse({
                            'status': 'error',
                            'message': _('Employee not found or email not available. Please contact IT support.')
                        }, status=404)
                        
                except Exception as soap_err:
                    logger.error(f"Oracle SOAP API error for employee {employee_id}: {str(soap_err)}")
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Unable to verify employee. Please try again later or contact IT support.')
                    }, status=500)
        
        if not email:
            return JsonResponse({
                'status': 'error',
                'message': _('No email address found for this employee ID.')
            }, status=404)
        
        # Create OTP record
        try:
            otp_record = OTPVerification.create_otp(employee_id, email)
            logger.info(f"Created OTP record for {employee_id}: {otp_record.otp_code}")
            
            # Send OTP email
            email_sent = send_otp_verification_email(
                employee_id=employee_id,
                email=email,
                otp_code=otp_record.otp_code,
                employee_name=employee_name
            )
            
            if email_sent:
                logger.info(f"OTP email sent successfully to {email}")
                return JsonResponse({
                    'status': 'success',
                    'message': _('Verification code sent to your email address.'),
                    'email': email,
                    'expires_in': 60  # 1 minute
                })
            else:
                logger.error(f"Failed to send OTP email to {email}")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Failed to send verification code. Please try again.')
                }, status=500)
                
        except Exception as otp_err:
            logger.error(f"Error creating OTP for {employee_id}: {str(otp_err)}")
            return JsonResponse({
                'status': 'error',
                'message': _('Failed to generate verification code. Please try again.')
            }, status=500)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid request format.')
        }, status=400)
    except Exception as e:
        logger.error(f"Unexpected error in request_otp_api: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('An unexpected error occurred. Please try again.')
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def verify_otp_api(request):
    """
    API endpoint to verify OTP and authenticate the user.
    
    If OTP is valid, create/authenticate user and log them in.
    """
    try:
        data = json.loads(request.body)
        employee_id = data.get('employee_id', '').strip()
        otp_code = data.get('otp_code', '').strip()
        
        if not employee_id or not otp_code:
            return JsonResponse({
                'status': 'error',
                'message': _('Employee ID and OTP code are required.')
            }, status=400)
        
        logger.info(f"OTP verification attempt for employee {employee_id}")
        
        # Find the most recent active OTP for this employee
        try:
            otp_record = OTPVerification.objects.filter(
                employee_id=employee_id,
                is_used=False,
                is_expired=False
            ).order_by('-created_at').first()
            
            if not otp_record:
                logger.warning(f"No active OTP found for employee {employee_id}")
                return JsonResponse({
                    'status': 'error',
                    'message': _('No valid OTP found. Please request a new verification code.')
                }, status=404)
            
            # Verify the OTP
            verification_success, verification_message = otp_record.verify(otp_code)
            
            if not verification_success:
                logger.warning(f"OTP verification failed for {employee_id}: {verification_message}")
                
                # If max attempts reached, send notification email
                if otp_record.attempts >= otp_record.max_attempts:
                    try:
                        # Get employee name for email
                        employee_name = None
                        try:
                            existing_user = CustomUser.objects.get(employee_id=employee_id)
                            employee_name = existing_user.get_full_name()
                        except CustomUser.DoesNotExist:
                            try:
                                gb_employee = GB_Employee.objects.get(employee_number=employee_id)
                                employee_name = gb_employee.english_name or gb_employee.full_name
                            except GB_Employee.DoesNotExist:
                                pass
                        
                        send_otp_max_attempts_notification(
                            employee_id=employee_id,
                            email=otp_record.email,
                            employee_name=employee_name
                        )
                        logger.info(f"Sent max attempts notification to {otp_record.email}")
                    except Exception as email_err:
                        logger.error(f"Failed to send max attempts notification: {str(email_err)}")
                
                return JsonResponse({
                    'status': 'error',
                    'message': verification_message,
                    'attempts_remaining': max(0, otp_record.max_attempts - otp_record.attempts),
                    'max_attempts_reached': otp_record.attempts >= otp_record.max_attempts
                }, status=400)
            
            # OTP verified successfully - now authenticate/create user
            logger.info(f"OTP verified successfully for {employee_id}")
            
            # Check if user already exists
            user = None
            try:
                user = CustomUser.objects.get(employee_id=employee_id)
                logger.info(f"Found existing user for employee {employee_id}")
            except CustomUser.DoesNotExist:
                # Create new user
                logger.info(f"Creating new user for employee {employee_id}")
                
                try:
                    # Get INTERNAL_GB user type
                    gb_user_type, created = UserType.objects.get_or_create(
                        code='INTERNAL_GB',
                        defaults={
                            'name': 'Internal GB Employee',
                            'description': 'Internal GB Corporation Employee'
                        }
                    )
                    
                    # Get Oracle data for user creation
                    oracle_data = None
                    gb_employee = None
                    
                    try:
                        gb_employee = GB_Employee.objects.get(employee_number=employee_id)
                        oracle_data = {
                            'employee_number': gb_employee.employee_number,
                            'english_name': gb_employee.english_name,
                            'full_name': gb_employee.full_name,
                            'oracle_email_address': gb_employee.oracle_email_address,
                            'department': gb_employee.department,
                            'position_name': gb_employee.position_name,
                            'hire_date': gb_employee.hire_date,
                            'grade_name': gb_employee.grade_name,
                        }
                    except GB_Employee.DoesNotExist:
                        # Fetch from Oracle if not in DB
                        try:
                            from .soap_client import OracleSOAPClient
                            soap_client = OracleSOAPClient()
                            oracle_data = soap_client.get_employee_data(employee_id)
                            
                            # Create GB_Employee record
                            if oracle_data:
                                gb_employee = GB_Employee.create_or_update_from_oracle(oracle_data)
                        except Exception as soap_err:
                            logger.error(f"Could not fetch Oracle data for user creation: {str(soap_err)}")
                    
                    # Create user
                    username = f"gb_{employee_id}"
                    email = otp_record.email
                    
                    # Extract name parts
                    first_name = ""
                    last_name = ""
                    if oracle_data and oracle_data.get('english_name'):
                        name_parts = oracle_data['english_name'].split(' ', 1)
                        first_name = name_parts[0]
                        if len(name_parts) > 1:
                            last_name = name_parts[1]
                    
                    user = CustomUser.objects.create_user(
                        username=username,
                        email=email,
                        first_name=first_name,
                        last_name=last_name,
                        employee_id=employee_id,
                        user_type=gb_user_type,
                        gb_employee=gb_employee,
                        account_status='ACTIVE',
                        is_active=True
                    )
                    # create a gb_employee record
                    gb_employee = GB_Employee.objects.create(
                        employee_number=employee_id,
                        english_name=oracle_data['english_name'],
                        arabic_name=oracle_data['arabic_name'],
                        full_name=oracle_data['full_name'],
                        oracle_email_address=oracle_data['oracle_email_address'],
                        oracle_username=oracle_data['oracle_username'],
                        oracle_phone_number=oracle_data['oracle_phone_number'],
                        supervisor_id=oracle_data['supervisor_id'],
                        supervisor_user_name=oracle_data['supervisor_user_name'],
                        payroll_id=oracle_data['payroll_id'],
                        grade_id=oracle_data['grade_id'],
                        organization_id=oracle_data['organization_id'],
                        department=oracle_data['department'],
                        position_name=oracle_data['position_name'],
                        hire_date=oracle_data['hire_date'],
                        grade_name=oracle_data['grade_name'],
                    )
                    
                    # Sync Oracle data
                    if gb_employee:
                        user.sync_oracle_data()
                    
                    logger.info(f"Created new user: {username} for employee {employee_id}")
                    
                except Exception as user_err:
                    logger.error(f"Error creating user for employee {employee_id}: {str(user_err)}")
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Failed to create user account. Please contact IT support.')
                    }, status=500)
            
            # Log the user in
            try:
                login(request, user, backend='website.backends.OracleEmployeeBackend')
                logger.info(f"User {user.username} logged in successfully via OTP")
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Login successful. Redirecting...'),
                    'redirect_url': '/dashboard-redirect/'
                })
                
            except Exception as login_err:
                logger.error(f"Error logging in user {user.username}: {str(login_err)}")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Authentication successful but login failed. Please try again.')
                }, status=500)
            
        except Exception as e:
            logger.error(f"Error during OTP verification for {employee_id}: {str(e)}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': _('Verification failed. Please try again.')
            }, status=500)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid request format.')
        }, status=400)
    except Exception as e:
        logger.error(f"Unexpected error in verify_otp_api: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('An unexpected error occurred. Please try again.')
        }, status=500)

@csrf_exempt
@require_http_methods(["POST"])
def resend_otp_api(request):
    """
    API endpoint to resend OTP for internal employee login.
    This is used when user reaches max attempts or OTP expires.
    """
    try:
        data = json.loads(request.body)
        employee_id = data.get('employee_id', '').strip()
        
        if not employee_id:
            return JsonResponse({
                'status': 'error',
                'message': _('Employee ID is required.')
            }, status=400)
        
        logger.info(f"OTP resend request for employee ID: {employee_id}")
        
        # Clean up old expired OTP records
        OTPVerification.cleanup_expired()
        
        # Find email from existing records or Oracle
        email = None
        employee_name = None
        
        try:
            # Check for existing CustomUser with this employee_id
            existing_user = CustomUser.objects.get(employee_id=employee_id)
            email = existing_user.email
            employee_name = existing_user.get_full_name()
        except CustomUser.DoesNotExist:
            try:
                # Check for existing GB_Employee record
                gb_employee = GB_Employee.objects.get(employee_number=employee_id)
                email = gb_employee.oracle_email_address
                employee_name = gb_employee.english_name or gb_employee.full_name
            except GB_Employee.DoesNotExist:
                # Check for previous OTP record
                try:
                    last_otp = OTPVerification.objects.filter(employee_id=employee_id).order_by('-created_at').first()
                    if last_otp:
                        email = last_otp.email
                        logger.info(f"Using email from previous OTP record: {email}")
                except Exception:
                    pass
                
                if not email:
                    # Fetch from Oracle SOAP API as last resort
                    try:
                        from .soap_client import OracleSOAPClient
                        soap_client = OracleSOAPClient()
                        oracle_data = soap_client.get_employee_data(employee_id)
                        
                        if oracle_data and oracle_data.get('oracle_email_address'):
                            email = oracle_data['oracle_email_address']
                            employee_name = oracle_data.get('english_name') or oracle_data.get('full_name')
                    except Exception as soap_err:
                        logger.error(f"Oracle SOAP API error during resend for {employee_id}: {str(soap_err)}")
        
        if not email:
            return JsonResponse({
                'status': 'error',
                'message': _('No email address found for this employee ID.')
            }, status=404)
        
        # Create new OTP record
        try:
            otp_record = OTPVerification.create_otp(employee_id, email)
            logger.info(f"Created new OTP record for resend to {employee_id}: {otp_record.otp_code}")
            
            # Send OTP email
            email_sent = send_otp_verification_email(
                employee_id=employee_id,
                email=email,
                otp_code=otp_record.otp_code,
                employee_name=employee_name
            )
            
            if email_sent:
                logger.info(f"OTP resent successfully to {email}")
                return JsonResponse({
                    'status': 'success',
                    'message': _('New verification code sent to your email address.'),
                    'email': email,
                    'expires_in': 60  # 1 minute
                })
            else:
                logger.error(f"Failed to resend OTP email to {email}")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Failed to send verification code. Please try again.')
                }, status=500)
                
        except Exception as otp_err:
            logger.error(f"Error creating OTP for resend to {employee_id}: {str(otp_err)}")
            return JsonResponse({
                'status': 'error',
                'message': _('Failed to generate verification code. Please try again.')
            }, status=500)
        
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid request format.')
        }, status=400)
    except Exception as e:
        logger.error(f"Unexpected error in resend_otp_api: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('An unexpected error occurred. Please try again.')
        }, status=500) 