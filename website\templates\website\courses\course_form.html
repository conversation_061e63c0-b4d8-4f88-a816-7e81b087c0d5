{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Page Header -->
        <div class="flex items-center space-x-3 mb-8">
            <a href="{% url 'website:courses' %}" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-white">{{ title }}</h1>
        </div>

        <!-- Form Card -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
            <form method="post" enctype="multipart/form-data" class="space-y-6">
                {% csrf_token %}
                
                {% if form.non_field_errors %}
                <div class="bg-red-500/10 border border-red-500/20 rounded-md p-4 mb-6">
                    {% for error in form.non_field_errors %}
                    <p class="text-red-500 text-sm">{{ error }}</p>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Basic Information -->
                <div class="space-y-4">
                    <h2 class="text-lg font-medium text-white">{% trans "Basic Information" %}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ form.name_en.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.name_en.label }}
                            </label>
                            <input type="text" name="{{ form.name_en.name }}" id="{{ form.name_en.id_for_label }}" 
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                {% if form.name_en.value %}value="{{ form.name_en.value }}"{% endif %}>
                            {% if form.name_en.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.name_en.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.name_ar.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.name_ar.label }}
                            </label>
                            <input type="text" name="{{ form.name_ar.name }}" id="{{ form.name_ar.id_for_label }}"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                {% if form.name_ar.value %}value="{{ form.name_ar.value }}"{% endif %}>
                            {% if form.name_ar.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.name_ar.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ form.description_en.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.description_en.label }}
                            </label>
                            <textarea name="{{ form.description_en.name }}" id="{{ form.description_en.id_for_label }}" rows="4"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">{% if form.description_en.value %}{{ form.description_en.value }}{% endif %}</textarea>
                            {% if form.description_en.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.description_en.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.description_ar.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.description_ar.label }}
                            </label>
                            <textarea name="{{ form.description_ar.name }}" id="{{ form.description_ar.id_for_label }}" rows="4"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">{% if form.description_ar.value %}{{ form.description_ar.value }}{% endif %}</textarea>
                            {% if form.description_ar.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.description_ar.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Course Details -->
                <div class="space-y-4">
                    <h2 class="text-lg font-medium text-white">{% trans "Course Details" %}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <label for="{{ form.category.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.category.label }}
                            </label>
                            <select name="{{ form.category.name }}" id="{{ form.category.id_for_label }}"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                {% for value, label in form.category.field.choices %}
                                <option value="{{ value }}" {% if form.category.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            {% if form.category.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.category.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.location.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.location.label }}
                            </label>
                            <select name="{{ form.location.name }}" id="{{ form.location.id_for_label }}"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                {% for value, label in form.location.field.choices %}
                                <option value="{{ value }}" {% if form.location.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            {% if form.location.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.location.errors.0 }}</p>
                            {% endif %}
                        </div>
                        
                        <div>
                            <label for="{{ form.session_type.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.session_type.label }}
                            </label>
                            <select name="{{ form.session_type.name }}" id="{{ form.session_type.id_for_label }}"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                {% for value, label in form.session_type.field.choices %}
                                <option value="{{ value }}" {% if form.session_type.value == value %}selected{% endif %}>{{ label }}</option>
                                {% endfor %}
                            </select>
                            {% if form.session_type.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.session_type.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>

                    <!-- Course Icon and Image -->
                    <div class="space-y-4 mt-4">
                        <div>
                            <label for="image" class="block text-sm font-medium text-white/70">
                                {% trans "Course Image" %}
                            </label>
                            <div class="mt-1 flex items-center">
                                {% if course and course.image %}
                                <div class="mr-4">
                                    <img src="{{ course.image.url }}" alt="{{ course.name_en }}" class="w-32 h-32 object-cover rounded-md">
                                </div>
                                {% endif %}
                                <input type="file" name="image" id="image" accept="image/*"
                                    class="block w-full text-sm text-white file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary/90">
                            </div>
                            <p class="mt-1 text-xs text-white/50">{% trans "Upload an image for the course (optional)" %}</p>
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-white/70 mb-2">
                            {% trans "Required Equipment Categories" %}
                        </label>
                        {{ form.equipment_categories }}
                        
                        <!-- DEBUG info -->
                        <div style="display: none;">
                            Equipment Categories Input Value: {{ form.equipment_categories.value }}
                        </div>
                        
                        <div id="equipment-categories-container" class="space-y-3 mt-1">
                            <!-- Categories will be added dynamically here -->
                        </div>
                        
                        <div class="mt-3">
                            <button type="button" id="add-category-btn" 
                                class="inline-flex items-center px-3 py-2 border border-white/20 text-sm leading-4 font-medium rounded-md text-white bg-white/5 hover:bg-white/10 focus:outline-none">
                                <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                {% trans "Add Equipment Category" %}
                            </button>
                        </div>
                        
                        <!-- Category Selection Modal -->
                        <div id="category-modal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
                            <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-md p-6">
                                <div class="flex justify-between items-center mb-4">
                                    <h3 class="text-lg font-medium text-white">{% trans "Select Equipment Category" %}</h3>
                                    <button type="button" id="close-modal-btn" class="text-white/70 hover:text-white">
                                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                                
                                <div class="space-y-4">
                                    <div>
                                        <label for="category-select" class="block text-sm font-medium text-white/70">
                                            {% trans "Category" %}
                                        </label>
                                        <select id="category-select" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">{% trans "Select a category" %}</option>
                                            {% for category in form.active_categories %}
                                            <option value="{{ category.category_id }}" data-name="{{ category.name }}" data-stock="{{ category.stock }}">
                                                {{ category.name }} (Stock: {{ category.stock }})
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    
                                    <div>
                                        <label for="quantity-input" class="block text-sm font-medium text-white/70">
                                            {% trans "Quantity" %}
                                        </label>
                                        <input type="number" id="quantity-input" min="1" value="1" 
                                            class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                    
                                    <div class="flex justify-end space-x-3 mt-6">
                                        <button type="button" id="cancel-category-btn" class="px-4 py-2 border border-white/20 rounded-md text-white bg-white/5 hover:bg-white/10 focus:outline-none">
                                            {% trans "Cancel" %}
                                        </button>
                                        <button type="button" id="add-category-confirm-btn" class="px-4 py-2 border border-transparent rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none">
                                            {% trans "Add" %}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.num_of_sessions.id_for_label }}" class="block text-sm font-medium text-white/70">
                            {{ form.num_of_sessions.label }}
                        </label>
                        <input type="number" name="{{ form.num_of_sessions.name }}" id="{{ form.num_of_sessions.id_for_label }}"
                            class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            {% if form.num_of_sessions.value %}value="{{ form.num_of_sessions.value }}"{% endif %}>
                        {% if form.num_of_sessions.errors %}
                        <p class="mt-1 text-sm text-red-500">{{ form.num_of_sessions.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Session Room Types Section -->
                    <div>
                        <label class="block text-sm font-medium text-white/70 mb-2">
                            {% trans "Session Room Types" %}
                        </label>
                        {{ form.session_room_types }}
                        
                        <!-- Debug output to verify room types -->
                        <div style="display: none;">
                            Room Types Debug: 
                            {% for room_type in form.active_room_types %}
                                {{ room_type.room_type_id }}: {{ room_type.name }};
                            {% empty %}
                                No room types found!
                            {% endfor %}
                        </div>
                        
                        <div id="session-room-types-container" class="space-y-3 mt-1">
                            <!-- Session room type selections will be dynamically added here -->
                        </div>
                        
                        <p class="text-sm text-white/50 mt-2">
                            {% trans "Specify the room type for each session. This will determine which room type is required when creating sessions for this course." %}
                        </p>
                        
                        <!-- No sessions message -->
                        <div id="no-sessions-message" class="mt-3 text-white/50 text-sm italic hidden">
                            {% trans "Please specify the number of sessions first." %}
                        </div>
                    </div>
                    
                    <div>
                        <label for="{{ form.capacity.id_for_label }}" class="block text-sm font-medium text-white/70">
                            {{ form.capacity.label }}
                        </label>
                        <input type="number" name="{{ form.capacity.name }}" id="{{ form.capacity.id_for_label }}"
                            class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            {% if form.capacity.value %}value="{{ form.capacity.value }}"{% endif %}>
                        {% if form.capacity.errors %}
                        <p class="mt-1 text-sm text-red-500">{{ form.capacity.errors.0 }}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Course Content Section -->
                {% if show_content_section %}
                <div class="space-y-4 border-t border-white/10 pt-6 mt-6">
                    <h2 class="text-lg font-medium text-white">{% trans "Course Content" %} ({% trans "Optional" %})</h2>
                    <p class="text-white/70 text-sm">{% trans "Add initial content like presentations, documents, images, or videos to your course." %}</p>
                    
                    <!-- Hidden input to store content data -->
                    <input type="hidden" name="course_contents" id="course-contents-input" value="[]">
                    
                    <div id="content-items-container" class="space-y-3 mt-4">
                        <!-- Content items will be added dynamically here -->
                    </div>
                    
                    <div class="mt-3">
                        <button type="button" id="add-content-btn" 
                            class="inline-flex items-center px-3 py-2 border border-white/20 text-sm leading-4 font-medium rounded-md text-white bg-white/5 hover:bg-white/10 focus:outline-none">
                            <svg class="mr-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                            </svg>
                            {% trans "Add Content" %}
                        </button>
                    </div>
                    
                    <!-- Content Modal -->
                    <div id="content-modal" class="fixed inset-0 bg-black/70 flex items-center justify-center z-50 hidden">
                        <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-lg p-6">
                            <div class="flex justify-between items-center mb-4">
                                <h3 class="text-lg font-medium text-white">{% trans "Add Course Content" %}</h3>
                                <button type="button" id="close-content-modal-btn" class="text-white/70 hover:text-white">
                                    <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                            </div>
                            
                            <div class="space-y-4" id="content-form">
                                <!-- Title -->
                                <div>
                                    <label for="content-title" class="block text-sm font-medium text-white/70">
                                        {% trans "Content Title" %}
                                    </label>
                                    <input type="text" id="content-title" 
                                        class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>

                                <!-- Content Type -->
                                <div>
                                    <label for="content-type" class="block text-sm font-medium text-white/70">
                                        {% trans "Content Type" %}
                                    </label>
                                    <div class="relative">
                                        <select id="content-type" 
                                            class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent appearance-none">
                                            {% for value, label in content_form.content_type.field.choices %}
                                            <option value="{{ value }}">{{ label }}</option>
                                            {% endfor %}
                                        </select>
                                        <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                                            <svg class="h-5 w-5 text-white/70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                    </div>
                                </div>

                                <!-- Description -->
                                <div>
                                    <label for="content-description" class="block text-sm font-medium text-white/70">
                                        {% trans "Content Description" %}
                                    </label>
                                    <textarea id="content-description" rows="4"
                                        class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"></textarea>
                                </div>

                                <!-- File Upload -->
                                <div>
                                    <label for="content-file" class="block text-sm font-medium text-white/70">
                                        {% trans "File" %}
                                    </label>
                                    <input type="file" id="content-file" accept="*/*"
                                        class="mt-1 block w-full text-sm text-white file:mr-4 file:py-2 file:px-4 file:rounded-md file:border-0 file:text-sm file:font-medium file:bg-primary file:text-white hover:file:bg-primary/90">
                                    <p class="mt-1 text-xs text-white/50">{% trans "Supported formats include PDF, PPTX, DOCX, JPG, PNG, MP4, etc." %}</p>
                                </div>
                                
                                <div class="flex justify-end space-x-3 mt-6">
                                    <button type="button" id="cancel-content-btn" class="px-4 py-2 border border-white/20 rounded-md text-white bg-white/5 hover:bg-white/10 focus:outline-none">
                                        {% trans "Cancel" %}
                                    </button>
                                    <button type="button" id="add-content-confirm-btn" class="px-4 py-2 border border-transparent rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none">
                                        {% trans "Add" %}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Display Settings -->
                <div class="space-y-4">
                    <h2 class="text-lg font-medium text-white">{% trans "Display Settings" %}</h2>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="{{ form.order.id_for_label }}" class="block text-sm font-medium text-white/70">
                                {{ form.order.label }}
                            </label>
                            <input type="number" name="{{ form.order.name }}" id="{{ form.order.id_for_label }}"
                                class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                                {% if form.order.value %}value="{{ form.order.value }}"{% endif %}>
                            {% if form.order.errors %}
                            <p class="mt-1 text-sm text-red-500">{{ form.order.errors.0 }}</p>
                            {% endif %}
                        </div>
                    </div>
                </div>

                <!-- Form actions -->
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'website:courses' %}" class="px-4 py-2 border border-white/20 rounded-md text-white bg-white/5 hover:bg-white/10 focus:outline-none">
                        {% trans "Cancel" %}
                    </a>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none">
                        {{ submit_text|default:_('Save Course') }}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // DOM Elements
        const equipmentCategoriesInput = document.querySelector('input[name="equipment_categories"]');
        const categoriesContainer = document.getElementById('equipment-categories-container');
        const addCategoryBtn = document.getElementById('add-category-btn');
        const categoryModal = document.getElementById('category-modal');
        const closeModalBtn = document.getElementById('close-modal-btn');
        const cancelCategoryBtn = document.getElementById('cancel-category-btn');
        const categorySelect = document.getElementById('category-select');
        const quantityInput = document.getElementById('quantity-input');
        const addCategoryConfirmBtn = document.getElementById('add-category-confirm-btn');
        
        // Initialize equipment categories data structure
        let equipmentCategories = {};
        
        // Initialize from existing data if available
        if (equipmentCategoriesInput.value) {
            try {
                console.log('Loading equipment categories from input:', equipmentCategoriesInput.value);
                // Fix for Python-style dictionaries - replace single quotes with double quotes
                let jsonValue = equipmentCategoriesInput.value;
                if (jsonValue.includes("'")) {
                    // Replace Python-style single quotes with JSON-compatible double quotes
                    jsonValue = jsonValue.replace(/'/g, '"');
                    console.log('Converted to JSON format:', jsonValue);
                }
                equipmentCategories = JSON.parse(jsonValue);
                console.log('Parsed equipment categories:', equipmentCategories);
                renderCategories();
            } catch (e) {
                console.error('Error parsing equipment categories:', e);
            }
        } else {
            console.log('No equipment categories input value found.');
        }
        
        // Open modal when Add Category button is clicked
        addCategoryBtn.addEventListener('click', function() {
            categoryModal.classList.remove('hidden');
            categorySelect.value = '';
            quantityInput.value = 1;
        });
        
        // Close modal when close button is clicked
        closeModalBtn.addEventListener('click', function() {
            categoryModal.classList.add('hidden');
        });
        
        // Close modal when cancel button is clicked
        cancelCategoryBtn.addEventListener('click', function() {
            categoryModal.classList.add('hidden');
        });
        
        // Add category when confirmed
        addCategoryConfirmBtn.addEventListener('click', function() {
            const categoryId = categorySelect.value;
            if (!categoryId) {
                alert('Please select a category');
                return;
            }
            
            const quantity = parseInt(quantityInput.value, 10);
            if (isNaN(quantity) || quantity < 1) {
                alert('Please enter a valid quantity');
                return;
            }
            
            const categoryName = categorySelect.options[categorySelect.selectedIndex].dataset.name;
            const categoryStock = parseInt(categorySelect.options[categorySelect.selectedIndex].dataset.stock, 10);
            
            // Validate that quantity doesn't exceed stock
            if (quantity > categoryStock) {
                alert(`Cannot request ${quantity} items. Only ${categoryStock} items in stock.`);
                return;
            }
            
            // Add or update category
            equipmentCategories[categoryId] = {
                id: categoryId,
                name: categoryName,
                quantity: quantity,
                stock: categoryStock
            };
            
            // Update hidden input
            equipmentCategoriesInput.value = JSON.stringify(equipmentCategories);
            
            // Render categories
            renderCategories();
            
            // Close modal
            categoryModal.classList.add('hidden');
        });
        
        // Function to render equipment categories
        function renderCategories() {
            categoriesContainer.innerHTML = '';
            
            Object.values(equipmentCategories).forEach(function(category) {
                const categoryElement = document.createElement('div');
                categoryElement.className = 'flex justify-between items-center p-3 bg-white/5 border border-white/10 rounded-md';
                categoryElement.innerHTML = `
                    <div>
                        <span class="text-white font-medium">${category.name}</span>
                        <span class="ml-2 text-white/70">x ${category.quantity}</span>
                    </div>
                    <div class="flex space-x-2">
                        <button type="button" class="edit-category text-white/70 hover:text-white" data-id="${category.id}">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                        </button>
                        <button type="button" class="remove-category text-white/70 hover:text-white" data-id="${category.id}">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                `;
                
                categoriesContainer.appendChild(categoryElement);
                
                // Add event listeners for edit and remove buttons
                categoryElement.querySelector('.edit-category').addEventListener('click', function() {
                    const catId = this.dataset.id;
                    const category = equipmentCategories[catId];
                    
                    categorySelect.value = catId;
                    quantityInput.value = category.quantity;
                    
                    categoryModal.classList.remove('hidden');
                });
                
                categoryElement.querySelector('.remove-category').addEventListener('click', function() {
                    const catId = this.dataset.id;
                    delete equipmentCategories[catId];
                    
                    equipmentCategoriesInput.value = JSON.stringify(equipmentCategories);
                    renderCategories();
                });
            });
            
            // Show or hide "no categories" message
            if (Object.keys(equipmentCategories).length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'text-white/50 text-sm';
                emptyMessage.textContent = '{% trans "No equipment categories added yet" %}';
                categoriesContainer.appendChild(emptyMessage);
            }
        }

        // Course Content Management
        const courseContentsInput = document.getElementById('course-contents-input');
        const contentItemsContainer = document.getElementById('content-items-container');
        const addContentBtn = document.getElementById('add-content-btn');
        const contentModal = document.getElementById('content-modal');
        const closeContentModalBtn = document.getElementById('close-content-modal-btn');
        const cancelContentBtn = document.getElementById('cancel-content-btn');
        const contentTitleInput = document.getElementById('content-title');
        const contentTypeSelect = document.getElementById('content-type');
        const contentDescriptionInput = document.getElementById('content-description');
        const contentFileInput = document.getElementById('content-file');
        const addContentConfirmBtn = document.getElementById('add-content-confirm-btn');
        
        // Supported file extensions
        const supportedExtensions = ['pdf', 'pptx', 'docx', 'jpg', 'jpeg', 'png', 'mp4'];
        let fileErrorElement = null;
        
        // Initialize course contents array
        let courseContents = [];
        let editingContentIndex = null;
        
        // Try to load existing course contents
        try {
            if (courseContentsInput.value && courseContentsInput.value !== '[]') {
                courseContents = JSON.parse(courseContentsInput.value);
                renderContentItems();
            }
        } catch (e) {
            console.error('Error parsing course contents:', e);
        }
        
        // File validation for content upload
        contentFileInput.addEventListener('change', function() {
            // Remove any existing error message
            if (fileErrorElement) {
                fileErrorElement.remove();
                fileErrorElement = null;
            }
            
            // Reset button state
            addContentConfirmBtn.disabled = false;
            addContentConfirmBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            
            if (this.files && this.files.length > 0) {
                const file = this.files[0];
                const fileName = file.name;
                const fileExtension = fileName.split('.').pop().toLowerCase();
                
                if (!supportedExtensions.includes(fileExtension)) {
                    // Create error message
                    fileErrorElement = document.createElement('p');
                    fileErrorElement.className = 'mt-2 text-sm text-red-400';
                    fileErrorElement.textContent = 'Unsupported file format. Supported formats are: ' + 
                        supportedExtensions.map(ext => ext.toUpperCase()).join(', ');
                    
                    // Add error message after file input
                    this.parentNode.insertBefore(fileErrorElement, this.nextSibling);
                    
                    // Disable the confirm button
                    addContentConfirmBtn.disabled = true;
                    addContentConfirmBtn.classList.add('opacity-50', 'cursor-not-allowed');
                }
            }
        });
        
        // Open modal when Add Content button is clicked
        addContentBtn.addEventListener('click', function() {
            // Reset the form
            contentTitleInput.value = '';
            contentTypeSelect.value = contentTypeSelect.options[0].value;
            contentDescriptionInput.value = '';
            contentFileInput.value = '';
            editingContentIndex = null;
            
            // Remove any previous error message
            if (fileErrorElement) {
                fileErrorElement.remove();
                fileErrorElement = null;
            }
            
            // Reset button state
            addContentConfirmBtn.disabled = false;
            addContentConfirmBtn.classList.remove('opacity-50', 'cursor-not-allowed');
            
            contentModal.classList.remove('hidden');
        });
        
        // Close modal when close button is clicked
        closeContentModalBtn.addEventListener('click', function() {
            contentModal.classList.add('hidden');
        });
        
        // Close modal when cancel button is clicked
        cancelContentBtn.addEventListener('click', function() {
            contentModal.classList.add('hidden');
        });
        
        // Add content when confirmed
        addContentConfirmBtn.addEventListener('click', function() {
            const title = contentTitleInput.value.trim();
            if (!title) {
                alert('Please enter a title');
                return;
            }
            
            const type = contentTypeSelect.value;
            const description = contentDescriptionInput.value.trim();
            
            // Check if file is selected
            if (!contentFileInput.files || contentFileInput.files.length === 0) {
                alert('Please select a file');
                return;
            }
            
            const file = contentFileInput.files[0];
            const fileName = file.name;
            
            // Validate file extension again
            const fileExtension = fileName.split('.').pop().toLowerCase();
            if (!supportedExtensions.includes(fileExtension)) {
                alert('Unsupported file format. Please select a supported file type: ' + 
                    supportedExtensions.map(ext => ext.toUpperCase()).join(', '));
                return;
            }
            
            // Create a FormData object for handling files
            const formData = new FormData();
            formData.append('content-file', file);
            
            // Generate a unique ID for this content item
            const contentId = 'temp_' + Date.now();
            
            // Add to content array
            const contentItem = {
                id: contentId,
                title: title,
                content_type: type,
                description: description,
                file_name: fileName,
                file: file
            };
            
            if (editingContentIndex !== null) {
                // Replace existing content
                courseContents[editingContentIndex] = contentItem;
            } else {
                // Add new content
                courseContents.push(contentItem);
            }
            
            // Create hidden form fields for submission
            addContentFormFields(contentItem);
            
            // Update JSON in hidden input
            updateCourseContentsInput();
            
            // Render content items
            renderContentItems();
            
            // Close modal
            contentModal.classList.add('hidden');
        });
        
        // Function to add hidden form fields for content
        function addContentFormFields(contentItem) {
            // Create hidden input for each content file
            // These will be used by the server to process the files
            const form = document.querySelector('form');
            
            // First, check if we already have inputs for this content item
            const existingInputs = document.querySelectorAll(`input[name^="content-${contentItem.id}"]`);
            existingInputs.forEach(input => input.remove());
            
            // Create hidden inputs for this content item
            const titleInput = document.createElement('input');
            titleInput.type = 'hidden';
            titleInput.name = `content-${contentItem.id}-title`;
            titleInput.value = contentItem.title;
            form.appendChild(titleInput);
            
            const typeInput = document.createElement('input');
            typeInput.type = 'hidden';
            typeInput.name = `content-${contentItem.id}-content_type`;
            typeInput.value = contentItem.content_type;
            form.appendChild(typeInput);
            
            const descriptionInput = document.createElement('input');
            descriptionInput.type = 'hidden';
            descriptionInput.name = `content-${contentItem.id}-description`;
            descriptionInput.value = contentItem.description;
            form.appendChild(descriptionInput);
            
            // For file, we need to handle it specially on submission
            const fileInfoInput = document.createElement('input');
            fileInfoInput.type = 'hidden';
            fileInfoInput.name = `content-${contentItem.id}-file_name`;
            fileInfoInput.value = contentItem.file_name;
            form.appendChild(fileInfoInput);
        }
        
        // Function to update the hidden input with JSON data
        function updateCourseContentsInput() {
            // Create a simplified version without the file objects
            const simplifiedContents = courseContents.map(item => ({
                id: item.id,
                title: item.title,
                content_type: item.content_type,
                description: item.description,
                file_name: item.file_name
            }));
            
            courseContentsInput.value = JSON.stringify(simplifiedContents);
        }
        
        // Function to render content items
        function renderContentItems() {
            contentItemsContainer.innerHTML = '';
            
            if (courseContents.length === 0) {
                const emptyMessage = document.createElement('div');
                emptyMessage.className = 'text-white/50 text-sm';
                emptyMessage.textContent = '{% trans "No content added yet" %}';
                contentItemsContainer.appendChild(emptyMessage);
                return;
            }
            
            // Create elements for each content item
            courseContents.forEach((item, index) => {
                const contentElement = document.createElement('div');
                contentElement.className = 'flex justify-between items-center p-3 bg-white/5 border border-white/10 rounded-md';
                
                const contentTypeLabel = getContentTypeLabel(item.content_type);
                
                contentElement.innerHTML = `
                    <div class="flex-grow">
                        <div class="flex items-center">
                            <span class="text-white font-medium">${item.title}</span>
                            <span class="ml-2 px-2 py-0.5 text-xs rounded-full bg-primary/20 text-primary-light">${contentTypeLabel}</span>
                        </div>
                        ${item.description ? `<p class="text-white/70 text-sm mt-1">${item.description}</p>` : ''}
                        <p class="text-white/50 text-xs mt-1">${item.file_name}</p>
                    </div>
                    <div class="flex space-x-2 flex-shrink-0">
                        <button type="button" class="edit-content text-white/70 hover:text-white" data-index="${index}">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"></path>
                            </svg>
                        </button>
                        <button type="button" class="remove-content text-white/70 hover:text-white" data-index="${index}">
                            <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                            </svg>
                        </button>
                    </div>
                `;
                
                contentItemsContainer.appendChild(contentElement);
                
                // Add event listeners
                contentElement.querySelector('.edit-content').addEventListener('click', function() {
                    const index = parseInt(this.dataset.index, 10);
                    editContentItem(index);
                });
                
                contentElement.querySelector('.remove-content').addEventListener('click', function() {
                    const index = parseInt(this.dataset.index, 10);
                    removeContentItem(index);
                });
            });
        }
        
        // Get readable label for content type
        function getContentTypeLabel(type) {
            const option = Array.from(contentTypeSelect.options).find(opt => opt.value === type);
            return option ? option.textContent : type;
        }
        
        // Function to edit a content item
        function editContentItem(index) {
            const item = courseContents[index];
            editingContentIndex = index;
            
            // Populate the form
            contentTitleInput.value = item.title;
            contentTypeSelect.value = item.content_type;
            contentDescriptionInput.value = item.description || '';
            
            // We can't repopulate the file input for security reasons
            // Just show a message that file will be replaced
            
            // Open the modal
            contentModal.classList.remove('hidden');
        }
        
        // Function to remove a content item
        function removeContentItem(index) {
            if (confirm('Are you sure you want to remove this content item?')) {
                // Get the content item ID
                const contentId = courseContents[index].id;
                
                // Remove hidden form fields
                const relatedInputs = document.querySelectorAll(`input[name^="content-${contentId}"]`);
                relatedInputs.forEach(input => input.remove());
                
                // Remove from array
                courseContents.splice(index, 1);
                
                // Update hidden input
                updateCourseContentsInput();
                
                // Render content items
                renderContentItems();
            }
        }
        
        // Handle form submission
        document.querySelector('form').addEventListener('submit', function(e) {
            // Ensure equipment categories data is saved to hidden input
            equipmentCategoriesInput.value = JSON.stringify(equipmentCategories);
            
            // Validate that all session room types are selected
            const numSessions = parseInt(numSessionsInput.value, 10);
            if (numSessions > 0) {
                const selects = document.querySelectorAll('#session-room-types-container select');
                let allValid = true;
                
                selects.forEach(select => {
                    if (!select.value) {
                        allValid = false;
                        select.classList.add('border-red-500');
                        // Add error message if not already present
                        if (!select.parentNode.querySelector('.error-message')) {
                            const errorMsg = document.createElement('p');
                            errorMsg.className = 'mt-1 text-sm text-red-500 error-message';
                            errorMsg.textContent = '{% trans "Please select a room type" %}';
                            select.parentNode.appendChild(errorMsg);
                        }
                    } else {
                        select.classList.remove('border-red-500');
                        const errorMsg = select.parentNode.querySelector('.error-message');
                        if (errorMsg) errorMsg.remove();
                    }
                });
                
                if (!allValid) {
                    e.preventDefault();
                    // Scroll to the first error
                    const firstErrorSelect = document.querySelector('#session-room-types-container select.border-red-500');
                    if (firstErrorSelect) {
                        firstErrorSelect.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    }
                    return false;
                }
            }
            
            // For course contents, we need to handle file uploads
            // Files will be submitted as part of the form's FormData
            
            // Create hidden input with content IDs to process
            const contentIdsInput = document.createElement('input');
            contentIdsInput.type = 'hidden';
            contentIdsInput.name = 'content_ids';
            contentIdsInput.value = courseContents.map(item => item.id).join(',');
            this.appendChild(contentIdsInput);
            
            // Add content files as regular file inputs
            courseContents.forEach(item => {
                if (item.file instanceof File) {
                    const fileInput = document.createElement('input');
                    fileInput.type = 'file';
                    fileInput.name = `content-${item.id}-file`;
                    fileInput.style.display = 'none';
                    
                    // Create a DataTransfer object to set the files property
                    const dataTransfer = new DataTransfer();
                    dataTransfer.items.add(item.file);
                    fileInput.files = dataTransfer.files;
                    
                    this.appendChild(fileInput);
                }
            });
        });
        
        // Handle room types
        const sessionRoomTypesInput = document.getElementById('{{ form.session_room_types.id_for_label }}');
        const numSessionsInput = document.getElementById('{{ form.num_of_sessions.id_for_label }}');
        const sessionRoomTypesContainer = document.getElementById('session-room-types-container');
        const noSessionsMessage = document.getElementById('no-sessions-message');
        
        // Initialize session room types object
        let sessionRoomTypes = {};
        
        // Load existing session room types from hidden input
        if (sessionRoomTypesInput.value) {
            try {
                // Fix for Python-style dictionaries - replace single quotes with double quotes
                let jsonValue = sessionRoomTypesInput.value;
                if (jsonValue.includes("'")) {
                    // Replace Python-style single quotes with JSON-compatible double quotes
                    jsonValue = jsonValue.replace(/'/g, '"');
                    console.log('Converted session room types to JSON format:', jsonValue);
                }
                sessionRoomTypes = JSON.parse(jsonValue);
                console.log('Loaded session room types:', sessionRoomTypes);
            } catch (e) {
                console.error('Error parsing session room types:', e);
                sessionRoomTypes = {};
            }
        }
        
        // Get available room types from the server-side (Django template)
        // If the template doesn't provide room types, include some hardcoded ones for testing
        let availableRoomTypes = [];
        
        {% if form.active_room_types %}
            // Template has room types from form
            availableRoomTypes = [
                {% for room_type in form.active_room_types %}
                    { id: {{ room_type.room_type_id }}, label: '{{ room_type.name }}' },
                {% endfor %}
            ];
        {% elif room_types %}
            // Room types from view context
            availableRoomTypes = [
                {% for room_type in room_types %}
                    { id: {{ room_type.room_type_id }}, label: '{{ room_type.name }}' },
                {% endfor %}
            ];
        {% else %}
            // Fallback to hardcoded room types for testing
            availableRoomTypes = [
                { id: 1, label: 'Classroom' },
                { id: 2, label: 'Workshop' },
                { id: 3, label: 'Seminar Room' },
                { id: 4, label: 'Computer Lab' }
            ];
            console.log('Using fallback room types - no room types provided from server');
        {% endif %}
        
        console.log('Available room types:', availableRoomTypes);
        
        // Listen for changes to num_of_sessions
        numSessionsInput.addEventListener('change', function() {
            renderSessionRoomTypes();
        });
        
        // Render session room types based on number of sessions
        function renderSessionRoomTypes() {
            const numSessions = parseInt(numSessionsInput.value, 10);
            console.log(`Rendering ${numSessions} session room types`);
            
            // Clear the container
            sessionRoomTypesContainer.innerHTML = '';
            
            // Show message if no sessions specified
            if (!numSessions || isNaN(numSessions) || numSessions <= 0) {
                noSessionsMessage.classList.remove('hidden');
                return;
            }
            
            // Hide no sessions message
            noSessionsMessage.classList.add('hidden');
            
            // Create fields for each session
            for (let i = 1; i <= numSessions; i++) {
                const sessionDiv = document.createElement('div');
                sessionDiv.className = 'flex items-center space-x-4 p-3 bg-white/5 border border-white/10 rounded-md';
                
                // Create session number label
                const labelDiv = document.createElement('div');
                labelDiv.className = 'w-24 flex-shrink-0';
                labelDiv.innerHTML = `<span class="font-medium text-white">{% trans "Session" %} ${i}</span>`;
                
                // Create room type selector
                const selectorDiv = document.createElement('div');
                selectorDiv.className = 'flex-grow';
                
                const select = document.createElement('select');
                select.className = 'w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent';
                select.dataset.session = i;
                select.required = true;
                
                // Add default option
                const defaultOption = document.createElement('option');
                defaultOption.value = '';
                defaultOption.textContent = '{% trans "Select Room Type" %}';
                select.appendChild(defaultOption);
                
                // Add room type options
                availableRoomTypes.forEach(function(roomType) {
                    const option = document.createElement('option');
                    option.value = roomType.id;
                    option.textContent = roomType.label;
                    
                    // Select the option if it was previously selected for this session
                    if (sessionRoomTypes[i] && sessionRoomTypes[i] == roomType.id) {
                        option.selected = true;
                    }
                    
                    select.appendChild(option);
                });
                
                // Handle selection change
                select.addEventListener('change', function() {
                    const sessionNum = parseInt(this.dataset.session, 10);
                    const roomTypeId = this.value;
                    
                    if (roomTypeId) {
                        sessionRoomTypes[sessionNum] = roomTypeId;
                    } else {
                        delete sessionRoomTypes[sessionNum];
                    }
                    
                    // Update hidden input
                    sessionRoomTypesInput.value = JSON.stringify(sessionRoomTypes);
                });
                
                selectorDiv.appendChild(select);
                
                // Add elements to session div
                sessionDiv.appendChild(labelDiv);
                sessionDiv.appendChild(selectorDiv);
                
                // Add session div to container
                sessionRoomTypesContainer.appendChild(sessionDiv);
            }
        }
        
        // Initialize session room types UI
        renderSessionRoomTypes();
    });
</script>
{% endblock %}
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %}

{% block extra_css %}
<style>
    /* Style for dropdown options */
    select option {
        background-color: #1a2542;
        color: white;
        padding: 8px;
    }
    
    /* Style for dropdown hover state */
    select option:hover,
    select option:focus,
    select option:active,
    select option:checked {
        background-color: #2a3b5e !important;
    }
</style>
{% endblock %} 