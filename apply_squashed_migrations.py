#!/usr/bin/env python
"""Script to mark squashed migrations as applied."""
import sqlite3

def apply_squashed_migrations():
    """Mark squashed migrations as applied in the database."""
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    try:
        # Add the missing squashed migrations
        squashed_migrations = [
            '0010_squashed_fix',
            '0019_fix_merge'
        ]
        
        for migration in squashed_migrations:
            # Check if migration record already exists
            cursor.execute(
                "SELECT * FROM django_migrations WHERE app = 'website' AND name = ?", 
                (migration,)
            )
            if not cursor.fetchone():
                cursor.execute(
                    "INSERT INTO django_migrations (app, name, applied) VALUES ('website', ?, datetime('now'))",
                    (migration,)
                )
                print(f"Added squashed migration: {migration}")
            else:
                print(f"Migration {migration} already exists")
        
        conn.commit()
        print("Squashed migrations marked as applied successfully!")
    except Exception as e:
        conn.rollback()
        print(f"Error: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    apply_squashed_migrations() 