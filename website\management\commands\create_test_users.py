from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from website.models import UserType, Corporate, Trainer, GB_Employee
from django.db import transaction
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Create test users for each user role in the LMS system'

    def handle(self, *args, **options):
        User = get_user_model()
        
        # Test user credentials
        test_users = [
            {
                'username': 'system_admin',
                'email': '<EMAIL>',
                'password': 'Admin123!',
                'first_name': 'System',
                'last_name': 'Administrator',
                'user_type_code': 'SYSTEM_ADMIN',
                'is_staff': True,
                'is_superuser': False,
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'super_admin',
                'email': '<EMAIL>',
                'password': 'SuperAdmin123!',
                'first_name': 'Super',
                'last_name': 'Administrator',
                'user_type_code': 'SUPER_ADMIN',
                'is_staff': True,
                'is_superuser': True,
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'corp_admin',
                'email': '<EMAIL>',
                'password': 'CorpAdmin123!',
                'first_name': 'Corporate',
                'last_name': 'Administrator',
                'user_type_code': 'EXTERNAL_CORPORATE_ADMIN',
                'company_name': 'Test Corporation Ltd.',
                'commercial_registration_number': 'CR123456789',
                'phone_number': '+************',
                'nationality': 'Egyptian',
                'national_id': '**************',
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'corp_trainee',
                'email': '<EMAIL>',
                'password': 'Trainee123!',
                'first_name': 'Corporate',
                'last_name': 'Trainee',
                'user_type_code': 'EXTERNAL_CORPORATE_TRAINEE',
                'company_name': 'Test Corporation Ltd.',
                'phone_number': '+************',
                'nationality': 'Egyptian',
                'national_id': '**************',
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'individual_user',
                'email': '<EMAIL>',
                'password': 'Individual123!',
                'first_name': 'Individual',
                'last_name': 'User',
                'user_type_code': 'EXTERNAL_INDIVIDUAL',
                'phone_number': '+************',
                'nationality': 'Egyptian',
                'national_id': '**************',
                'passport_number': 'A12345678',
                'address': '123 Test Street, Cairo, Egypt',
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'trainer_user',
                'email': '<EMAIL>',
                'password': 'Trainer123!',
                'first_name': 'Professional',
                'last_name': 'Trainer',
                'user_type_code': 'TRAINER',
                'phone_number': '+************',
                'nationality': 'Egyptian',
                'national_id': '**************',
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'gb_employee',
                'email': '<EMAIL>',
                'password': None,  # GB employees use OTP
                'first_name': 'GB',
                'last_name': 'Employee',
                'user_type_code': 'INTERNAL_GB',
                'employee_id': 'GB001234',
                'oracle_department': 'Information Technology',
                'oracle_position': 'Software Developer',
                'phone_number': '+************',
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            },
            {
                'username': 'gb_supervisor',
                'email': '<EMAIL>',
                'password': None,  # GB employees use OTP
                'first_name': 'GB',
                'last_name': 'Supervisor',
                'user_type_code': 'INTERNAL_GB',
                'employee_id': 'GB005678',
                'oracle_department': 'Information Technology',
                'oracle_position': 'IT Manager',
                'phone_number': '+************',
                'account_status': 'ACTIVE',
                'is_profile_complete': True
            }
        ]

        with transaction.atomic():
            for user_data in test_users:
                try:
                    # Get or create user type
                    user_type_code = user_data.pop('user_type_code')
                    user_type, created = UserType.objects.get_or_create(
                        code=user_type_code,
                        defaults={
                            'name': self.get_user_type_name(user_type_code),
                            'description': f'{user_type_code} user type',
                            'is_active': True
                        }
                    )
                    
                    # Check if user already exists
                    username = user_data['username']
                    if User.objects.filter(username=username).exists():
                        self.stdout.write(
                            self.style.WARNING(f'User {username} already exists, skipping...')
                        )
                        continue
                    
                    # Create user
                    password = user_data.pop('password', None)
                    user_data['user_type'] = user_type
                    
                    if password:
                        user = User.objects.create_user(
                            password=password,
                            **user_data
                        )
                    else:
                        # For GB employees (no password needed)
                        user = User.objects.create_user(
                            password='temp_password',  # Temporary, won't be used
                            **user_data
                        )
                        user.set_unusable_password()  # Disable password login
                        user.save()
                    
                    # Create additional records for specific user types
                    if user_type_code == 'TRAINER':
                        self.create_trainer_profile(user)
                    elif user_type_code in ['EXTERNAL_CORPORATE_ADMIN', 'EXTERNAL_CORPORATE_TRAINEE']:
                        self.create_corporate_profile(user)
                    elif user_type_code == 'INTERNAL_GB':
                        self.create_gb_employee_profile(user)
                    
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully created user: {username}')
                    )
                    
                except Exception as e:
                    self.stdout.write(
                        self.style.ERROR(f'Error creating user {user_data.get("username", "unknown")}: {str(e)}')
                    )

    def get_user_type_name(self, code):
        names = {
            'SYSTEM_ADMIN': 'System Administrator',
            'SUPER_ADMIN': 'Super Administrator',
            'EXTERNAL_CORPORATE_ADMIN': 'External Corporate Admin',
            'EXTERNAL_CORPORATE_TRAINEE': 'External Corporate Trainee',
            'EXTERNAL_INDIVIDUAL': 'External Individual',
            'TRAINER': 'Trainer',
            'INTERNAL_GB': 'Internal GB Employee'
        }
        return names.get(code, code)

    def create_trainer_profile(self, user):
        """Create trainer profile for trainer users"""
        try:
            trainer, created = Trainer.objects.get_or_create(
                user=user,
                defaults={
                    'status': 'READY',
                    'is_supervisor': False
                }
            )
            if created:
                self.stdout.write(f'Created trainer profile for {user.username}')
        except Exception as e:
            self.stdout.write(f'Error creating trainer profile: {str(e)}')

    def create_corporate_profile(self, user):
        """Create corporate profile for corporate users"""
        try:
            corporate, created = Corporate.objects.get_or_create(
                legal_name=user.company_name,
                defaults={
                    'corporate_admin': user,
                    'commercial_registration_number': user.commercial_registration_number or 'CR123456789',
                    'tax_registration_number': 'TAX123456789',
                    'phone_number': user.phone_number,
                    'address': user.address or '123 Corporate Street, Cairo, Egypt',
                    'capacity': 100,
                    'category': 'A'
                }
            )
            if created:
                self.stdout.write(f'Created corporate profile: {corporate.legal_name}')
        except Exception as e:
            self.stdout.write(f'Error creating corporate profile: {str(e)}')

    def create_gb_employee_profile(self, user):
        """Create GB employee profile for internal users"""
        try:
            gb_employee, created = GB_Employee.objects.get_or_create(
                employee_number=user.employee_id,
                defaults={
                    'english_name': f'{user.first_name} {user.last_name}',
                    'oracle_email_address': user.email,
                    'department': user.oracle_department,
                    'position_name': user.oracle_position,
                    'oracle_phone_number': user.phone_number,
                    'is_active': True
                }
            )
            if created:
                user.gb_employee = gb_employee
                user.save()
                self.stdout.write(f'Created GB employee profile for {user.employee_id}')
        except Exception as e:
            self.stdout.write(f'Error creating GB employee profile: {str(e)}')
