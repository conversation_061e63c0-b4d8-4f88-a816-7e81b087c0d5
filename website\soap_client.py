import requests
import xml.etree.ElementTree as ET
from django.conf import settings
import logging
from .models import GB_Employee, CustomUser, UserType
from django.utils import timezone
from django.db import transaction

logger = logging.getLogger(__name__)

class OracleSOAPClient:
    """
    SOAP client for Oracle employee information API
    """
    
    def __init__(self):
        # Oracle EBS credentials from environment variables
        self.soap_url = getattr(settings, 'ORACLE_SOAP_URL', 'http://your-oracle-server/soap/endpoint')
        self.username = getattr(settings, 'ORACLE_SOAP_USERNAME', 'hr-Q5VuZ0Li8Hzv')
        self.password = getattr(settings, 'ORACLE_SOAP_PASSWORD', 'qrpV4O5V5f8B')
    
    def create_soap_envelope(self, employee_number):
        """
        Create SOAP envelope for employee information request
        """
        soap_envelope = f"""<?xml version="1.0" encoding="UTF-8"?>
<soapenv:Envelope xmlns:soapenv="http://schemas.xmlsoap.org/soap/envelope/" 
                  xmlns:rgi="http://xmlns.oracle.com/apps/ar/soaprovider/plsql/rgi_hr_mobile_api/" 
                  xmlns:get="http://xmlns.oracle.com/apps/ar/soaprovider/plsql/rgi_hr_mobile_api/get_emp_info/">
   <soapenv:Header>
      <wsse:Security xmlns:wsse="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd">
         <wsse:UsernameToken>
            <wsse:Username>{self.username}</wsse:Username>
            <wsse:Password Type="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1.0#PasswordText">{self.password}</wsse:Password>
         </wsse:UsernameToken>
      </wsse:Security>
   </soapenv:Header>
   <soapenv:Body>
      <get:InputParameters>
         <get:P_EMP_NO>{employee_number}</get:P_EMP_NO>
      </get:InputParameters>
   </soapenv:Body>
</soapenv:Envelope>"""
        return soap_envelope
    
    def call_soap_api(self, employee_number):
        """
        Make SOAP API call to Oracle for employee information
        """
        try:
            soap_envelope = self.create_soap_envelope(employee_number)
            
            headers = {
                'Content-Type': 'text/xml; charset=utf-8',
                'SOAPAction': 'http://xmlns.oracle.com/apps/ar/soaprovider/plsql/rgi_hr_mobile_api/get_emp_info/',
                'Content-Length': str(len(soap_envelope))
            }
            
            logger.info(f"Making SOAP request for employee: {employee_number}")
            
            response = requests.post(
                self.soap_url,
                data=soap_envelope,
                headers=headers,
                timeout=30
            )
            
            logger.info(f"SOAP response status: {response.status_code}")
            logger.debug(f"SOAP response content: {response.text[:500]}...")  # First 500 chars for debugging
            
            if response.status_code == 200:
                return self.parse_soap_response(response.text)
            else:
                logger.error(f"SOAP API error: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.RequestException as e:
            logger.error(f"SOAP API request failed: {str(e)}")
            return None
        except Exception as e:
            logger.error(f"Unexpected error in SOAP API call: {str(e)}")
            return None
    
    def parse_soap_response(self, response_xml):
        """
        Parse SOAP response and extract employee data
        """
        try:
            # Parse XML with namespaces
            root = ET.fromstring(response_xml)
            
            # Define namespaces from the response
            namespaces = {
                'env': 'http://schemas.xmlsoap.org/soap/envelope/',
                'wsa': 'http://www.w3.org/2005/08/addressing',
                'get': 'http://xmlns.oracle.com/apps/ar/soaprovider/plsql/rgi_hr_mobile_api'
            }
            
            # Look for employee data in SOAP body
            employee_data = {}
            
            # Find the SOAP body
            soap_body = root.find('.//env:Body', namespaces)
            if soap_body is not None:
                # First check for Oracle error status
                status_elements = soap_body.findall('.//*')
                for elem in status_elements:
                    tag_name = elem.tag.split('}')[1] if '}' in elem.tag else elem.tag
                    if tag_name == 'P_STATUS' and elem.text == 'E':
                        # Look for error message
                        for error_elem in status_elements:
                            error_tag = error_elem.tag.split('}')[1] if '}' in error_elem.tag else error_elem.tag
                            if error_tag == 'P_ERROR_MSG':
                                error_msg = error_elem.text if error_elem.text else "Unknown Oracle error"
                                logger.warning(f"Oracle SOAP API returned error: {error_msg}")
                                return None
                
                # Look for the response data - the structure may vary
                # Let's first try to find any elements that look like employee data
                for elem in soap_body.iter():
                    if elem.text and elem.text.strip():
                        tag_name = elem.tag
                        # Remove namespace prefix if present
                        if '}' in tag_name:
                            tag_name = tag_name.split('}')[1]
                        
                        # Map Oracle fields to our model fields
                        field_mapping = {
                            'EMPLOYEE_NUMBER': 'EMPLOYEE_NUMBER',
                            'FULL_NAME': 'FULL_NAME', 
                            'ENGLISH_NAME': 'ENGLISH_NAME',
                            'ARABIC_NAME': 'ARABIC_NAME',
                            'USER_NAME': 'USER_NAME',
                            'DEPARTMENT': 'DEPARTMENT',
                            'POSITION_NAME': 'POSITION_NAME',
                            'ORGANIZATION_ID': 'ORGANIZATION_ID',
                            'SUPERVISOR_ID': 'SUPERVISOR_ID',
                            'SUPERVISOR_USER_NAME': 'SUPERVISOR_USER_NAME',
                            'HIRE_DATE': 'HIRE_DATE',
                            'PAYROLL_ID': 'PAYROLL_ID',
                            'GRADE_NAME': 'GRADE_NAME',
                            'GRADE_ID': 'GRADE_ID',
                            'PHONE_NUMBER': 'PHONE_NUMBER',
                            'EMAIL_ADDRESS': 'EMAIL_ADDRESS',
                            'HEAD_DEPARTMENT': 'HEAD_DEPARTMENT',
                            'COST_CENTER': 'COST_CENTER'
                        }
                        
                        if tag_name.upper() in field_mapping:
                            employee_data[field_mapping[tag_name.upper()]] = elem.text.strip()
            
            # If no specific data found, let's log the response structure for debugging
            if not employee_data:
                logger.warning("No employee data found in SOAP response")
                logger.debug(f"SOAP Body content: {ET.tostring(soap_body, encoding='unicode') if soap_body is not None else 'No body found'}")
            
            logger.info(f"Parsed employee data: {employee_data}")
            return employee_data if employee_data else None
            
        except ET.ParseError as e:
            logger.error(f"Failed to parse SOAP response: {str(e)}")
            logger.error(f"Raw response content: {response_xml[:1000]}...")  # Show first 1000 chars
            return None
        except Exception as e:
            logger.error(f"Unexpected error parsing SOAP response: {str(e)}")
            return None
    
    def get_employee_data(self, employee_number):
        """
        Get employee data from Oracle SOAP API (used for OTP verification)
        Returns dictionary with employee information including email
        """
        try:
            oracle_data = self.call_soap_api(employee_number)
            if oracle_data:
                # Map Oracle fields to our expected format
                mapped_data = {
                    'employee_number': oracle_data.get('EMPLOYEE_NUMBER'),
                    'full_name': oracle_data.get('FULL_NAME'),
                    'english_name': oracle_data.get('ENGLISH_NAME'),
                    'arabic_name': oracle_data.get('ARABIC_NAME'),
                    'oracle_email_address': oracle_data.get('EMAIL_ADDRESS'),
                    'department': oracle_data.get('DEPARTMENT'),
                    'position_name': oracle_data.get('POSITION_NAME'),
                    'phone_number': oracle_data.get('PHONE_NUMBER'),
                }
                return mapped_data
            return None
        except Exception as e:
            logger.error(f"Error getting employee data for {employee_number}: {str(e)}")
            return None

    def authenticate_employee(self, employee_number):
        """
        Authenticate employee and create/update database records
        """
        try:
            # Call Oracle SOAP API
            oracle_data = self.call_soap_api(employee_number)
            
            if not oracle_data:
                logger.warning(f"No data returned from Oracle for employee: {employee_number}")
                return None
            
            # Validate that we have essential data
            if not oracle_data.get('EMPLOYEE_NUMBER'):
                logger.error("Oracle response missing EMPLOYEE_NUMBER")
                return None
            
            with transaction.atomic():
                # Create or update GB_Employee record
                gb_employee = GB_Employee.create_or_update_from_oracle(oracle_data)
                
                # Get or create INTERNAL_GB user type
                internal_user_type, created = UserType.objects.get_or_create(
                    code='INTERNAL_GB',
                    defaults={
                        'name': 'Internal GB Corp User',
                        'description': 'Internal GB employees',
                        'is_active': True
                    }
                )
                
                # Check if CustomUser already exists
                user = None
                
                # Try to find by employee_id first
                if gb_employee.employee_number:
                    user = CustomUser.objects.filter(employee_id=gb_employee.employee_number).first()
                
                # Try to find by email if not found by employee_id
                if not user and gb_employee.oracle_email_address:
                    user = CustomUser.objects.filter(email=gb_employee.oracle_email_address).first()
                
                if user:
                    # Update existing user
                    user.gb_employee = gb_employee
                    user.user_type = internal_user_type
                    user.sync_oracle_data()
                    logger.info(f"Updated existing user: {user.username}")
                else:
                    # Create new user
                    username = self._generate_username(gb_employee)
                    email = gb_employee.oracle_email_address or f"{gb_employee.employee_number}@gb.corp"
                    
                    user = CustomUser.objects.create_user(
                        username=username,
                        email=email,
                        password=None,  # No password needed for Oracle authentication
                        user_type=internal_user_type,
                        employee_id=gb_employee.employee_number,
                        gb_employee=gb_employee,
                        account_status='ACTIVE',
                        is_profile_complete=True
                    )
                    
                    # Sync Oracle data
                    user.sync_oracle_data()
                    
                    logger.info(f"Created new user: {user.username}")
                
                return user
                
        except Exception as e:
            logger.error(f"Error in authenticate_employee: {str(e)}")
            return None
    
    def _generate_username(self, gb_employee):
        """
        Generate a unique username for the employee
        """
        # Try oracle username first
        if gb_employee.oracle_username:
            base_username = gb_employee.oracle_username.lower()
        elif gb_employee.english_name:
            # Generate from name
            name_parts = gb_employee.english_name.lower().split()
            if len(name_parts) >= 2:
                base_username = f"{name_parts[0]}.{name_parts[-1]}"
            else:
                base_username = name_parts[0] if name_parts else f"emp{gb_employee.employee_number}"
        else:
            base_username = f"emp{gb_employee.employee_number}"
        
        # Ensure uniqueness
        username = base_username
        counter = 1
        while CustomUser.objects.filter(username=username).exists():
            username = f"{base_username}{counter}"
            counter += 1
        
        return username 