import sqlite3
import os
import sys

def transfer_data():
    # Database paths
    source_db_path = os.path.join('..', 'db.sqlite3')
    target_db_path = 'db.sqlite3'
    
    print(f"Source DB: {source_db_path}")
    print(f"Target DB: {target_db_path}")
    
    # Connect to both databases
    try:
        source_conn = sqlite3.connect(source_db_path)
        source_cursor = source_conn.cursor()
        
        target_conn = sqlite3.connect(target_db_path)
        target_cursor = target_conn.cursor()
        
        # Get all tables from source database
        source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in source_cursor.fetchall()]
        
        # Exclude the survey table and any specific tables to skip
        excluded_tables = ['website_survey']
        if 'website_survey' in tables:
            tables.remove('website_survey')
        
        # Tables that should be skipped (internal SQLite tables)
        skip_tables = ['sqlite_sequence', 'sqlite_stat1', 'sqlite_stat2', 'sqlite_stat3', 'sqlite_stat4'] + excluded_tables
        tables = [table for table in tables if table not in skip_tables]
        
        print(f"Found {len(tables)} tables to transfer")
        
        # For each table, transfer data
        for table in tables:
            try:
                print(f"Transferring table: {table}")
                
                # Check if target table exists
                target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if not target_cursor.fetchone():
                    print(f"  Table {table} doesn't exist in target database, skipping")
                    continue
                
                # Get source table schema
                source_cursor.execute(f"PRAGMA table_info({table})")
                source_columns = source_cursor.fetchall()
                source_column_names = [column[1] for column in source_columns]
                
                # Get target table schema
                target_cursor.execute(f"PRAGMA table_info({table})")
                target_columns = target_cursor.fetchall()
                target_column_names = [column[1] for column in target_columns]
                
                # Find common columns (intersection of source and target columns)
                common_columns = [col for col in source_column_names if col in target_column_names]
                
                if not common_columns:
                    print(f"  No common columns found for table {table}, skipping")
                    continue
                
                # Handle reserved keywords by quoting column names
                safe_common_columns = [f'"{col}"' if col.lower() in ('order', 'group', 'table') else col for col in common_columns]
                
                # Get data from source table, only for common columns
                columns_str = ', '.join(common_columns)
                source_cursor.execute(f"SELECT {columns_str} FROM {table}")
                rows = source_cursor.fetchall()
                
                if not rows:
                    print(f"  Table {table} is empty, skipping")
                    continue
                
                print(f"  Found {len(rows)} rows to transfer")
                print(f"  Common columns: {len(common_columns)} of {len(source_column_names)} source columns and {len(target_column_names)} target columns")
                
                # Clear target table first (optional, comment out if you want to preserve existing data)
                target_cursor.execute(f"DELETE FROM {table}")
                
                # Insert data into target table
                placeholders = ', '.join(['?' for _ in common_columns])
                safe_columns_str = ', '.join(safe_common_columns)
                
                insert_count = 0
                error_count = 0
                
                for row in rows:
                    try:
                        target_cursor.execute(f"INSERT INTO {table} ({safe_columns_str}) VALUES ({placeholders})", row)
                        insert_count += 1
                    except sqlite3.Error as e:
                        error_count += 1
                        if error_count <= 5:  # Limit error reporting to avoid flooding output
                            print(f"  Error inserting row into {table}: {e}")
                        continue
                
                if error_count > 5:
                    print(f"  ... and {error_count - 5} more errors")
                
                target_conn.commit()
                print(f"  Successfully transferred {insert_count} rows to table: {table} (with {error_count} errors)")
                
            except sqlite3.Error as e:
                print(f"Error processing table {table}: {e}")
                continue
        
        print("Data transfer completed")
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        if 'source_conn' in locals():
            source_conn.close()
        if 'target_conn' in locals():
            target_conn.close()

if __name__ == "__main__":
    transfer_data() 