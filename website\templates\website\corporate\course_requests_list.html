{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Corporate Course Requests" %}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        <script>
            // Auto-dismiss Django flash messages
            document.addEventListener('DOMContentLoaded', function() {
                const messages = document.querySelectorAll('.notification-message');
                messages.forEach(message => {
                    const type = message.getAttribute('data-type');
                    let timeout = 5000; // Default 5 seconds
                    
                    // Different timeouts based on message type
                    if (type === 'success') {
                        timeout = 5000; // 5 seconds for success
                    } else if (type === 'warning') {
                        timeout = 7000; // 7 seconds for warnings
                    } else if (type === 'error') {
                        timeout = 10000; // 10 seconds for errors
                    }
                    
                    setTimeout(() => {
                        message.style.transition = 'opacity 0.5s ease';
                        message.style.opacity = '0';
                        setTimeout(() => {
                            message.remove();
                            
                            // If no more messages, remove the container
                            if (document.querySelectorAll('.notification-message').length === 0) {
                                const container = document.getElementById('notifications-container');
                                if (container) container.remove();
                            }
                        }, 500);
                    }, timeout);
                });
            });
        </script>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center justify-between mb-8">
            <h1 class="text-2xl font-bold text-white">{% trans "Corporate Course Requests" %}</h1>
        </div>

        <!-- Stats Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn active" data-filter="all">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "All Requests" %}</p>
                            <h3 class="text-2xl font-semibold text-white mt-1">{{ course_requests.count|add:new_course_requests.count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-primary/10">
                            <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn" data-filter="PENDING">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "Pending" %}</p>
                            <h3 class="text-2xl font-semibold text-yellow-400 mt-1">{{ pending_count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-yellow-400/10">
                            <svg class="w-6 h-6 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn" data-filter="NEW_COURSE">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "New Course Requests" %}</p>
                            <h3 class="text-2xl font-semibold text-blue-400 mt-1">{{ pending_new_course_count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-blue-400/10">
                            <svg class="w-6 h-6 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v3m0 0v3m0-3h3m-3 0H9m12 0a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn" data-filter="PENDING_CANCEL">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "Pending Cancellation" %}</p>
                            <h3 class="text-2xl font-semibold text-orange-400 mt-1">{{ pending_cancel_count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-orange-400/10">
                            <svg class="w-6 h-6 text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn" data-filter="APPROVED">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "Approved" %}</p>
                            <h3 class="text-2xl font-semibold text-green-400 mt-1">{{ approved_count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-green-400/10">
                            <svg class="w-6 h-6 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn" data-filter="REJECTED">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "Rejected" %}</p>
                            <h3 class="text-2xl font-semibold text-red-400 mt-1">{{ rejected_count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-red-400/10">
                            <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
            
            <div class="bg-white/5 backdrop-blur-md border border-white/10 rounded-lg overflow-hidden">
                <a href="#" class="block px-6 py-4 hover:bg-white/5 transition-colors duration-200 filter-btn" data-filter="CANCELLED">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-sm font-medium text-white">{% trans "Cancelled" %}</p>
                            <h3 class="text-2xl font-semibold text-red-400 mt-1">{{ cancelled_count }}</h3>
                        </div>
                        <div class="rounded-full p-3 bg-red-400/10">
                            <svg class="w-6 h-6 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                            </svg>
                        </div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Filter Controls -->
        <div class="flex flex-wrap gap-4 mb-6">
            <button type="button" data-filter="all" class="filter-btn active px-4 py-2 rounded-md bg-primary text-white">
                {% trans "All" %}
            </button>
            <button type="button" data-filter="PENDING" class="filter-btn px-4 py-2 rounded-md bg-white/10 text-white hover:bg-primary/80">
                {% trans "Pending" %}
            </button>
            <button type="button" data-filter="NEW_COURSE" class="filter-btn px-4 py-2 rounded-md bg-white/10 text-white hover:bg-primary/80">
                {% trans "New Course Requests" %}
            </button>
            <button type="button" data-filter="APPROVED" class="filter-btn px-4 py-2 rounded-md bg-white/10 text-white hover:bg-primary/80">
                {% trans "Approved" %}
            </button>
            <button type="button" data-filter="REJECTED" class="filter-btn px-4 py-2 rounded-md bg-white/10 text-white hover:bg-primary/80">
                {% trans "Rejected" %}
            </button>
            <button type="button" data-filter="PENDING_CANCEL" class="filter-btn px-4 py-2 rounded-md bg-white/10 text-white hover:bg-primary/80">
                {% trans "Pending Cancellation" %}
            </button>
            <button type="button" data-filter="CANCELLED" class="filter-btn px-4 py-2 rounded-md bg-white/10 text-white hover:bg-primary/80">
                {% trans "Cancelled" %}
            </button>
        </div>
                
                <!-- Requests Table -->
                {% if course_requests or new_course_requests %}
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead>
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Corporate" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Course" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Type" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Capacity" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Status" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Actions" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-white/10">
                            <!-- Regular course requests -->
                            {% for request in course_requests %}
                            <tr class="request-row" data-status="{{ request.status }}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-white">
                                            {{ request.corporate.legal_name }}
                                        </div>
                                        <div class="text-sm text-white/70">
                                            {{ request.corporate_admin.user.email }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    {% if request.course %}
                                    <div>
                                        <div class="text-sm font-medium text-white">{{ request.course.name_en }}</div>
                                        <div class="text-xs text-white/70">ID: {{ request.course.course_id }}</div>
                                    </div>
                                    {% else %}
                                    <span class="text-sm text-white/70">{% trans "Not specified" %}</span>
                                    {% endif %}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm text-white">{{ request.get_course_type_display }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm text-white">{{ request.capacity }} {% trans "seats" %}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs rounded-full {% if request.status == 'PENDING' %}bg-amber-500/20 text-amber-400{% elif request.status == 'APPROVED' %}bg-green-500/20 text-green-400{% elif request.status == 'PENDING_CANCEL' %}bg-orange-500/20 text-orange-400{% else %}bg-red-500/20 text-red-400{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                    <button type="button" class="text-primary hover:text-primary/80 mr-3 view-details-btn" 
                                            data-request-id="{{ request.request_id }}"
                                            data-request-type="regular"
                                            data-corporate="{{ request.corporate.legal_name }}"
                                            data-admin="{{ request.corporate_admin.user.first_name }} {{ request.corporate_admin.user.last_name }}"
                                            data-email="{{ request.corporate_admin.user.email }}"
                                            data-type="{{ request.get_course_type_display }}"
                                            data-capacity="{{ request.capacity }}"
                                            data-notes="{{ request.notes }}"
                                            data-status="{{ request.status }}"
                                            data-admin-notes="{{ request.admin_notes }}"
                                            {% if request.course %}
                                            data-course-id="{{ request.course.course_id }}"
                                            data-course-name="{{ request.course.name_en }}"
                                            {% endif %}>
                                        {% trans "View" %}
                                    </button>
                                    {% if request.status == 'PENDING' %}
                                    <button type="button" class="text-green-400 hover:text-green-300 mr-3 approve-btn" data-request-id="{{ request.request_id }}" data-request-type="regular">
                                        {% trans "Approve" %}
                                    </button>
                                    <button type="button" class="text-red-400 hover:text-red-300 reject-btn" data-request-id="{{ request.request_id }}" data-request-type="regular">
                                        {% trans "Reject" %}
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                            
                            <!-- New course requests -->
                            {% for request in new_course_requests %}
                            <tr class="request-row" data-status="{% if request.status == 'PENDING' %}NEW_COURSE{% else %}{{ request.status }}{% endif %}">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-white">
                                            {{ request.corporate.legal_name }}
                                        </div>
                                        <div class="text-sm text-white/70">
                                            {{ request.corporate_admin.user.email }}
                                        </div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div>
                                        <div class="text-sm font-medium text-white">{{ request.course_name }}</div>
                                        <div class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-400">{% trans "NEW" %}</div>
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm text-white">{{ request.get_course_type_display }}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="text-sm text-white">{{ request.capacity }} {% trans "seats" %}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="inline-flex px-2 py-1 text-xs rounded-full {% if request.status == 'PENDING' %}bg-amber-500/20 text-amber-400{% elif request.status == 'APPROVED' %}bg-green-500/20 text-green-400{% elif request.status == 'PENDING_CANCEL' %}bg-orange-500/20 text-orange-400{% else %}bg-red-500/20 text-red-400{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                    <button type="button" class="text-primary hover:text-primary/80 mr-3 view-details-btn" 
                                            data-request-id="{{ request.request_id }}"
                                            data-request-type="new_course"
                                            data-corporate="{{ request.corporate.legal_name }}"
                                            data-admin="{{ request.corporate_admin.user.first_name }} {{ request.corporate_admin.user.last_name }}"
                                            data-email="{{ request.corporate_admin.user.email }}"
                                            data-type="{{ request.get_course_type_display }}"
                                            data-capacity="{{ request.capacity }}"
                                            data-notes="{{ request.notes }}"
                                            data-status="{{ request.status }}"
                                            data-admin-notes="{{ request.admin_notes }}"
                                            data-course-name="{{ request.course_name }}"
                                            data-course-description="{{ request.course_description }}">
                                        {% trans "View" %}
                                    </button>
                                    {% if request.status == 'PENDING' %}
                                    <button type="button" class="text-green-400 hover:text-green-300 mr-3 approve-btn" data-request-id="{{ request.request_id }}" data-request-type="new_course">
                                        {% trans "Approve" %}
                                    </button>
                                    <button type="button" class="text-red-400 hover:text-red-300 reject-btn" data-request-id="{{ request.request_id }}" data-request-type="new_course">
                                        {% trans "Reject" %}
                                    </button>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-8">
                    <p class="text-white/70">{% trans "No course requests found" %}</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Request Details Modal -->
<div id="requestDetailsModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden flex items-center justify-center">
    <div class="bg-gray-800 rounded-lg max-w-lg w-full mx-4 overflow-hidden">
        <div class="bg-primary p-4 flex justify-between items-center">
            <h3 class="text-white font-semibold">{% trans "Request Details" %}</h3>
            <button type="button" class="text-white hover:text-white/80" id="closeDetailsModal">
                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        </div>
        <div class="p-6 space-y-4">
            <div>
                <span class="text-white/70 text-sm block">{% trans "Corporate" %}</span>
                <span class="text-white text-base font-medium" id="modalCorporate"></span>
            </div>
            <div>
                <span class="text-white/70 text-sm block">{% trans "Admin" %}</span>
                <span class="text-white text-base" id="modalAdmin"></span>
                <span class="text-white/70 text-sm block" id="modalEmail"></span>
            </div>
            <div id="modalCourseContainer" class="hidden">
                <span class="text-white/70 text-sm block">{% trans "Course" %}</span>
                <div>
                    <span class="text-white text-base" id="modalCourseName"></span>
                    <span class="text-white/70 text-sm block">{% trans "Course ID" %}: <span id="modalCourseId"></span></span>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <span class="text-white/70 text-sm block">{% trans "Course Type" %}</span>
                    <span class="text-white text-base" id="modalType"></span>
                </div>
                <div>
                    <span class="text-white/70 text-sm block">{% trans "Capacity" %}</span>
                    <span class="text-white text-base" id="modalCapacity"></span>
                </div>
            </div>
            <div>
                <span class="text-white/70 text-sm block">{% trans "Notes" %}</span>
                <p class="text-white text-base" id="modalNotes"></p>
            </div>
            <div id="modalStatusContainer">
                <span class="text-white/70 text-sm block">{% trans "Status" %}</span>
                <span class="inline-flex px-2 py-1 text-xs rounded-full" id="modalStatus"></span>
            </div>
            <div id="modalAdminNotesContainer" class="hidden">
                <span class="text-white/70 text-sm block">{% trans "Admin Notes" %}</span>
                <p class="text-white text-base" id="modalAdminNotes"></p>
            </div>
            
            <!-- Action Form (for Pending only) -->
            <div id="actionForm" class="border-t border-white/10 pt-4 mt-4 hidden">
                <h4 class="text-white font-medium mb-3">{% trans "Update Request Status" %}</h4>
                <textarea id="adminNotesInput" class="w-full px-3 py-2 bg-white/10 border border-white/20 rounded-md text-white" rows="3" placeholder="{% trans 'Admin notes...' %}"></textarea>
                <div class="flex space-x-3 mt-3">
                    <button id="confirmApproveBtn" class="flex-1 bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-md">
                        {% trans "Approve" %}
                    </button>
                    <button id="confirmRejectBtn" class="flex-1 bg-red-500 hover:bg-red-600 text-white py-2 px-4 rounded-md">
                        {% trans "Reject" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Setup for filter buttons
        const filterButtons = document.querySelectorAll('.filter-btn');
        const requestRows = document.querySelectorAll('.request-row');
        
        // Handle filter button clicks
        filterButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Remove active class from all buttons and add to clicked button
                filterButtons.forEach(btn => btn.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.getAttribute('data-filter');
                
                // Show/hide rows based on filter
                requestRows.forEach(row => {
                    if (filter === 'all' || row.getAttribute('data-status') === filter) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
        
        // View details button handling
        const detailsButtons = document.querySelectorAll('.view-details-btn');
        const modal = document.getElementById('requestDetailsModal');
        const closeModalBtn = document.getElementById('closeDetailsModal');
        
        detailsButtons.forEach(button => {
            button.addEventListener('click', function() {
                const requestType = this.getAttribute('data-request-type');
                const requestId = this.getAttribute('data-request-id');
                const status = this.getAttribute('data-status');
                
                // Populate modal with request data from data attributes
                document.getElementById('modalCorporate').textContent = this.getAttribute('data-corporate');
                document.getElementById('modalAdmin').textContent = this.getAttribute('data-admin');
                document.getElementById('modalEmail').textContent = this.getAttribute('data-email');
                document.getElementById('modalType').textContent = this.getAttribute('data-type');
                document.getElementById('modalCapacity').textContent = this.getAttribute('data-capacity') + ' {% trans "seats" %}';
                document.getElementById('modalNotes').textContent = this.getAttribute('data-notes') || '{% trans "No notes provided" %}';
                
                // Handle course display based on request type
                const courseContainer = document.getElementById('modalCourseContainer');
                if (requestType === 'regular' && this.hasAttribute('data-course-name')) {
                    // Regular course request with existing course
                    courseContainer.classList.remove('hidden');
                    document.getElementById('modalCourseName').textContent = this.getAttribute('data-course-name');
                    document.getElementById('modalCourseId').textContent = this.getAttribute('data-course-id');
                    
                    // Hide course description container if it exists
                    const descriptionContainer = document.getElementById('modalCourseDescriptionContainer');
                    if (descriptionContainer) descriptionContainer.classList.add('hidden');
                } else if (requestType === 'new_course') {
                    // New course request
                    courseContainer.classList.remove('hidden');
                    document.getElementById('modalCourseName').textContent = this.getAttribute('data-course-name');
                    
                    // Show course description
                    let descriptionContainer = document.getElementById('modalCourseDescriptionContainer');
                    
                    // Create description container if it doesn't exist
                    if (!descriptionContainer) {
                        descriptionContainer = document.createElement('div');
                        descriptionContainer.id = 'modalCourseDescriptionContainer';
                        
                        // Create content elements
                        const label = document.createElement('span');
                        label.className = 'text-white/70 text-sm block';
                        label.textContent = '{% trans "Course Description" %}';
                        
                        const description = document.createElement('p');
                        description.id = 'modalCourseDescription';
                        description.className = 'text-white text-base';
                        
                        // Add elements to container
                        descriptionContainer.appendChild(label);
                        descriptionContainer.appendChild(description);
                        
                        // Insert after course container
                        courseContainer.parentNode.insertBefore(descriptionContainer, courseContainer.nextSibling);
                    }
                    
                    // Show and update description
                    descriptionContainer.classList.remove('hidden');
                    document.getElementById('modalCourseDescription').textContent = this.getAttribute('data-course-description') || '{% trans "No description provided" %}';
                    
                    // Hide course ID if showing it
                    const courseIdElement = document.getElementById('modalCourseId').parentNode;
                    courseIdElement.classList.add('hidden');
                } else {
                    // Hide course container for regular requests without a course
                    courseContainer.classList.add('hidden');
                    
                    // Hide description container if it exists
                    const descriptionContainer = document.getElementById('modalCourseDescriptionContainer');
                    if (descriptionContainer) descriptionContainer.classList.add('hidden');
                }
                
                // Update status display
                const statusContainer = document.getElementById('modalStatusContainer');
                const statusElement = document.getElementById('modalStatus');
                let statusClass = '';
                let statusText = '';
                
                switch (status) {
                    case 'PENDING':
                        statusClass = 'bg-amber-500/20 text-amber-400';
                        statusText = '{% trans "Pending" %}';
                        break;
                    case 'APPROVED':
                        statusClass = 'bg-green-500/20 text-green-400';
                        statusText = '{% trans "Approved" %}';
                        break;
                    case 'REJECTED':
                        statusClass = 'bg-red-500/20 text-red-400';
                        statusText = '{% trans "Rejected" %}';
                        break;
                    case 'PENDING_CANCEL':
                        statusClass = 'bg-orange-500/20 text-orange-400';
                        statusText = '{% trans "Pending Cancellation" %}';
                        break;
                    case 'CANCELLED':
                        statusClass = 'bg-red-500/20 text-red-400';
                        statusText = '{% trans "Cancelled" %}';
                        break;
                }
                
                statusElement.className = `inline-flex px-2 py-1 text-xs rounded-full ${statusClass}`;
                statusElement.textContent = statusText;
                
                // Show admin notes if available
                const adminNotesContainer = document.getElementById('modalAdminNotesContainer');
                const adminNotes = this.getAttribute('data-admin-notes');
                
                if (adminNotes) {
                    document.getElementById('modalAdminNotes').textContent = adminNotes;
                    adminNotesContainer.classList.remove('hidden');
                } else {
                    adminNotesContainer.classList.add('hidden');
                }
                
                // Show action form for pending requests
                const actionForm = document.getElementById('actionForm');
                const confirmApproveBtn = document.getElementById('confirmApproveBtn');
                const confirmRejectBtn = document.getElementById('confirmRejectBtn');
                
                if (status === 'PENDING') {
                    actionForm.classList.remove('hidden');
                    
                    // Add request ID and type as data attributes to the action buttons
                    confirmApproveBtn.setAttribute('data-request-id', requestId);
                    confirmApproveBtn.setAttribute('data-request-type', requestType);
                    confirmRejectBtn.setAttribute('data-request-id', requestId);
                    confirmRejectBtn.setAttribute('data-request-type', requestType);
                } else {
                    actionForm.classList.add('hidden');
                }
                
                // Show modal
                modal.classList.remove('hidden');
            });
        });
        
        // Close modal when clicking the close button
        if (closeModalBtn) {
            closeModalBtn.addEventListener('click', function() {
                modal.classList.add('hidden');
            });
        }
        
        // Close modal when clicking outside
        window.addEventListener('click', function(event) {
            if (event.target === modal) {
                modal.classList.add('hidden');
            }
        });
        
        // Setup approval buttons
        const approveButtons = document.querySelectorAll('.approve-btn');
        approveButtons.forEach(button => {
            button.addEventListener('click', function() {
                const requestId = this.getAttribute('data-request-id');
                const requestType = this.getAttribute('data-request-type');
                if (confirm('{% trans "Are you sure you want to approve this request?" %}')) {
                    approveRequest(requestId, requestType);
                }
            });
        });
        
        // Setup rejection buttons
        const rejectButtons = document.querySelectorAll('.reject-btn');
        rejectButtons.forEach(button => {
            button.addEventListener('click', function() {
                const requestId = this.getAttribute('data-request-id');
                const requestType = this.getAttribute('data-request-type');
                const reason = prompt('{% trans "Please provide a reason for rejection (optional):" %}');
                if (reason !== null) {  // Only if user didn't click Cancel
                    rejectRequest(requestId, requestType, reason);
                }
            });
        });
        
        // Setup confirm approve button in modal
        const confirmApproveBtn = document.getElementById('confirmApproveBtn');
        if (confirmApproveBtn) {
            confirmApproveBtn.addEventListener('click', function() {
                const requestId = this.getAttribute('data-request-id');
                const requestType = this.getAttribute('data-request-type');
                const adminNotes = document.getElementById('adminNotesInput').value.trim();
                approveRequest(requestId, requestType, adminNotes);
            });
        }
        
        // Setup confirm reject button in modal
        const confirmRejectBtn = document.getElementById('confirmRejectBtn');
        if (confirmRejectBtn) {
            confirmRejectBtn.addEventListener('click', function() {
                const requestId = this.getAttribute('data-request-id');
                const requestType = this.getAttribute('data-request-type');
                const adminNotes = document.getElementById('adminNotesInput').value.trim();
                rejectRequest(requestId, requestType, adminNotes);
            });
        }
        
        // Function to approve a request
        function approveRequest(requestId, requestType, adminNotes = '') {
            const csrfToken = getCookie('csrftoken');
            
            // Choose the appropriate endpoint based on request type using Django template tags
            const endpoint = requestType === 'new_course'
                ? `{% url "website:update_new_course_request_status" 0 %}`.replace('0', requestId)
                : `{% url "website:update_course_request_status" 0 %}`.replace('0', requestId);
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    status: 'APPROVED',
                    admin_notes: adminNotes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Hide the modal if it's open
                    document.getElementById('requestDetailsModal').classList.add('hidden');
                    
                    // Refresh the page to show updated status
                    window.location.reload();
                } else {
                    alert(`{% trans "Error: " %}${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error approving request:', error);
                alert('{% trans "An error occurred while approving the request. Please try again." %}');
            });
        }
        
        // Function to reject a request
        function rejectRequest(requestId, requestType, adminNotes = '') {
            const csrfToken = getCookie('csrftoken');
            
            // Choose the appropriate endpoint based on request type using Django template tags
            const endpoint = requestType === 'new_course'
                ? `{% url "website:update_new_course_request_status" 0 %}`.replace('0', requestId)
                : `{% url "website:update_course_request_status" 0 %}`.replace('0', requestId);
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify({
                    status: 'REJECTED',
                    admin_notes: adminNotes
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Hide the modal if it's open
                    document.getElementById('requestDetailsModal').classList.add('hidden');
                    
                    // Refresh the page to show updated status
                    window.location.reload();
                } else {
                    alert(`{% trans "Error: " %}${data.message}`);
                }
            })
            .catch(error => {
                console.error('Error rejecting request:', error);
                alert('{% trans "An error occurred while rejecting the request. Please try again." %}');
            });
        }
        
        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Add this class to the currently active button
        document.querySelector('.filter-btn[data-filter="all"]').classList.add('active');
    });
</script>
{% endblock %} 