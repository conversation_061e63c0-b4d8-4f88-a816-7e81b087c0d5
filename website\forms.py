from django import forms
from django.contrib.auth.forms import UserCreationForm
from .models import CustomUser, UserType, Course, Trainer, Equipment, Category, RoomType, CourseContent
from django.utils.html import format_html
import uuid
from django.contrib.auth import get_user_model
import json
from django.utils.translation import gettext_lazy as _

class UserTypeSelectionForm(forms.Form):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['user_type'] = forms.ModelChoiceField(
            queryset=UserType.objects.filter(
                code__in=['EXTERNAL_INDIVIDUAL', 'EXTERNAL_CORPORATE_ADMIN'],
                is_active=True
            ),
            widget=forms.RadioSelect(attrs={
                'class': 'sr-only peer'
            }),
            empty_label=None,
            to_field_name='code'
        )

class AccountRegistrationForm(forms.Form):
    email = forms.EmailField(required=True)
    username = forms.CharField(required=True)
    password1 = forms.CharField(
        label='Password',
        widget=forms.PasswordInput(attrs={
            'class': 'password-input',
            'data-validation': 'true',
        }),
        help_text=format_html(
            '<div class="password-requirements text-xs space-y-1 mt-1">'
            '<ul class="grid grid-cols-2 gap-1">'
            '<li class="flex items-center" data-requirement="length">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>8+ characters</span></li>'
            '<li class="flex items-center" data-requirement="alphanumeric">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Letters & numbers</span></li>'
            '<li class="flex items-center" data-requirement="non-numeric">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Not only numbers</span></li>'
            '<li class="flex items-center" data-requirement="uncommon">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Not common</span></li>'
            '<li class="flex items-center col-span-2" data-requirement="personal">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Not similar to personal info</span></li>'
            '</ul>'
            '</div>'
        )
    )
    
    password2 = forms.CharField(
        label='Confirm Password',
        widget=forms.PasswordInput(attrs={
            'class': 'confirm-password-input',
            'data-validation': 'true',
        }),
        help_text=format_html(
            '<div class="passwords-match-message hidden text-xs mt-1"></div>'
        )
    )

class PersonalInfoForm(forms.Form):
    first_name = forms.CharField(required=True)
    last_name = forms.CharField(required=True)
    phone_number = forms.CharField(required=True)
    nationality = forms.CharField(required=True)
    national_id = forms.CharField(required=True)
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True
    )
    address = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3}),
        required=True
    )

class CorporateInfoForm(forms.Form):
    company_name = forms.CharField(required=True)
    commercial_registration_number = forms.CharField(required=True)
    tax_registration_number = forms.CharField(required=True)
    key_person_name = forms.CharField(required=True)
    key_person_phone = forms.CharField(required=True)
    key_person_email = forms.EmailField(required=True)

class BaseRegistrationForm(UserCreationForm):
    email = forms.EmailField(required=True)
    first_name = forms.CharField(required=True)
    last_name = forms.CharField(required=True)
    phone_number = forms.CharField(required=True)
    nationality = forms.CharField(required=True)
    national_id = forms.CharField(required=True)
    date_of_birth = forms.DateField(
        widget=forms.DateInput(attrs={'type': 'date'}),
        required=True
    )
    address = forms.CharField(widget=forms.Textarea, required=True)

    # Customize password field help texts
    password1 = forms.CharField(
        label='Password',
        widget=forms.PasswordInput(attrs={
            'class': 'password-input',
            'data-validation': 'true',
        }),
        help_text=format_html(
            '<div class="password-requirements text-xs space-y-1 mt-1">'
            '<ul class="grid grid-cols-2 gap-1">'
            '<li class="flex items-center" data-requirement="length">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>8+ characters</span></li>'
            '<li class="flex items-center" data-requirement="alphanumeric">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Letters & numbers</span></li>'
            '<li class="flex items-center" data-requirement="non-numeric">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Not only numbers</span></li>'
            '<li class="flex items-center" data-requirement="uncommon">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Not common</span></li>'
            '<li class="flex items-center col-span-2" data-requirement="personal">'
            '<svg class="w-4 h-4 mr-1 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">'
            '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"></path></svg>'
            '<span>Not similar to personal info</span></li>'
            '</ul>'
            '</div>'
        )
    )
    
    password2 = forms.CharField(
        label='Confirm Password',
        widget=forms.PasswordInput(attrs={
            'class': 'confirm-password-input',
            'data-validation': 'true',
        }),
        help_text=format_html(
            '<div class="passwords-match-message hidden text-xs mt-1"></div>'
        )
    )

    class Meta:
        model = CustomUser
        fields = ('email', 'username', 'password1', 'password2', 'first_name', 
                 'last_name', 'phone_number', 'nationality', 'national_id',
                 'date_of_birth', 'address')

class IndividualRegistrationForm(BaseRegistrationForm):
    class Meta(BaseRegistrationForm.Meta):
        fields = BaseRegistrationForm.Meta.fields

class CorporateRegistrationForm(BaseRegistrationForm):
    company_name = forms.CharField(required=True)
    commercial_registration_number = forms.CharField(required=True)
    tax_registration_number = forms.CharField(required=True)
    key_person_name = forms.CharField(required=True)
    key_person_phone = forms.CharField(required=True)
    key_person_email = forms.EmailField(required=True)

    class Meta(BaseRegistrationForm.Meta):
        fields = BaseRegistrationForm.Meta.fields + (
            'company_name', 'commercial_registration_number', 'tax_registration_number',
            'key_person_name', 'key_person_phone', 'key_person_email'
        )

class CourseForm(forms.ModelForm):
    # Add a field for equipment categories with quantities
    equipment_categories = forms.CharField(
        required=False,
        widget=forms.HiddenInput(),
        label=_('Required Equipment Categories')
    )
    
    # Add a field for session room types
    session_room_types = forms.CharField(
        required=False,
        widget=forms.HiddenInput(),
        label=_('Session Room Types')
    )
    
    # Hide icon_svg field but keep it in the form
    icon_svg = forms.CharField(
        required=False,
        widget=forms.HiddenInput(),
        label=_('Icon SVG')
    )
    
    class Meta:
        model = Course
        fields = [
            'name_en', 
            'name_ar',
            'description_en',
            'description_ar',
            'category',
            'location',
            'session_type',
            'num_of_sessions',
            'capacity',
            'prerequisite',
            'equipment_categories',
            'session_room_types',
            'icon_svg',
            'image',
            'order',
        ]
        widgets = {
            'description_en': forms.Textarea(attrs={
                'rows': 4,
                'class': 'mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            }),
            'description_ar': forms.Textarea(attrs={
                'rows': 4,
                'class': 'mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            }),
            'prerequisite': forms.Textarea(attrs={
                'rows': 3,
                'class': 'mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            }),
            'category': forms.Select(attrs={
                'class': 'mt-1 block w-full bg-[#1a2542] border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            }),
            'location': forms.Select(attrs={
                'class': 'mt-1 block w-full bg-[#1a2542] border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            }),
            'session_type': forms.Select(attrs={
                'class': 'mt-1 block w-full bg-[#1a2542] border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
            }),
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # Add styling to remaining fields (excluding selects and textareas)
        for field_name, field in self.fields.items():
            if not isinstance(field.widget, (forms.Textarea, forms.Select, forms.SelectMultiple, forms.CheckboxSelectMultiple, forms.HiddenInput)):
                field.widget.attrs.update({
                    'class': 'mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
                })
        
        # Removed the required_equipment field from the Meta fields list and handling
        # and replace it with equipment_categories
        if 'required_equipment' in self.fields:
            self.fields.pop('required_equipment')
            
        # Get all active categories for equipment selection
        try:
            self.active_categories = Category.objects.filter(is_active=True).order_by('name')
        except:
            self.active_categories = []
        
        # Get all active room types for session room type selection
        try:
            self.active_room_types = RoomType.objects.filter(is_active=True).order_by('name')
        except:
            self.active_room_types = []
        
        # Initialize equipment_categories from instance or set to empty
        if self.instance and self.instance.pk:
            if self.instance.equipment_categories:
                try:
                    # If it's already a string, ensure it's valid JSON
                    if isinstance(self.instance.equipment_categories, str):
                        # Try to parse and re-stringify to ensure valid JSON format
                        equipment_categories = json.loads(self.instance.equipment_categories)
                        self.fields['equipment_categories'].initial = json.dumps(equipment_categories)
                    else:
                        # It's a dictionary, convert it to proper JSON
                        self.fields['equipment_categories'].initial = json.dumps(self.instance.equipment_categories)
                    
                    print(f"Loading equipment categories: {self.fields['equipment_categories'].initial}")
                except json.JSONDecodeError as e:
                    print(f"Error parsing equipment categories: {e}")
                    self.fields['equipment_categories'].initial = '{}'
            else:
                # If it's None or empty, initialize as empty dict
                self.fields['equipment_categories'].initial = '{}'
                print("No equipment categories found, initializing as empty")
            
            # Initialize session_room_types from instance
            if self.instance.session_room_types:
                try:
                    if isinstance(self.instance.session_room_types, str):
                        # Try to parse and re-stringify to ensure valid JSON format
                        session_room_types = json.loads(self.instance.session_room_types)
                        self.fields['session_room_types'].initial = json.dumps(session_room_types)
                    else:
                        # It's a dictionary, convert it to proper JSON
                        self.fields['session_room_types'].initial = json.dumps(self.instance.session_room_types)
                except json.JSONDecodeError as e:
                    print(f"Error parsing session room types: {e}")
                    self.fields['session_room_types'].initial = '{}'
            else:
                self.fields['session_room_types'].initial = '{}'
        
    def clean(self):
        cleaned_data = super().clean()
        # Convert the JSON string from the hidden input to a dictionary
        categories_json = cleaned_data.get('equipment_categories', '{}')
        try:
            if categories_json:
                equipment_categories = json.loads(categories_json)
                cleaned_data['equipment_categories'] = equipment_categories
        except json.JSONDecodeError:
            self.add_error('equipment_categories', _('Invalid format for equipment categories'))
        
        return cleaned_data 

class CourseContentForm(forms.ModelForm):
    # Define supported file extensions
    SUPPORTED_EXTENSIONS = ['pdf', 'pptx', 'docx', 'jpg', 'jpeg', 'png', 'mp4']
    
    class Meta:
        model = CourseContent
        fields = [
            'title',
            'description',
            'content_type',
            'file',
        ]
        widgets = {
            'content_type': forms.Select(
                attrs={
                    'class': 'mt-1 block w-full bg-[#1a2542] border border-white/20 rounded-md py-2 px-3 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent cursor-pointer',
                    'style': 'background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3E%3Cpath stroke=\'%23ffffff\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3E%3C/svg%3E"); background-position: right 0.5rem center; background-repeat: no-repeat; background-size: 1.5em 1.5em; padding-right: 2.5rem;'
                }
            ),
        }
        
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        
        # Ensure content_type has a default value if not set
        if not self.instance.pk or not self.instance.content_type:
            self.fields['content_type'].initial = 'DOCUMENT'
            
        # Add styling to form fields
        for field_name, field in self.fields.items():
            if isinstance(field.widget, forms.ClearableFileInput):
                field.widget.attrs.update({
                    'class': 'block w-full text-white/70 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:bg-primary/70 file:text-white'
                })
            elif not isinstance(field.widget, (forms.Textarea, forms.Select, forms.SelectMultiple)):
                field.widget.attrs.update({
                    'class': 'mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
                })
            elif isinstance(field.widget, forms.Textarea):
                field.widget.attrs.update({
                    'class': 'mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent',
                    'rows': 3
                })
            elif isinstance(field.widget, (forms.Select, forms.SelectMultiple)) and field_name != 'content_type':
                field.widget.attrs.update({
                    'class': 'mt-1 block w-full bg-[#1a2542] border border-white/20 rounded-md py-2 px-3 text-white appearance-none focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent'
                }) 
    
    def clean_file(self):
        file = self.cleaned_data.get('file')
        if file:
            # Get the file extension
            file_ext = file.name.split('.')[-1].lower()
            if file_ext not in self.SUPPORTED_EXTENSIONS:
                raise forms.ValidationError(
                    _('Unsupported file format. Supported formats are: {}').format(
                        ', '.join([ext.upper() for ext in self.SUPPORTED_EXTENSIONS])
                    )
                )
        return file 