{% load static %}

<div class="mb-8 relative z-10">
    <div class="max-w-3xl mx-auto">
        <div class="relative">
            <!-- Timeline Bar -->
            <div class="absolute inset-0 flex items-center" aria-hidden="true">
                <div class="h-0.5 w-full bg-white/10">
                    <div class="h-full bg-blue-500 transition-all duration-500" 
                         style="width: {% if current_step == 1 %}0%{% elif current_step == 2 %}{% if show_corporate %}33%{% else %}50%{% endif %}{% elif current_step == 3 %}{% if show_corporate %}66%{% else %}100%{% endif %}{% else %}100%{% endif %};"></div>
                </div>
            </div>
            
            <!-- Timeline Steps -->
            <div class="relative flex {% if show_corporate %}justify-between{% else %}justify-around{% endif %} transition-all duration-500">
                <!-- Step 1: Registration Type -->
                <div class="flex flex-col items-center group">
                    <div class="relative flex h-8 w-8 items-center justify-center rounded-full 
                              {% if current_step == 1 %}bg-blue-500 border-2 border-blue-300 animate-pulse{% elif current_step > 1 %}bg-green-500{% else %}bg-white/10{% endif %} 
                              transition-all duration-300 transform hover:scale-110">
                        {% if current_step > 1 %}
                            <svg class="h-5 w-5 text-white transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        {% else %}
                            <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                            </svg>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-xs text-white/80 group-hover:text-white transition-colors duration-300">Registration Type</span>
                </div>

                <!-- Step 2: Account Info -->
                <div class="flex flex-col items-center group">
                    <div class="relative flex h-8 w-8 items-center justify-center rounded-full 
                              {% if current_step == 2 %}bg-blue-500 border-2 border-blue-300 animate-pulse{% elif current_step > 2 %}bg-green-500{% else %}bg-white/10{% endif %} 
                              transition-all duration-300 transform hover:scale-110">
                        {% if current_step > 2 %}
                            <svg class="h-5 w-5 text-white transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        {% else %}
                            <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9a2 2 0 012-2h14a2 2 0 012 2zM15 7v2m4 0v2H5V9a2 2 0 012-2h8zM3 12h18"/>
                            </svg>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-xs text-white/80 group-hover:text-white transition-colors duration-300">Account Info</span>
                </div>

                <!-- Step 3: Personal Info -->
                <div class="flex flex-col items-center group">
                    <div class="relative flex h-8 w-8 items-center justify-center rounded-full 
                              {% if current_step == 3 %}bg-blue-500 border-2 border-blue-300 animate-pulse{% elif current_step > 3 %}bg-green-500{% else %}bg-white/10{% endif %} 
                              transition-all duration-300 transform hover:scale-110">
                        {% if current_step > 3 %}
                            <svg class="h-5 w-5 text-white transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        {% else %}
                            <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                            </svg>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-xs text-white/80 group-hover:text-white transition-colors duration-300">Personal Info</span>
                </div>

                <!-- Step 4: Corporate Info (Only for Corporate Registration) -->
                {% if show_corporate %}
                <div class="flex flex-col items-center group">
                    <div class="relative flex h-8 w-8 items-center justify-center rounded-full 
                              {% if current_step == 4 %}bg-blue-500 border-2 border-blue-300 animate-pulse{% elif current_step > 4 %}bg-green-500{% else %}bg-white/10{% endif %} 
                              transition-all duration-300 transform hover:scale-110">
                        {% if current_step > 4 %}
                            <svg class="h-5 w-5 text-white transform transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        {% else %}
                            <svg class="h-4 w-4 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                            </svg>
                        {% endif %}
                    </div>
                    <span class="mt-2 text-xs text-white/80 group-hover:text-white transition-colors duration-300">Corporate Info</span>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div> 