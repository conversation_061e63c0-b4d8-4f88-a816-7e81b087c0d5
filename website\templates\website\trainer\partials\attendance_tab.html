{% load i18n %}

{% if users_attendance %}
    <div class="flex items-center justify-between bg-gray-700/30 border-t border-b border-gray-700 px-4 py-3 mb-4">
        <div class="flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <span class="font-semibold text-white">{{ users_attendance|length }}</span>
            <span class="ml-1 text-gray-300">{% trans "registered users" %}</span>
        </div>

        {% if session_started %}
        <div class="text-sm text-gray-400">
            <span class="inline-flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                {% trans "Attendance can be marked" %}
            </span>
        </div>
        {% endif %}
    </div>
    
    <form method="post" action="{% url 'website:trainer_manage_session' session_id=session.session_id %}">
        {% csrf_token %}
        <input type="hidden" name="attendance_submit" value="1">
        
        <h4 class="font-semibold text-lg text-white mb-4 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            {% trans "Attendees" %} ({{ users_attendance|length }})
        </h4>
        
        {% if not session_started %}
            <div class="mb-6 bg-blue-900/30 border border-blue-900 rounded-md p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                        </svg>
                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-blue-400">{% trans "Note" %}</h3>
                        <div class="mt-2 text-sm text-blue-300">
                            <p>{% trans "Attendance can only be marked once the session has started." %}</p>
                        </div>
                    </div>
                </div>
            </div>
        {% endif %}
        
        <div class="overflow-x-auto rounded-lg border border-gray-700">
            <table class="min-w-full divide-y divide-gray-700">
                <thead class="bg-gray-700/50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                            {% trans "Name" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                            {% trans "Email" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                            {% trans "First Half" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                            {% trans "Second Half" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700 bg-gray-800">
                    {% for user_data in users_attendance %}
                        <tr class="hover:bg-gray-700 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                        </svg>
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-white">
                                            {{ user_data.user.get_full_name }}
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                <div class="flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                    </svg>
                                    {{ user_data.user.email }}
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                <label class="inline-flex items-center justify-center space-x-3 cursor-pointer {% if not session_started %}opacity-50 cursor-not-allowed{% endif %}">
                                    <input type="checkbox" 
                                           name="first_half_{{ user_data.user.id }}" 
                                           id="first_half_{{ user_data.user.id }}"
                                           class="h-5 w-5 rounded border-gray-500 bg-gray-700 text-blue-600 focus:ring-blue-600 focus:ring-offset-gray-800" 
                                           {% if user_data.first_half %}checked{% endif %}
                                           {% if not session_started %}disabled{% endif %}>
                                    <span class="text-white font-medium">{% trans "Present" %}</span>
                                </label>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                <label class="inline-flex items-center justify-center space-x-3 cursor-pointer {% if not session_started %}opacity-50 cursor-not-allowed{% endif %}">
                                    <input type="checkbox" 
                                           name="second_half_{{ user_data.user.id }}" 
                                           id="second_half_{{ user_data.user.id }}"
                                           class="h-5 w-5 rounded border-gray-500 bg-gray-700 text-blue-600 focus:ring-blue-600 focus:ring-offset-gray-800" 
                                           {% if user_data.second_half %}checked{% endif %}
                                           {% if not session_started %}disabled{% endif %}>
                                    <span class="text-white font-medium">{% trans "Present" %}</span>
                                </label>
                            </td>
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        
        {% if session_started %}
            <div class="mt-6 flex justify-end">
                <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    {% trans "Save Attendance" %}
                </button>
            </div>
        {% endif %}
    </form>
{% else %}
    <div class="text-center py-10">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
        </svg>
        <h3 class="text-xl font-medium text-white mb-2">{% trans "No attendees found" %}</h3>
        <p class="text-gray-400 max-w-md mx-auto">{% trans "There are no registered attendees for this session yet." %}</p>
    </div>
{% endif %} 