import sqlite3
import os

# Path to databases
source_db_path = os.path.join('..', 'db.sqlite3')
target_db_path = 'db.sqlite3'

# Function to get table names and structure
def get_db_info(db_path):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Get all table names
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        print(f"Tables in {db_path}:")
        for table in tables:
            print(f"  - {table}")
            # Get table schema
            cursor.execute(f"PRAGMA table_info({table})")
            columns = cursor.fetchall()
            print(f"    Columns: {len(columns)}")
            
            # Count rows
            cursor.execute(f"SELECT COUNT(*) FROM {table}")
            row_count = cursor.fetchone()[0]
            print(f"    Rows: {row_count}")
            
        conn.close()
        return tables
    except Exception as e:
        print(f"Error accessing {db_path}: {e}")
        return []

# Examine both databases
print("\nSOURCE DATABASE:")
source_tables = get_db_info(source_db_path)

print("\nTARGET DATABASE:")
target_tables = get_db_info(target_db_path) 