import logging
import uuid
from django.core.mail import send_mail
from django.conf import settings
from django.utils.translation import gettext as _
from django.utils import timezone

logger = logging.getLogger(__name__)

def send_otp_notification_email(recipient_email, subject, message, cc_emails=None):
    """
    Send OTP email notification to internal GB employees using the same approach as other email utils.
    
    Args:
        recipient_email: Primary recipient's email address
        subject: Email subject
        message: HTML message content
        cc_emails: List of CC email addresses (optional)
    """
    if not cc_emails:
        cc_emails = []
    
    # Check if email settings are properly configured
    if not all([settings.EMAIL_HOST, settings.EMAIL_PORT, settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD]):
        logger.error(f"Email settings are not properly configured. Check EMAIL_HOST, EMAIL_PORT, EMAIL_HOST_USER, and EMAIL_HOST_PASSWORD settings.")
        
        # Try to log to database anyway
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='FAILED',
                error_message='Email settings not configured properly'
            )
        except Exception as db_err:
            logger.debug(f"Could not log to database: {str(db_err)}")
            
        return False

    # Generate a transaction ID for tracking this email
    transaction_id = uuid.uuid4().hex[:8]
    logger.info(f"[OTPEmail:{transaction_id}] Preparing to send OTP email to {recipient_email} with subject '{subject}'")

    try:
        # IMPORTANT: Always use EMAIL_HOST_USER as the sender email to avoid auth issues
        from_email = settings.EMAIL_HOST_USER
        
        # Log the actual settings being used
        logger.info(f"[OTPEmail:{transaction_id}] Using EMAIL_HOST: {settings.EMAIL_HOST}, PORT: {settings.EMAIL_PORT}")
        logger.info(f"[OTPEmail:{transaction_id}] Sending as: {from_email} to: {recipient_email}")
        
        # Handle CC recipients
        all_recipients = [recipient_email]
        cc_list = []
        
        if cc_emails and len(cc_emails) > 0:
            cc_list = cc_emails
            all_recipients.extend(cc_emails)
            logger.info(f"[OTPEmail:{transaction_id}] Adding CC recipients: {', '.join(cc_emails)}")
        
        # Use Django's send_mail function with all recipients in recipient_list
        result = send_mail(
            subject=subject,
            message="",  # Empty plain text version
            html_message=message,  # HTML version
            from_email=from_email,
            recipient_list=all_recipients,
            fail_silently=False
        )
        
        # Log result and save to database
        if result:
            logger.info(f"[OTPEmail:{transaction_id}] SUCCESS: OTP email sent to {recipient_email}, CC: {', '.join(cc_list) if cc_list else 'none'}")
            
            # Log to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='SUCCESS',
                    transaction_id=transaction_id,
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log successful email to database: {str(db_err)}")
        else:
            logger.warning(f"[OTPEmail:{transaction_id}] FAILED: OTP email not sent to {recipient_email}")
            
            # Log failure to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='FAILED',
                    transaction_id=transaction_id,
                    error_message='Email send returned False',
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log failed email to database: {str(db_err)}")
            
        return result
    except Exception as e:
        logger.error(f"[OTPEmail:{transaction_id}] ERROR: Failed to send OTP email to {recipient_email}: {str(e)}", exc_info=True)
        
        # Log error to database
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='ERROR',
                transaction_id=transaction_id,
                error_message=str(e),
                sent_at=timezone.now()
            )
        except Exception as db_err:
            logger.debug(f"Could not log email error to database: {str(db_err)}")
            
        return False

def send_otp_verification_email(employee_id, email, otp_code, employee_name=None):
    """Send OTP verification email to internal GB employee"""
    logger.info(f"Preparing OTP verification email for employee {employee_id} at {email}")
    
    subject = _('GB Academy - Login Verification Code')
    
    # Use employee name if available, otherwise use employee ID
    display_name = employee_name if employee_name else f"Employee {employee_id}"
    
    message = f"""
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c5aa0; margin: 0; font-size: 24px;">GB Academy</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Learning Management System</p>
            </div>
            
            <h2 style="color: #333; margin-bottom: 20px;">Login Verification Code</h2>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Dear {display_name},
            </p>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Your verification code for GB Academy login is:
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
                <div style="display: inline-block; background-color: #f0f8ff; border: 2px solid #2c5aa0; border-radius: 8px; padding: 20px 30px;">
                    <span style="font-size: 32px; font-weight: bold; color: #2c5aa0; letter-spacing: 8px;">{otp_code}</span>
                </div>
            </div>
            
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #856404; font-size: 14px;">
                    <strong>⚠️ Important:</strong><br>
                    • This code expires in <strong>1 minute</strong><br>
                    • You have <strong>3 attempts</strong> to enter the correct code<br>
                    • If you didn't request this code, please ignore this email
                </p>
            </div>
            
            <p style="color: #555; font-size: 14px; line-height: 1.6; margin-top: 30px;">
                If you're having trouble logging in, please contact IT support.
            </p>
            
            <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #999; font-size: 12px; margin: 0;">
                    This is an automated email from GB Academy Learning Management System.<br>
                    Please do not reply to this email.
                </p>
            </div>
        </div>
    </div>
    """
    
    return send_otp_notification_email(email, subject, message)

def send_otp_max_attempts_notification(employee_id, email, employee_name=None):
    """Send notification when user reaches maximum OTP attempts"""
    logger.info(f"Preparing max attempts notification email for employee {employee_id} at {email}")
    
    subject = _('GB Academy - Login Verification Locked')
    
    # Use employee name if available, otherwise use employee ID
    display_name = employee_name if employee_name else f"Employee {employee_id}"
    
    message = f"""
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c5aa0; margin: 0; font-size: 24px;">GB Academy</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Learning Management System</p>
            </div>
            
            <h2 style="color: #dc3545; margin-bottom: 20px;">Login Verification Locked</h2>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Dear {display_name},
            </p>
            
            <div style="background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #721c24; font-size: 14px;">
                    <strong>🔒 Access Temporarily Locked</strong><br>
                    You have exceeded the maximum number of verification attempts (3) for your login.
                </p>
            </div>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                For security reasons, you will need to request a new verification code to continue.
            </p>
            
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Please return to the login page and click "Resend Code" to receive a new verification code.
            </p>
            
            <div style="background-color: #d1ecf1; border: 1px solid #bee5eb; border-radius: 6px; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; color: #0c5460; font-size: 14px;">
                    <strong>💡 Tips for next time:</strong><br>
                    • Double-check the verification code before submitting<br>
                    • Make sure to enter the code within 1 minute<br>
                    • Contact IT support if you continue having issues
                </p>
            </div>
            
            <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #999; font-size: 12px; margin: 0;">
                    This is an automated email from GB Academy Learning Management System.<br>
                    Please do not reply to this email.
                </p>
            </div>
        </div>
    </div>
    """
    
    return send_otp_notification_email(email, subject, message) 