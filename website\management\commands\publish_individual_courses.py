from django.core.management.base import BaseCommand
from django.utils import timezone
from website.models import Course, CourseInstance


class Command(BaseCommand):
    help = 'Publish course instances for individual users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be changed without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        self.stdout.write(self.style.SUCCESS('=== PUBLISHING INDIVIDUAL COURSE INSTANCES ==='))
        
        # Find course instances that are INDIVIDUAL type but not published
        unpublished_individual_instances = CourseInstance.objects.filter(
            course_type='INDIVIDUAL',
            published=False,
            is_active=True
        ).select_related('course')
        
        self.stdout.write(f'\nFound {unpublished_individual_instances.count()} unpublished INDIVIDUAL course instances')
        
        if unpublished_individual_instances.count() == 0:
            self.stdout.write(self.style.SUCCESS('All INDIVIDUAL course instances are already published!'))
            return
        
        for instance in unpublished_individual_instances:
            self.stdout.write(f'\nCourse Instance ID: {instance.instance_id}')
            self.stdout.write(f'  Course: {instance.course.name_en} (ID: {instance.course.course_id})')
            self.stdout.write(f'  Course Type: {instance.course_type}')
            self.stdout.write(f'  Start Date: {instance.start_date}')
            self.stdout.write(f'  End Date: {instance.end_date}')
            self.stdout.write(f'  Capacity: {instance.capacity}')
            self.stdout.write(f'  Currently Published: {instance.published}')
            self.stdout.write(f'  Is Active: {instance.is_active}')
            
            if not dry_run:
                instance.published = True
                instance.save()
                self.stdout.write(self.style.SUCCESS(f'  ✅ Published instance {instance.instance_id}'))
            else:
                self.stdout.write(self.style.WARNING(f'  🔄 Would publish instance {instance.instance_id}'))
        
        if not dry_run:
            self.stdout.write(self.style.SUCCESS(f'\n✅ Successfully published {unpublished_individual_instances.count()} course instances'))
        else:
            self.stdout.write(self.style.WARNING(f'\n🔄 Would publish {unpublished_individual_instances.count()} course instances'))
            self.stdout.write('Run without --dry-run to make the changes')
        
        # Show summary of what will be visible to individual users after changes
        self.stdout.write(self.style.SUCCESS('\n=== SUMMARY AFTER CHANGES ==='))
        
        if not dry_run:
            # Get courses that are now visible to individual users
            active_instances = CourseInstance.objects.filter(
                is_active=True,
                published=True,
                course_type='INDIVIDUAL'
            ).select_related('course')
            
            course_ids = active_instances.values_list('course_id', flat=True).distinct()
            visible_courses = Course.objects.filter(course_id__in=course_ids, is_active=True)
            
            self.stdout.write(f'Courses now visible to individual users: {visible_courses.count()}')
            for course in visible_courses:
                instances_count = active_instances.filter(course=course).count()
                self.stdout.write(f'  - {course.name_en} (ID: {course.course_id}) - {instances_count} instances')
        else:
            self.stdout.write('Run without --dry-run to see the final results')
