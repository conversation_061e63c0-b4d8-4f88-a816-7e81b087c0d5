version: "3.9"

networks:
  frontend:
  backend:

volumes:
  postgres_data:
  static:
  media:

services:
  app:
    restart: always
    build: .
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - static:/static
      - media:/media
    container_name: Project-App
    env_file: .env
    depends_on:
      - db
    ports:
      - "8000:8000"
    networks:
      - backend
      - frontend
  db:
    restart: always
    image: postgres:14
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - postgres_data:/var/lib/postgresql/data/
    container_name: Project-DB
    env_file: .env
    ports:
      - "5432:5432"
    networks:
      - backend
  nginx:
    restart: always
    build: ./nginx
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - static:/static
      - media:/media
    container_name: Project-Nginx
    env_file: .env
    ports:
      - "443:443"
    depends_on:
      - app
    networks:
      - frontend
