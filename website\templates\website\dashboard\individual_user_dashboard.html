{% extends 'website/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "My Dashboard" %} | GB Academy{% endblock %}

{% block content %}
<div class="min-h-screen bg-[#172b56]">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto">
        <div class="flex justify-between items-center mb-4">
            <div>
                <h1 class="text-3xl font-bold text-white">{% trans "My Dashboard" %}</h1>
                <p class="text-gray-300">{% trans "Welcome back" %} {{ request.user.get_full_name }}!</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'website:user_calendar' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {% trans "My Calendar" %}
                </a>
                <a href="{% url 'website:user_reservations' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary hover:bg-secondary/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                    {% trans "My Reservations" %}
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20 mb-8">
            <h2 class="text-xl font-semibold text-white mb-4">{% trans "Quick Actions" %}</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                <a href="{% url 'website:user_reservations' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-green-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "My Reservations" %}</span>
                </a>
                
                <a href="{% url 'website:user_calendar' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-blue-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Calendar" %}</span>
                </a>
                
                <a href="{% url 'website:courses' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-purple-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Browse Courses" %}</span>
                </a>
                
                <a href="{% url 'website:profile' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-teal-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "My Profile" %}</span>
                </a>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
            <!-- Active Sessions -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Active Sessions" %}</p>
                        <p class="text-2xl font-bold text-white">{{ active_sessions.count|default:"0" }}</p>
                    </div>
                    <div class="p-3 bg-green-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- Upcoming Events -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Upcoming Events" %}</p>
                        <p class="text-2xl font-bold text-white" id="upcoming-events-count">0</p>
                    </div>
                    <div class="p-3 bg-blue-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                </div>
            </div>
            
            <!-- To-Do Items -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Pending Tasks" %}</p>
                        <p class="text-2xl font-bold text-white">{{ pending_questionnaires.count|add:pending_surveys.count }}</p>
                    </div>
                    <div class="p-3 bg-yellow-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Active Sessions -->
        <div class="grid grid-cols-1 gap-4 mb-8">
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-green-800/40 to-blue-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "Active Sessions" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-green-500/30 text-green-300 border border-green-500/30">
                            {{ active_sessions.count|default:"0" }} {% trans "Active" %}
                        </span>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Course" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Time" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Location" %}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white/5 divide-y divide-white/10">
                            {% if active_sessions %}
                                {% for session in active_sessions %}
                                    <tr class="hover:bg-white/10 cursor-pointer transition-colors duration-150">
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.course.name_en }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.start_date|date:"M d, Y" }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.start_date|date:"H:i" }} - {{ session.end_date|date:"H:i" }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {% if session.location == "PHYSICAL" and session.room %}
                                                {{ session.room.name }}
                                            {% else %}
                                                {% trans "Online" %}
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-white/70">
                                        {% trans "No active sessions at the moment" %}
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <!-- Upcoming Events/Sessions -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-blue-800/40 to-indigo-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "Upcoming Events" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-blue-500/30 text-blue-300 border border-blue-500/30">
                            {% trans "Next 5" %}
                        </span>
                    </div>
                </div>
                <div id="dashboardUpcomingEvents" class="p-4 space-y-2 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                    <div class="flex items-center justify-center h-24">
                        <div class="text-white/70 flex flex-col items-center">
                            <div class="w-8 h-8 mb-2 relative">
                                <div class="absolute inset-0 rounded-full border-t-2 border-b-2 border-blue-500/60 animate-spin"></div>
                                <div class="absolute inset-1 rounded-full border-r-2 border-l-2 border-blue-300/40 animate-spin animation-delay-150"></div>
                                <div class="absolute inset-2 rounded-full border-t-2 border-blue-400/50 animate-pulse"></div>
                            </div>
                            <p class="text-sm">{% trans "Loading events..." %}</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- To-Do List Section -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-yellow-800/40 to-amber-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "To-Do List" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-yellow-500/30 text-yellow-300 border border-yellow-500/30">
                            {{ pending_questionnaires.count|add:pending_surveys.count }} {% trans "Pending" %}
                        </span>
                    </div>
                </div>
                <div class="p-4 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                    <div class="space-y-3">
                        {% if pending_questionnaires.count > 0 or pending_surveys.count > 0 %}
                            {% if pending_questionnaires %}
                                {% for response in pending_questionnaires %}
                                    <div class="bg-white/5 hover:bg-white/10 transition-colors duration-150 rounded-lg p-4 border border-white/10">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <div class="flex items-center space-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                                    </svg>
                                                    <h4 class="text-white font-medium">{{ response.questionnaire.title }}</h4>
                                                </div>
                                                <p class="text-white/60 text-sm mt-1">{{ response.course.name_en }}</p>
                                            </div>
                                            <a href="{% url 'website:take_questionnaire' questionnaire_id=response.questionnaire.questionnaire_id %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors">
                                                {% trans "Complete" %}
                                            </a>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% endif %}
                            
                            {% if pending_surveys %}
                                {% for response in pending_surveys %}
                                    <div class="bg-white/5 hover:bg-white/10 transition-colors duration-150 rounded-lg p-4 border border-white/10">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <div class="flex items-center space-x-2">
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                                    </svg>
                                                    <h4 class="text-white font-medium">{{ response.survey.title }}</h4>
                                                </div>
                                                <p class="text-white/60 text-sm mt-1">{{ response.reservation.course_instance.course.name_en }}</p>
                                            </div>
                                            <a href="{% url 'website:take_survey' survey_id=response.survey.survey_id reservation_id=response.reservation.reservation_id %}" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm transition-colors">
                                                {% trans "Complete" %}
                                            </a>
                                        </div>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% else %}
                            <div class="text-center py-8">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-white/20 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                <p class="text-white/60 text-lg">{% trans "All caught up! No pending tasks." %}</p>
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize dashboard
        fetchUpcomingEvents();
        
        // Function to fetch upcoming events
        function fetchUpcomingEvents() {
            // Get current date and end of week
            const today = new Date();
            const endOfWeek = new Date(today);
            endOfWeek.setDate(today.getDate() + 14); // Get events for next two weeks
            
            // Format dates for API
            const formatDate = (date) => {
                return date.toISOString().split('T')[0]; // YYYY-MM-DD format
            };
            
            // Show loading state
            const container = document.getElementById('dashboardUpcomingEvents');
            container.innerHTML = `
                <div class="flex items-center justify-center h-24">
                    <div class="text-white/70 flex flex-col items-center">
                        <div class="w-8 h-8 mb-2 relative">
                            <div class="absolute inset-0 rounded-full border-t-2 border-b-2 border-blue-500/60 animate-spin"></div>
                            <div class="absolute inset-1 rounded-full border-r-2 border-l-2 border-blue-300/40 animate-spin animation-delay-150"></div>
                            <div class="absolute inset-2 rounded-full border-t-2 border-blue-400/50 animate-pulse"></div>
                        </div>
                        <p class="text-sm">{% trans "Loading events..." %}</p>
                    </div>
                </div>
            `;
            
            // Build API URL
            fetch('{% url "website:user_calendar_events_api" %}')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    console.log('API response:', data); // Debug: Log the data structure
                    // Check if data has an events property, otherwise use data directly
                    const eventsData = data.events || data;
                    showUpcomingEvents(eventsData);
                })
                .catch(error => {
                    console.error('Error fetching upcoming events:', error);
                    document.getElementById('dashboardUpcomingEvents').innerHTML = `
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto mb-2 text-red-400/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-white/70">{% trans "Error loading events. Please try again later." %}</p>
                        </div>
                    `;
                });
        }
        
        // Function to display upcoming events
        function showUpcomingEvents(events) {
            const container = document.getElementById('dashboardUpcomingEvents');
            
            // Ensure events is an array
            if (!events || !Array.isArray(events)) {
                console.error('Events is not an array:', events);
                container.innerHTML = `
                    <div class="text-center py-8">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-blue-400/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p class="text-white/70">{% trans "No upcoming events" %}</p>
                    </div>
                `;
                // Update the counter to show 0
                document.getElementById('upcoming-events-count').textContent = "0";
                return;
            }
            
            try {
                // Sort events by date
                const sortedEvents = [...events].sort((a, b) => {
                    const dateA = new Date(a.start);
                    const dateB = new Date(b.start);
                    return dateA - dateB;
                });
                
                // Filter to only future events
                const today = new Date();
                const allUpcomingEvents = sortedEvents.filter(event => {
                    const eventDate = new Date(event.start);
                    return eventDate >= today;
                });
                
                // Update the upcoming events count
                document.getElementById('upcoming-events-count').textContent = allUpcomingEvents.length;
                
                // Limit to 5 events for display
                const upcomingEvents = allUpcomingEvents.slice(0, 5);
                
                // Clear container
                container.innerHTML = '';
                
                // Display upcoming events
                if (upcomingEvents.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-blue-400/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <p class="text-white/70">{% trans "No upcoming events" %}</p>
                        </div>
                    `;
                    return;
                }
                
                // Create elements for each event
                upcomingEvents.forEach(event => {
                    if (!event || !event.start) {
                        console.warn('Invalid event object:', event);
                        return; // Skip this event and continue with the next one
                    }
                    
                    const eventDate = new Date(event.start);
                    const day = eventDate.getDate();
                    const month = eventDate.toLocaleString('en-US', { month: 'short' });
                    const time = eventDate.toLocaleString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                    
                    // Create event element
                    const eventElement = document.createElement('div');
                    eventElement.className = 'flex items-start space-x-4 p-3 rounded-lg hover:bg-white/10 transition-all duration-200 cursor-pointer border border-transparent hover:border-white/20';
                    
                    // Add click action
                    eventElement.addEventListener('click', function() {
                        // Could add navigation to event details in the future
                        eventElement.classList.add('bg-white/5');
                        setTimeout(() => eventElement.classList.remove('bg-white/5'), 200);
                    });
                    
                    // Date box
                    const dateBox = document.createElement('div');
                    dateBox.className = 'flex-shrink-0 w-12 text-center bg-white/5 rounded-lg py-1 border border-white/10';
                    dateBox.innerHTML = `
                        <div class="text-sm font-semibold text-blue-400">${day}</div>
                        <div class="text-xs text-white/70">${month}</div>
                    `;
                    
                    // Event details
                    const eventDetails = document.createElement('div');
                    eventDetails.className = 'flex-1 min-w-0';
                    
                    // Get icon based on event type
                    let typeIcon, typeClass, typeBadgeClass;
                    
                    if (event.source === 'session') {
                        typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /></svg>';
                        typeClass = 'text-green-400';
                        typeBadgeClass = 'bg-green-400/20 text-green-400 border-green-400/20';
                    } else if (event.source === 'holiday') {
                        typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>';
                        typeClass = 'text-red-400';
                        typeBadgeClass = 'bg-red-400/20 text-red-400 border-red-400/20';
                    } else {
                        typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>';
                        typeClass = 'text-blue-400';
                        typeBadgeClass = 'bg-blue-400/20 text-blue-400 border-blue-400/20';
                    }
                    
                    // Add holiday emoji for holidays
                    const holidayPrefix = event.source === 'holiday' ? '🎉 ' : '';
                    
                    // For holidays, show "All day" instead of time
                    const timeDisplay = event.source === 'holiday' ? 'All day' : time;
                    
                    // Location info if available
                    const locationDisplay = event.location ? `· ${event.location}` : '';
                    
                    // Show title and info with improved styling
                    eventDetails.innerHTML = `
                        <p class="text-sm font-medium text-white truncate">${holidayPrefix}${event.title || 'Untitled Event'}</p>
                        <div class="flex items-center mt-1">
                            <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${typeBadgeClass}">
                                ${typeIcon}
                                ${event.source || 'Event'}
                            </span>
                            <span class="mx-1.5 text-white/40">•</span>
                            <span class="text-xs text-white/70">${timeDisplay} ${locationDisplay}</span>
                        </div>
                    `;
                    
                    // Add elements to container
                    eventElement.appendChild(dateBox);
                    eventElement.appendChild(eventDetails);
                    container.appendChild(eventElement);
                });
            } catch (error) {
                console.error('Error processing events:', error);
                container.innerHTML = `
                    <div class="text-center py-8">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-red-400/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        <p class="text-white/70">{% trans "Error processing events. Please try again later." %}</p>
                    </div>
                `;
            }
        }
        
        // Fetch events when page loads
        fetchUpcomingEvents();
        
        // Refresh events every 5 minutes
        setInterval(fetchUpcomingEvents, 300000);
    });
</script>
{% endblock %} 