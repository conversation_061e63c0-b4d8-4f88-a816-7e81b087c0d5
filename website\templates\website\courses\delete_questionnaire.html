{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Delete Questionnaire" %} - {{ questionnaire.title }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Delete Confirmation Box -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <div class="p-6 border-b border-white/10">
                <h2 class="text-xl font-semibold text-white">{% trans "Delete Questionnaire" %}</h2>
                <p class="text-white/70 text-sm mt-1">
                    {% trans "Are you sure you want to delete this questionnaire?" %}
                </p>
            </div>

            <div class="p-6 space-y-6">
                <div class="bg-red-500/10 border border-red-500/30 p-4 rounded-md text-red-400">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <h3 class="text-sm font-medium">{% trans "Warning" %}</h3>
                            <p class="text-sm mt-1">
                                {% trans "This action cannot be undone. This will permanently delete the questionnaire titled" %} <strong>"{{ questionnaire.title }}"</strong>.
                            </p>
                        </div>
                    </div>
                </div>

                <div>
                    <h3 class="text-white font-medium">{% trans "Questionnaire Information" %}</h3>
                    <div class="mt-2 border border-white/10 rounded-md overflow-hidden">
                        <div class="bg-white/5 px-4 py-3 border-b border-white/10">
                            <p class="text-white"><span class="text-white/70">{% trans "Title:" %}</span> {{ questionnaire.title }}</p>
                        </div>
                        <div class="bg-white/5 px-4 py-3 border-b border-white/10">
                            <p class="text-white"><span class="text-white/70">{% trans "Session Number:" %}</span> {{ questionnaire.session_number }}</p>
                        </div>
                        <div class="bg-white/5 px-4 py-3 border-b border-white/10">
                            <p class="text-white"><span class="text-white/70">{% trans "Questions:" %}</span> {{ questionnaire.questions|length }}</p>
                        </div>
                        <div class="bg-white/5 px-4 py-3">
                            <p class="text-white"><span class="text-white/70">{% trans "Created:" %}</span> {{ questionnaire.created_at|date:"M d, Y" }}</p>
                        </div>
                    </div>
                </div>

                <form method="post" class="pt-4 border-t border-white/10">
                    {% csrf_token %}
                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'website:course_content_list' course.course_id %}" class="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-md">
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md">
                            {% trans "Delete Questionnaire" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 