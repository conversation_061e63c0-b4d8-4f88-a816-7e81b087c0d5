/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Smooth Hover Transitions */
.hover-transition {
    transition: all 0.3s ease-in-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2a51a3;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1a3b82;
}

/* Slideshow Styles */
.slideshow-container {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transform: scale(1.1);
    transition: all 1s cubic-bezier(0.4, 0, 0.2, 1);
    background-color: rgba(42, 81, 163, 0.2);
}

.slide.active {
    opacity: 1;
    transform: scale(1);
}

.slide.previous {
    opacity: 0;
    transform: scale(0.95);
}

.overlay {
    background: linear-gradient(
        45deg,
        rgba(42, 81, 163, 0.92),
        rgba(0, 0, 0, 0.95)
    );
    transition: opacity 0.5s ease;
}

/* Add a base overlay for additional dimming */
.slide::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.3);
    z-index: 1;
}

/* Make sure the image is behind the overlay */
.slide img {
    transform: scale(1.1);
    transition: transform 8s ease-out;
    z-index: 0;
    opacity: 0;
}

.slide img.loaded {
    opacity: 1;
}

/* Ensure content is above the overlay */
.slide-content {
    position: relative;
    z-index: 2;
}

/* Enhanced slide image animation */
.slide.active img {
    transform: scale(1);
}

/* Card Hover Effects */
.feature-card {
    transition: all 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
} 