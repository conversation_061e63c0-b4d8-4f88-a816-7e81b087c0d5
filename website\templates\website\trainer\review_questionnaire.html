{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}
{% load website_extras %}

{% block title %}{% trans "Review Questionnaire Response" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    .rating-display span {
        width: 40px;
        height: 40px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        border: 2px solid rgba(16, 185, 129, 0.5);
        border-radius: 50%;
        margin: 0 5px;
        font-weight: bold;
        transition: all 0.2s ease;
    }
    
    .rating-display span.selected {
        background-color: rgba(16, 185, 129, 0.9);
        border-color: rgba(52, 211, 153, 1);
        color: white;
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(5, 150, 105, 0.5);
    }
    
    .question-card {
        border-left: 4px solid transparent;
        transition: all 0.3s ease;
    }
    
    .question-card:hover {
        border-left-color: #10b981;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    }
    
    .correct-answer {
        background-color: rgba(16, 185, 129, 0.2);
        border-left: 3px solid #10b981;
    }
    
    .incorrect-answer {
        background-color: rgba(239, 68, 68, 0.2);
        border-left: 3px solid #ef4444;
    }
    
    .submit-btn {
        transition: all 0.3s ease;
    }
    
    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.5);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex items-center mb-6 space-x-3">
        <a href="{% if response.session %}{% url 'website:trainer_manage_session' session_id=response.session.session_id %}?tab=questionnaires{% else %}{% url 'website:trainer_sessions' %}{% endif %}" class="text-blue-400 hover:text-blue-300 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            {% trans "Back" %}
        </a>
        <h1 class="text-3xl font-bold text-white">
            {% trans "Review Questionnaire Response" %}
        </h1>
    </div>
    
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <!-- Left column - Questionnaire info -->
        <div class="lg:col-span-1">
            <div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <div class="bg-green-900/40 px-6 py-4 border-b border-gray-700">
                    <h2 class="text-xl font-semibold text-white">{% trans "Response Details" %}</h2>
                </div>
                
                <div class="p-6 space-y-4">
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "User" %}</h3>
                        <p class="text-white text-lg">{{ user.get_full_name }}</p>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "Course" %}</h3>
                        <p class="text-white">{{ response.course.name_en }}</p>
                    </div>
                    
                    {% if response.session %}
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "Session" %}</h3>
                        <p class="text-white">{{ response.session.session_id }}</p>
                    </div>
                    {% endif %}
                    
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "Questionnaire" %}</h3>
                        <p class="text-white">{{ questionnaire.title }}</p>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "Submitted" %}</h3>
                        <p class="text-white">{{ response.completed_at|date:"d M Y, H:i" }}</p>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "Current Score" %}</h3>
                        <div class="bg-gray-700 rounded-lg p-3 mt-1">
                            <p class="text-2xl font-bold text-center text-green-400">
                                {{ response.total_score }} <span class="text-gray-400 text-lg font-normal">/ {{ response.max_total_score }}</span>
                            </p>
                        </div>
                    </div>
                    
                    {% if response.comment %}
                    <div>
                        <h3 class="text-sm font-medium text-gray-400">{% trans "User Comment" %}</h3>
                        <div class="bg-gray-700 rounded-lg p-3 mt-1">
                            <p class="text-white">{{ response.comment }}</p>
                        </div>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
        
        <!-- Right column - User responses -->
        <div class="lg:col-span-2">
            <div class="bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                <div class="bg-blue-900/40 px-6 py-4 border-b border-gray-700">
                    <h2 class="text-xl font-semibold text-white">{% trans "User Responses" %}</h2>
                </div>
                
                <div class="p-6">
                    <form method="post" class="space-y-6">
                        {% csrf_token %}
                        
                        {% for question in questions %}
                        <div class="bg-gray-700 rounded-lg p-5 question-card">
                            <div class="flex justify-between items-start mb-4">
                                <h3 class="text-lg font-medium text-white">{{ forloop.counter }}. {{ question.text }}</h3>
                                {% if question.score %}
                                <span class="bg-green-900/30 text-green-400 px-2 py-1 rounded-md text-sm font-medium">
                                    {% trans "Score" %}: {{ question.score }}
                                </span>
                                {% endif %}
                            </div>
                            
                            {% with user_answer=response.question_responses|get_item:forloop.counter0|default:None %}
                            
                            {% if question.type == 'rating' %}
                            <div class="mt-4 rating-display flex items-center justify-center">
                                {% for i in "12345" %}
                                <span class="{% if user_answer == i %}selected{% endif %}">
                                    {{ i }}
                                </span>
                                {% endfor %}
                            </div>
                            
                            {% elif question.type == 'multiplechoice' or question.type == 'checkbox' %}
                            <div class="space-y-2 mt-3">
                                {% for option in question.options %}
                                <div class="p-3 rounded-md {% if option.id|stringformat:'s' == user_answer or option.id in user_answer %}{% if option.id in question.correct_answers %}correct-answer{% else %}incorrect-answer{% endif %}{% endif %}">
                                    <div class="flex items-center">
                                        <span class="ml-2 text-white">
                                            {{ option.text }}
                                            
                                            {% if option.id in question.correct_answers %}
                                            <span class="ml-2 text-green-500 text-sm font-medium">({% trans "Correct Answer" %})</span>
                                            {% endif %}
                                            
                                            {% if option.id|stringformat:'s' == user_answer or option.id in user_answer %}
                                            <span class="ml-2 text-blue-400 text-sm font-medium">({% trans "Selected" %})</span>
                                            {% endif %}
                                        </span>
                                    </div>
                                </div>
                                {% endfor %}
                            </div>
                            
                            {% elif question.type == 'text' %}
                            <div class="mt-3 p-4 bg-gray-600 rounded-md">
                                <p class="text-white">{{ user_answer|default:"No answer provided" }}</p>
                            </div>
                            {% endif %}
                            
                            {% endwith %}
                        </div>
                        {% endfor %}
                        
                        <div class="bg-blue-900/20 rounded-lg p-6 border border-blue-800/40 mt-8">
                            <h3 class="text-xl font-medium text-white mb-4">{% trans "Trainer Review" %}</h3>
                            
                            <div class="mb-4">
                                <label for="adjusted_score" class="block text-sm font-medium text-gray-400 mb-1">
                                    {% trans "Adjusted Score" %} ({% trans "Max" %}: {{ response.max_total_score }})
                                </label>
                                <input type="number" id="adjusted_score" name="adjusted_score" min="0" max="{{ response.max_total_score }}" value="{{ response.total_score }}" 
                                       class="block w-full rounded-md bg-gray-700 border border-gray-600 py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                            </div>
                            
                            <div class="mb-6">
                                <label for="trainer_comment" class="block text-sm font-medium text-gray-400 mb-1">
                                    {% trans "Trainer Comment" %}
                                </label>
                                <textarea id="trainer_comment" name="trainer_comment" rows="4" 
                                          class="block w-full rounded-md bg-gray-700 border border-gray-600 py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500">{{ response.trainer_comment|default:'' }}</textarea>
                            </div>
                            
                            <div class="flex justify-end">
                                <button type="submit" class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-6 rounded-md shadow-md flex items-center submit-btn">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    {% trans "Complete Review" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Add JavaScript for any additional functionality
</script>
{% endblock %} 