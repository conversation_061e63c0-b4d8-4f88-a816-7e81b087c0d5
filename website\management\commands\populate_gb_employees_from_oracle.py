#!/usr/bin/env python3
"""
Management command to populate GB_Employee table data using Oracle SOAP client.
This command fetches employee data from Oracle for all internal GB users and creates/updates
their GB_Employee records with complete Oracle data.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from website.models import CustomUser, GB_Employee, UserType
from website.soap_client import OracleSOAPClient
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Populate GB_Employee table data using Oracle SOAP client for internal GB users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making changes',
        )
        parser.add_argument(
            '--employee-id',
            type=str,
            help='Process only a specific employee ID',
        )
        parser.add_argument(
            '--force-update',
            action='store_true',
            help='Force update existing GB_Employee records even if they already have data',
        )
        parser.add_argument(
            '--create-missing',
            action='store_true',
            help='Create GB_Employee records for internal GB users who don\'t have them',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        specific_employee_id = options['employee_id']
        force_update = options['force_update']
        create_missing = options['create_missing']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no changes will be made')
            )
        
        try:
            # Initialize SOAP client
            soap_client = OracleSOAPClient()
            
            # Get internal GB user type
            try:
                gb_user_type = UserType.objects.get(code='INTERNAL_GB')
            except UserType.DoesNotExist:
                raise CommandError("INTERNAL_GB user type not found")
            
            # Find internal GB users to process
            if specific_employee_id:
                # Process specific employee
                internal_gb_users = CustomUser.objects.filter(
                    user_type=gb_user_type,
                    employee_id=specific_employee_id
                )
                if not internal_gb_users.exists():
                    raise CommandError(f"Internal GB user with employee ID {specific_employee_id} not found")
            else:
                # Process all internal GB users with employee_id
                internal_gb_users = CustomUser.objects.filter(
                    user_type=gb_user_type
                ).exclude(employee_id='').exclude(employee_id__isnull=True)
            
            total_users = internal_gb_users.count()
            
            if total_users == 0:
                self.stdout.write(
                    self.style.SUCCESS('No internal GB users found to process')
                )
                return
            
            self.stdout.write(
                f'Found {total_users} internal GB users to process'
            )
            
            processed_count = 0
            created_count = 0
            updated_count = 0
            linked_count = 0
            failed_count = 0
            skipped_count = 0
            
            for user in internal_gb_users:
                try:
                    with transaction.atomic():
                        employee_id = user.employee_id
                        
                        if dry_run:
                            self.stdout.write(
                                f'DRY-RUN: Would process user {user.username} (Employee ID: {employee_id})'
                            )
                            processed_count += 1
                            continue
                        
                        self.stdout.write(f'Processing user {user.username} (Employee ID: {employee_id})')
                        
                        # Check if GB_Employee record exists
                        existing_gb_employee = GB_Employee.objects.filter(
                            employee_number=employee_id
                        ).first()
                        
                        # Skip if record exists and has data, unless force_update is True
                        if existing_gb_employee and not force_update:
                            # Check if the record has meaningful Oracle data
                            has_oracle_data = (
                                existing_gb_employee.english_name or
                                existing_gb_employee.department or
                                existing_gb_employee.position_name or
                                existing_gb_employee.oracle_email_address
                            )
                            
                            if has_oracle_data:
                                # Just link the user to existing record if not linked
                                if not user.gb_employee:
                                    user.gb_employee = existing_gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    user.sync_oracle_data()
                                    linked_count += 1
                                    self.stdout.write(f'  Linked user to existing GB_Employee record')
                                else:
                                    skipped_count += 1
                                    self.stdout.write(f'  Skipped - GB_Employee record already has data')
                                processed_count += 1
                                continue
                        
                        # Fetch employee data from Oracle
                        try:
                            self.stdout.write(f'  Fetching Oracle data for employee {employee_id}...')
                            oracle_data = soap_client.get_employee_data(employee_id)
                            
                            if oracle_data:
                                # Create or update GB_Employee record with Oracle data
                                if existing_gb_employee:
                                    # Update existing record
                                    gb_employee = GB_Employee.create_or_update_from_oracle(oracle_data)
                                    updated_count += 1
                                    self.stdout.write(f'  Updated GB_Employee record with Oracle data')
                                else:
                                    # Create new record
                                    gb_employee = GB_Employee.create_or_update_from_oracle(oracle_data)
                                    created_count += 1
                                    self.stdout.write(f'  Created new GB_Employee record with Oracle data')
                                
                                # Link user to GB_Employee record
                                if user.gb_employee != gb_employee:
                                    user.gb_employee = gb_employee
                                    user.save(update_fields=['gb_employee'])
                                
                                # Sync Oracle data to CustomUser fields
                                user.sync_oracle_data()
                                
                                self.stdout.write(f'  Successfully populated Oracle data for {employee_id}')
                                
                            else:
                                # No Oracle data found
                                if existing_gb_employee:
                                    # Link to existing record
                                    if not user.gb_employee:
                                        user.gb_employee = existing_gb_employee
                                        user.save(update_fields=['gb_employee'])
                                        linked_count += 1
                                        self.stdout.write(f'  Linked to existing GB_Employee (no Oracle data available)')
                                    else:
                                        skipped_count += 1
                                        self.stdout.write(f'  No Oracle data available for employee {employee_id}')
                                elif create_missing:
                                    # Create basic record
                                    gb_employee = GB_Employee.objects.create(
                                        employee_number=employee_id,
                                        english_name=f"{user.first_name} {user.last_name}".strip(),
                                        oracle_email_address=user.email,
                                        is_active=True
                                    )
                                    user.gb_employee = gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    created_count += 1
                                    self.stdout.write(f'  Created basic GB_Employee record (no Oracle data available)')
                                else:
                                    failed_count += 1
                                    self.stdout.write(
                                        self.style.WARNING(f'  No Oracle data found for employee {employee_id}')
                                    )
                            
                        except Exception as oracle_err:
                            # Oracle fetch failed
                            self.stdout.write(
                                self.style.WARNING(f'  Oracle fetch failed for {employee_id}: {str(oracle_err)}')
                            )
                            
                            if existing_gb_employee:
                                # Link to existing record
                                if not user.gb_employee:
                                    user.gb_employee = existing_gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    linked_count += 1
                                    self.stdout.write(f'  Linked to existing GB_Employee (Oracle fetch failed)')
                                else:
                                    skipped_count += 1
                            elif create_missing:
                                # Create basic record
                                try:
                                    gb_employee = GB_Employee.objects.create(
                                        employee_number=employee_id,
                                        english_name=f"{user.first_name} {user.last_name}".strip(),
                                        oracle_email_address=user.email,
                                        is_active=True
                                    )
                                    user.gb_employee = gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    created_count += 1
                                    self.stdout.write(f'  Created basic GB_Employee record (Oracle fetch failed)')
                                except Exception as create_err:
                                    failed_count += 1
                                    self.stdout.write(
                                        self.style.ERROR(f'  Failed to create GB_Employee: {str(create_err)}')
                                    )
                            else:
                                failed_count += 1
                        
                        processed_count += 1
                        
                except Exception as e:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'Error processing user {user.username}: {str(e)}')
                    )
            
            # Summary
            if not dry_run:
                self.stdout.write('\n' + '='*60)
                self.stdout.write(f'Summary:')
                self.stdout.write(f'  Total users processed: {processed_count}')
                self.stdout.write(f'  GB_Employee records created: {created_count}')
                self.stdout.write(f'  GB_Employee records updated: {updated_count}')
                self.stdout.write(f'  Users linked to existing records: {linked_count}')
                self.stdout.write(f'  Records skipped (already have data): {skipped_count}')
                self.stdout.write(f'  Failed: {failed_count}')
                
                total_success = created_count + updated_count + linked_count
                if failed_count == 0:
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully processed all {total_success} users!')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Processed {total_success} users successfully, {failed_count} failed')
                    )
            
        except Exception as e:
            raise CommandError(f'Command failed: {str(e)}') 