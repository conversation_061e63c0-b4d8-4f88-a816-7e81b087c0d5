{% extends "website/login_base.html" %}
{% load static %}

{% block content %}
<!-- Background Slideshow -->
<div class="fixed inset-0 z-0">
    <div class="slideshow-container">
        <div class="slide">
            <img src="{% static 'images/slides/slide1.jpg' %}" class="slide-image" alt="Background 1">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide2.jpg' %}" class="slide-image" alt="Background 2">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide3.jpg' %}" class="slide-image" alt="Background 3">
            <div class="overlay"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="min-h-screen flex items-center justify-center relative z-10">
    <div class="container px-4 py-8">
        <div class="max-w-5xl mx-auto">
            <div class="w-full space-y-3 bg-black/20 backdrop-blur-md p-6 rounded-lg shadow-xl border border-white/10">
                <div class="text-center">
                    <div class="bg-white inline-block p-4 rounded-lg mb-4">
                        <img class="mx-auto h-20 w-auto" src="{% static 'images/logo.png' %}" alt="GB Academy Logo">
                    </div>
                    <h2 class="text-3xl font-bold tracking-tight text-white mb-1">
                        Create Account
                    </h2>
                    <p class="text-base text-white/80">
                        Step 3 of 4: Additional Information
                    </p>
                </div>

                {% include "website/registration/timeline.html" with current_step=current_step show_corporate=show_corporate %}
                
                <form method="post" class="mt-8 space-y-8">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="bg-red-500/20 backdrop-blur-md border border-red-500/30 text-white/90 px-4 py-3 rounded-md" role="alert">
                        <strong class="font-bold">Please fix the following errors:</strong>
                        <ul class="mt-2">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <!-- First Name and Last Name -->
                        <div class="space-y-2">
                            <label for="{{ form.first_name.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.first_name.label }}
                            </label>
                            <input type="text"
                                   name="{{ form.first_name.name }}"
                                   id="{{ form.first_name.id_for_label }}"
                                   class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                   required>
                        </div>
                        <div class="space-y-2">
                            <label for="{{ form.last_name.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.last_name.label }}
                            </label>
                            <input type="text"
                                   name="{{ form.last_name.name }}"
                                   id="{{ form.last_name.id_for_label }}"
                                   class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                   required>
                        </div>

                        <!-- Phone and Nationality -->
                        <div class="space-y-2">
                            <label for="{{ form.phone_number.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.phone_number.label }}
                            </label>
                            <input type="text"
                                   name="{{ form.phone_number.name }}"
                                   id="{{ form.phone_number.id_for_label }}"
                                   class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                   required>
                        </div>
                        <div class="space-y-2">
                            <label for="{{ form.nationality.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.nationality.label }}
                            </label>
                            <input type="text"
                                   name="{{ form.nationality.name }}"
                                   id="{{ form.nationality.id_for_label }}"
                                   class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                   required>
                        </div>

                        <!-- National ID and Date of Birth -->
                        <div class="space-y-2">
                            <label for="{{ form.national_id.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.national_id.label }}
                            </label>
                            <input type="text"
                                   name="{{ form.national_id.name }}"
                                   id="{{ form.national_id.id_for_label }}"
                                   class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                   required>
                        </div>
                        <div class="space-y-2">
                            <label for="{{ form.date_of_birth.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.date_of_birth.label }}
                            </label>
                            <input type="date"
                                   name="{{ form.date_of_birth.name }}"
                                   id="{{ form.date_of_birth.id_for_label }}"
                                   class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                   required>
                        </div>

                        <!-- Address (Full Width) -->
                        <div class="space-y-2 md:col-span-2">
                            <label for="{{ form.address.id_for_label }}" 
                                   class="block text-sm font-medium text-white/90">
                                {{ form.address.label }}
                            </label>
                            <textarea name="{{ form.address.name }}"
                                      id="{{ form.address.id_for_label }}"
                                      class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                      rows="3"
                                      required></textarea>
                        </div>
                    </div>
                    
                    <div class="pt-6 text-center max-w-2xl mx-auto">
                        <button type="submit"
                                class="w-full bg-white/20 hover:bg-white/30 text-white text-base rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-300 transform hover:scale-[1.02]">
                            {% if show_corporate %}
                                Continue
                            {% else %}
                                Complete Registration
                            {% endif %}
                        </button>
                    </div>
                </form>

                <div class="mt-6 text-center">
                    <a href="{% url 'website:register_step2' %}" class="text-sm text-white/80 hover:text-white transition-colors duration-300">
                        ← Back to Account Information
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 