# Learning Management System (LMS)
A comprehensive Learning Management System designed to handle both internal and external training management, user registration, course administration, and resource management.

## Features

### User Management
- Multiple user types support:
  - Internal GB Corp Users (Employee integration)
  - External Individual Users
  - External Corporate Users
  - External Corporate Trainees
  - System Administrators
  - Trainers/Trainer Managers

### Course Management
- Course creation and scheduling
- Learning path management
- Material management
- Assignment/Quiz/Exam support
- Grading system
- Certificate and badge management
- Room reservation system

### Registration System
- Automated internal user registration via Oracle EBS
- External user registration with payment integration
- Corporate group registration
- Course enrollment workflow
- Waiting list management

### Administrative Features
- User and profile management
- Resource allocation
- Room management
- Calendar management
- Reporting and analytics
- Support ticket system

### Integration Capabilities
- Oracle HR integration
- Payment gateway
- Chat system
- Meeting tools
- Oracle Finance integration

## Technical Requirements

### Backend Requirements
- User management system
- Course and schedule management
- Resource allocation system
- Reporting engine
- Notification system
- Integration interfaces

### Frontend Requirements
- Responsive web interface
- User registration and authentication
- Course catalog and search
- User dashboard
- Administrative interface
- Chat and support system

## Getting Started

[Installation and setup instructions to be added]

## Documentation

For detailed documentation about the system requirements and specifications, please refer to the business requirements document.

## Contributing

[Contribution guidelines to be added]

## License

[License information to be added]

## Support

[Support contact information to be added]
#   L M S _ G B 
 
 