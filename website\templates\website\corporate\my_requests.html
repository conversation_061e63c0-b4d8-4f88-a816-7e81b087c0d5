{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-5">
    <!-- <PERSON> Header - Updated to match reservations style -->
    <div class="flex justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z" />
            </svg>
            {{ page_title }}
        </h1>
        <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            {% trans "Browse Courses" %}
        </a>
    </div>

    <!-- Requests Table - Updated to match reservations style -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Course/Request" %}</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Type" %}</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Capacity" %}</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Status" %}</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Requested At" %}</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Admin Notes" %}</th>
                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Actions" %}</th>
                    </tr>
                </thead>
                <tbody id="requests-table-body" class="bg-white/5 divide-y divide-white/10">
                    <!-- Rows will be populated by JavaScript -->
                    <tr>
                        <td colspan="7" class="px-3 py-3 text-center text-white/70">{% trans "Loading requests..." %}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    fetchRequests();
});

function fetchRequests() {
    fetch('{% url "website:my_corporate_requests_api" %}')
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                populateTable(data.requests);
            } else {
                console.error('Error fetching requests:', data.message);
                displayError('Failed to load requests.');
            }
        })
        .catch(error => {
            console.error('Network error fetching requests:', error);
            displayError('Network error. Please try again later.');
        });
}

function populateTable(requests) {
    const tableBody = document.getElementById('requests-table-body');
    tableBody.innerHTML = ''; // Clear loading state

    if (requests.length === 0) {
        tableBody.innerHTML = `<tr><td colspan="7" class="px-3 py-3 text-center text-white/70">{% trans "You haven't made any course requests yet." %}</td></tr>`;
        return;
    }

    requests.forEach(req => {
        const row = tableBody.insertRow();
        row.className = 'hover:bg-white/10 transition-colors duration-150';

        // Status badge styling
        let statusClass = '';
        switch (req.status_code) {
            case 'PENDING':
                statusClass = 'bg-yellow-500/20 text-yellow-400';
                break;
            case 'APPROVED':
                statusClass = 'bg-green-500/20 text-green-400';
                break;
            case 'REJECTED':
                statusClass = 'bg-red-500/20 text-red-400';
                break;
            case 'CANCELLED':
                statusClass = 'bg-gray-500/20 text-gray-400';
                break;
            default:
                statusClass = 'bg-gray-500/20 text-gray-400';
        }
        
        // For new course requests, show a "NEW" indicator
        let courseName = req.course_name;
        if (req.request_type === 'new_course') {
            courseName += ` <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-500/20 text-blue-400">{% trans "NEW" %}</span>`;
        }

        row.innerHTML = `
            <td class="px-3 py-3 whitespace-nowrap text-sm text-white">${courseName}</td>
            <td class="px-3 py-3 whitespace-nowrap text-sm text-white">${req.course_type}</td>
            <td class="px-3 py-3 whitespace-nowrap text-sm text-white">${req.capacity}</td>
            <td class="px-3 py-3 whitespace-nowrap text-sm">
                <span class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${statusClass}">
                    ${req.status}
                </span>
            </td>
            <td class="px-3 py-3 whitespace-nowrap text-sm text-white/80">${req.created_at}</td>
            <td class="px-3 py-3 text-sm text-white/80">${req.admin_notes || '-'}</td>
            <td class="px-3 py-3 text-sm text-white/80">
                ${req.status_code === 'PENDING' ? 
                    `<button onclick="cancelRequest(${req.request_id}, '${req.request_type}')" class="px-3 py-1 bg-red-500/20 text-red-400 hover:bg-red-500/30 rounded-full text-xs font-semibold transition-colors duration-150 cursor-pointer">{% trans "Cancel" %}</button>` : 
                  req.status_code === 'APPROVED' ? 
                    `<button onclick="requestCancelApproved(${req.request_id}, '${req.request_type}')" class="px-3 py-1 bg-orange-500/20 text-orange-400 hover:bg-orange-500/30 rounded-full text-xs font-semibold transition-colors duration-150 cursor-pointer">{% trans "Request Cancellation" %}</button>` : 
                  '-'}
            </td>
        `;
    });
}

function displayError(message) {
    const tableBody = document.getElementById('requests-table-body');
    tableBody.innerHTML = `<tr><td colspan="7" class="px-3 py-3 text-center text-red-400">${message}</td></tr>`;
}

function cancelRequest(requestId, requestType = 'regular') {
    if (!confirm("{% trans 'Are you sure you want to cancel this course request?' %}")) {
        return;
    }
    
    // Use a form submission approach instead of fetch to avoid method issues
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "website:cancel_course_request_api" %}';
    form.style.display = 'none';
    
    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrfmiddlewaretoken';
    csrfToken.value = getCookie('csrftoken');
    form.appendChild(csrfToken);
    
    // Add request_id
    const requestIdInput = document.createElement('input');
    requestIdInput.type = 'hidden';
    requestIdInput.name = 'request_id';
    requestIdInput.value = requestId;
    form.appendChild(requestIdInput);
    
    // Add request_type
    const requestTypeInput = document.createElement('input');
    requestTypeInput.type = 'hidden';
    requestTypeInput.name = 'request_type';
    requestTypeInput.value = requestType;
    form.appendChild(requestTypeInput);
    
    // Add to body and submit
    document.body.appendChild(form);
    form.submit();
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function requestCancelApproved(requestId, requestType) {
    if (!confirm("{% trans 'Are you sure you want to request cancellation of this approved course? This will need system admin approval.' %}")) {
        return;
    }
    
    // Use a form submission approach
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = '{% url "website:request_cancel_approved_api" %}';
    form.style.display = 'none';
    
    // Add CSRF token
    const csrfToken = document.createElement('input');
    csrfToken.type = 'hidden';
    csrfToken.name = 'csrfmiddlewaretoken';
    csrfToken.value = getCookie('csrftoken');
    form.appendChild(csrfToken);
    
    // Add request_id
    const requestIdInput = document.createElement('input');
    requestIdInput.type = 'hidden';
    requestIdInput.name = 'request_id';
    requestIdInput.value = requestId;
    form.appendChild(requestIdInput);
    
    // Add request_type
    const requestTypeInput = document.createElement('input');
    requestTypeInput.type = 'hidden';
    requestTypeInput.name = 'request_type';
    requestTypeInput.value = requestType;
    form.appendChild(requestTypeInput);
    
    // Add reason field (optional)
    const reason = prompt("{% trans 'Please provide a reason for this cancellation request:' %}", "");
    if (reason !== null) {
        const reasonInput = document.createElement('input');
        reasonInput.type = 'hidden';
        reasonInput.name = 'reason';
        reasonInput.value = reason;
        form.appendChild(reasonInput);
        
        // Add to body and submit
        document.body.appendChild(form);
        form.submit();
    }
}
</script>
{% endblock %} 