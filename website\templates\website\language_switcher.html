{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}
{% get_available_languages as LANGUAGES %}
{% get_language_info_list for LANGUAGES as languages %}

<form id="language-form" action="{% url 'set_language' %}" method="post" class="inline-flex">
    {% csrf_token %}
    <input name="next" type="hidden" value="{{ request.path }}">
    <div class="inline-flex bg-white/10 rounded-lg p-1">
        <button type="button" data-lang="en" class="lang-button px-4 py-2 rounded-md text-sm font-medium text-white/90 {% if LANGUAGE_CODE == 'en' %}lang-btn-active{% endif %}">
            EN
        </button>
        <button type="button" data-lang="ar" class="lang-button px-4 py-2 rounded-md text-sm font-medium text-white/90 {% if LANGUAGE_CODE == 'ar' %}lang-btn-active{% endif %}">
            AR
        </button>
        <input type="hidden" name="language" id="language-input">
    </div>
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const languageForm = document.getElementById('language-form');
    const languageInput = document.getElementById('language-input');
    const languageButtons = document.querySelectorAll('.lang-button');

    languageButtons.forEach(button => {
        button.addEventListener('click', function() {
            const lang = this.getAttribute('data-lang');
            languageInput.value = lang;
            languageForm.submit();
        });
    });

    // Update RTL/LTR based on current language
    const isRTL = '{{ LANGUAGE_CODE }}' === 'ar';
    if (isRTL) {
        document.documentElement.dir = 'rtl';
        document.documentElement.classList.add('rtl-mode');
    } else {
        document.documentElement.dir = 'ltr';
        document.documentElement.classList.remove('rtl-mode');
    }
});
</script> 