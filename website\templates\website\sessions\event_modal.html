{% load i18n %}

<!-- Add CSRF token -->
{% csrf_token %}

<div id="eventModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-20 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all mt-20 sm:my-16 sm:align-middle sm:max-w-5xl sm:w-full">
            <div class="bg-gray-900 px-4 pt-12 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-xl leading-6 font-bold text-white text-center" id="modalTitle">
                            {% trans "Create Event" %}
                        </h3>
                        <!-- Error and Success Messages -->
                        <div id="eventModalError" class="mt-2 p-2 bg-red-500 text-white rounded hidden"></div>
                        <div id="eventModalSuccess" class="mt-2 p-2 bg-green-500 text-white rounded hidden"></div>
                        
                        <!-- Conflict Summary -->
                        <div id="eventConflictSummary" class="mt-2 p-3 bg-red-500/20 border border-red-500 rounded-md hidden">
                            <div class="flex items-start">
                                <div class="flex-shrink-0">
                                    <!-- Warning Icon -->
                                    <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h4 class="text-sm font-medium text-red-400">{% trans "Room Conflict Detected" %}</h4>
                                    <p id="eventConflictMessage" class="text-sm text-red-400"></p>
                                    
                                    <!-- Conflict Details (expandable) -->
                                    <div class="mt-2">
                                        <button id="showConflictDetails" class="text-xs text-red-400 underline cursor-pointer">{% trans "Show conflict details" %}</button>
                                        <div id="conflictDetails" class="mt-2 hidden">
                                            <div id="eventConflicts" class="hidden">
                                                <h5 class="text-xs font-semibold text-red-400">{% trans "Conflicting Events" %}</h5>
                                                <ul id="conflictingEventsList" class="text-xs text-red-400 list-disc ml-5 space-y-1"></ul>
                                            </div>
                                            <div id="sessionConflicts" class="mt-1 hidden">
                                                <h5 class="text-xs font-semibold text-red-400">{% trans "Conflicting Sessions" %}</h5>
                                                <ul id="conflictingSessionsList" class="text-xs text-red-400 list-disc ml-5 space-y-1"></ul>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-8">
                            <form id="eventForm" class="space-y-4" onsubmit="event.preventDefault(); window.handleEventSave();">
                                <!-- Two column layout -->
                                <div class="flex flex-col md:flex-row md:space-x-6">
                                    <!-- Left column for main form fields -->
                                    <div class="md:w-1/2 space-y-4">
                                        <div class="bg-gray-800/30 rounded-lg border border-gray-700/50 h-full shadow-md">
                                            <div class="flex items-center justify-between bg-gray-800 p-3 rounded-t-lg sticky top-0 z-10">
                                                <h4 class="text-sm font-medium text-gray-300">{% trans "Event Details" %}</h4>
                                            </div>
                                            <div class="p-4 pt-3 overflow-y-auto max-h-[65vh]">
                                                <div class="space-y-4">
                                                    <div>
                                                        <label for="name" class="block text-sm font-medium text-gray-300">{% trans "Event Name" %}</label>
                                                        <input type="text" name="name" id="name" required class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                    </div>
                                                    
                                                    <div>
                                                        <label for="event_type" class="block text-sm font-medium text-gray-300">{% trans "Event Type" %}</label>
                                                        <select id="event_type" name="event_type" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                                            <option value="">{% trans "Select Event Type" %}</option>
                                                            {% for type in event_types %}
                                                            <option value="{{ type.event_type_id }}">{{ type.type }}</option>
                                                            {% endfor %}
                                                        </select>
                                                    </div>
                                                    <div>
                                                        <label for="capacity" class="block text-sm font-medium text-gray-300">{% trans "Capacity" %}</label>
                                                        <input type="number" name="capacity" id="capacity" min="1" required class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                                    </div>
                                                    <div>
                                                        <label for="description" class="block text-sm font-medium text-gray-300">{% trans "Description" %}</label>
                                                        <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"></textarea>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- Right column for dates and equipment -->
                                    <div class="md:w-1/2 md:border-l border-gray-700 md:pl-4 h-full">
                                        <!-- Event Dates Container -->
                                        <div id="eventDatesContainer" class="space-y-4 h-full">
                                            <div class="flex items-center justify-between bg-gray-800 p-3 rounded-t-lg sticky top-0 z-10">
                                                <h4 class="text-sm font-medium text-gray-300">{% trans "Event Days" %}</h4>
                                                <span id="eventCount" class="text-sm text-gray-400 bg-gray-700 px-2 py-1 rounded-md">0 / 1 days selected</span>
                                            </div>
                                            
                                            <!-- Event Days Selection -->
                                            <div id="event_days_container" class="bg-gray-800/30 p-3 rounded-lg border border-gray-700/50 shadow-md">
                                                <label for="event_days" class="block text-sm font-medium text-gray-300">{% trans "Number of Days" %}</label>
                                                <select id="event_days" name="event_days" class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                                    <option value="1" selected>1 Day</option>
                                                    <option value="2">2 Days</option>
                                                    <option value="3">3 Days</option>
                                                    <option value="4">4 Days</option>
                                                    <option value="5">5 Days</option>
                                                </select>
                                            </div>
                                            
                                            <div id="eventDates" class="space-y-4 max-h-[65vh] overflow-y-auto pr-2 bg-gray-800/30 p-3 rounded-lg border border-gray-700/50 shadow-md">
                                                <!-- Event dates will be added here dynamically -->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Hidden room select for JavaScript use -->
                                <div class="hidden">
                                    <select id="room" name="room">
                                        <option value="">{% trans "Select a Room" %}</option>
                                        {% for room in rooms %}
                                        <option value="{{ room.room_id }}">{{ room.name }}</option>
                                        {% endfor %}
                                    </select>
                                    <div id="equipment-list" class="hidden">
                                        {% for equipment in equipment_list %}
                                        <label class="hidden">
                                            <input type="checkbox" name="equipment" value="{{ equipment.equipment_id }}">
                                            <span>{{ equipment.name }} ({{ equipment.code }})</span>
                                        </label>
                                        {% endfor %}
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="saveEventBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Save" %}
                </button>
                <button type="button" data-action="close-event-modal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Global state for event modal
window.eventModalState = {
    isSubmitting: false,
    flatpickrInstance: null,
    currentEventId: null,
    requiredDays: 1,
    selectedDates: [],
    busySlots: {}
};

// Functions to check if a date is a weekend (Friday or Saturday)
function isWeekend(dateObj) {
    const day = dateObj.getDay();
    // Friday is 5, Saturday is 6 in JavaScript Date
    return day === 5 || day === 6;
}

// Function to check if a date is a holiday
function isHoliday(dateObj) {
    if (!window.holidaysLoaded) return false;
    
    const dateStr = dateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    return window.holidaysData.includes(dateStr);
}

// Function to show date warning for weekend or holiday
function showDateWarning(dateInput, dateObj, index) {
    // Check if date warning container already exists
    let warningContainer = document.getElementById(`date_warning_${index}`);
    
    // If no warning container exists, create one
    if (!warningContainer) {
        warningContainer = document.createElement('div');
        warningContainer.id = `date_warning_${index}`;
        warningContainer.className = 'mt-1 p-2 bg-yellow-500/20 border border-yellow-500 rounded-md text-sm text-yellow-600';
        
        // Insert after the date input container
        const dateInputContainer = dateInput.parentElement;
        if (dateInputContainer && dateInputContainer.parentElement) {
            const parentDiv = dateInputContainer.parentElement;
            parentDiv.insertBefore(warningContainer, dateInputContainer.nextSibling);
        }
    }
    
    // Set warning text based on what's detected
    if (isWeekend(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a weekend day (Friday or Saturday)" %}';
        warningContainer.classList.remove('hidden');
    } else if (isHoliday(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a holiday" %}';
        warningContainer.classList.remove('hidden');
    } else {
        warningContainer.classList.add('hidden');
    }
}

// Add an identifier to track if we're currently handling an edit-event
let handlingEditEvent = false;

// Listen for edit-event events
document.addEventListener('edit-event', function(e) {
    // Prevent recursion
    if (handlingEditEvent) {
        console.warn('Already handling an edit-event, ignoring recursive call');
        return;
    }
    
    handlingEditEvent = true;
    console.log('edit-event received with details:', e.detail);
    
    try {
        if (e.detail && e.detail.eventId) {
            // Instead of calling openEventModal, directly load the event data
            const eventId = e.detail.eventId;
            console.log('Directly handling edit event for ID:', eventId);
            
            // Initialize the modal if needed
            initEventModal();
            
            // Reset state
            window.eventModalState = {
                isSubmitting: false,
                flatpickrInstance: null,
                currentEventId: eventId,
                requiredDays: 1,
                selectedDates: [],
                busySlots: []
            };
            
            // Reset form
            const form = document.getElementById('eventForm');
            if (form) {
                form.reset();
            }
            
            // Hide the days selection when editing
            const daysSelectContainer = document.getElementById('event_days_container');
            if (daysSelectContainer) {
                daysSelectContainer.classList.add('hidden');
            }
            
            // Update the event dates UI
            updateEventDatesUI();
            
            // Reset count
            updateEventCount();
            
            // Hide conflict summary
            const conflictSummary = document.getElementById('eventConflictSummary');
            if (conflictSummary) {
                conflictSummary.classList.add('hidden');
            }
            
            // Reset save button
            const saveButton = document.getElementById('saveEventBtn');
            if (saveButton) {
                saveButton.disabled = false;
                saveButton.textContent = '{% trans "Save" %}';
            }
            
            // Hide error and success messages
            hideEventMessages();
            
            // Show modal
            const modal = document.getElementById('eventModal');
            if (modal) {
                console.log('Showing event modal');
                modal.classList.remove('hidden');
                
                // Set modal title - try both potential IDs
                const modalTitle = document.getElementById('modalTitle') || document.getElementById('eventModalTitle');
                if (modalTitle) {
                    console.log('Setting modal title to Edit Event');
                    modalTitle.textContent = '{% trans "Edit Event" %}';
                } else {
                    console.error('Modal title element not found with ID modalTitle or eventModalTitle');
                }
            }
            
            // Now load the event data after a longer delay to ensure DOM is updated
            console.log('Scheduling event data load after DOM update');
            setTimeout(() => {
                console.log('Loading event data for ID:', eventId);
                loadEventData(eventId);
            }, 500); // Increased delay to 500ms
        } else {
            console.error('Invalid edit-event: Missing eventId in event details');
        }
    } catch (error) {
        console.error('Error handling edit-event:', error);
    } finally {
        // Reset the handling flag when done
        setTimeout(() => {
            handlingEditEvent = false;
        }, 600); // Make sure this is longer than the loadEventData delay
    }
});

// Listen for open-new-event events
document.addEventListener('open-new-event', function() {
    console.log('Received open-new-event event');
    
    // Reset state
    window.eventModalState = {
        isSubmitting: false,
        flatpickrInstance: null,
        currentEventId: null,
        requiredDays: 1,
        selectedDates: [],
        busySlots: []
    };
    
    // Initialize the modal if needed
    initEventModal();
    
    // Reset form
    const form = document.getElementById('eventForm');
    if (form) {
        form.reset();
        
        // Explicitly ensure the name field is cleared
        const nameInput = document.getElementById('name');
        if (nameInput) {
            nameInput.value = '';
            nameInput.focus();
            nameInput.blur();
        }
    }
    
    // Set the default number of days to 1
    const daysSelect = document.getElementById('event_days');
    if (daysSelect) {
        daysSelect.value = '1';
    }
    
    // Show days selection for new events
    const daysSelectContainer = document.getElementById('event_days_container');
    if (daysSelectContainer) {
        daysSelectContainer.classList.remove('hidden');
    }
    
    // Update the event dates UI
    updateEventDatesUI();
    
    // Reset count
    updateEventCount();
    
    // Hide conflict summary
    const conflictSummary = document.getElementById('eventConflictSummary');
    if (conflictSummary) {
        conflictSummary.classList.add('hidden');
    }
    
    // Reset save button
    const saveButton = document.getElementById('saveEventBtn');
    if (saveButton) {
        saveButton.disabled = false;
        saveButton.textContent = '{% trans "Save" %}';
    }
    
    // Hide error and success messages
    hideEventMessages();
    
    // Show modal
    const modal = document.getElementById('eventModal');
    if (modal) {
        console.log('Showing event modal for new event');
        modal.classList.remove('hidden');
        
        // Set modal title for creating new event
        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            modalTitle.textContent = '{% trans "Create Event" %}';
        }
    }
    
    // Focus on the name field for new events
    setTimeout(() => {
        const nameInput = document.getElementById('name');
        if (nameInput) {
            nameInput.focus();
        }
    }, 100);
});

// Add function to initialize event modal
function initEventModal() {
    try {
        // Track if the modal is already initialized
        if (window.eventModalState.isInitialized) {
            return;
        }
        
        // Add event listener for number of days select
        const daysSelect = document.getElementById('event_days');
        if (daysSelect) {
            daysSelect.addEventListener('change', function() {
                const days = parseInt(daysSelect.value);
                if (!isNaN(days) && days > 0) {
                    window.eventModalState.requiredDays = days;
                    updateEventDatesUI();
                    updateEventCount();
            }
        });
    }

        // Add click handler for conflict details toggle
    const showDetailsBtn = document.getElementById('showConflictDetails');
        const conflictDetails = document.getElementById('conflictDetails');
    
        if (showDetailsBtn && conflictDetails) {
        showDetailsBtn.addEventListener('click', function() {
                const isHidden = conflictDetails.classList.contains('hidden');
                if (isHidden) {
                    conflictDetails.classList.remove('hidden');
                showDetailsBtn.textContent = '{% trans "Hide conflict details" %}';
            } else {
                    conflictDetails.classList.add('hidden');
                showDetailsBtn.textContent = '{% trans "Show conflict details" %}';
            }
        });
    }
    
        // Add event listener for save button
        const saveButton = document.getElementById('saveEventBtn');
        if (saveButton) {
            saveButton.addEventListener('click', handleEventSave);
        }
        
        // Mark as initialized
        window.eventModalState.isInitialized = true;
    } catch (error) {
        console.error('Error initializing event modal:', error);
    }
}

// Function to open the event modal
function openEventModal(eventId = null) {
    try {
        // Important: check if this is being called with an eventId and from index.html
        if (eventId) {
            console.log('openEventModal: Direct call with ID detected, passing to edit-event custom event');
            
            // Avoid direct handling - use the custom event approach to prevent recursion
            document.dispatchEvent(new CustomEvent('edit-event', { 
                detail: { eventId: eventId }
            }));
            
            // Return immediately to avoid any further processing
            return;
        }
        
        // Set a flag to prevent recursive calls
        if (window.isOpeningEventModal) {
            console.warn('Already opening event modal, preventing recursive call');
            return;
        }
        
        // Set the flag to indicate we're opening the modal
        window.isOpeningEventModal = true;
        
        console.log('openEventModal called for new event creation');
        
        try {
            // Initialize the modal if needed
            initEventModal();
            
            // Reset state
            window.eventModalState = {
                isSubmitting: false,
                flatpickrInstance: null,
                currentEventId: null,
                requiredDays: 1,
                selectedDates: [],
                busySlots: []
            };
            
            // Reset form
            const form = document.getElementById('eventForm');
            if (form) {
                form.reset();
                
                // Explicitly ensure the name field is cleared
                const nameInput = document.getElementById('name');
                if (nameInput) {
                    nameInput.value = '';
                    nameInput.focus();
                    nameInput.blur();
                }
            } else {
                console.error('Event form not found in DOM');
            }
            
            // Set the default number of days to 1
            const daysSelect = document.getElementById('event_days');
            if (daysSelect) {
                daysSelect.value = '1';
            }
            
            // Show days selection for new events
            const daysSelectContainer = document.getElementById('event_days_container');
            if (daysSelectContainer) {
                daysSelectContainer.classList.remove('hidden');
            }
            
            // Update the event dates UI
            updateEventDatesUI();
            
            // Reset count
            updateEventCount();
            
            // Hide conflict summary
            const conflictSummary = document.getElementById('eventConflictSummary');
            if (conflictSummary) {
                conflictSummary.classList.add('hidden');
            }
            
            // Reset save button
            const saveButton = document.getElementById('saveEventBtn');
            if (saveButton) {
                saveButton.disabled = false;
                saveButton.textContent = '{% trans "Save" %}';
            }
            
            // Hide error and success messages
            hideEventMessages();
            
            // Show modal
            const modal = document.getElementById('eventModal');
            if (modal) {
                console.log('Showing event modal for new event');
                modal.classList.remove('hidden');
                
                // Set modal title for creating new event
                const modalTitle = document.getElementById('modalTitle');
                if (modalTitle) {
                    modalTitle.textContent = '{% trans "Create Event" %}';
                }
            } else {
                console.error('Event modal not found in DOM!');
            }
            
            // Focus on the name field for new events
            setTimeout(() => {
                const nameInput = document.getElementById('name');
                if (nameInput) {
                    nameInput.focus();
                }
            }, 100);
        } finally {
            // Clear the flag regardless of success or failure
            setTimeout(() => {
                window.isOpeningEventModal = false;
            }, 200);
        }
    } catch (error) {
        console.error('Error opening event modal:', error);
        window.isOpeningEventModal = false;
    }
}

// Function to close the event modal
function closeEventModal() {
    const modal = document.getElementById('eventModal');
    if (modal) {
        modal.classList.add('hidden');
    }
    hideEventMessages();
}

// Function to hide event modal messages
function hideEventMessages() {
    const errorDiv = document.getElementById('eventModalError');
    const successDiv = document.getElementById('eventModalSuccess');
    
    if (errorDiv) {
        errorDiv.textContent = '';
        errorDiv.classList.add('hidden');
    }
    
    if (successDiv) {
        successDiv.textContent = '';
        successDiv.classList.add('hidden');
    }
}

// Function to show error message
function showEventError(message) {
    const errorDiv = document.getElementById('eventModalError');
    if (errorDiv) {
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
    }
}

// Function to show success message
function showEventSuccess(message) {
    const successDiv = document.getElementById('eventModalSuccess');
    if (successDiv) {
        successDiv.textContent = message;
        successDiv.classList.remove('hidden');
    }
}

// Function to update the event dates UI based on the number of days
function updateEventDatesUI() {
    try {
        const container = document.getElementById('eventDates');
        if (!container) {
            console.error('Event dates container not found');
            return;
        }
        
        // Clear existing content
        container.innerHTML = '';
        console.log('Cleared eventDates container, creating new date inputs');
        
        // Create date inputs for each required day
        for (let i = 0; i < window.eventModalState.requiredDays; i++) {
            const dateDiv = document.createElement('div');
            dateDiv.className = 'event-date-pair mb-4 border-l-2 border-gray-700 pl-3';
            dateDiv.dataset.dayIndex = i;  // Add data attribute for easier identification
            
            // Day label - For edit mode (single day), use "Event Date" instead of "Day 1"
            const dayLabel = document.createElement('div');
            dayLabel.className = 'text-sm font-medium text-gray-400 mb-2';
            
            // If we're in edit mode, use "Event Date" instead of "Day 1" for the first day
            if (i === 0 && window.eventModalState.currentEventId) {
                dayLabel.textContent = '{% trans "Event Date" %}';
            } else {
                dayLabel.textContent = `Day ${i + 1}`;
            }
            
            // Date input container
            const dateInputContainer = document.createElement('div');
            dateInputContainer.className = 'mb-3';
            
            const dateLabel = document.createElement('label');
            dateLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
            dateLabel.htmlFor = `event_date_${i}`;
            dateLabel.textContent = '{% trans "Date" %}';
            
            const dateInput = document.createElement('input');
            dateInput.type = 'date';
            dateInput.name = `event_date_${i}`;
            dateInput.id = `event_date_${i}`;
            dateInput.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
            dateInput.required = true;
            dateInput.min = new Date().toISOString().split('T')[0]; // Set min date to today
            
            dateInputContainer.appendChild(dateLabel);
            dateInputContainer.appendChild(dateInput);
            
            // Time input container
            const timeInputsContainer = document.createElement('div');
            timeInputsContainer.className = 'flex space-x-2';
            
            // Start time input
            const startTimeContainer = document.createElement('div');
            startTimeContainer.className = 'flex-1';
            
            const startTimeLabel = document.createElement('label');
            startTimeLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
            startTimeLabel.textContent = '{% trans "Start Time" %}';
            startTimeLabel.htmlFor = `start_time_${i}`;
            
            const startTimeInput = document.createElement('input');
            startTimeInput.type = 'time';
            startTimeInput.name = `start_time_${i}`;
            startTimeInput.id = `start_time_${i}`;
            startTimeInput.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
            startTimeInput.required = true;
            startTimeInput.value = '09:00';
            
            // End time input
            const endTimeContainer = document.createElement('div');
            endTimeContainer.className = 'flex-1';
            
            const endTimeLabel = document.createElement('label');
            endTimeLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
            endTimeLabel.textContent = '{% trans "End Time" %}';
            endTimeLabel.htmlFor = `end_time_${i}`;
            
            const endTimeInput = document.createElement('input');
            endTimeInput.type = 'time';
            endTimeInput.name = `end_time_${i}`;
            endTimeInput.id = `end_time_${i}`;
            endTimeInput.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
            endTimeInput.required = true;
            endTimeInput.value = '16:00';
            
            startTimeContainer.appendChild(startTimeLabel);
            startTimeContainer.appendChild(startTimeInput);
            timeInputsContainer.appendChild(startTimeContainer);
            
            endTimeContainer.appendChild(endTimeLabel);
            endTimeContainer.appendChild(endTimeInput);
            timeInputsContainer.appendChild(endTimeContainer);
            
            // Room selection for this day
            const roomContainer = document.createElement('div');
            roomContainer.className = 'mt-3';
            
            const roomLabel = document.createElement('label');
            roomLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
            roomLabel.textContent = '{% trans "Room" %}';
            roomLabel.htmlFor = `room_${i}`;
            
            const roomSelect = document.createElement('select');
            roomSelect.name = `room_${i}`;
            roomSelect.id = `room_${i}`;
            roomSelect.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
            roomSelect.required = true;
            
            // Add default option
            const defaultOption = document.createElement('option');
            defaultOption.value = "";
            defaultOption.textContent = '{% trans "Select a Room" %}';
            roomSelect.appendChild(defaultOption);
            
            // Add rooms from the main room select
            const mainRoomSelect = document.getElementById('room');
            if (mainRoomSelect) {
                Array.from(mainRoomSelect.options).forEach(opt => {
                    if (opt.value) { // Skip the empty default option
                        const option = document.createElement('option');
                        option.value = opt.value;
                        option.textContent = opt.textContent;
                        roomSelect.appendChild(option);
                    }
                });
            }
            
            roomContainer.appendChild(roomLabel);
            roomContainer.appendChild(roomSelect);
            
            // Equipment selection for this day
            const equipmentContainer = document.createElement('div');
            equipmentContainer.className = 'mt-3';
            
            const equipmentLabel = document.createElement('label');
            equipmentLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
            equipmentLabel.textContent = '{% trans "Required Equipment" %}';
            equipmentLabel.htmlFor = `equipment_${i}`;
            
            const equipmentSelectContainer = document.createElement('div');
            equipmentSelectContainer.className = 'mt-1 max-h-32 overflow-y-auto border border-gray-600 rounded-md bg-gray-900 p-2';
            equipmentSelectContainer.id = `equipment_container_${i}`;
            
            // Initial empty message
            equipmentSelectContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select a date, time, and room first" %}</span></div>';
            
            equipmentContainer.appendChild(equipmentLabel);
            equipmentContainer.appendChild(equipmentSelectContainer);
            
            // Add conflict warning
            const conflictWarning = document.createElement('div');
            conflictWarning.className = 'flex items-center text-sm text-red-500 mt-2 hidden';
            conflictWarning.id = `event_conflict_warning_${i}`;
            
            const warningIcon = document.createElement('span');
            warningIcon.className = 'mr-1';
            warningIcon.innerHTML = '<svg class="h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd" /></svg>';
            
            const warningText = document.createElement('span');
            warningText.id = `event_conflict_text_${i}`;
            
            conflictWarning.appendChild(warningIcon);
            conflictWarning.appendChild(warningText);
            
            // Add event listeners
            dateInput.addEventListener('change', function() {
                updateEventDate(i, dateInput, startTimeInput, endTimeInput, roomSelect);
                
                // Check if the selected date is a weekend or holiday
                if (dateInput.value) {
                    const dateObj = new Date(dateInput.value);
                    showDateWarning(dateInput, dateObj, i);
                }
            });
            
            startTimeInput.addEventListener('change', function() {
                updateEventDate(i, dateInput, startTimeInput, endTimeInput, roomSelect);
            });
            
            endTimeInput.addEventListener('change', function() {
                updateEventDate(i, dateInput, startTimeInput, endTimeInput, roomSelect);
            });
            
            roomSelect.addEventListener('change', function() {
                updateEventDate(i, dateInput, startTimeInput, endTimeInput, roomSelect);
                
                // Fetch equipment when room changes
                if (dateInput.value && roomSelect.value) {
                    const startTime = `${dateInput.value}T${startTimeInput.value}`;
                    const endTime = `${dateInput.value}T${endTimeInput.value}`;
                    fetchAvailableEquipment(i, startTime, endTime);
                }
            });
            
            // Add elements to date div
            dateDiv.appendChild(dayLabel);
            dateDiv.appendChild(dateInputContainer);
            dateDiv.appendChild(timeInputsContainer);
            dateDiv.appendChild(roomContainer);
            dateDiv.appendChild(equipmentContainer);
            dateDiv.appendChild(conflictWarning);
            
            // Add date div to container
            container.appendChild(dateDiv);
        }
        
        // Verify the elements were created correctly
        console.log('Event dates UI updated, verifying fields:');
        for (let i = 0; i < window.eventModalState.requiredDays; i++) {
            const dateInput = document.getElementById(`event_date_${i}`);
            const startTimeInput = document.getElementById(`start_time_${i}`);
            const endTimeInput = document.getElementById(`end_time_${i}`);
            console.log(`Day ${i} fields:`, {
                dateInput: !!dateInput,
                startTimeInput: !!startTimeInput,
                endTimeInput: !!endTimeInput
            });
        }
    } catch (error) {
        console.error('Error updating event dates UI:', error);
    }
}

// Function to update event date data
function updateEventDate(index, dateInput, startTimeInput, endTimeInput, roomSelect) {
    if (!dateInput || !dateInput.value || !startTimeInput || !endTimeInput) return;
    
    const date = dateInput.value; // YYYY-MM-DD
    const startTime = startTimeInput.value; // HH:MM
    const endTime = endTimeInput.value; // HH:MM
    const roomId = roomSelect ? roomSelect.value : null;
    
    // Check if the selected date is a weekend or holiday
    if (dateInput.value) {
        const dateObj = new Date(dateInput.value);
        showDateWarning(dateInput, dateObj, index);
    }
    
    // Get selected equipment for this day
    const equipment = [];
    if (window.eventModalState.requiredDays > 1) {
        const equipmentCheckboxes = document.querySelectorAll(`input[name="equipment_${index}"]:checked`);
        if (equipmentCheckboxes) {
            equipment.push(...Array.from(equipmentCheckboxes).map(cb => cb.value));
        }
    }
    
    // Make sure end time is after start time
    if (startTime >= endTime) {
        const warningElement = document.getElementById(`event_conflict_warning_${index}`);
        const warningText = document.getElementById(`event_conflict_text_${index}`);
        
        if (warningElement && warningText) {
            warningText.textContent = '{% trans "End time must be after start time" %}';
            warningElement.classList.remove('hidden');
        }
        
        // Set a default end time that is at least 1 hour after start time
        const startHour = parseInt(startTime.split(':')[0]);
        const newEndHour = Math.min(startHour + 1, 18); // Ensure it doesn't go beyond 6 PM
        endTimeInput.value = `${newEndHour.toString().padStart(2, '0')}:00`;
        
        // Update the data with corrected end time
        window.eventModalState.selectedDates[index] = {
            start_time: `${date}T${startTime}`,
            end_time: `${date}T${endTimeInput.value}`,
            room_id: roomId,
            equipment: equipment
        };
    } else {
        // Hide warning if it's showing
        const warningElement = document.getElementById(`event_conflict_warning_${index}`);
        if (warningElement) {
            warningElement.classList.add('hidden');
        }
        
        // Update the data
        window.eventModalState.selectedDates[index] = {
            start_time: `${date}T${startTime}`,
            end_time: `${date}T${endTime}`,
            room_id: roomId,
            equipment: equipment
        };
    }
    
    // Fetch available rooms
    if (date && startTime && endTime) {
        fetchAvailableRooms(index, `${date}T${startTime}`, `${date}T${endTime}`);
        
        // Fetch available equipment if room is selected
        if (roomId) {
            fetchAvailableEquipment(index, `${date}T${startTime}`, `${date}T${endTime}`);
        }
    }
    
    // Update event count
    updateEventCount();
}

// Function to fetch available rooms
async function fetchAvailableRooms(dayIndex, startTime, endTime) {
    try {
        const roomSelect = document.getElementById(`room_${dayIndex}`);
        if (!roomSelect) return;
        
        // Show loading state
        const currentValue = roomSelect.value;
        roomSelect.disabled = true;
        
        // Add loading option if not present
        let loadingOption = roomSelect.querySelector('option[value="loading"]');
        if (!loadingOption) {
            loadingOption = document.createElement('option');
            loadingOption.value = 'loading';
            loadingOption.textContent = '{% trans "Loading available rooms..." %}';
            roomSelect.appendChild(loadingOption);
        }
        
        // Select the loading option
        roomSelect.value = 'loading';
        
        // Get the current event ID if editing
        const eventId = window.eventModalState.currentEventId || '';
        
        // Fetch available rooms
        const response = await fetch('{% url "website:check_room_availability" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                start_time: startTime,
                end_time: endTime,
                event_id: eventId,
                get_available: true  // Special flag to indicate we want all available rooms
            })
        });
        
        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error(data.message || '{% trans "Failed to fetch available rooms" %}');
        }
        
        // Save the current value to restore if possible
        const previousValue = currentValue !== 'loading' ? currentValue : '';
        
        // Clear all options except the first empty one
        while (roomSelect.options.length > 1) {
            roomSelect.remove(1);
        }
        
        // Get available rooms
        const availableRooms = data.available_rooms || [];
        
        if (availableRooms.length === 0) {
            const noRoomsOption = document.createElement('option');
            noRoomsOption.value = '';
            noRoomsOption.textContent = '{% trans "No rooms available at selected time" %}';
            noRoomsOption.disabled = true;
            roomSelect.appendChild(noRoomsOption);
        } else {
            // Add available rooms to the dropdown
            availableRooms.forEach(room => {
                const option = document.createElement('option');
                option.value = room.id;
                option.textContent = room.name;
                roomSelect.appendChild(option);
            });
            
            // Try to restore the previous selection
            if (previousValue && Array.from(roomSelect.options).some(option => option.value === previousValue)) {
                roomSelect.value = previousValue;
            } else {
                roomSelect.value = '';
            }
        }
        
        // Re-enable the select
        roomSelect.disabled = false;
        
    } catch (error) {
        console.error('Error fetching available rooms:', error);
        // Reset the room select to a usable state
        const roomSelect = document.getElementById(`room_${dayIndex}`);
        if (roomSelect) {
            roomSelect.disabled = false;
            roomSelect.value = '';
        }
    }
}

// Function to fetch available equipment
async function fetchAvailableEquipment(dayIndex, startTime, endTime) {
    try {
        const equipmentContainer = document.getElementById(`equipment_container_${dayIndex}`);
        if (!equipmentContainer) return;
        
        // Show loading indicator
        equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Loading available equipment..." %}</span></div>';
        
        // Get the current event ID if editing
        const eventId = window.eventModalState.currentEventId || '';
        
        // Get currently selected room
        const roomSelect = document.getElementById(`room_${dayIndex}`);
        const roomId = roomSelect ? roomSelect.value : null;
        
        if (!roomId) {
            equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "Please select a room first" %}</span></div>';
            return;
        }
        
        // Fetch available equipment
        const response = await fetch('{% url "website:check_equipment_availability" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                start_time: startTime,
                end_time: endTime,
                event_id: eventId
            })
        });
        
        const data = await response.json();
        
        if (data.status !== 'success') {
            throw new Error(data.message || '{% trans "Failed to fetch available equipment" %}');
        }
        
        // Clear equipment container
        equipmentContainer.innerHTML = '';
        
        // Get available equipment
        const availableEquipment = data.available_equipment || [];
        
        if (availableEquipment.length === 0) {
            equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No equipment available at selected time" %}</span></div>';
            return;
        }
        
        // Get previously selected equipment for this day
        const dayData = window.eventModalState.selectedDates[dayIndex] || {};
        const selectedEquipment = dayData.equipment || [];
        
        // Create a map of selected equipment IDs for quick lookup
        const selectedEquipmentMap = {};
        selectedEquipment.forEach(id => {
            selectedEquipmentMap[id] = true;
        });
        
        // Sort equipment: assigned first, then by name
        availableEquipment.sort((a, b) => {
            const aAssigned = selectedEquipmentMap[a.id] || selectedEquipmentMap[String(a.id)];
            const bAssigned = selectedEquipmentMap[b.id] || selectedEquipmentMap[String(b.id)];
            
            if (aAssigned && !bAssigned) return -1;
            if (!aAssigned && bAssigned) return 1;
            return a.name.localeCompare(b.name);
        });
        
        // Create checkboxes for each available equipment
        availableEquipment.forEach(item => {
            const isAssigned = selectedEquipmentMap[item.id] || selectedEquipmentMap[String(item.id)];
            
            const label = document.createElement('label');
            
            // Apply blue background for assigned equipment
            if (isAssigned) {
                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-blue-900/30';
            } else {
                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-gray-900';
            }
            
            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.name = `equipment_${dayIndex}`;
            checkbox.value = item.id;
            checkbox.className = 'form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary';
            checkbox.checked = isAssigned;
            
            const span = document.createElement('span');
            span.className = 'ml-2 text-sm text-white flex items-center';
            span.textContent = `${item.name} (${item.code})`;
            
            // Add "Assigned" badge for already assigned equipment
            if (isAssigned) {
                const assignedBadge = document.createElement('span');
                assignedBadge.className = 'ml-2 px-3 py-1 bg-blue-500 text-xs rounded-md text-white';
                assignedBadge.textContent = 'Assigned';
                span.appendChild(assignedBadge);
            }
            
            label.appendChild(checkbox);
            label.appendChild(span);
            equipmentContainer.appendChild(label);
            
            // Add change event to update selected equipment
            checkbox.addEventListener('change', function() {
                // Make sure the event state is initialized
                if (!window.eventModalState.selectedDates[0]) {
                    window.eventModalState.selectedDates[0] = {
                        equipment: []
                    };
                }
                
                // Get current equipment selections as strings
                let equipment = (window.eventModalState.selectedDates[0].equipment || [])
                    .map(id => String(id));
                
                // Always use string value for consistency
                const equipmentId = String(this.value);
                console.log(`Equipment ${this.checked ? 'checked' : 'unchecked'}: ${equipmentId}`);
                
                if (this.checked) {
                    // Add to equipment list if not already there
                    if (!equipment.includes(equipmentId)) {
                        equipment.push(equipmentId);
                    }
                    
                    // Update badge if needed
                    const parent = this.closest('label');
                    if (parent) {
                        // Add blue background to indicate selection
                        parent.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-blue-900/30';
                        
                        const span = parent.querySelector('span');
                        if (span && !span.querySelector('span')) {
                            const assignedBadge = document.createElement('span');
                            assignedBadge.className = 'ml-2 px-3 py-1 bg-blue-500 text-xs rounded-md text-white';
                            assignedBadge.textContent = 'Assigned';
                            span.appendChild(assignedBadge);
                        }
                    }
                } else {
                    // Remove from equipment list
                    equipment = equipment.filter(id => id !== equipmentId);
                    
                    // Remove badge if needed
                    const parent = this.closest('label');
                    if (parent) {
                        // Restore original background
                        parent.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-gray-900';
                        
                        const span = parent.querySelector('span');
                        if (span) {
                            const badge = span.querySelector('span');
                            if (badge) {
                                badge.remove();
                            }
                        }
                    }
                }
                
                // Update the selected dates
                window.eventModalState.selectedDates[0].equipment = equipment;
                console.log('Updated equipment in state:', equipment);
            });
        });
        
    } catch (error) {
        console.error('Error fetching available equipment:', error);
        const equipmentContainer = document.getElementById(`equipment_container_${dayIndex}`);
        if (equipmentContainer) {
            equipmentContainer.innerHTML = `<div class="text-center py-4 text-red-400">{% trans "Error loading equipment" %}: ${error.message}</div>`;
        }
    }
}

// Function to update event count
function updateEventCount() {
    const eventCount = document.getElementById('eventCount');
    if (eventCount) {
        const validDates = window.eventModalState.selectedDates.filter(date => date && date.start_time && date.end_time).length;
        
        // If we're in edit mode, hide the count or display a different message
        if (window.eventModalState.currentEventId) {
            eventCount.classList.add('hidden');  // Hide the count when editing
        } else {
            eventCount.classList.remove('hidden');  // Show the count when creating
            eventCount.textContent = `${validDates} / ${window.eventModalState.requiredDays} days selected`;
        }
    }
}

// Function to update the conflict summary
function updateEventConflictSummary(conflicts) {
    const summaryDiv = document.getElementById('eventConflictSummary');
    const messageDiv = document.getElementById('eventConflictMessage');
    const eventConflicts = document.getElementById('eventConflicts');
    const sessionConflicts = document.getElementById('sessionConflicts');
    const eventsList = document.getElementById('conflictingEventsList');
    const sessionsList = document.getElementById('conflictingSessionsList');
    
    if (!summaryDiv || !messageDiv) return;
    
    if (conflicts.length > 0) {
        // Show summary
        summaryDiv.classList.remove('hidden');
        
        // Build message
        if (conflicts.length === 1) {
            messageDiv.textContent = `{% trans "Room is not available on Day" %} ${conflicts[0].day} (${conflicts[0].date}) {% trans "from" %} ${conflicts[0].start_time} {% trans "to" %} ${conflicts[0].end_time}`;
        } else {
            messageDiv.textContent = `{% trans "Room is not available on" %} ${conflicts.length} {% trans "days" %}`;
        }
        
        // Build conflict details
        if (eventsList && sessionsList) {
            eventsList.innerHTML = '';
            sessionsList.innerHTML = '';
            
            let hasEvents = false;
            let hasSessions = false;
            
            // Process all conflicts
            conflicts.forEach(conflict => {
                conflict.conflicts.forEach(item => {
                    const li = document.createElement('li');
                    
                    if (item.type === 'event') {
                        hasEvents = true;
                        li.textContent = `${conflict.date} (${conflict.start_time}-${conflict.end_time}): ${item.name}`;
                        eventsList.appendChild(li);
                    } else if (item.type === 'session') {
                        hasSessions = true;
                        li.textContent = `${conflict.date} (${conflict.start_time}-${conflict.end_time}): ${item.course_name} with ${item.trainer_name}`;
                        sessionsList.appendChild(li);
                    }
                });
            });
            
            // Show/hide conflict sections
            if (eventConflicts && sessionConflicts) {
                eventConflicts.classList.toggle('hidden', !hasEvents);
                sessionConflicts.classList.toggle('hidden', !hasSessions);
            }
        }
        
        // Disable save button
        const saveButton = document.getElementById('saveEventBtn');
        if (saveButton) {
            saveButton.disabled = true;
        }
    } else {
        // Hide summary
        summaryDiv.classList.add('hidden');
        
        // Enable save button if we have the required number of dates
        const validDates = window.eventModalState.selectedDates.filter(date => date && date.start_time && date.end_time).length;
        const saveButton = document.getElementById('saveEventBtn');
        
        if (saveButton) {
            saveButton.disabled = validDates !== window.eventModalState.requiredDays;
        }
    }
}

// Function to handle event save
async function handleEventSave() {
    try {
        if (window.eventModalState.isSubmitting) {
            console.warn('Save already in progress');
            return;
        }
        
        window.eventModalState.isSubmitting = true;
        
        const nameInput = document.getElementById('name');
        const eventTypeSelect = document.getElementById('event_type');
        const capacityInput = document.getElementById('capacity');
        const descriptionInput = document.getElementById('description');
        
        // Validate required fields
        if (!nameInput.value) {
            throw new Error('{% trans "Please enter an event name" %}');
        }
        
        if (!eventTypeSelect.value) {
            throw new Error('{% trans "Please select an event type" %}');
        }
        
        if (!capacityInput.value || parseInt(capacityInput.value) < 1) {
            throw new Error('{% trans "Please enter a valid capacity" %}');
        }
        
        const name = nameInput.value;
        const eventTypeId = eventTypeSelect.value;
        const capacity = parseInt(capacityInput.value);
        const description = descriptionInput.value;
        
        // Validate event dates
        const validDates = window.eventModalState.selectedDates.filter(date => 
            date && date.start_time && date.end_time);
        
        if (validDates.length !== window.eventModalState.requiredDays) {
            throw new Error(`{% trans "Please select dates and times for all" %} ${window.eventModalState.requiredDays} {% trans "days" %}`);
        }
        
        // Validate that room and equipment is selected for each day
        for (let i = 0; i < validDates.length; i++) {
            if (!validDates[i].room_id) {
                throw new Error(`{% trans "Please select a room for Day" %} ${i+1}`);
            }
        }
        
        // Check for any visible warning indicators
        const warningElements = document.querySelectorAll('[id^="event_conflict_warning_"]:not(.hidden)');
        if (warningElements.length > 0) {
            throw new Error('{% trans "Cannot save event with warnings. Please resolve all issues first." %}');
        }
        
        // Update save button
        const saveButton = document.getElementById('saveEventBtn');
        if (saveButton) {
            saveButton.disabled = true;
            saveButton.textContent = '{% trans "Saving..." %}';
        }
        
        // Get CSRF token
        const csrfToken = getCsrfToken();
        if (!csrfToken) {
            throw new Error('{% trans "CSRF token not found" %}');
        }
        
        // Create an array to store responses
        const responses = [];
        let hasError = false;
        
        // If editing a single day event, just update that one
        if (window.eventModalState.currentEventId && window.eventModalState.requiredDays === 1) {
            // Get equipment directly from checked checkboxes - the most reliable way
            const selectedEquipment = Array.from(document.querySelectorAll('input[name="equipment_0"]:checked'))
                .filter(checkbox => !checkbox.disabled) // Exclude disabled (unavailable) equipment
                .map(checkbox => checkbox.value);
                
            console.log('Selected equipment from checkboxes:', selectedEquipment);
            
            // Include the event ID for updates
            const eventData = {
                name: name,
                event_type: eventTypeId,
                room: validDates[0].room_id,
                capacity: capacity,
                description: description,
                equipment: selectedEquipment,
                event_id: window.eventModalState.currentEventId,
                start_time: validDates[0].start_time,
                end_time: validDates[0].end_time
            };
            
            console.log('Sending event data to server:', eventData);
            
            // Send request
            try {
                const response = await fetch('{% url "website:create_event" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': csrfToken
                    },
                    body: JSON.stringify(eventData)
                });
                
                const data = await response.json();
                responses.push(data);
                
                if (data.status !== 'success') {
                    hasError = true;
                }
            } catch (error) {
                hasError = true;
                responses.push({
                    status: 'error',
                    message: error.message || '{% trans "Failed to update event" %}'
                });
            }
        } else {
            // For new events or multi-day events, create separate events for each day
            for (let i = 0; i < validDates.length; i++) {
                // Get selected equipment for this day
                const selectedEquipment = Array.from(document.querySelectorAll(`input[name="equipment_${i}"]:checked`))
                    .map(checkbox => parseInt(checkbox.value));
                
                console.log(`Selected equipment for day ${i+1}:`, selectedEquipment);
                
                // Create an event for this day with its own room and equipment
                const eventData = {
                    name: window.eventModalState.requiredDays > 1 ? `${name} - Day ${i+1}` : name,
                    event_type: eventTypeId,
                    room: validDates[i].room_id,
                    capacity: capacity,
                    description: description,
                    equipment: selectedEquipment.length > 0 ? selectedEquipment : [], // Use checkbox data directly
                    start_time: validDates[i].start_time,
                    end_time: validDates[i].end_time
                };
                
                // Send request
                try {
                    const response = await fetch('{% url "website:create_event" %}', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRFToken': csrfToken
                        },
                        body: JSON.stringify(eventData)
                    });
                    
                    const data = await response.json();
                    responses.push(data);
                    
                    if (data.status !== 'success') {
                        hasError = true;
                    }
                } catch (error) {
                    hasError = true;
                    responses.push({
                        status: 'error',
                        message: error.message || '{% trans "Failed to create event" %}'
                    });
                }
            }
        }
        
        // Process responses
        if (hasError) {
            // Show the first error message
            for (const response of responses) {
                if (response.status !== 'success') {
                    showEventError(response.message || '{% trans "Failed to save event" %}');
                    break;
                }
            }
        } else {
            // Show success message
            showEventSuccess(window.eventModalState.requiredDays > 1 ? 
                '{% trans "All events created successfully" %}' : 
                '{% trans "Event saved successfully" %}');
            
            // Reload page after a delay
                        setTimeout(() => {
                window.location.reload();
            }, 1500);
        }
                        
                    } catch (error) {
        console.error('Error in handleEventSave:', error);
        showEventError(error.message || '{% trans "An error occurred while saving the event" %}');
    } finally {
        window.eventModalState.isSubmitting = false;
        
        // Reset save button
        const saveButton = document.getElementById('saveEventBtn');
                if (saveButton) {
                    saveButton.disabled = false;
                    saveButton.textContent = '{% trans "Save" %}';
                }
    }
}

// Function to load event data for editing
async function loadEventData(eventId) {
    try {
        console.log('Loading event data for ID:', eventId);
        
        if (!eventId) {
            console.error('Missing event ID');
            throw new Error('{% trans "Missing event ID" %}');
        }
        
        // Show loading indicator in the form
        const nameInput = document.getElementById('name');
        if (nameInput) {
            nameInput.value = '{% trans "Loading..." %}';
            nameInput.disabled = true;
        }
        
        // Clear the event dates container once to avoid repeated DOM operations
        const container = document.getElementById('eventDates');
        if (container) {
            container.innerHTML = '<div class="p-4 text-center"><div class="inline-block animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-primary"></div><div class="mt-2 text-gray-400">{% trans "Loading event data..." %}</div></div>';
        }
        
        // Call API to get event data with all related information in one request
        console.log('Fetching event data from:', `{% url "website:create_event" %}?event_id=${eventId}&include_all=true`);
        const response = await fetch(`{% url "website:create_event" %}?event_id=${eventId}&include_all=true`);
        
        if (!response.ok) {
            throw new Error(`{% trans "HTTP error! status:" %} ${response.status}`);
        }
        
        console.log('Received response, parsing JSON');
        const data = await response.json();
        
        console.log('Response data:', data);
        
        if (data.status === 'success') {
            const event = data.event;
            console.log('Event data:', event);
            
            // Set form values synchronously
            if (nameInput) {
                nameInput.value = event.name || '';
                nameInput.disabled = false;
            }
            
            // Set other basic form values
            const eventTypeSelect = document.getElementById('event_type');
            const capacityInput = document.getElementById('capacity');
            const descriptionInput = document.getElementById('description');
            
            if (eventTypeSelect) eventTypeSelect.value = event.event_type || '';
            if (capacityInput) capacityInput.value = event.capacity || '';
            if (descriptionInput) descriptionInput.value = event.description || '';
            
            // In edit mode, we only handle one day
            window.eventModalState.requiredDays = 1;
            
            // Hide the days selection when editing
            const daysSelectContainer = document.getElementById('event_days_container');
            if (daysSelectContainer) {
                daysSelectContainer.classList.add('hidden');
            }
            
            // Change the title of the dates section to make it clear we're editing a single event
            const eventDatesTitle = document.querySelector('#eventDatesContainer h4');
            if (eventDatesTitle) {
                eventDatesTitle.textContent = '{% trans "Event Date" %}';
            }
            
            // Set the modal title
            const modalTitle = document.getElementById('modalTitle');
            if (modalTitle) {
                console.log('Setting modal title to Edit Event (from loadEventData)');
                modalTitle.textContent = '{% trans "Edit Event" %}';
            }
            
            // Set event days select to 1 (as a fallback)
            const daysSelect = document.getElementById('event_days');
            if (daysSelect) {
                daysSelect.value = '1';
                daysSelect.disabled = true; // Disable changing days in edit mode
            }
            
            // Update dates UI once
            updateEventDatesUI();
            
            // Parse the start and end times
            if (!event.start_time || !event.end_time) {
                throw new Error('Missing start_time or end_time in event data');
            }
            
            // Pre-populate the available rooms in the window state
            if (data.available_rooms) {
                window.eventModalState.availableRooms = data.available_rooms;
            }
            
            // Pre-populate the available equipment in the window state
            if (data.available_equipment) {
                window.eventModalState.availableEquipment = data.available_equipment;
            }
            
            // Create a timeout to allow the DOM to update
            setTimeout(async () => {
                try {
                    // Find all necessary date and time fields
                    const dateInput = document.getElementById('event_date_0');
                    let startTimeInput = document.getElementById('start_time_0');
                    let endTimeInput = document.getElementById('end_time_0');
                    let roomSelect = document.getElementById('room_0');
                    
                    // If date input exists but time selects don't, create them
                    if (dateInput && (!startTimeInput || !endTimeInput)) {
                        console.log('Creating missing time fields');
                        // Find the parent container
                        const dateDiv = dateInput.closest('.event-date-pair');
                        if (dateDiv) {
                            // Create or find time inputs container
                            let timeInputsContainer = dateDiv.querySelector('.flex.space-x-2');
                            if (!timeInputsContainer) {
                                timeInputsContainer = document.createElement('div');
                                timeInputsContainer.className = 'flex space-x-2';
                                // Insert after date input container
                                const dateInputContainer = dateInput.closest('div');
                                if (dateInputContainer) {
                                    dateDiv.insertBefore(timeInputsContainer, dateInputContainer.nextSibling);
                                } else {
                                    dateDiv.appendChild(timeInputsContainer);
                                }
                            }
                            
                            // Create start time if missing
                            if (!startTimeInput) {
                                const startTimeContainer = document.createElement('div');
                                startTimeContainer.className = 'flex-1';
                                
                                const startTimeLabel = document.createElement('label');
                                startTimeLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
                                startTimeLabel.textContent = '{% trans "Start Time" %}';
                                startTimeLabel.htmlFor = 'start_time_0';
                                
                                startTimeInput = document.createElement('input');
                                startTimeInput.type = 'time';
                                startTimeInput.name = 'start_time_0';
                                startTimeInput.id = 'start_time_0';
                                startTimeInput.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
                                startTimeInput.required = true;
                                startTimeInput.value = '09:00';
                                
                                startTimeContainer.appendChild(startTimeLabel);
                                startTimeContainer.appendChild(startTimeInput);
                                timeInputsContainer.appendChild(startTimeContainer);
                            }
                            
                            // Create end time if missing
                            if (!endTimeInput) {
                                const endTimeContainer = document.createElement('div');
                                endTimeContainer.className = 'flex-1';
                                
                                const endTimeLabel = document.createElement('label');
                                endTimeLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
                                endTimeLabel.textContent = '{% trans "End Time" %}';
                                endTimeLabel.htmlFor = 'end_time_0';
                                
                                endTimeInput = document.createElement('input');
                                endTimeInput.type = 'time';
                                endTimeInput.name = 'end_time_0';
                                endTimeInput.id = 'end_time_0';
                                endTimeInput.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
                                endTimeInput.required = true;
                                endTimeInput.value = '16:00';
                                
                                endTimeContainer.appendChild(endTimeLabel);
                                endTimeContainer.appendChild(endTimeInput);
                                timeInputsContainer.appendChild(endTimeContainer);
                            }
                        }
                    }
                    
                    // If we still don't have the critical fields, throw an error
                    if (!dateInput || !startTimeInput || !endTimeInput) {
                        throw new Error('Failed to find or create date/time fields');
                    }
                    
                    // Parse dates
                    console.log('Parsing dates:', event.start_time, event.end_time);
                    const startDate = new Date(event.start_time);
                    const endDate = new Date(event.end_time);
                    
                    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                        throw new Error('Invalid date format in event data');
                    }
                    
                    // Get date string in YYYY-MM-DD format
                    const dateStr = startDate.toISOString().split('T')[0];
                    
                    // Format times (HH:MM)
                    const startHour = startDate.getHours().toString().padStart(2, '0');
                    const startMinute = startDate.getMinutes().toString().padStart(2, '0');
                    const endHour = endDate.getHours().toString().padStart(2, '0');
                    const endMinute = endDate.getMinutes().toString().padStart(2, '0');
                    
                    const startTimeStr = `${startHour}:${startMinute}`;
                    const endTimeStr = `${endHour}:${endMinute}`;
                    
                    console.log('Formatted times:', startTimeStr, endTimeStr);
                    
                    // Set the date and time values
                    dateInput.value = dateStr;
                    
                    // Set time values with fallbacks to nearest options
                    if (startTimeInput) {
                        const startOptions = Array.from(startTimeInput.options);
                        if (startOptions.length > 0) {
                            const bestStartOption = startOptions.reduce((prev, curr) => {
                                const prevDiff = Math.abs(timeStrToMinutes(prev.value) - timeStrToMinutes(startTimeStr));
                                const currDiff = Math.abs(timeStrToMinutes(curr.value) - timeStrToMinutes(startTimeStr));
                                return prevDiff < currDiff ? prev : curr;
                            });
                            startTimeInput.value = bestStartOption.value;
                        } else {
                            console.warn('No options found in startTimeInput, using default');
                            startTimeInput.value = '09:00';
                        }
                    }
                    
                    if (endTimeInput) {
                        const endOptions = Array.from(endTimeInput.options);
                        if (endOptions.length > 0) {
                            const bestEndOption = endOptions.reduce((prev, curr) => {
                                const prevDiff = Math.abs(timeStrToMinutes(prev.value) - timeStrToMinutes(endTimeStr));
                                const currDiff = Math.abs(timeStrToMinutes(curr.value) - timeStrToMinutes(endTimeStr));
                                return prevDiff < currDiff ? prev : curr;
                            });
                            endTimeInput.value = bestEndOption.value;
                        } else {
                            console.warn('No options found in endTimeInput, using default');
                            endTimeInput.value = '16:00';
                        }
                    }
                    
                    // Set up or create the room dropdown
                    if (!roomSelect) {
                        // Find container to create room select
                        const dateDiv = dateInput.closest('.event-date-pair');
                        if (dateDiv) {
                            const roomContainer = document.createElement('div');
                            roomContainer.className = 'mt-3';
                            
                            const roomLabel = document.createElement('label');
                            roomLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
                            roomLabel.textContent = '{% trans "Room" %}';
                            roomLabel.htmlFor = 'room_0';
                            
                            roomSelect = document.createElement('select');
                            roomSelect.name = 'room_0';
                            roomSelect.id = 'room_0';
                            roomSelect.className = 'block w-full px-3 py-2 text-sm border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary rounded-md';
                            roomSelect.required = true;
                            
                            roomContainer.appendChild(roomLabel);
                            roomContainer.appendChild(roomSelect);
                            dateDiv.appendChild(roomContainer);
                        }
                    }
                    
                    // Populate room options directly with data from the server
                    if (roomSelect) {
                        roomSelect.innerHTML = '<option value="">{% trans "Select a Room" %}</option>';
                        
                        // Add all available rooms from the pre-populated state
                        if (window.eventModalState.availableRooms) {
                            window.eventModalState.availableRooms.forEach(room => {
                                const option = document.createElement('option');
                                option.value = room.id;
                                option.textContent = room.name;
                                roomSelect.appendChild(option);
                            });
                        }
                        
                        // Add the current room if not in available list
                        if (event.room && (!window.eventModalState.availableRooms || 
                            !window.eventModalState.availableRooms.some(r => r.id == event.room))) {
                            const option = document.createElement('option');
                            option.value = event.room;
                            option.textContent = `${event.room_name || 'Room'} (Currently booked)`;
                            option.className = 'text-yellow-500';
                            roomSelect.appendChild(option);
                        }
                        
                        // Set the room value
                        roomSelect.value = event.room || '';
                    }
                    
                    // Create and populate equipment selection
                    const dateDiv = dateInput.closest('.event-date-pair');
                    if (dateDiv) {
                        // Find or create equipment container
                        let equipmentContainer = document.getElementById('equipment_container_0');
                        if (!equipmentContainer) {
                            const equipmentSection = document.createElement('div');
                            equipmentSection.className = 'mt-3';
                            
                            const equipmentLabel = document.createElement('label');
                            equipmentLabel.className = 'block text-xs font-medium text-gray-500 mb-1';
                            equipmentLabel.textContent = '{% trans "Required Equipment" %}';
                            equipmentLabel.htmlFor = 'equipment_0';
                            
                            equipmentContainer = document.createElement('div');
                            equipmentContainer.className = 'mt-1 max-h-32 overflow-y-auto border border-gray-600 rounded-md bg-gray-900 p-2';
                            equipmentContainer.id = 'equipment_container_0';
                            
                            equipmentSection.appendChild(equipmentLabel);
                            equipmentSection.appendChild(equipmentContainer);
                            dateDiv.appendChild(equipmentSection);
                        }
                        
                        // Clear and populate equipment options directly
                        equipmentContainer.innerHTML = '';
                        
                        if (window.eventModalState.availableEquipment && window.eventModalState.availableEquipment.length > 0) {
                            // Create checkboxes for each available equipment
                            window.eventModalState.availableEquipment.forEach(item => {
                                const label = document.createElement('label');
                                label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-gray-900';
                                
                                const checkbox = document.createElement('input');
                                checkbox.type = 'checkbox';
                                checkbox.name = 'equipment_0';
                                checkbox.value = item.id;
                                checkbox.className = 'form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary';
                                
                                const span = document.createElement('span');
                                span.className = 'ml-2 text-sm text-white flex items-center';
                                span.textContent = `${item.name} (${item.code})`;
                                
                                // Check if this equipment was previously selected
                                let isPreSelected = false;
                                
                                // Log the equipment data for debugging
                                console.log('Checking equipment:', item.id, 'against event.equipment:', event.equipment);
                                console.log('And against event.required_equipment:', event.required_equipment);
                                
                                // Convert item ID to both string and number for reliable comparison
                                const itemIdString = String(item.id);
                                const itemIdNumber = parseInt(item.id, 10);
                                
                                // Check in event.equipment array (handles both string and number formats)
                                if (event.equipment && Array.isArray(event.equipment)) {
                                    isPreSelected = event.equipment.some(id => 
                                        String(id) === itemIdString || 
                                        (Number.isInteger(id) && id === itemIdNumber));
                                }
                                
                                // Also check in event.required_equipment array as fallback
                                if (!isPreSelected && event.required_equipment && Array.isArray(event.required_equipment)) {
                                    isPreSelected = event.required_equipment.some(id => 
                                        String(id) === itemIdString || 
                                        (Number.isInteger(id) && id === itemIdNumber) ||
                                        (id.equipment_id && (String(id.equipment_id) === itemIdString || id.equipment_id === itemIdNumber)));
                                }
                                
                                if (isPreSelected) {
                                    checkbox.checked = true;
                                    
                                    // Add "Assigned" badge with blue background for assigned equipment
                                    const assignedBadge = document.createElement('span');
                                    assignedBadge.className = 'ml-2 px-3 py-1 bg-blue-500 text-xs rounded-md text-white';
                                    assignedBadge.textContent = 'Assigned';
                                    span.appendChild(assignedBadge);
                                    
                                    // Add blue background for the entire row to match session modal
                                    label.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-blue-900/30';
                                    
                                    // Log that we found and selected this equipment
                                    console.log('Successfully pre-selected equipment item:', item.id, item.name);
                                }
                                
                                label.appendChild(checkbox);
                                label.appendChild(span);
                                equipmentContainer.appendChild(label);
                                
                                // Add change event to update selected equipment
                                checkbox.addEventListener('change', function() {
                                    // Make sure the event state is initialized
                                    if (!window.eventModalState.selectedDates[0]) {
                                        window.eventModalState.selectedDates[0] = {
                                            equipment: []
                                        };
                                    }
                                    
                                    // Get current equipment selections as strings
                                    let equipment = (window.eventModalState.selectedDates[0].equipment || [])
                                        .map(id => String(id));
                                    
                                    // Always use string value for consistency
                                    const equipmentId = String(this.value);
                                    console.log(`Equipment ${this.checked ? 'checked' : 'unchecked'}: ${equipmentId}`);
                                    
                                    if (this.checked) {
                                        // Add to equipment list if not already there
                                        if (!equipment.includes(equipmentId)) {
                                            equipment.push(equipmentId);
                                        }
                                        
                                        // Update badge if needed
                                        const parent = this.closest('label');
                                        if (parent) {
                                            // Add blue background to indicate selection
                                            parent.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-blue-900/30';
                                            
                                            const span = parent.querySelector('span');
                                            if (span && !span.querySelector('span')) {
                                                const assignedBadge = document.createElement('span');
                                                assignedBadge.className = 'ml-2 px-3 py-1 bg-blue-500 text-xs rounded-md text-white';
                                                assignedBadge.textContent = 'Assigned';
                                                span.appendChild(assignedBadge);
                                            }
                                        }
                                    } else {
                                        // Remove from equipment list
                                        equipment = equipment.filter(id => id !== equipmentId);
                                        
                                        // Remove badge if needed
                                        const parent = this.closest('label');
                                        if (parent) {
                                            // Restore original background
                                            parent.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-800 p-2 rounded-md bg-gray-900';
                                            
                                            const span = parent.querySelector('span');
                                            if (span) {
                                                const badge = span.querySelector('span');
                                                if (badge) {
                                                    badge.remove();
                                                }
                                            }
                                        }
                                    }
                                    
                                    // Update the selected dates
                                    window.eventModalState.selectedDates[0].equipment = equipment;
                                    console.log('Updated equipment in state:', equipment);
                                });
                            });
                            
                            // BUGFIX: Initialize the equipment data in selectedDates when editing
                            if (event.equipment && event.equipment.length > 0) {
                                // Convert all equipment IDs to strings for consistency and proper comparison
                                const normalizedEquipment = event.equipment.map(id => String(id));
                                console.log('Converting equipment IDs to strings:', normalizedEquipment);
                                
                                // Ensure we have the date entry in selectedDates
                                if (!window.eventModalState.selectedDates[0]) {
                                    window.eventModalState.selectedDates[0] = {};
                                }
                                
                                // Store the equipment IDs as strings for consistency
                                window.eventModalState.selectedDates[0].equipment = normalizedEquipment;
                                
                                console.log('Initialized equipment in eventModalState:', normalizedEquipment);
                                
                                // Also initialize any missing properties if needed
                                if (!window.eventModalState.selectedDates[0].start_time && event.start_time) {
                                    window.eventModalState.selectedDates[0].start_time = event.start_time;
                                }
                                
                                if (!window.eventModalState.selectedDates[0].end_time && event.end_time) {
                                    window.eventModalState.selectedDates[0].end_time = event.end_time;
                                }
                                
                                if (!window.eventModalState.selectedDates[0].room_id && event.room) {
                                    window.eventModalState.selectedDates[0].room_id = event.room;
                                }
                            }
                            
                            // Add any missing equipment as disabled options
                            if (event.equipment && event.equipment.length > 0) {
                                const availableEquipmentIds = window.eventModalState.availableEquipment.map(eq => parseInt(eq.id));
                                const missingEquipment = event.equipment.filter(id => !availableEquipmentIds.includes(parseInt(id)));
                                
                                if (missingEquipment.length > 0) {
                                    console.log('Adding missing equipment:', missingEquipment);
                                    
                                    const unavailableContainer = document.createElement('div');
                                    unavailableContainer.className = 'mt-4 border-t border-gray-600 pt-2';
                                    
                                    const unavailableHeading = document.createElement('div');
                                    unavailableHeading.className = 'text-xs font-medium text-yellow-500 mb-2';
                                    unavailableHeading.textContent = '{% trans "Currently unavailable equipment:" %}';
                                    unavailableContainer.appendChild(unavailableHeading);
                                    
                                    // Add each missing equipment item
                                    missingEquipment.forEach(equipId => {
                                        const equipName = event.equipment_names?.find(e => e.id === parseInt(equipId))?.name || `ID: ${equipId}`;
                                        
                                        const label = document.createElement('label');
                                        label.className = 'inline-flex items-center w-full p-2 rounded-md bg-yellow-900/30';
                                        
                                        const checkbox = document.createElement('input');
                                        checkbox.type = 'checkbox';
                                        checkbox.name = 'equipment_0';
                                        checkbox.value = equipId;
                                        checkbox.className = 'form-checkbox h-4 w-4 text-yellow-500 border-gray-600 rounded bg-gray-700';
                                        checkbox.checked = true;
                                        checkbox.disabled = true;
                                        
                                        const span = document.createElement('span');
                                        span.className = 'ml-2 text-sm text-yellow-500 flex items-center';
                                        span.textContent = equipName;
                                        
                                        // Add a warning badge
                                        const warningBadge = document.createElement('span');
                                        warningBadge.className = 'ml-2 px-3 py-1 bg-yellow-500 text-xs rounded-md text-white';
                                        warningBadge.textContent = 'Unavailable';
                                        span.appendChild(warningBadge);
                                        
                                        label.appendChild(checkbox);
                                        label.appendChild(span);
                                        unavailableContainer.appendChild(label);
                                    });
                                    
                                    equipmentContainer.appendChild(unavailableContainer);
                                }
                            }
                            
                        } else {
                            equipmentContainer.innerHTML = '<div class="text-center py-4"><span class="text-gray-400">{% trans "No equipment available" %}</span></div>';
                        }
                    }
                    
                    // Store the selected date and times in the state
                    window.eventModalState.selectedDates[0] = {
                        start_time: `${dateStr}T${startTimeInput.value}`,
                        end_time: `${dateStr}T${endTimeInput.value}`,
                        room_id: event.room,
                        equipment: Array.isArray(event.equipment) 
                            ? event.equipment.map(id => String(id)) 
                            : []
                    };
                    
                    console.log('Initialized selectedDates with equipment:', window.eventModalState.selectedDates[0].equipment);
                    
                    // Update the event count
                    updateEventCount();
                    console.log('Event data loaded successfully');
                    
                } catch (error) {
                    console.error('Error setting up form fields:', error);
                    showEventError(`{% trans "Error setting up form:" %} ${error.message}`);
                }
            }, 100); // Shorter timeout since we're doing everything in one go
            
        } else {
            console.error('Error in API response:', data.message);
            showEventError(data.message || '{% trans "Failed to load event data" %}');
        }
    } catch (error) {
        console.error('Error loading event:', error);
        // Re-enable the name input if it was disabled
        const nameInput = document.getElementById('name');
        if (nameInput && nameInput.disabled) {
            nameInput.disabled = false;
            nameInput.value = '';
        }
        showEventError(error.message || '{% trans "An error occurred while loading the event" %}');
    }
}

// Utility function to get CSRF token
function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    return csrfInput ? csrfInput.value : '';
}

// Utility function to format time from ISO string
function formatTime(isoString) {
    try {
        const date = new Date(isoString);
        return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } catch (error) {
        console.error('Error formatting time:', error);
        return isoString;
    }
}

// Utility function to convert time string (HH:MM) to minutes since midnight
function timeStrToMinutes(timeStr) {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
}

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded - initializing event modal');
    
    // Add event listener for opening event modal
    document.addEventListener('click', function(e) {
        if (e.target.matches('[data-action="open-event-modal"]')) {
            // Dispatch the custom event instead of calling the function directly
            document.dispatchEvent(new CustomEvent('open-new-event'));
        } else if (e.target.matches('[data-action="close-event-modal"]')) {
            closeEventModal();
        }
    });

    // Initialize the modal
    initEventModal();
    
    // Assign functions to window object for global access
    // This is important: we're overriding any existing functions to ensure
    // our implementation is the one that runs when these functions are called
    window.openEventModal = openEventModal;
    window.closeEventModal = closeEventModal;
    window.handleEventSave = handleEventSave;
    
    // Check if the event modal exists in the DOM
    const eventModal = document.getElementById('eventModal');
    if (!eventModal) {
        console.error('Critical Error: Event modal element not found in the DOM!');
    } else {
        console.log('Event modal found in DOM');
    }
    
    // Check if essential elements exist
    const nameInput = document.getElementById('name');
    if (!nameInput) {
        console.error('Warning: Name input field not found on page load');
    } else {
        console.log('Name input field found successfully');
    }
    
    // Verify other essential elements
    const modalTitle = document.getElementById('modalTitle');
    const eventForm = document.getElementById('eventForm');
    const saveBtn = document.getElementById('saveEventBtn');
    
    if (!modalTitle) console.error('Critical: Modal title element not found');
    if (!eventForm) console.error('Critical: Event form element not found');
    if (!saveBtn) console.error('Critical: Save button not found');
});
</script> 