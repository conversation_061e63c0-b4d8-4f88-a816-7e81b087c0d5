{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-900 to-gray-900">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <!-- Profile Header -->
            <div class="relative h-32 bg-primary">
                <div class="absolute -bottom-16 left-8">
                    <div class="h-32 w-32 rounded-full bg-white p-1">
                        <div class="h-full w-full rounded-full bg-primary flex items-center justify-center text-white text-4xl">
                            {{ request.user.username|first|upper }}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile Content -->
            <div class="mt-20 px-8 pb-8">
                <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                    <!-- Left Column - User Info -->
                    <div class="md:col-span-2 space-y-6">
                        <div>
                            <h2 class="text-2xl font-bold text-white mb-4">{% trans "Profile Information" %}</h2>
                            <div class="bg-white/5 rounded-lg p-6 space-y-4">
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Username" %}</label>
                                    <p class="mt-1 text-white">{{ request.user.username }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Email" %}</label>
                                    <p class="mt-1 text-white">{{ request.user.email }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Role" %}</label>
                                    <p class="mt-1 text-white">
                                        {% if request.user.is_staff %}
                                            {% trans "Administrator" %}
                                        {% elif request.user.user_type.code == 'TRAINER' %}
                                            {% trans "Trainer" %}
                                        {% elif request.user.user_type.code == 'CORPORATE' %}
                                            {% trans "Corporate User" %}
                                        {% elif request.user.user_type.code == 'EXTERNAL_INDIVIDUAL' %}
                                            {% trans "Student" %}
                                        {% else %}
                                            {{ request.user.user_type.name_en }}
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h2 class="text-2xl font-bold text-white mb-4">{% trans "Activity" %}</h2>
                            <div class="bg-white/5 rounded-lg p-6">
                                <div class="text-center text-white/70 py-8">
                                    {% trans "No recent activity" %}
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Quick Actions -->
                    <div>
                        <h2 class="text-2xl font-bold text-white mb-4">{% trans "Quick Actions" %}</h2>
                        <div class="bg-white/5 rounded-lg p-6 space-y-4">
                            <a href="{% url 'website:settings' %}" class="block w-full px-4 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md text-center">
                                {% trans "Edit Profile" %}
                            </a>
                            <a href="#" class="block w-full px-4 py-2 text-sm font-medium text-white bg-white/10 hover:bg-white/20 rounded-md text-center">
                                {% trans "View Certificates" %}
                            </a>
                            <a href="#" class="block w-full px-4 py-2 text-sm font-medium text-white bg-white/10 hover:bg-white/20 rounded-md text-center">
                                {% trans "My Courses" %}
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 