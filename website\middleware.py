from django.shortcuts import redirect
from django.urls import resolve, reverse
from django.utils.deprecation import MiddlewareMixin
from django.contrib import messages
from django.utils.translation import gettext_lazy as _

class ForcePasswordResetMiddleware(MiddlewareMixin):
    """
    Middleware to force password reset on first login for newly created users.
    Redirects users to the password reset page if their force_password_reset flag is True.
    """
    
    def process_request(self, request):
        # Exempt certain URLs from redirection
        exempt_urls = [
            reverse('website:force_password_reset'),
            reverse('website:logout'),
            '/admin/',  # Admin URLs
            '/static/',  # Static files
            '/media/',   # Media files
        ]
        
        # Skip if user is not authenticated
        if not request.user.is_authenticated:
            return None
        
        # Check if current URL is exempt
        current_path = request.path_info
        for url in exempt_urls:
            if current_path.startswith(url):
                return None
        
        # If user has force_password_reset flag set to True, redirect to password reset page
        if hasattr(request.user, 'force_password_reset') and request.user.force_password_reset:
            messages.info(request, _('You must change your password before continuing.'))
            return redirect('website:force_password_reset') 
        
class AdminITGroupMiddleware(MiddlewareMixin):
    """
    Middleware to restrict admin access to users in the IT group.
    """
    def process_request(self, request):
        # Skip for non-authenticated users and for the password reset page itself
        if not request.user.is_authenticated or request.path.startswith('/force-password-reset/'):
            return None
        
        if request.path.startswith('/dzJAMvwB/') and not request.path.startswith('/dzJAMvwB/login/'):
            # i want to check if the user is staff or 
            if request.user.is_staff:
                return None
            else:
                messages.error(request, _('Only staff members can access the admin panel.'))
                return redirect('website:main')

        
        
        
        # Check if we're accessing the admin site
        # if request.path.startswith('/dzJAMvwB/') and not request.path.startswith('/dzJAMvwB/login/'):
        #     # Allow if user is in IT group 
        #     if "IT" in [group.name for group in request.user.groups.all()]:
        #         return None
            
        #     # User is not in IT group - redirect to main page with error message
        #     messages.error(request, _('Only IT group members can access the admin panel.'))
        #     return redirect('website:main')
            
        return None 
    