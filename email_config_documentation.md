# Email Configuration Documentation

## Overview

This documentation describes the email configuration and functionality in the Survey application. The application uses custom email backend settings to handle SSL/TLS connections and includes various email-related features for survey distribution and reminders.

## Email Backend Configuration

The application uses a custom email backend that extends Django's standard `SMTPBackend` to handle SSL/TLS connections securely.

### Custom Email Backend (`core/email_backend.py`)

```python
import ssl
from django.core.mail.backends.smtp import EmailBackend as SMTPBackend
from django.utils.functional import cached_property

class EmailBackend(SMTPBackend):
    @cached_property
    def ssl_context(self):
        if self.ssl_certfile or self.ssl_keyfile:
            ssl_context = ssl.SSLContext(protocol=ssl.PROTOCOL_TLS_CLIENT)
            ssl_context.load_cert_chain(self.ssl_certfile, self.ssl_keyfile)
            return ssl_context
        else:
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            return ssl_context
```

This customized backend:
- Extends Django's built-in SMTP email backend
- Modifies SSL context handling
- Disables hostname verification and certificate validation when no certfile/keyfile is provided
- Allows for secure email transmission even with self-signed certificates

## Email Configuration in Django Settings

To use this custom email backend, you must configure the following in your `settings.py`:

```python
# Example settings.py configuration
EMAIL_BACKEND = 'core.email_backend.EmailBackend'
EMAIL_HOST = 'your-smtp-server.example.com'
EMAIL_PORT = 587  # Typically 587 for TLS, 465 for SSL
EMAIL_USE_TLS = True  # or False, depending on your SMTP server
EMAIL_HOST_USER = '<EMAIL>'
EMAIL_HOST_PASSWORD = 'your-password'
DEFAULT_FROM_EMAIL = 'your-display-name <<EMAIL>>'
```

## Email-Related Models

### Employee Model

The `Employee` model contains email-related fields:

```python
class Employee(models.Model):
    Name = models.CharField(max_length=255)
    Department = models.ForeignKey(Department, on_delete=models.CASCADE)
    Email = models.EmailField(unique=True)
    cc_email = models.TextField(null=True, blank=True, help_text="Enter multiple email addresses separated by commas")
    # ... other fields
```

### Survey Model

The `Survey` model contains cc_email fields for additional notification recipients:

```python
class Survey(models.Model):
    Name = models.CharField(max_length=255)
    # ... other fields
    cc_email = models.TextField(null=True, blank=True, help_text="Enter multiple email addresses separated by commas")
    # ... more fields
```

## Email Sending Functionality

The application has several methods for sending emails:

### 1. Survey Email Sending (`send_survey_email`)

Sends invitation emails to employees for a specific survey:

```python
def send_survey_email(request, survey_id):
    survey = get_object_or_404(Survey, pk=survey_id)
    # ... code to retrieve survey details
    
    for employee in survey.AllowedEmployees.all():
        cc_emails = [email.strip() for email in employee.cc_email.split(',')] if employee.cc_email else []
        # Create personalized survey link
        survey_link = request.build_absolute_uri(reverse('survey_form', args=[survey.Name, recipient_email]))
        
        message = (
            f'Please participate in the survey by following this link:<br><br>'
            f'<a href="{survey_link}">Start Survey</a><br><br>'
            # ... more message content
        )
        
        email = EmailMessage(
            'Survey Invitation',
            message,
            settings.DEFAULT_FROM_EMAIL,
            [employee.Email],
            cc=cc_emails
        )
        email.content_subtype = "html"
        email.send(fail_silently=False)
```

### 2. Sequence Email Sending (`send_sequence_email`)

Sends emails for a sequence of surveys:

```python
def send_sequence_email(request, sequence_id):
    sequence = get_object_or_404(SurveySequence, pk=sequence_id)
    
    for employee in sequence.surveysequenceorder_set.first().survey.AllowedEmployees.all():
        cc_emails = [email.strip() for email in employee.cc_email.split(',')] if employee.cc_email else []
        # Create sequence link
        sequence_link = request.build_absolute_uri(reverse('survey_form_sequence', args=[first_survey.Name, sequence.id, 1])) + f"?email={employee.Email}"
        
        # ... construct email message
        
        email_message = EmailMessage(
            subject,
            message,
            settings.DEFAULT_FROM_EMAIL,
            [employee.Email],
            cc=cc_emails
        )
        email_message.content_subtype = "html"
        email_message.send()
```

### 3. Reminder Emails (`send_reminders`)

Automatically sends reminder emails to employees who haven't completed their surveys:

```python
def send_reminders(request):
    now = timezone.now()
    
    # Find surveys that need reminders today
    surveys = Survey.objects.filter(
        reminder_date=now.date(),
        reminder_time__hour=now.hour,
        reminder_time__minute=now.minute,
        start_date__lte=now.date(),
        end_date__gte=now.date()
    )
    
    for survey in surveys:
        # Find employees who haven't responded
        unanswered_employees = survey.AllowedEmployees.exclude(
            id__in=Response.objects.filter(Survey=survey).values_list('Employee_id', flat=True)
        )
        
        for employee in unanswered_employees:
            # ... construct email with survey link
            
            email = EmailMessage(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [employee.Email],
                cc=cc_emails
            )
            email.content_subtype = "html"
            email.send(fail_silently=False)
```

### 4. AJAX Email Sending

The application also includes AJAX-powered email sending:

```python
@login_required
def send_survey_email_ajax(request, survey_id):
    # ... retrieve survey data
    
    # For POST requests, send emails
    if request.method == 'POST':
        selected_employee_ids = request.POST.getlist('selected_employees')
        # ... process selected employees
        
        for emp in selected_employees:
            # ... create personalized survey link
            
            email = EmailMessage(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [emp.Email],
                cc=cc_list_for_this_email
            )
            email.content_subtype = "html"
            email.send()
```

```python
@login_required
def send_sequence_email_ajax(request, sequence_id):
    # ... retrieve sequence data
    
    # For POST requests, send emails
    if request.method == 'POST':
        # ... get common employees across all surveys in sequence
        
        for emp in common_employees:
            # ... create sequence start link
            
            email = EmailMessage(
                subject,
                message,
                settings.DEFAULT_FROM_EMAIL,
                [emp.Email],
                cc=cc_list_for_this_email
            )
            email.content_subtype = "html"
            email.send()
```

## Reminder Scheduling

The application includes scheduling for reminders:

1. Survey model has fields for reminder configuration:
   ```python
   class Survey(models.Model):
       # ... other fields
       reminder_date = models.DateField(null=True, blank=True)
       reminder_time = models.TimeField(null=True, blank=True)
   ```

2. The `send_reminders` function looks for surveys that need reminders at the current time:
   ```python
   surveys = Survey.objects.filter(
       reminder_date=now.date(),
       reminder_time__hour=now.hour,
       reminder_time__minute=now.minute,
       start_date__lte=now.date(),
       end_date__gte=now.date()
   )
   ```

3. For automated scheduling, you should configure a periodic task using Celery, cron, or a similar task scheduler to call the `send_reminders` function regularly.

## Best Practices

1. **Email Templates**: Consider moving email templates to separate HTML files for better maintainability.

2. **Error Handling**: The code includes error handling for email sending operations:
   ```python
   try:
       email.send()
       # ... success logging
   except Exception as e:
       logger.error(f"Failed to send email: {str(e)}")
   ```

3. **CC Emails**: The application supports CC emails for managers or other stakeholders:
   ```python
   cc_emails = [email.strip() for email in employee.cc_email.split(',')] if employee.cc_email else []
   ```

4. **Email Content Type**: All emails are sent as HTML:
   ```python
   email.content_subtype = "html"
   ```

## Security Considerations

1. The custom email backend disables hostname verification and certificate validation when no certfile/keyfile is provided. This may be a security risk in production environments.

2. Email credentials should be kept secure, preferably in environment variables rather than directly in settings files.

3. Consider using Django's built-in password reset functionality rather than custom email solutions for password-related operations.

## Troubleshooting

1. **Emails Not Sending**: Check SMTP server settings, credentials, and ensure the server allows connections from your application.

2. **SSL/TLS Issues**: The custom email backend may need adjustments based on your SMTP server's requirements.

3. **Logging**: The application includes comprehensive logging of email operations:
   ```python
   logger.info(f"Email sent successfully to {employee.Email}, CC: {', '.join(cc_emails)}")
   ```
   Check the logs for detailed information about email sending attempts. 