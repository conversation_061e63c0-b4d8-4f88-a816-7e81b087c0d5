{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Join Waiting List" %} | GB Academy{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-2xl mx-auto">
        <h1 class="text-3xl font-bold text-white mb-8 flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            {% trans "Join Waiting List" %}
        </h1>

        {% if messages %}
        <div class="mb-8">
            {% for message in messages %}
            <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        <div class="bg-gray-800/50 backdrop-blur-sm border border-white/10 rounded-lg p-6 mb-8">
            <h2 class="text-xl font-semibold text-white mb-4">{% trans "Course Information" %}</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Course Name" %}</p>
                    <p class="text-white">{{ course_instance.course.name_en }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Category" %}</p>
                    <p class="text-white">{{ course_instance.course.get_category_display }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Start Date" %}</p>
                    <p class="text-white">{{ course_instance.start_date|date:"M d, Y" }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "End Date" %}</p>
                    <p class="text-white">{{ course_instance.end_date|date:"M d, Y" }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Capacity" %}</p>
                    <p class="text-white">{{ course_instance.capacity }}</p>
                </div>
                <div>
                    <p class="text-gray-400 text-sm">{% trans "Sessions" %}</p>
                    <p class="text-white">{{ course_instance.sessions.count }}</p>
                </div>
            </div>

            <div class="p-4 bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20 rounded-lg mb-6">
                <p class="flex items-start">
                    <svg class="w-5 h-5 mr-2 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                    </svg>
                    <span>
                        {% trans "This course is currently fully booked. By joining the waiting list, you'll be notified if a spot becomes available." %}
                    </span>
                </p>
            </div>

            <h3 class="text-lg font-semibold text-white mb-4">{% trans "Waiting List Information" %}</h3>
            <div class="p-4 bg-gray-900/50 border border-white/10 rounded-lg mb-6">
                <ul class="list-disc list-inside text-white space-y-2">
                    <li>{% trans "You will be placed on the waiting list in the order you joined." %}</li>
                    <li>{% trans "If a spot becomes available, you will be notified via your profile notifications." %}</li>
                    <li>{% trans "You will have 48 hours to confirm your enrollment once notified." %}</li>
                    <li>{% trans "You can cancel your position on the waiting list at any time from your reservations page." %}</li>
                </ul>
            </div>

            <div class="flex justify-end space-x-4">
                <a href="{% url 'website:course_detail' course_id=course_instance.course.course_id %}" class="px-4 py-2 text-white/70 hover:text-white">
                    {% trans "Cancel" %}
                </a>
                <a href="{{ waitlist_url }}" class="px-4 py-2 bg-primary text-white rounded hover:bg-primary/90">
                    {% trans "Join Waiting List" %}
                </a>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 