{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Corporate Admin Calendar" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-1 py-2">
    <!-- Page Header -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            {% trans "Corporate Admin Calendar" %}
        </h1>
        
        
    </div>

    <!-- Main Content -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <!-- Calendar Section -->
        <div class="lg:col-span-3">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible">
                <!-- Calendar Header -->
                <div class="px-6 py-4 flex items-center justify-between border-b border-white/10">
                    
                    <div class="flex items-center">
                        <button id="prevMonth" class="p-2 hover:bg-white/5 rounded-md">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <h2 id="current-month-year" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                        <button id="nextMonth" class="p-2 hover:bg-white/5 rounded-md">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex rounded-md bg-gray-700 p-1">
                            <button id="viewMonth" class="px-3 py-1 text-sm rounded-md bg-primary text-white">{% trans "Month" %}</button>
                            <button id="viewWeek" class="px-3 py-1 text-sm rounded-md text-gray-300">{% trans "Week" %}</button>
                            <button id="viewDay" class="px-3 py-1 text-sm rounded-md text-gray-300">{% trans "Day" %}</button>
                        </div>
                        <button id="todayBtn" class="text-sm text-white hover:text-primary ml-2">{% trans "Today" %}</button>
                    </div>
                </div>
                
                <div id="calendarGrid" class="grid grid-cols-7 text-sm text-white">
                    <!-- Calendar cells will be generated via JavaScript -->
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4">
            <!-- Calendar Filters -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Calendar Filters" %}</h3>
                <div class="space-y-3 calendar-filters">
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-green-600 h-4 w-4 rounded" checked data-filter="session">
                        <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-blue-500 h-4 w-4 rounded" checked data-filter="event">
                        <span class="ml-2 text-sm text-white">{% trans "Events" %}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-red-500 h-4 w-4 rounded" checked data-filter="holiday">
                        <span class="ml-2 text-sm text-white">{% trans "Holidays" %}</span>
                    </label>
                </div>
            </div>

            <!-- Upcoming Events -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Events" %}</h3>
                <div id="upcomingEvents" class="space-y-4">
                    <!-- Events will be populated via JavaScript -->
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 text-center">
                            <div class="text-sm font-semibold text-primary">{% now "d" %}</div>
                            <div class="text-xs text-white/70">{% now "M" %}</div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white">Loading events...</p>
                            <p class="text-xs text-white/70">Please wait</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Corporate Course Stats -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Corporate Course Stats" %}</h3>
                <div class="space-y-3">
                    <div class="p-3 border border-white/10 rounded-md flex justify-between">
                        <span class="text-sm text-white">{% trans "Total Reserved Seats" %}</span>
                        <span class="text-sm font-medium text-white">{{ total_reservations }}</span>
                    </div>
                    <div class="p-3 border border-white/10 rounded-md flex justify-between">
                        <span class="text-sm text-white">{% trans "Available Capacity" %}</span>
                        <span class="text-sm font-medium text-white">{{ available_capacity }}</span>
                    </div>
                    <div class="p-3 border border-white/10 rounded-md flex justify-between">
                        <span class="text-sm text-white">{% trans "Active Courses" %}</span>
                        <span class="text-sm font-medium text-white">{{ active_courses }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Add these styles to match the index.html styling */
.form-checkbox {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}
.form-checkbox:checked {
    border-color: transparent;
    background-color: currentColor;
}

/* Dropdown menu styles */
select {
    z-index: 999 !important;
    position: relative;
    appearance: auto !important;
    -webkit-appearance: auto !important;
    -moz-appearance: auto !important;
}

select option {
    background-color: #1e293b;
    color: white;
    padding: 8px;
}

/* Position the dropdown container relatively */
.corporate-selector {
    position: relative;
    z-index: 30;
}

/* Ensure dropdown options are visible */
select:focus {
    outline: 2px solid #4f46e5;
}

/* Fix for dropdown menus */
select {
    position: relative !important;
    z-index: 9999 !important;
}

select option {
    position: relative !important;
    z-index: 10000 !important;
    background-color: #1e293b !important;
    color: white !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
    select {
        -moz-appearance: menulist !important;
    }
}

/* Fix for Chrome */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select {
        -webkit-appearance: menulist !important;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    {% if corporate_sessions %}
    // Current date information
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();
    let currentView = 'month';

    // Load and display calendar
    loadCalendar(currentMonth, currentYear);

    // Calendar navigation
    document.getElementById('prevMonth').addEventListener('click', function() {
        if (currentView === 'month') {
            // Move back one month
            currentMonth--;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            }
        } else if (currentView === 'week') {
            // Move back one week
            currentDate.setDate(currentDate.getDate() - 7);
            currentMonth = currentDate.getMonth();
            currentYear = currentDate.getFullYear();
        } else if (currentView === 'day') {
            // Move back one day
            currentDate.setDate(currentDate.getDate() - 1);
            currentMonth = currentDate.getMonth();
            currentYear = currentDate.getFullYear();
        }
        loadCalendar(currentMonth, currentYear);
    });

    document.getElementById('nextMonth').addEventListener('click', function() {
        if (currentView === 'month') {
            // Move forward one month
            currentMonth++;
            if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
        } else if (currentView === 'week') {
            // Move forward one week
            currentDate.setDate(currentDate.getDate() + 7);
            currentMonth = currentDate.getMonth();
            currentYear = currentDate.getFullYear();
        } else if (currentView === 'day') {
            // Move forward one day
            currentDate.setDate(currentDate.getDate() + 1);
            currentMonth = currentDate.getMonth();
            currentYear = currentDate.getFullYear();
        }
        loadCalendar(currentMonth, currentYear);
    });

    document.getElementById('todayBtn').addEventListener('click', function() {
        currentDate = new Date();
        currentMonth = currentDate.getMonth();
        currentYear = currentDate.getFullYear();
        loadCalendar(currentMonth, currentYear);
    });

    // View mode
    document.getElementById('viewMonth').addEventListener('click', function() {
        document.getElementById('viewMonth').classList.add('bg-primary', 'text-white');
        document.getElementById('viewMonth').classList.remove('text-gray-300');
        document.getElementById('viewWeek').classList.remove('bg-primary', 'text-white');
        document.getElementById('viewWeek').classList.add('text-gray-300');
        document.getElementById('viewDay').classList.remove('bg-primary', 'text-white');
        document.getElementById('viewDay').classList.add('text-gray-300');
        currentView = 'month';
        loadCalendar(currentMonth, currentYear);
    });

    document.getElementById('viewWeek').addEventListener('click', function() {
        document.getElementById('viewMonth').classList.remove('bg-primary', 'text-white');
        document.getElementById('viewMonth').classList.add('text-gray-300');
        document.getElementById('viewWeek').classList.add('bg-primary', 'text-white');
        document.getElementById('viewWeek').classList.remove('text-gray-300');
        document.getElementById('viewDay').classList.remove('bg-primary', 'text-white');
        document.getElementById('viewDay').classList.add('text-gray-300');
        currentView = 'week';
        loadCalendar(currentMonth, currentYear);
    });

    document.getElementById('viewDay').addEventListener('click', function() {
        document.getElementById('viewMonth').classList.remove('bg-primary', 'text-white');
        document.getElementById('viewMonth').classList.add('text-gray-300');
        document.getElementById('viewWeek').classList.remove('bg-primary', 'text-white');
        document.getElementById('viewWeek').classList.add('text-gray-300');
        document.getElementById('viewDay').classList.add('bg-primary', 'text-white');
        document.getElementById('viewDay').classList.remove('text-gray-300');
        currentView = 'day';
        loadCalendar(currentMonth, currentYear);
    });

    // Calendar functions
    let events = [];

    function loadCalendar(month, year) {
        // Update header
        const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        document.getElementById('current-month-year').textContent = `${monthNames[month]} ${year}`;

        // Fetch events - NOTE: We're using a different API endpoint for corporate admin
        fetch('{% url "website:corporate_calendar_events_api" %}')
            .then(response => response.json())
            .then(data => {
                events = data.events;
                renderCalendar(month, year);
                updateUpcomingSessions(events);
                
                // Set up filter listeners
                setupFilterListeners();
            })
            .catch(error => {
                console.error('Error fetching calendar events:', error);
            });
    }

    function renderCalendar(month, year) {
        const calendarGrid = document.getElementById('calendarGrid');
        calendarGrid.innerHTML = '';

        if (currentView === 'month') {
            renderMonthView(month, year, calendarGrid);
        } else if (currentView === 'week') {
            renderWeekView(month, year, calendarGrid);
        } else if (currentView === 'day') {
            renderDayView(month, year, calendarGrid);
        }
    }

    function renderMonthView(month, year, calendarGrid) {
        // Reset grid styles
        calendarGrid.className = 'grid grid-cols-7 text-sm text-white';
        calendarGrid.style.gridTemplateColumns = '';
        
        // Update the header to show month and year
        const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
        document.getElementById('current-month-year').textContent = `${monthNames[month]} ${year}`;
        
        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        // Add day headers
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        dayNames.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.className = 'py-2 text-center font-medium bg-white/5 border-b border-white/10';
            dayHeader.textContent = day;
            calendarGrid.appendChild(dayHeader);
        });

        // Previous month days
        const prevMonthDays = firstDay;
        const prevMonth = month === 0 ? 11 : month - 1;
        const prevMonthYear = month === 0 ? year - 1 : year;
        const daysInPrevMonth = new Date(prevMonthYear, prevMonth + 1, 0).getDate();
        
        for (let i = 0; i < prevMonthDays; i++) {
            const day = daysInPrevMonth - prevMonthDays + i + 1;
            const dayCell = createDayCell(day, true);
            calendarGrid.appendChild(dayCell);
        }

        // Get filtered events
        const filteredEvents = getFilteredEvents();

        // Current month days
        for (let i = 1; i <= daysInMonth; i++) {
            const dayCell = createDayCell(i, false);
            
            // Check if this is today
            const today = new Date();
            if (today.getDate() === i && today.getMonth() === month && today.getFullYear() === year) {
                dayCell.classList.add('bg-white/10');
            }
            
            // Add events for this day
            const dayEvents = filteredEvents.filter(event => {
                const eventDate = new Date(event.start);
                return eventDate.getDate() === i && 
                       eventDate.getMonth() === month && 
                       eventDate.getFullYear() === year;
            });
            
            if (dayEvents.length > 0) {
                const eventsContainer = document.createElement('div');
                eventsContainer.className = 'mt-1 space-y-1';
                
                dayEvents.forEach(event => {
                    const eventTime = new Date(event.start).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                    const eventEl = document.createElement('div');
                    // Use different colors for different event types
                    let bgColor;
                    if (event.type === 'session') {
                        bgColor = 'bg-green-600/80'; // Sessions in green
                    } else if (event.type === 'holiday') {
                        bgColor = 'bg-red-600/80'; // Holidays in red
                    } else {
                        bgColor = 'bg-blue-600/80'; // Other events in blue
                    }
                    eventEl.className = `px-1 py-0.5 text-xs rounded ${bgColor} text-white truncate`;
                    eventEl.textContent = `${eventTime} ${event.title}`;
                    eventEl.title = event.title;
                    eventsContainer.appendChild(eventEl);
                });
                
                dayCell.appendChild(eventsContainer);
            }
            
            calendarGrid.appendChild(dayCell);
        }

        // Next month days
        const totalCells = 42; // 6 rows of 7 days
        const nextMonthDays = totalCells - (prevMonthDays + daysInMonth);
        
        for (let i = 1; i <= nextMonthDays; i++) {
            const dayCell = createDayCell(i, true);
            calendarGrid.appendChild(dayCell);
        }
    }

    function renderWeekView(month, year, calendarGrid) {
        // Weekly view implementation similar to user_calendar.html
        // But with corporate session highlighting
        
        // Determine the current week
        const today = new Date();
        const currentDay = currentDate.getDate();
        const currentMonth = currentDate.getMonth();
        const currentYear = currentDate.getFullYear();
        
        // Get the current day of week (0-6, 0 is Sunday)
        const dayOfWeek = currentDate.getDay();
        
        // Find the first day of the week (Sunday)
        const firstDayOfWeek = new Date(currentYear, currentMonth, currentDay - dayOfWeek);
        
        // Clear all existing classes and set grid to display as week view
        calendarGrid.className = 'grid text-sm text-white';
        calendarGrid.style.gridTemplateColumns = 'auto repeat(7, 1fr)';
        
        // Update header
        const weekStart = firstDayOfWeek.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        const weekEnd = new Date(firstDayOfWeek);
        weekEnd.setDate(weekEnd.getDate() + 6);
        const weekEndStr = weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
        document.getElementById('current-month-year').textContent = `${weekStart} - ${weekEndStr}`;
        
        // Create header row with time column + days
        const headerRow = document.createElement('div');
        headerRow.className = 'contents';
        
        // Time column header
        const timeHeader = document.createElement('div');
        timeHeader.className = 'py-2 px-3 font-medium bg-white/5 border-b border-white/10 text-center';
        timeHeader.textContent = 'Time';
        headerRow.appendChild(timeHeader);
        
        // Day column headers
        const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        
        for (let i = 0; i < 7; i++) {
            const date = new Date(firstDayOfWeek);
            date.setDate(firstDayOfWeek.getDate() + i);
            
            const dayHeader = document.createElement('div');
            dayHeader.className = 'py-2 text-center font-medium bg-white/5 border-b border-white/10';
            
            // Highlight today's date
            const isToday = date.getDate() === today.getDate() && 
                          date.getMonth() === today.getMonth() && 
                          date.getFullYear() === today.getFullYear();
            
            if (isToday) {
                dayHeader.classList.add('bg-white/10');
            }
            
            // Format: Day name (3 letters) + date
            dayHeader.innerHTML = `
                <div>${days[i]}</div>
                <div>${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}</div>
            `;
            
            headerRow.appendChild(dayHeader);
        }
        
        calendarGrid.appendChild(headerRow);
        
        // Create time slots (8 AM to 8 PM)
        const hours = ['8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM', '4 PM', '5 PM', '6 PM', '7 PM', '8 PM'];
        
        // Create a row for each hour
        hours.forEach(hour => {
            const hourRow = document.createElement('div');
            hourRow.className = 'contents';
            
            // Time cell
            const timeCell = document.createElement('div');
            timeCell.className = 'py-2 px-3 font-medium border-b border-white/10';
            timeCell.textContent = hour;
            hourRow.appendChild(timeCell);
            
            // Day cells for this hour
            for (let i = 0; i < 7; i++) {
                const date = new Date(firstDayOfWeek);
                date.setDate(firstDayOfWeek.getDate() + i);
                
                const dayCell = document.createElement('div');
                dayCell.className = 'min-h-[60px] border-b border-l border-white/10 relative';
                dayCell.setAttribute('data-date', date.toISOString().split('T')[0]);
                dayCell.setAttribute('data-hour', hour);
                
                // Check if this is today and highlight it
                const isToday = date.getDate() === today.getDate() && 
                              date.getMonth() === today.getMonth() && 
                              date.getFullYear() === today.getFullYear();
                
                if (isToday) {
                    dayCell.classList.add('bg-white/5');
                }
                
                hourRow.appendChild(dayCell);
            }
            
            calendarGrid.appendChild(hourRow);
        });
        
        // Get filtered events
        const filteredEvents = getFilteredEvents();
        
        // Group events by day for efficient placement
        const eventsByDay = {};
        
        // Preprocess events to organize them by day
        filteredEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            // Check if event is in the current week view
            const eventDate = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());
            const firstDateOfWeek = new Date(firstDayOfWeek.getFullYear(), firstDayOfWeek.getMonth(), firstDayOfWeek.getDate());
            const lastDateOfWeek = new Date(firstDateOfWeek);
            lastDateOfWeek.setDate(lastDateOfWeek.getDate() + 6);
            
            if (eventDate >= firstDateOfWeek && eventDate <= lastDateOfWeek) {
                // Format date for indexing
                const dateKey = eventDate.toISOString().split('T')[0];
                
                // Initialize day if not exists
                if (!eventsByDay[dateKey]) {
                    eventsByDay[dateKey] = [];
                }
                
                // Add event to the day
                eventsByDay[dateKey].push({
                    event: event,
                    startTime: eventStart,
                    endTime: eventEnd
                });
            }
        });
        
        // Place events in the calendar
        for (const dateKey in eventsByDay) {
            const dayEvents = eventsByDay[dateKey];
            
            dayEvents.forEach(({event, startTime, endTime}) => {
                // Extract title parts
                const titleParts = event.title.split(' - ');
                const courseName = titleParts[0];
                const roomInfo = titleParts.length > 1 ? titleParts.slice(1).join(' - ') : '';
                
                // Get start hour (in 24h format)
                const startHour = startTime.getHours();
                const endHour = endTime.getHours() + (endTime.getMinutes() > 0 ? 1 : 0);
                
                // Format time for display
                const formattedTime = startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                // Map 24h hour to index in our hours array
                const mapHourToIndex = h => {
                    if (h < 8) return 0; // Before 8 AM
                    if (h > 20) return hours.length - 1; // After 8 PM
                    return h - 8; // Map 8-20 to 0-12 index
                };
                
                // Find range of hours this event spans
                const startIndex = mapHourToIndex(startHour);
                const endIndex = Math.min(mapHourToIndex(endHour), hours.length - 1);
                
                // Get all cells for this date
                const dayCells = calendarGrid.querySelectorAll(`[data-date="${dateKey}"]`);
                
                // Add event to each hour cell
                for (let i = startIndex; i <= endIndex; i++) {
                    // Skip if we don't have this cell
                    if (!dayCells[i]) continue;
                    
                    // Create event element
                    const eventEl = document.createElement('div');
                    
                    // Set class based on event type
                    let eventTypeClass;
                    if (event.type === 'session') {
                        eventTypeClass = 'bg-green-600/80'; // Sessions in green
                    } else if (event.type === 'holiday') {
                        eventTypeClass = 'bg-red-600/80'; // Holidays in red
                    } else {
                        eventTypeClass = 'bg-blue-600/80'; // Other events in blue
                    }
                    eventEl.className = `${eventTypeClass} text-white px-2 py-2 text-sm rounded-sm m-0`;
                    
                    // Set content with just the course name and time
                    eventEl.innerHTML = `
                        <div class="font-semibold truncate">${courseName}</div>
                        <div class="text-xs opacity-80">${formattedTime}</div>
                    `;
                    
                    dayCells[i].appendChild(eventEl);
                }
            });
        }
    }

    function renderDayView(month, year, calendarGrid) {
        // Clear all classes and set grid for day view
        calendarGrid.className = 'grid text-sm text-white';
        calendarGrid.style.gridTemplateColumns = 'auto 1fr';
        
        // Format the day header
        const currentDayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' });
        const formattedDate = currentDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
        
        // Update the header
        document.getElementById('current-month-year').textContent = `${currentDayName}, ${formattedDate.split(',')[0]}`;
        
        // Create full-width day header
        const dayHeader = document.createElement('div');
        dayHeader.className = 'col-span-2 py-4 text-center font-medium bg-white/5 border-b border-white/10 text-xl';
        dayHeader.textContent = `${currentDayName}, ${formattedDate.split(',')[0]}`;
        calendarGrid.appendChild(dayHeader);
        
        // Create time slots (7 AM to 8 PM)
        const hours = ['7 AM', '8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM', '4 PM', '5 PM', '6 PM', '7 PM', '8 PM'];
        const hourCells = {}; // Store references to time cells for later use
        
        // Create a row for each hour
        hours.forEach(hour => {
            const hourRow = document.createElement('div');
            hourRow.className = 'contents';
            
            // Time cell
            const timeCell = document.createElement('div');
            timeCell.className = 'py-2 px-3 font-medium border-b border-white/10';
            timeCell.textContent = hour;
            hourRow.appendChild(timeCell);
            
            // Day cell for this hour
            const dayCell = document.createElement('div');
            dayCell.className = 'min-h-[60px] border-b border-l border-white/10 relative';
            dayCell.setAttribute('data-hour', hour);
            hourRow.appendChild(dayCell);
            
            // Store the day cell reference
            hourCells[hour] = dayCell;
            
            calendarGrid.appendChild(hourRow);
        });
        
        // Get filtered events
        const filteredEvents = getFilteredEvents();
        
        // Filter events for the current day
        const dayEvents = filteredEvents.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate.getDate() === currentDate.getDate() && 
                   eventDate.getMonth() === currentDate.getMonth() && 
                   eventDate.getFullYear() === currentDate.getFullYear();
        });
        
        // Process each event and create separate blocks for each hour
        dayEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
            
            // Get start and end hours
            const startHour = eventStart.getHours();
            const endHour = eventEnd.getHours() === 0 ? 24 : eventEnd.getHours(); // Handle midnight as 24
            
            // Extract components from title
            const titleParts = event.title.split(' - ');
            const courseName = titleParts[0];
            const trainerName = titleParts.length > 1 ? titleParts[1] : '';
            const roomName = titleParts.length > 2 ? titleParts[2] : '';
            
            // Format times for display
            const startTime = eventStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            const endTime = eventEnd.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            // Add event blocks for each hour from start to end
            for (let h = startHour; h < endHour; h++) {
                // Convert hour to 12-hour format for cell lookup
                const hourFormat = h === 0 ? '12 AM' : 
                                 h < 12 ? `${h} AM` : 
                                 h === 12 ? '12 PM' : 
                                 `${h - 12} PM`;
                
                // Get the cell for this hour
                if (hours.includes(hourFormat) && hourCells[hourFormat]) {
                    const cell = hourCells[hourFormat];
                    
                    // Create event element
                    const eventEl = document.createElement('div');
                    
                    // Set class based on event type
                    let eventTypeClass;
                    if (event.type === 'session') {
                        eventTypeClass = 'bg-green-600/80'; // Sessions in green
                    } else if (event.type === 'holiday') {
                        eventTypeClass = 'bg-red-600/80'; // Holidays in red
                    } else {
                        eventTypeClass = 'bg-blue-600/80'; // Other events in blue
                    }
                    eventEl.className = `${eventTypeClass} text-white px-2 py-2 text-sm rounded-sm m-0`;
                    
                    // Set content (this will be the same for all hour blocks)
                    eventEl.innerHTML = `
                        <div class="font-medium">${courseName} - ${roomName}</div>
                        <div class="text-white/80 text-xs">${startTime} - ${endTime}</div>
                    `;
                    
                    // Add to cell
                    cell.innerHTML = ''; // Clear any existing content
                    cell.appendChild(eventEl);
                }
            }
        });
    }

    function createDayCell(day, isOtherMonth) {
        const dayCell = document.createElement('div');
        dayCell.className = `min-h-[100px] p-2 border-t border-l border-white/10 relative ${isOtherMonth ? 'text-white/30' : 'text-white'}`;
        
        const dayNumber = document.createElement('div');
        dayNumber.className = 'text-sm';
        dayNumber.textContent = day;
        dayCell.appendChild(dayNumber);
        
        return dayCell;
    }

    // Function to update the upcoming sessions panel
    function updateUpcomingSessions(events) {
        const upcomingSessionsContainer = document.getElementById('upcomingEvents');
        upcomingSessionsContainer.innerHTML = '';
        
        // Sort events by start date
        events.sort((a, b) => new Date(a.start) - new Date(b.start));
        
        // Get today's date
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Filter for upcoming events (today and future)
        const upcomingEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= today;
        }).slice(0, 5); // Take only the first 5
        
        if (upcomingEvents.length === 0) {
            upcomingSessionsContainer.innerHTML = `
                <p class="text-sm text-white/70">{% trans "No upcoming events" %}</p>
            `;
            return;
        }
        
        // Add each upcoming event to the panel
        upcomingEvents.forEach(event => {
            const eventDate = new Date(event.start);
            const day = eventDate.getDate();
            const month = eventDate.toLocaleString('default', { month: 'short' });
            const time = eventDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            // Determine badge class based on event type
            let typeClass, typeLabel;
            if (event.type === 'session') {
                typeClass = 'text-green-400'; // Sessions in green
                typeLabel = 'session';
            } else if (event.type === 'holiday') {
                typeClass = 'text-red-400'; // Holidays in red
                typeLabel = 'holiday';
            } else {
                typeClass = 'text-blue-400'; // Other events in blue
                typeLabel = 'event';
            }
            
            const eventElement = document.createElement('div');
            eventElement.className = 'flex items-start space-x-4 p-3 border border-white/10 rounded-md hover:bg-white/5';
            eventElement.innerHTML = `
                <div class="flex-shrink-0 w-12 text-center">
                    <div class="text-sm font-semibold text-primary">${day}</div>
                    <div class="text-xs text-white/70">${month}</div>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-white">${event.title}</p>
                    <p class="text-xs ${typeClass}">${typeLabel} · ${time}</p>
                </div>
            `;
            upcomingSessionsContainer.appendChild(eventElement);
        });
    }

    // Setup event listeners for filters
    function setupFilterListeners() {
        const filterCheckboxes = document.querySelectorAll('.calendar-filters input[type="checkbox"]');
        
        filterCheckboxes.forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                // Re-render the calendar with filtered events
                renderCalendar(currentMonth, currentYear);
            });
        });
    }
    
    // Filter events based on checked filters
    function getFilteredEvents() {
        const sessionFilterChecked = document.querySelector('.calendar-filters input[data-filter="session"]').checked;
        const eventFilterChecked = document.querySelector('.calendar-filters input[data-filter="event"]').checked;
        const holidayFilterChecked = document.querySelector('.calendar-filters input[data-filter="holiday"]').checked;
        
        return events.filter(event => {
            if (event.type === 'session' && sessionFilterChecked) return true;
            if (event.type === 'event' && eventFilterChecked) return true;
            if (event.type === 'holiday' && holidayFilterChecked) return true;
            return false;
        });
    }
    {% endif %}
});
</script>
{% endblock %} 