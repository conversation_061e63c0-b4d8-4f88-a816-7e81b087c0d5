{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% translate "Corporate Management" %}{% endblock %}

{% block extra_css %}
<style>
    /* Ensure modals stack properly above all content */
    #addCorporateModal,
    #editCorporateModal,
    #viewCorporateModal,
    #deleteCorporateModal {
        z-index: 9999 !important; /* Force highest z-index */
    }
    
    /* Ensure modal content is above backdrop */
    #addCorporateModal > div > div:last-child,
    #editCorporateModal > div > div:last-child,
    #viewCorporateModal > div > div:last-child,
    #deleteCorporateModal > div > div:last-child {
        position: relative;
        z-index: 10000 !important; /* Even higher z-index */
    }
    
    /* Make sure notification panels have lower z-index */
    .mb-8.bg-yellow-900\/20 {
        z-index: 10;
        position: relative;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-8 flex items-center">
        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
        </svg>
        {% translate "Corporate Management" %}
    </h1>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Corporate Course Requests Notification Panel -->
    <div class="mb-8 bg-yellow-900/20 backdrop-blur-md rounded-lg border border-yellow-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-yellow-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                </svg>
                {% translate "Corporate Course Requests" %}
                <span id="course-requests-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-yellow-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:course_requests_list' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-yellow-600 text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                {% translate "View All Requests" %}
            </a>
        </div>

        <div id="course-requests-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <!-- Main content section -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% translate "Corporate Admins" %}</h2>
            <button type="button" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" onclick="openModal('addCorporateModal')">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                {% translate "Add Corporate Admin" %}
            </button>
        </div>

        {% if corporates %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "User" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Corporate Name" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Email" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Phone" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Category" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Capacity" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% translate "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for corporate in corporates %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ corporate.corporate_admin.first_name }} {{ corporate.corporate_admin.last_name }}</div>
                            <div class="text-xs text-gray-400">{{ corporate.corporate_admin.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ corporate.legal_name }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ corporate.corporate_admin.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ corporate.phone_number }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                {{ corporate.get_category_display }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                {{ corporate.capacity }}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if corporate.corporate_admin.is_active %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                {% translate "Active" %}
                            </span>
                            {% else %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-800/30 text-red-400">
                                {% translate "Inactive" %}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex justify-end items-center space-x-4">
                                {% comment %} <button type="button" class="text-blue-400 hover:text-blue-300 text-sm" onclick="openViewModal('{{ corporate.corporate_id }}')">
                                    {% translate "View" %}
                                </button> {% endcomment %}
                                <button type="button" class="text-blue-400 hover:text-blue-300 text-sm" onclick="openEditModal('{{ corporate.corporate_id }}')">
                                    {% translate "Edit" %}
                                </button>
                                <button type="button" class="text-red-400 hover:text-red-300 text-sm" onclick="openDeleteModal('{{ corporate.corporate_id }}', '{{ corporate.legal_name }}')">
                                    {% translate "Delete" %}
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% translate "No Corporate Admins" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% translate "There are no corporate admins in the system yet." %}</p>
            <div class="mt-6">
                <button type="button" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" onclick="openModal('addCorporateModal')">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% translate "Add Your First Corporate Admin" %}
                </button>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Add Corporate Modal -->
<div id="addCorporateModal" class="fixed inset-0 z-[9999] hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-20 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full relative">
            <div class="border-b border-blue-800 px-6 py-4">
                <h5 class="text-lg font-medium text-white" id="addCorporateModalLabel">{% translate "Add Corporate Admin" %}</h5>
                <button type="button" class="absolute top-3 right-3 text-gray-400 hover:text-white" onclick="closeModal('addCorporateModal')">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <form id="add-corporate-form" class="space-y-4" method="post" action="{% url 'website:process_corporate_form' %}">
                    {% csrf_token %}
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <h6 class="text-sm font-semibold text-white mb-3">{% translate "Admin Information" %}</h6>
                            <div>
                                <label for="admin_first_name" class="block text-sm font-medium text-gray-300 mb-1">{% translate "First Name" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="admin_first_name" name="admin_first_name" required>
                            </div>
                            <div>
                                <label for="admin_last_name" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Last Name" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="admin_last_name" name="admin_last_name" required>
                            </div>
                            <div>
                                <label for="admin_email" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Email" %}</label>
                                <input type="email" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="admin_email" name="admin_email" required>
                            </div>
                            <div>
                                <label for="admin_username" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Username" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="admin_username" name="admin_username">
                                <p class="text-xs text-gray-400 mt-1">{% translate "Leave empty to generate from email" %}</p>
                            </div>
                            <div>
                                <label for="admin_password" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Password" %}</label>
                                <input type="password" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="admin_password" name="admin_password" required>
                                <div class="flex flex-col mt-1">
                                    <div class="flex items-center text-xs text-gray-400">
                                        <span class="inline-block w-3 h-3 bg-gray-400 rounded-full mr-1"></span>
                                        {% translate "8+ characters" %}
                                    </div>
                                    <div class="flex items-center text-xs text-gray-400">
                                        <span class="inline-block w-3 h-3 bg-gray-400 rounded-full mr-1"></span>
                                        {% translate "Letters & numbers" %}
                                    </div>
                                    <div class="flex items-center text-xs text-gray-400">
                                        <span class="inline-block w-3 h-3 bg-gray-400 rounded-full mr-1"></span>
                                        {% translate "Not common" %}
                                    </div>
                                </div>
                            </div>
                            <div>
                                <label for="admin_confirm_password" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Confirm Password" %}</label>
                                <input type="password" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="admin_confirm_password" name="admin_confirm_password" required>
                            </div>
                            <div>
                                <label for="national_id" class="block text-sm font-medium text-gray-300 mb-1">{% translate "National ID" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="national_id" name="national_id">
                            </div>
                        </div>
                        <div class="space-y-4">
                            <h6 class="text-sm font-semibold text-white mb-3">{% translate "Corporate Information" %}</h6>
                            <div>
                                <label for="legal_name" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Legal Name" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="legal_name" name="legal_name" required>
                            </div>
                            <div>
                                <label for="phone_number" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Phone Number" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="phone_number" name="phone_number">
                            </div>
                            <div>
                                <label for="nationality" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Nationality" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="nationality" name="nationality">
                            </div>
                            <div>
                                <label for="date_of_birth" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Date of Birth" %}</label>
                                <input type="date" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="date_of_birth" name="date_of_birth">
                            </div>
                            <div>
                                <label for="address" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Address" %}</label>
                                <textarea class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="address" name="address" rows="2"></textarea>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="capacity" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Capacity" %}</label>
                                    <input type="number" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="capacity" name="capacity" value="10">
                                </div>
                                <div>
                                    <label for="category" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Category" %}</label>
                                    <select class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="category" name="category">
                                        <option value="A">{% translate "Category A" %}</option>
                                        <option value="B">{% translate "Category B" %}</option>
                                        <option value="C">{% translate "Category C" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div>
                            <label for="tax_registration_number" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Tax Registration Number" %}</label>
                            <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="tax_registration_number" name="tax_registration_number">
                        </div>
                        <div>
                            <label for="commercial_registration_number" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Commercial Registration Number" %}</label>
                            <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="commercial_registration_number" name="commercial_registration_number">
                        </div>
                    </div>
                </form>
            </div>
            <div class="bg-blue-800/50 px-6 py-4 flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 border border-gray-500 rounded-md text-gray-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="closeModal('addCorporateModal')">
                    {% translate "Cancel" %}
                </button>
                <button type="button" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="save-corporate-btn">
                    {% translate "Save" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Corporate Modal -->
<div id="editCorporateModal" class="fixed inset-0 z-[9999] hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-20 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full relative">
            <div class="border-b border-blue-800 px-6 py-4">
                <h5 class="text-lg font-medium text-white" id="editCorporateModalLabel">{% translate "Edit Corporate Admin" %}</h5>
                <button type="button" class="absolute top-3 right-3 text-gray-400 hover:text-white" onclick="closeModal('editCorporateModal')">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <form id="edit-corporate-form" class="space-y-4" method="post" action="{% url 'website:update_corporate_form' %}">
                    {% csrf_token %}
                    <input type="hidden" id="edit_corporate_id" name="corporate_id">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div class="space-y-4">
                            <h6 class="text-sm font-semibold text-white mb-3">{% translate "Admin Information" %}</h6>
                            <div>
                                <label for="edit_admin_first_name" class="block text-sm font-medium text-gray-300 mb-1">{% translate "First Name" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_admin_first_name" name="admin_first_name" required>
                            </div>
                            <div>
                                <label for="edit_admin_last_name" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Last Name" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_admin_last_name" name="admin_last_name" required>
                            </div>
                            <div>
                                <label for="edit_admin_email" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Email" %}</label>
                                <input type="email" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_admin_email" name="admin_email" required>
                            </div>
                            <div>
                                <label for="edit_admin_username" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Username" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_admin_username" name="admin_username">
                                <p class="text-xs text-gray-400 mt-1">{% translate "Leave empty to generate from email" %}</p>
                            </div>
                            <div>
                                <label for="edit_national_id" class="block text-sm font-medium text-gray-300 mb-1">{% translate "National ID" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_national_id" name="national_id">
                            </div>
                        </div>
                        <div class="space-y-4">
                            <h6 class="text-sm font-semibold text-white mb-3">{% translate "Corporate Information" %}</h6>
                            <div>
                                <label for="edit_legal_name" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Legal Name" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_legal_name" name="legal_name" required>
                            </div>
                            <div>
                                <label for="edit_phone_number" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Phone Number" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_phone_number" name="phone_number">
                            </div>
                            <div>
                                <label for="edit_nationality" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Nationality" %}</label>
                                <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_nationality" name="nationality">
                            </div>
                            <div>
                                <label for="edit_date_of_birth" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Date of Birth" %}</label>
                                <input type="date" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_date_of_birth" name="date_of_birth">
                            </div>
                            <div>
                                <label for="edit_address" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Address" %}</label>
                                <textarea class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_address" name="address" rows="2"></textarea>
                            </div>
                            <div class="grid grid-cols-2 gap-4">
                                <div>
                                    <label for="edit_capacity" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Capacity" %}</label>
                                    <input type="number" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_capacity" name="capacity">
                                </div>
                                <div>
                                    <label for="edit_category" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Category" %}</label>
                                    <select class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_category" name="category">
                                        <option value="A">{% translate "Category A" %}</option>
                                        <option value="B">{% translate "Category B" %}</option>
                                        <option value="C">{% translate "Category C" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                        <div>
                            <label for="edit_tax_registration_number" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Tax Registration Number" %}</label>
                            <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_tax_registration_number" name="tax_registration_number">
                        </div>
                        <div>
                            <label for="edit_commercial_registration_number" class="block text-sm font-medium text-gray-300 mb-1">{% translate "Commercial Registration Number" %}</label>
                            <input type="text" class="w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2 focus:ring-primary focus:border-primary" id="edit_commercial_registration_number" name="commercial_registration_number">
                        </div>
                    </div>
                </form>
            </div>
            <div class="bg-blue-800/50 px-6 py-4 flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 border border-gray-500 rounded-md text-gray-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="closeModal('editCorporateModal')">
                    {% translate "Cancel" %}
                </button>
                <button type="button" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary" id="update-corporate-btn">
                    {% translate "Update" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- View Corporate Details Modal -->
<div id="viewCorporateModal" class="fixed inset-0 z-[9999] hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-20 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full relative">
            <div class="border-b border-blue-800 px-6 py-4">
                <h5 class="text-lg font-medium text-white" id="viewCorporateModalLabel">{% translate "Corporate Details" %}</h5>
                <button type="button" class="absolute top-3 right-3 text-gray-400 hover:text-white" onclick="closeModal('viewCorporateModal')">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h6 class="text-sm font-semibold text-white mb-4">{% translate "Admin Information" %}</h6>
                        <p class="mb-3"><span class="text-gray-400 text-sm font-medium">{% translate "Name" %}:</span> <span id="view_admin_name" class="text-white ml-2"></span></p>
                        <p class="mb-3"><span class="text-gray-400 text-sm font-medium">{% translate "Email" %}:</span> <span id="view_admin_email" class="text-white ml-2"></span></p>
                    </div>
                    <div>
                        <h6 class="text-sm font-semibold text-white mb-4">{% translate "Corporate Information" %}</h6>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Legal Name" %}:</span> <span id="view_legal_name" class="text-white ml-2"></span></p>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Address" %}:</span> <span id="view_address" class="text-white ml-2"></span></p>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Phone Number" %}:</span> <span id="view_phone_number" class="text-white ml-2"></span></p>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Capacity" %}:</span> <span id="view_capacity" class="text-white ml-2"></span></p>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Category" %}:</span> <span id="view_category" class="text-white ml-2"></span></p>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Tax Registration Number" %}:</span> <span id="view_tax_registration_number" class="text-white ml-2"></span></p>
                        <p class="mb-2"><span class="text-gray-400 text-sm font-medium">{% translate "Commercial Registration Number" %}:</span> <span id="view_commercial_registration_number" class="text-white ml-2"></span></p>
                    </div>
                </div>
                <div class="mt-6">
                    <h6 class="text-sm font-semibold text-white mb-4">{% translate "Corporate Users" %}</h6>
                    <div id="corporate-users-container" class="bg-blue-800/30 rounded-lg p-4">
                        <div class="flex justify-center py-3">
                            <svg class="animate-spin h-8 w-8 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-blue-800/50 px-6 py-4 flex justify-end">
                <button type="button" class="px-4 py-2 border border-gray-500 rounded-md text-gray-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="closeModal('viewCorporateModal')">
                    {% translate "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteCorporateModal" class="fixed inset-0 z-[9999] hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-20 px-4 pb-20 text-center">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-blue-900 border border-red-800/30 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full relative">
            <div class="bg-red-800/30 border-b border-red-800/30 px-6 py-4">
                <h5 class="text-lg font-medium text-white" id="deleteCorporateModalLabel">{% translate "Confirm Delete" %}</h5>
                <button type="button" class="absolute top-3 right-3 text-gray-400 hover:text-white" onclick="closeModal('deleteCorporateModal')">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>
            <div class="p-6">
                <p class="text-white mb-3">{% translate "Are you sure you want to delete the corporate" %} <strong id="delete-corporate-name"></strong>?</p>
                <p class="text-red-400 text-sm">{% translate "This action cannot be undone. The corporate admin account and all associated data will be permanently deleted." %}</p>
                <input type="hidden" id="delete-corporate-id">
            </div>
            <div class="bg-blue-800/50 px-6 py-4 flex justify-end space-x-2">
                <button type="button" class="px-4 py-2 border border-gray-500 rounded-md text-gray-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500" onclick="closeModal('deleteCorporateModal')">
                    {% translate "Cancel" %}
                </button>
                <button type="button" class="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" id="confirm-delete-btn">
                    {% translate "Delete" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch course requests when the page loads
        fetchCourseRequests();
        
        // Add event listener for update button
        document.getElementById('update-corporate-btn').addEventListener('click', function() {
            document.getElementById('edit-corporate-form').submit();
        });
        
        // Add event listener for save button 
        document.getElementById('save-corporate-btn').addEventListener('click', function() {
            document.getElementById('add-corporate-form').submit();
        });

        // Add event listener for confirm delete button
        document.getElementById('confirm-delete-btn').addEventListener('click', function() {
            const corporateId = document.getElementById('delete-corporate-id').value;
            fetch(`{% url "website:delete_corporate" 0 %}`.replace('0', corporateId), {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': getCsrfToken(),
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    alert(data.message || '{% translate "Corporate deleted successfully." %}');
                    closeModal('deleteCorporateModal');
                    location.reload(); // Reload the page to reflect changes
                } else {
                    alert(data.message || '{% translate "Error deleting corporate." %}');
                }
            })
            .catch(error => {
                console.error('Error deleting corporate:', error);
                alert('{% translate "An error occurred while deleting the corporate. Please try again." %}');
            });
        });
    });

    // Function to fetch and display course requests
    function fetchCourseRequests() {
        fetch('{% url "website:course_requests_api" %}')
            .then(response => response.json())
            .then(data => {
                if (data.status !== 'success') {
                    throw new Error(data.message || 'Failed to fetch course requests');
                }
                
                const pendingCount = data.pending_count;
                const pendingNewCourseCount = data.pending_new_course_count || 0;
                const pendingRequests = data.pending_requests;
                
                // Update count badge with total count
                const totalCount = data.total_count;
                document.getElementById('course-requests-count').textContent = totalCount;
                
                // Get container
                const container = document.getElementById('course-requests-container');
                
                // Clear loading indicator
                container.innerHTML = '';
                
                if (totalCount === 0) {
                    // Show no requests message
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="fas fa-check-circle text-warning opacity-50 fa-3x mb-3"></i>
                            <h6 class="fw-bold">{% translate "No Pending Requests" %}</h6>
                            <p class="text-muted small">{% translate "There are no corporate course requests waiting for review." %}</p>
                        </div>
                    `;
                } else {
                    // Create a table to display the requests
                    const table = document.createElement('div');
                    table.className = 'overflow-x-auto';
                    table.innerHTML = `
                        <table class="min-w-full divide-y divide-yellow-800/30">
                            <thead class="bg-yellow-900/30">
                                <tr>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        {% translate "Corporate" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        {% translate "Course" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        {% translate "Type" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                        {% translate "Status" %}
                                    </th>
                                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                        {% translate "Actions" %}
                                    </th>
                                </tr>
                            </thead>
                            <tbody class="bg-transparent divide-y divide-yellow-800/30" id="requests-table-body">
                            </tbody>
                        </table>
                    `;
                    
                    container.appendChild(table);
                    const tableBody = document.getElementById('requests-table-body');
                    
                    // Add up to 5 most recent requests, prioritizing cancellation requests
                    const allRequests = pendingRequests;
                    console.log("All pending requests:", allRequests);
                    
                    // Make sure to only include one request per course/corporate combination
                    const cancelRequests = allRequests.filter(req => req.is_cancellation_request === true);
                    const newCourseRequests = allRequests.filter(req => req.request_type === 'new_course' && !req.is_cancellation_request);
                    const normalRequests = allRequests.filter(req => req.status === 'PENDING' && req.request_type !== 'new_course');
                    
                    console.log("Cancel requests:", cancelRequests.length);
                    console.log("New course requests:", newCourseRequests.length);
                    console.log("Normal requests:", normalRequests.length);
                    
                    // Sort cancellation requests to the top, then new course requests, then normal requests
                    const requestsToShow = [...cancelRequests, ...newCourseRequests, ...normalRequests].slice(0, 5);
                    
                    // Final client-side deduplication check
                    const seenIds = new Set();
                    const uniqueRequests = [];
                    
                    for (const req of requestsToShow) {
                        const uniqueId = `${req.request_type}_${req.request_id}`;
                        if (!seenIds.has(uniqueId)) {
                            seenIds.add(uniqueId);
                            uniqueRequests.push(req);
                        } else {
                            console.log(`Skipping duplicate request: ${uniqueId}`);
                        }
                    }
                    
                    uniqueRequests.forEach(request => {
                        // Format date
                        const createdAt = new Date(request.created_at);
                        const isPendingCancel = request.is_cancellation_request === true;
                        const isNewCourseRequest = request.request_type === 'new_course';
                        
                        const row = document.createElement('tr');
                        row.className = 'hover:bg-yellow-900/20';
                        
                        // Different UI for cancellation requests
                        const statusBadge = isPendingCancel 
                            ? `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-orange-800/30 text-orange-400">
                                  {% translate "Pending Cancellation" %}
                               </span>`
                            : `<span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-800/30 text-yellow-400">
                                  ${request.capacity} {% translate "seats" %}
                               </span>`;
                        
                        // For new course requests we need a different badge
                        const newCourseLabel = isNewCourseRequest 
                            ? `<span class="ml-1 px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                  {% translate "NEW" %}
                               </span>` 
                            : '';
                                
                        const actions = isPendingCancel
                            ? `<button onclick="approveCancelRequest('${request.request_id}', '${request.request_type || 'regular'}')" class="text-green-400 hover:text-green-300 text-sm mx-2">
                                  {% translate "Approve Cancel" %}
                               </button>
                               <button onclick="rejectCancelRequest('${request.request_id}', '${request.request_type || 'regular'}')" class="text-red-400 hover:text-red-300 text-sm">
                                  {% translate "Reject Cancel" %}
                               </button>`
                            : `<button onclick="approveRequest('${request.request_id}', '${request.request_type || 'regular'}')" class="text-green-400 hover:text-green-300 text-sm mx-2">
                                  {% translate "Approve" %}
                               </button>
                               <button onclick="rejectRequest('${request.request_id}', '${request.request_type || 'regular'}')" class="text-red-400 hover:text-red-300 text-sm">
                                  {% translate "Reject" %}
                               </button>`;
                        
                        // Determine course name based on request type
                        let courseName = '';
                        if (isNewCourseRequest) {
                            courseName = `${request.course_name}${newCourseLabel}`;
                        } else {
                            courseName = request.course ? request.course.name : '{% translate "Custom Request" %}';
                        }
                        
                        row.innerHTML = `
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-white">${request.corporate.legal_name}</div>
                                <div class="text-xs text-gray-400">${request.corporate_admin.email}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-white">${courseName}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-white">${request.course_type_display}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                ${statusBadge}
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right">
                                ${actions}
                            </td>
                        `;
                        
                        tableBody.appendChild(row);
                    });
                    
                    // If there are more requests than shown, add a note
                    if (totalCount > 5) {
                        const moreMsg = document.createElement('div');
                        moreMsg.className = 'text-center py-2 text-yellow-500 text-sm';
                        moreMsg.textContent = `{% translate "and" %} ${totalCount - 5} {% translate "more pending requests" %}`;
                        container.appendChild(moreMsg);
                    }
                }
            })
            .catch(error => {
                console.error('Error fetching course requests:', error);
                const container = document.getElementById('course-requests-container');
                container.innerHTML = `
                    <div class="text-center py-4">
                        <svg class="mx-auto h-10 w-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                        </svg>
                        <h3 class="mt-2 text-sm font-medium text-white">{% translate "Error Loading Requests" %}</h3>
                        <p class="mt-1 text-sm text-red-400">{% translate "Unable to load course requests." %}</p>
                    </div>
                `;
                document.getElementById('course-requests-count').textContent = "!";
            });
    }

    // Function to approve a request
    function approveRequest(requestId, requestType = 'regular') {
        if (confirm('{% translate "Are you sure you want to approve this course request?" %}')) {
            updateRequestStatus(requestId, 'APPROVED', '', requestType);
        }
    }

    // Function to reject a request
    function rejectRequest(requestId, requestType = 'regular') {
        const reason = prompt('{% translate "Please provide a reason for rejection (optional):" %}');
        if (reason !== null) {  // User didn't click cancel
            updateRequestStatus(requestId, 'REJECTED', reason, requestType);
        }
    }

    // Function to update request status
    function updateRequestStatus(requestId, status, adminNotes = '', requestType = 'regular') {
        // Determine the endpoint based on request type
        const endpoint = requestType === 'new_course' 
            ? `{% url "website:update_new_course_request_status" 0 %}`.replace('0', requestId)
            : `{% url "website:update_course_request_status" 0 %}`.replace('0', requestId);
            
        fetch(endpoint, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({
                status: status,
                admin_notes: adminNotes,
                request_type: requestType
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Refresh the requests
                fetchCourseRequests();
                // Show success message
                alert(status === 'APPROVED' ? 
                    '{% translate "Course request has been approved." %}' : 
                    '{% translate "Course request has been rejected." %}');
            } else {
                throw new Error(data.message || 'Failed to update request');
            }
        })
        .catch(error => {
            console.error('Error updating request:', error);
            alert('{% translate "An error occurred. Please try again." %}');
        });
    }
    
    // Function to approve a cancellation request
    function approveCancelRequest(requestId, requestType = 'regular') {
        if (confirm('{% translate "Are you sure you want to approve this cancellation request? This will cancel all associated reservations." %}')) {
            // Determine the endpoint based on request type
            const endpoint = requestType === 'new_course' 
                ? `{% url "website:update_new_course_request_status" 0 %}`.replace('0', requestId)
                : `{% url "website:update_course_request_status" 0 %}`.replace('0', requestId);
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    status: 'APPROVED',
                    admin_notes: '{% translate "Cancellation approved by system admin" %}'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Refresh the requests
                    fetchCourseRequests();
                    
                    // Show success message with additional info about reservations
                    let message = '{% translate "Cancellation request has been approved." %}';
                    if (data.cancelled_reservations) {
                        message += ` ${data.cancelled_reservations} {% translate "reservations were automatically cancelled." %}`;
                    }
                    alert(message);
                } else {
                    throw new Error(data.message || 'Failed to approve cancellation');
                }
            })
            .catch(error => {
                console.error('Error approving cancellation:', error);
                alert('{% translate "An error occurred. Please try again." %}');
            });
        }
    }
    
    // Function to reject a cancellation request
    function rejectCancelRequest(requestId, requestType = 'regular') {
        const reason = prompt('{% translate "Please provide a reason for rejecting this cancellation request (optional):" %}');
        if (reason !== null) {  // User didn't click cancel
            // Determine the endpoint based on request type
            const endpoint = requestType === 'new_course' 
                ? `{% url "website:update_new_course_request_status" 0 %}`.replace('0', requestId)
                : `{% url "website:update_course_request_status" 0 %}`.replace('0', requestId);
            
            fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                },
                body: JSON.stringify({
                    status: 'REJECTED',
                    admin_notes: reason
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Refresh the requests
                    fetchCourseRequests();
                    // Show success message
                    alert('{% translate "Cancellation request has been rejected. The course request status has been set back to APPROVED." %}');
                } else {
                    throw new Error(data.message || 'Failed to reject cancellation');
                }
            })
            .catch(error => {
                console.error('Error rejecting cancellation:', error);
                alert('{% translate "An error occurred. Please try again." %}');
            });
        }
    }

    // Function to get CSRF token
    function getCsrfToken() {
        return document.querySelector('[name=csrfmiddlewaretoken]').value;
    }

    // Modal functions
    function openModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
    }

    function closeModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    function openEditModal(corporateId) {
            // Fetch corporate details
        fetch(`{% url "website:corporate_detail" 0 %}`.replace('0', corporateId))
                .then(response => {
                    if (!response.ok) {
                    return response.text().then(text => {
                        throw new Error(`Server responded with ${response.status}: ${text}`);
                    });
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.corporate) {
                    // Populate edit form
                    document.getElementById('edit_corporate_id').value = data.corporate.corporate_id;
                    document.getElementById('edit_admin_first_name').value = data.corporate.admin_name.split(' ')[0];
                    document.getElementById('edit_admin_last_name').value = data.corporate.admin_name.split(' ')[1] || '';
                    document.getElementById('edit_admin_email').value = data.corporate.admin_email;
                    document.getElementById('edit_admin_username').value = data.corporate.admin_username;
                    document.getElementById('edit_national_id').value = data.corporate.national_id || '';
                    document.getElementById('edit_legal_name').value = data.corporate.legal_name;
                    document.getElementById('edit_phone_number').value = data.corporate.phone_number;
                    document.getElementById('edit_nationality').value = data.corporate.nationality || '';
                    document.getElementById('edit_date_of_birth').value = data.corporate.date_of_birth ? data.corporate.date_of_birth.split('T')[0] : '';
                    document.getElementById('edit_address').value = data.corporate.address;
                    document.getElementById('edit_tax_registration_number').value = data.corporate.tax_registration_number;
                    document.getElementById('edit_commercial_registration_number').value = data.corporate.commercial_registration_number;
                    document.getElementById('edit_capacity').value = data.corporate.capacity;
                    document.getElementById('edit_category').value = data.corporate.category;
                    
                    // Show the modal
                    openModal('editCorporateModal');
                    } else {
                    throw new Error('Invalid response format');
                    }
                })
                .catch(error => {
                console.error('Error fetching corporate details:', error);
                alert('Error fetching corporate details. Please try again.');
            });
    }

    function openDeleteModal(corporateId, corporateName) {
        // Populate modal fields
        document.getElementById('delete-corporate-id').value = corporateId;
        document.getElementById('delete-corporate-name').textContent = corporateName;

        // Open the modal
        openModal('deleteCorporateModal');
    }
</script>
{% endblock %} 