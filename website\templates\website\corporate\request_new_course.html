{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Request New Course" %}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- <PERSON> Header -->
        <div class="flex justify-between items-center mb-8">
            <div class="flex items-center space-x-3">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122"/>
                </svg>
                <h1 class="text-2xl font-bold text-white">{% trans "Request New Course" %}</h1>
            </div>
            <div>
                <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-600 hover:bg-gray-700">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                    {% trans "Back to Courses" %}
                </a>
            </div>
        </div>

        <!-- Form Card -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
            <div class="mb-6">
                <h2 class="text-xl font-medium text-white mb-2">{% trans "Request a New Course" %}</h2>
                <p class="text-white/70">
                    {% trans "Fill out this form to request a course that doesn't exist in our catalog. Our team will review your request and get back to you." %}
                </p>
            </div>

            <form method="post" action="{% url 'website:request_new_course' %}">
                {% csrf_token %}
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                    <!-- Course Name -->
                    <div class="col-span-1 md:col-span-2">
                        <label for="course_name" class="block text-sm font-medium text-white/70 mb-1">
                            {% trans "Course Name" %} <span class="text-red-500">*</span>
                        </label>
                        <input type="text" name="course_name" id="course_name" required
                            class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            placeholder="{% trans 'Enter a descriptive name for the course' %}">
                    </div>
                    
                    <!-- Course Description -->
                    <div class="col-span-1 md:col-span-2">
                        <label for="course_description" class="block text-sm font-medium text-white/70 mb-1">
                            {% trans "Course Description" %}
                        </label>
                        <textarea name="course_description" id="course_description" rows="4"
                            class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            placeholder="{% trans 'Describe the course content, objectives, and target audience' %}"></textarea>
                    </div>
                    
                    <!-- Course Type -->
                    <div>
                        <label for="course_type" class="block text-sm font-medium text-white/70 mb-1">
                            {% trans "Course Type" %}
                        </label>
                        <select name="course_type" id="course_type"
                            class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            <option value="OFFLINE" class="bg-gray-800">{% trans "Physical" %}</option>
                            <option value="ONLINE" class="bg-gray-800">{% trans "Online" %}</option>
                        </select>
                    </div>
                    
                    <!-- Capacity -->
                    <div>
                        <label for="capacity" class="block text-sm font-medium text-white/70 mb-1">
                            {% trans "Desired Capacity" %}
                        </label>
                        <input type="number" name="capacity" id="capacity" min="1" value="20"
                            class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            placeholder="{% trans 'Number of participants' %}">
                    </div>
                    
                    <!-- Additional Notes -->
                    <div class="col-span-1 md:col-span-2">
                        <label for="notes" class="block text-sm font-medium text-white/70 mb-1">
                            {% trans "Additional Notes" %}
                        </label>
                        <textarea name="notes" id="notes" rows="3"
                            class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            placeholder="{% trans 'Any additional information, special requirements, or preferences' %}"></textarea>
                    </div>
                </div>
                
                <!-- Submit Button -->
                <div class="flex justify-end space-x-3">
                    <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-white/20 rounded-md text-sm font-medium text-white hover:bg-white/5">
                        {% trans "Cancel" %}
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                        {% trans "Submit Request" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 