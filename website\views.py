from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login
from django.contrib import messages
from django.urls import reverse, reverse_lazy
from .models import (
    Attendance, CustomUser, UserType, Section, Feature, Course, ContactInfo, 
    Trainer, Room, RoomAvailability, Session, TrainerAvailability, 
    Event, EventType, RoomType, Equipment, EquipmentAvailability, 
    Brand, Category, Model, Reservation, CourseInstance,
    EmergencyCancellationRequest, Payment, Notification, Holiday,
    CourseContent, Corporate, Survey, Questionnaire, CorporateAdminRequest
)
from .forms import (
    UserTypeSelectionForm, AccountRegistrationForm, 
    PersonalInfoForm, CorporateInfoForm, CourseForm,
    CourseContentForm
)
from django.contrib.auth.decorators import login_required, user_passes_test
from .decorators import super_admin_or_staff, it_group_required
from datetime import datetime, timedelta, time
from django.utils.translation import gettext_lazy as _
from django.contrib.admin.views.decorators import staff_member_required
import uuid
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
import json
from django.db import transaction
import logging
from django.core.exceptions import ValidationError
from django.http import Http404
from django.template.loader import render_to_string
from django.db.models import Q
from django.db import IntegrityError
from django.utils.dateparse import parse_datetime
from django.http import HttpResponse
from functools import wraps
from django.conf import settings
from django.db import models
from django.db.models import Count
from django.db import connection
import traceback

# Helper functions
def is_corporate_admin(user):
    """Helper function to check if a user is a corporate admin"""
    if not user.is_authenticated:
        return False
    
    try:
        return (hasattr(user, 'user_type') and 
                user.user_type and 
                user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN' and
                hasattr(user, 'corporate_admin_profile') and 
                user.corporate_admin_profile.is_active)
    except:
        return False

# Import views from corporate_views.py
from .corporate_views import (
    corporate_admins_view, corporate_admin_dashboard, corporate_reservations_view,
    list_corporates, corporate_detail, create_corporate, update_corporate, delete_corporate,
    get_corporate_users, process_corporate_form, update_corporate_form, course_request_form,
    course_requests_list, update_course_request_status, course_requests_api,
    corporate_admin_calendar, corporate_calendar_events_api,
    my_corporate_requests_view, my_corporate_requests_api,
    my_corporate_users_view, corporate_users_api, add_corporate_user, delete_corporate_user
)

# Configure logger
logger = logging.getLogger(__name__)

def trainer_required(view_func):
    """
    Decorator for views that checks that the user is a trainer.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
            logger.info(f"SUPER_ADMIN user {request.user.username} accessing trainer-only view: {view_func.__name__}")
            return view_func(request, *args, **kwargs)
        
        if not request.user.is_authenticated:
            messages.error(request, _('Please log in first.'))
            return redirect('login')
        if not hasattr(request.user, 'trainer'):
            messages.error(request, _('You must be a trainer to access this page.'))
            return redirect('website:courses')
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def trainer_supervisor_required(view_func):
    """
    Decorator for views that checks that the user is a trainer supervisor.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
            # Add logging to help diagnose issues for Super Admins
            logger.info(f"SUPER_ADMIN user {request.user.username} accessing supervisor-only view: {view_func.__name__}")
            return view_func(request, *args, **kwargs)
        if not request.user.is_authenticated:
            messages.error(request, _('Please log in first.'))
            return redirect('login')
        # First check if user is SUPER_ADMIN - they don't need to be a trainer
        if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
            # Add logging to help diagnose issues for Super Admins
            logger.info(f"SUPER_ADMIN user {request.user.username} accessing supervisor-only view: {view_func.__name__}")
            return view_func(request, *args, **kwargs)
            
        if not hasattr(request.user, 'trainer'):
            messages.error(request, _('You must be a trainer to access this page.'))
            return redirect('website:courses')
        if not request.user.trainer.is_supervisor:
            messages.error(request, _('You must be a trainer supervisor to access this page.'))
            return redirect('website:courses')
        return view_func(request, *args, **kwargs)
    return _wrapped_view

# Create your views here.

def home(request):
    # Handle login form submission
    if request.method == 'POST':
        login_identifier = request.POST.get('login_identifier')
        password = request.POST.get('password')
        user_type = request.POST.get('user_type', 'external')
        
        # Add debug messages
        print(f"Login attempt - Identifier: {login_identifier}, Type: {user_type}")
        
        if user_type == 'internal':
            # Internal users now use OTP authentication
            # The login process is handled via JavaScript/AJAX with OTP endpoints
            # This should not be reached for internal users anymore
            messages.info(request, 'Internal users now use email verification. Please use the login form.')
        else:
            # Handle external user authentication (email/username + password)
            # Clear any existing session first to prevent session conflicts
            if request.user.is_authenticated:
                from django.contrib.auth import logout
                logout(request)
            
            # Use the backend that supports both email and username
            user = authenticate(request, username=login_identifier, password=password)
                
            print(f"Authentication result: {'Success' if user else 'Failed'}")
            
            if user is not None:
                # Verify this is actually not an internal user
                if hasattr(user, 'user_type') and user.user_type and user.user_type.code != 'INTERNAL_GB':
                    login(request, user, backend='website.backends.EmailOrUsernameModelBackend')
                    print("External user logged in successfully")
                    return redirect('website:dashboard_redirect')
                else:
                    messages.error(request, 'This login method is only for external users.')
                    print("Invalid user type for external login")
            else:
                messages.error(request, 'Invalid login credentials')
                print("External login failed - invalid credentials")

    # Fetch all sections with their features
    sections = Section.objects.prefetch_related('features').filter(is_active=True).order_by('order')
    
    # Debug: Print sections
    print("\nFetched Sections:")
    for section in sections:
        print(f"Section: {section.code} - {section.name_en}")
    
    # Create a dictionary to store section data
    section_data = {}
    for section in sections:
        section_data[section.code] = {
            'section': section,
            'features': section.features.filter(is_active=True).order_by('order')
        }
    
    # Debug: Print section_data keys
    print("\nSection Data Keys:", section_data.keys())

    # Fetch courses grouped by category
    courses = Course.objects.filter(is_active=True).order_by('category', 'order')
    course_categories = {}
    for course in courses:
        if course.category not in course_categories:
            course_categories[course.category] = []
        course_categories[course.category].append(course)
    
    # Debug: Print course categories
    print("\nCourse Categories:", course_categories.keys())

    # Fetch contact information
    contact_info = ContactInfo.objects.filter(is_active=True).order_by('order')
    
    # Debug: Print contact info count
    print("\nContact Info Count:", contact_info.count())

    context = {
        'section_data': section_data,
        'course_categories': course_categories,
        'contact_info': contact_info,
    }
    
    return render(request, 'website/home.html', context)

@login_required
def main_view(request):
    """
    Main view that redirects users to appropriate dashboards based on their role
    """
    # Use the redirect_to_dashboard function from corporate_views
    from .corporate_views import redirect_to_dashboard
    return redirect_to_dashboard(request)

def test_oracle_authentication(request, employee_id):
    """
    Test view for Oracle SOAP authentication (for development only)
    """
    from django.http import JsonResponse
    from .soap_client import OracleSOAPClient
    
    if not settings.DEBUG:
        return JsonResponse({'error': 'This endpoint is only available in debug mode'}, status=403)
    
    try:
        soap_client = OracleSOAPClient()
        user = soap_client.authenticate_employee(employee_id)
        
        if user:
            return JsonResponse({
                'success': True,
                'message': f'Employee {employee_id} authenticated successfully',
                'user_data': {
                    'username': user.username,
                    'email': user.email,
                    'employee_id': user.employee_id,
                    'oracle_department': user.oracle_department,
                    'oracle_position': user.oracle_position,
                    'user_type': user.user_type.code if user.user_type else None
                }
            })
        else:
            return JsonResponse({
                'success': False,
                'message': f'Employee {employee_id} not found or authentication failed'
            })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        })

def register_step1(request):
    # Get the previous registration type from session if it exists
    initial_type = request.session.get('registration_user_type')
    initial_corporate = False
    if initial_type:
        try:
            user_type = UserType.objects.get(code=initial_type)
            initial_corporate = user_type.code == 'EXTERNAL_CORPORATE_ADMIN'
        except UserType.DoesNotExist:
            pass

    if request.method == 'POST':
        form = UserTypeSelectionForm(request.POST)
        if form.is_valid():
            user_type = form.cleaned_data['user_type']
            request.session['registration_user_type'] = user_type.code
            request.session['registration_step'] = 2
            request.session['total_steps'] = 4 if user_type.code == 'EXTERNAL_CORPORATE_ADMIN' else 3
            return redirect('website:register_step2')
    else:
        form = UserTypeSelectionForm(initial={'user_type': initial_type} if initial_type else None)
    
    return render(request, 'website/registration/step1.html', {
        'form': form,
        'current_step': 1,
        'initial_type': initial_type,
        'initial_corporate': initial_corporate
    })

def register_step2(request):
    user_type_code = request.session.get('registration_user_type')
    if not user_type_code:
        return redirect('website:register_step1')
    
    is_corporate = user_type_code == 'EXTERNAL_CORPORATE_ADMIN'
    total_steps = request.session.get('total_steps', 4 if is_corporate else 3)
    
    if request.method == 'POST':
        form = AccountRegistrationForm(request.POST)
        if form.is_valid():
            # Check if email already exists
            email = form.cleaned_data['email']
            if CustomUser.objects.filter(email=email).exists():
                form.add_error('email', 'This email is already registered.')
                return render(request, 'website/registration/step2.html', {
                    'form': form,
                    'current_step': 2,
                    'show_corporate': is_corporate,
                    'total_steps': total_steps
                })
            
            # Store account info in session
            request.session['account_info'] = form.cleaned_data
            request.session['registration_step'] = 3
            return redirect('website:register_step3')
    else:
        form = AccountRegistrationForm()
    
    return render(request, 'website/registration/step2.html', {
        'form': form,
        'current_step': 2,
        'show_corporate': is_corporate,
        'total_steps': total_steps
    })

def register_step3(request):
    user_type_code = request.session.get('registration_user_type')
    account_info = request.session.get('account_info')
    
    if not user_type_code or not account_info:
        return redirect('website:register_step1')
    
    is_corporate = user_type_code == 'EXTERNAL_CORPORATE_ADMIN'
    total_steps = request.session.get('total_steps', 4 if is_corporate else 3)
    
    if request.method == 'POST':
        form = PersonalInfoForm(request.POST)
        if form.is_valid():
            # Store personal info in session
            personal_info = form.cleaned_data.copy()
            if personal_info.get('date_of_birth'):
                personal_info['date_of_birth'] = personal_info['date_of_birth'].isoformat()
            request.session['personal_info'] = personal_info
            if is_corporate:
                request.session['registration_step'] = 4
                return redirect('website:register_step4')
            else:
                # Create user for individual registration
                user = create_user(request)
                if user is None:
                    return redirect('website:register_step1')
                login(request, user, backend='website.backends.EmailOrUsernameModelBackend')
                messages.success(request, 'Registration successful!')
                return redirect('website:main')
    else:
        form = PersonalInfoForm()
    
    return render(request, 'website/registration/step3.html', {
        'form': form,
        'current_step': 3,
        'show_corporate': is_corporate,
        'total_steps': total_steps
    })

def register_step4(request):
    user_type_code = request.session.get('registration_user_type')
    account_info = request.session.get('account_info')
    personal_info = request.session.get('personal_info')
    
    if not all([user_type_code, account_info, personal_info]) or user_type_code != 'EXTERNAL_CORPORATE_ADMIN':
        return redirect('website:register_step1')
    
    if request.method == 'POST':
        form = CorporateInfoForm(request.POST)
        if form.is_valid():
            # Store corporate info and create user
            request.session['corporate_info'] = form.cleaned_data
            user = create_user(request)
            if user is None:
                return redirect('website:register_step1')
            login(request, user, backend='website.backends.EmailOrUsernameModelBackend')
            messages.success(request, 'Registration successful!')
            return redirect('website:main')
    else:
        form = CorporateInfoForm()
    
    return render(request, 'website/registration/step4.html', {
        'form': form,
        'current_step': 4,
        'show_corporate': True,
        'total_steps': 4
    })

def registration_timeline(request):
    """AJAX view to update the registration timeline."""
    show_corporate = request.GET.get('show_corporate') == 'true'
    current_step = int(request.GET.get('current_step', 1))
    total_steps = 4 if show_corporate else 3
    
    return render(request, 'website/registration/timeline.html', {
        'show_corporate': show_corporate,
        'current_step': current_step,
        'total_steps': total_steps
    })

def create_user(request):
    try:
        user_type_code = request.session.get('registration_user_type')
        account_info = request.session.get('account_info')
        personal_info = request.session.get('personal_info').copy()
        corporate_info = request.session.get('corporate_info')
        
        # Double check if email is still available
        email = account_info['email']
        if CustomUser.objects.filter(email=email).exists():
            messages.error(request, 'This email is already registered. Please try again with a different email.')
            return None
        
        # Convert date string back to date object
        if personal_info and personal_info.get('date_of_birth'):
            personal_info['date_of_birth'] = datetime.fromisoformat(personal_info['date_of_birth']).date()
        
        # Get the UserType instance
        try:
            user_type = UserType.objects.get(code=user_type_code)
        except UserType.DoesNotExist:
            messages.error(request, 'Invalid user type selected.')
            return None
        
        user = CustomUser.objects.create_user(
            email=account_info['email'],
            username=account_info['username'],
            password=account_info['password1'],
            user_type=user_type,
            **personal_info
        )
        
        if corporate_info:
            for key, value in corporate_info.items():
                setattr(user, key, value)
            user.save()
            
            # Create Corporate record if this is a corporate admin
            if user_type_code == 'EXTERNAL_CORPORATE_ADMIN':
                corporate = Corporate.objects.create(
                    corporate_admin=user,
                    legal_name=corporate_info.get('company_name', ''),
                    address=personal_info.get('address', ''),
                    tax_registration_number=corporate_info.get('tax_registration_number', ''),
                    commercial_registration_number=corporate_info.get('commercial_registration_number', ''),
                    phone_number=personal_info.get('phone_number', ''),
                    capacity=corporate_info.get('capacity', 10),
                    category=corporate_info.get('category', 'A')
                )
                
                # Create CorporateAdmin record
                from .models import CorporateAdmin
                CorporateAdmin.objects.create(
                    user=user,
                    corporate=corporate,
                    position=corporate_info.get('position', 'Admin'),
                    department=corporate_info.get('department', ''),
                    direct_phone=personal_info.get('phone_number', ''),
                    is_active=True
                )
        
        # Clear session data
        keys_to_delete = ['registration_user_type', 'registration_step', 
                         'account_info', 'personal_info', 'corporate_info']
        for key in keys_to_delete:
            request.session.pop(key, None)
        
        return user
    except Exception as e:
        messages.error(request, f'An error occurred during registration: {str(e)}')
        return None

@login_required
def profile_view(request):
    return render(request, 'website/profile.html')

@login_required
def settings_view(request):
    if request.method == 'POST':
        if 'full_name' in request.POST:
            # Handle profile settings form
            first_name = request.POST.get('full_name').split()[0]
            last_name = ' '.join(request.POST.get('full_name').split()[1:]) if len(request.POST.get('full_name').split()) > 1 else ''
            request.user.first_name = first_name
            request.user.last_name = last_name
            request.user.email = request.POST.get('email')
            request.user.save()
            messages.success(request, _('Profile updated successfully'))
        
        elif 'current_password' in request.POST:
            # Handle password change form
            current_password = request.POST.get('current_password')
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')
            
            if not request.user.check_password(current_password):
                messages.error(request, _('Current password is incorrect'))
            elif new_password != confirm_password:
                messages.error(request, _('New passwords do not match'))
            else:
                request.user.set_password(new_password)
                request.user.save()
                messages.success(request, _('Password updated successfully'))
                # Re-authenticate the user to prevent logout
                user = authenticate(username=request.user.username, password=new_password)
                if user:
                    login(request, user)
        
        elif 'email_notifications' in request.POST:
            # Handle notification settings
            # You can save these to user preferences model if you have one
            messages.success(request, _('Notification preferences updated'))
        
        return redirect('website:settings')
    
    return render(request, 'website/settings.html')

@login_required
def courses_view(request):
    """
    View for listing courses with different access levels:
    - Staff members (admins) can see all courses but can't manage them
    - Trainers can see and manage their courses
    - Supervisor trainers can see and manage all courses
    - Regular users can only see active courses
    - EXTERNAL_INDIVIDUAL users see only courses that have active instances
    - EXTERNAL_CORPORATE_TRAINEE users are redirected to their corporate courses page
    """
    # Redirect corporate trainees to their dedicated courses page
    if request.user.user_type and request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE':
        messages.info(request, _('Corporate users can only view their enrolled courses'))
        return redirect('website:corporate_user_courses_view')
    
    is_trainer = hasattr(request.user, 'trainer')
    is_supervisor = is_trainer and request.user.trainer.is_supervisor
    is_gb_user = request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'

    # Check if the user is an external individual
    is_external_individual = request.user.user_type and request.user.user_type.code == 'EXTERNAL_INDIVIDUAL'
    
    if is_external_individual:
        # For external individuals, show only courses that have active instances
        # First get all active course instances
        active_instances = CourseInstance.objects.filter(
            is_active=True,
            published=True,
            course_type='INDIVIDUAL'  # Only show INDIVIDUAL course types to individual users
        ).select_related('course').order_by('start_date')
        
        # Get unique courses that have active instances
        course_ids = active_instances.values_list('course_id', flat=True).distinct()
        courses = Course.objects.filter(course_id__in=course_ids, is_active=True)
        
        # Group courses by category
        course_categories = {}
        for course in courses:
            category_display = course.get_category_display()
            if category_display not in course_categories:
                course_categories[category_display] = []
            
            # Get all active instances for this course
            course_instances = list(active_instances.filter(course=course))
            
            # Add additional information needed for enrollment modal
            now = timezone.now()
            for instance in course_instances:
                instance.session_count = instance.sessions.count()
                
                # Check if course has already started
                instance.course_started = now > instance.start_date
                
                # Calculate remaining time until course starts (for UI purposes)
                if not instance.course_started:
                    time_until_start = instance.start_date - now
                    days = time_until_start.days
                    hours = (time_until_start.seconds // 3600)
                    minutes = (time_until_start.seconds % 3600) // 60
                    if days > 0:
                        instance.time_until_deadline = f"{days} days, {hours} hours"
                    else:
                        instance.time_until_deadline = f"{hours} hours, {minutes} minutes" 
                else:
                    instance.time_until_deadline = "Deadline has passed"
            
            # Create a dictionary with course data including instances
            course_data = {
                'course_id': course.course_id,
                'name_en': course.name_en,
                'name_ar': course.name_ar,
                'description_en': course.description_en,
                'description_ar': course.description_ar,
                'category': course.category,
                'location': course.location,
                'icon_svg': course.icon_svg,
                'image': course.image,
                'get_category_display': course.get_category_display,
                'get_location_display': course.get_location_display,
                'instances': course_instances,
                'instance_count': len(course_instances)
            }
            
            course_categories[category_display].append(course_data)
        
        # Get user's active reservations for the template
        user_reservations = Reservation.objects.filter(
            user=request.user
        ).exclude(status='CANCELLED').select_related('course_instance', 'course_instance__course')
        
        context = {
            'course_categories': course_categories,
            'can_manage': False,
            'is_trainer': False,
            'is_supervisor': False,
            'is_external_individual': True,
            'is_corporate_trainee': False,
            'user_reservations': user_reservations,
        }
        
    elif is_gb_user:
        # For internal GB users, show only courses with active 'GB' type instances
        active_instances = CourseInstance.objects.filter(
            is_active=True,
            published=True,
            course_type='GB'
        ).select_related('course').order_by('start_date')

        course_ids = active_instances.values_list('course_id', flat=True).distinct()
        courses = Course.objects.filter(course_id__in=course_ids, is_active=True)

        course_categories = {}
        for course in courses:
            category_display = course.get_category_display()
            if category_display not in course_categories:
                course_categories[category_display] = []

            course_instances = list(active_instances.filter(course=course))

            course_data = {
                'course_id': course.course_id,
                'name_en': course.name_en,
                'name_ar': course.name_ar,
                'description_en': course.description_en,
                'description_ar': course.description_ar,
                'category': course.category,
                'location': course.location,
                'icon_svg': course.icon_svg,
                'image': course.image,
                'get_category_display': course.get_category_display,
                'get_location_display': course.get_location_display,
                'instances': course_instances,
                'instance_count': len(course_instances)
            }

            course_categories[category_display].append(course_data)

        # Get user's active reservations for the template
        user_reservations = Reservation.objects.filter(
            user=request.user
        ).exclude(status='CANCELLED').select_related('course_instance', 'course_instance__course')
        
        context = {
            'course_categories': course_categories,
            'can_manage': False,
            'is_trainer': False,
            'is_supervisor': False,
            'is_external_individual': False,
            'is_gb_user': True,
            'is_corporate_trainee': False,
            'user_reservations': user_reservations,
        }

    elif request.user.user_type and request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE':
        # For corporate trainees, show only courses with active 'CORPORATE' type instances for their company
        active_instances = CourseInstance.objects.filter(
            is_active=True,
            published=True,
            course_type='CORPORATE',
            corporate__legal_name=request.user.company_name
        ).select_related('course').order_by('start_date')

        course_ids = active_instances.values_list('course_id', flat=True).distinct()
        courses = Course.objects.filter(course_id__in=course_ids, is_active=True)

        course_categories = {}
        for course in courses:
            category_display = course.get_category_display()
            if category_display not in course_categories:
                course_categories[category_display] = []

            course_instances = list(active_instances.filter(course=course))

            course_data = {
                'course_id': course.course_id,
                'name_en': course.name_en,
                'name_ar': course.name_ar,
                'description_en': course.description_en,
                'description_ar': course.description_ar,
                'category': course.category,
                'location': course.location,
                'icon_svg': course.icon_svg,
                'image': course.image,
                'get_category_display': course.get_category_display,
                'get_location_display': course.get_location_display,
                'instances': course_instances,
                'instance_count': len(course_instances)
            }

            course_categories[category_display].append(course_data)

        # Get user's active reservations for the template
        user_reservations = Reservation.objects.filter(
            user=request.user
        ).exclude(status='CANCELLED').select_related('course_instance', 'course_instance__course')
        
        context = {
            'course_categories': course_categories,
            'can_manage': False,
            'is_trainer': False,
            'is_supervisor': False,
            'is_external_individual': False,
            'is_gb_user': False,
            'is_corporate_trainee': True,
            'user_reservations': user_reservations,
        }
        
    else:
        # Get courses based on user role (original logic)
        if request.user.is_staff:
            courses = Course.objects.all()
            can_manage = False  # Admins can only view
        elif is_supervisor:
            courses = Course.objects.all()  # Supervisors can see all courses
            can_manage = True  # Supervisors can manage all courses
        elif is_trainer:
            courses = request.user.trainer.courses.all()
            can_manage = True  # Trainers can manage their courses
        else:
            courses = Course.objects.filter(is_active=True)
            can_manage = False  # Regular users can only view

        # Group courses by category
        course_categories = {}
        for course in courses:
            if course.get_category_display() not in course_categories:
                course_categories[course.get_category_display()] = []
            course_categories[course.get_category_display()].append(course)

        # Get user's active reservations for the template
        user_reservations = Reservation.objects.filter(
            user=request.user
        ).exclude(status='CANCELLED').select_related('course_instance', 'course_instance__course')
        
        context = {
            'course_categories': course_categories,
            'can_manage': can_manage,
            'is_trainer': is_trainer,
            'is_supervisor': is_supervisor,
            'is_external_individual': False,
            'is_gb_user': False,
            'is_corporate_trainee': False,
            'user_reservations': user_reservations,
        }

    return render(request, 'website/courses/courses.html', context)

@login_required
def calendar_view(request):
    # Check if the user is an external individual and redirect to their personal calendar
    if request.user.user_type and request.user.user_type.code == 'EXTERNAL_INDIVIDUAL':
        return redirect('website:user_calendar')
    
    # Check if the user is a corporate admin and redirect to corporate admin calendar
    if request.user.user_type and request.user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN':
        return redirect('website:corporate_admin_calendar')
    
    # Check if the user is a trainer and redirect to trainer calendar
    if request.user.user_type and request.user.user_type.code == 'TRAINER':
        return redirect('website:trainer_calendar')
    
    # Check if the user is a GB user and redirect to GB user calendar
    if request.user.user_type and request.user.user_type.code == 'INTERNAL_GB':
        return redirect('website:gb_user_calendar')
    
    # Only staff members (system admins and super admins) can access the system calendar
    if not request.user.is_staff:
        messages.warning(request, _('You do not have permission to access the system calendar.'))
        return redirect('website:main')
        
    # Get all active rooms
    rooms = Room.objects.filter(is_active=True)
    
    # Get all active trainers
    trainers = Trainer.objects.all().select_related('user')
    
    # Get all active equipment
    equipment = Equipment.objects.filter(is_active=True)
    
    # Get room availability data
    room_availability = RoomAvailability.objects.filter(
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('room', 'session', 'event')
    
    # Get trainer availability data
    trainer_availability = TrainerAvailability.objects.filter(
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('trainer', 'trainer__user', 'session', 'event')
    
    # Get equipment availability data
    equipment_availability = EquipmentAvailability.objects.filter(
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('equipment', 'session', 'event')
    
    # Prepare context
    context = {
        'rooms': rooms,
        'trainers': trainers,
        'equipment': equipment,
        'room_availability': room_availability,
        'trainer_availability': trainer_availability,
        'equipment_availability': equipment_availability,
    }
    
    return render(request, 'website/calendar/index.html', context) 
@login_required
def trainer_calendar_view(request):
    # Check if the user is a trainer
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        # If the user is not a trainer, redirect to the main calendar view
        messages.warning(request, _('You do not have trainer privileges.'))
        return redirect('website:calendar')
    
    # Get trainer availability data
    trainer_availability = TrainerAvailability.objects.filter(
        trainer=trainer,
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('session', 'event')
    
    # Prepare context
    context = {
        'trainer': trainer,
        'trainer_availability': trainer_availability,
    }
    
    return render(request, 'website/calendar/trainer_calendar.html', context)

@staff_member_required
def rooms_view(request):
    rooms = Room.objects.prefetch_related('availability_slots', 'fixed_equipment').select_related('room_type').all()
    room_types = RoomType.objects.filter(is_active=True)
    
    # Get all active equipment
    equipment_list = Equipment.objects.filter(
        is_active=True
    ).order_by('code')
    
    # Get all active categories, brands, and models
    categories = Category.objects.filter(is_active=True).order_by('name')
    brands = Brand.objects.filter(is_active=True).order_by('name')
    models = Model.objects.filter(is_active=True).select_related('brand').order_by('brand__name', 'name')
    
    context = {
        'rooms': rooms,
        'room_types': room_types,
        'equipment_list': equipment_list,
        'categories': categories,
        'brands': brands,
        'models': models,
    }
    return render(request, 'website/rooms/index.html', context)

@staff_member_required
def trainers_view(request):
    # Get all trainers with their related user information
    trainers = Trainer.objects.select_related('user', 'user__user_type').all()
    
    context = {
        'trainers': trainers,
    }
    return render(request, 'website/trainers.html', context)

@staff_member_required
def get_users(request):
    # Get all users who are students (EXTERNAL_INDIVIDUAL)
    users = CustomUser.objects.filter(
        user_type__code='EXTERNAL_INDIVIDUAL'
    ).values('id', 'first_name', 'last_name', 'email')
    return JsonResponse(list(users), safe=False)

@staff_member_required
@require_http_methods(["POST"])
def promote_to_trainer(request):
    data = json.loads(request.body)
    user_id = data.get('user_id')
    
    try:
        user = CustomUser.objects.get(id=user_id)
        trainer_type = UserType.objects.get(code='TRAINER')
        user.user_type = trainer_type
        user.save()
        
        # Create Trainer profile if it doesn't exist
        trainer, created = Trainer.objects.get_or_create(user=user)
        
        # Create trainer availability if trainer was just created
        if created:
            TrainerAvailability.objects.create(
                trainer=trainer,
                status='ACTIVE',
                start_time=timezone.now()
            )
        
        messages.success(request, _('User successfully promoted to trainer.'))
        return JsonResponse({'status': 'success'})
    except CustomUser.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('User not found.')
        }, status=404)
    except UserType.DoesNotExist:
        return JsonResponse({
            'status': 'error', 
            'message': _('Trainer user type not found.')
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
def trainer_detail(request, trainer_id):
    trainer = get_object_or_404(Trainer, id=trainer_id)
    context = {
        'trainer': trainer,
    }
    return render(request, 'website/trainer_detail.html', context)

@staff_member_required
def edit_trainer(request, trainer_id):
    trainer = get_object_or_404(Trainer, id=trainer_id)
    if request.method == 'POST':
        # Handle form submission
        # Add your form handling logic here
        messages.success(request, _('Trainer updated successfully!'))
        return redirect('website:trainer_detail', trainer_id=trainer.id)
    
    context = {
        'trainer': trainer,
    }
    return render(request, 'website/edit_trainer.html', context)

@staff_member_required
@require_http_methods(["POST"])
def create_trainer(request):
    data = json.loads(request.body)
    try:
        # Start a transaction
        with transaction.atomic():
            # Check if username or email already exists
            if CustomUser.objects.filter(username=data['username']).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('Username already exists. Please choose a different username.')
                }, status=400)
            
            if CustomUser.objects.filter(email=data['email']).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('Email already exists. Please use a different email address.')
                }, status=400)
            
            # Create the user
            user = CustomUser.objects.create_user(
                username=data['username'],
                email=data['email'],
                password=data['password'],
                first_name=data['first_name'],
                last_name=data['last_name']
            )
            
            # Set user type to trainer
            trainer_type = UserType.objects.get(code='TRAINER')
            user.user_type = trainer_type
            user.save()
            
            # Create trainer profile
            trainer = Trainer.objects.create(user=user)
            
            # Create trainer availability
            TrainerAvailability.objects.create(
                trainer=trainer,
                status='READY'
            )
            
            messages.success(request, _('Trainer created successfully!'))
            return JsonResponse({'status': 'success'})
    except UserType.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('Trainer user type not found. Please configure user types first.')
        }, status=400)
    except Exception as e:
        # If any error occurs, rollback the transaction
        if 'user' in locals():
            user.delete()  # This will cascade delete trainer and availability
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["POST"])
def create_room(request):
    try:
        data = json.loads(request.body)
        logger.info(f"CREATE ROOM: Received data: {json.dumps(data, indent=2)}")
        
        # Get room type
        try:
            room_type = RoomType.objects.get(room_type_id=data['room_type'])
            logger.info(f"CREATE ROOM: Found room type: {room_type.name} (ID: {room_type.room_type_id})")
        except RoomType.DoesNotExist:
            logger.error(f"CREATE ROOM: Room type with ID {data['room_type']} does not exist")
            return JsonResponse({
                'status': 'error',
                'message': _('Selected room type does not exist')
            }, status=400)
        
        # Create the room in a transaction - without equipment
        logger.info("CREATE ROOM: Starting transaction for room creation")
        with transaction.atomic():
            # Create the room
            logger.info(f"CREATE ROOM: Creating room with name='{data['name']}', capacity={data.get('capacity', 0)}")
            room = Room.objects.create(
                name=data['name'],
                capacity=data.get('capacity', 0),
                description=data.get('description', ''),
                room_type=room_type,
            )
            room.save()
            logger.info(f"CREATE ROOM: Room created with ID: {room.room_id}")
        
        # Return response with the created room
        response_data = {
            'status': 'success',
            'message': _('Room created successfully'),
            'room': {
                'id': room.room_id,
                'name': room.name,
                'type': room.room_type.name,
                'capacity': room.capacity,
                'fixed_equipment': []  # Empty list as no equipment is added during creation
            }
        }
        logger.info(f"CREATE ROOM: Sending response: {json.dumps(response_data, default=str)}")
        return JsonResponse(response_data)
        
    except IntegrityError as e:
        logger.error(f"CREATE ROOM: Integrity error creating room: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('A room with this name already exists')
        }, status=400)
    except Exception as e:
        logger.error(f"CREATE ROOM: Error creating room: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["PUT"])
def update_room(request, room_id):
    room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment'), room_id=room_id)
    try:
        data = json.loads(request.body)
        logger.info(f"Updating room {room_id} with data: {data}")
        
        # Process equipment IDs outside of transaction
        equipment_ids = []
        if 'fixed_equipment' in data:
            for eq_id in data['fixed_equipment']:
                try:
                    if isinstance(eq_id, str):
                        equipment_ids.append(int(eq_id))
                    elif isinstance(eq_id, int):
                        equipment_ids.append(eq_id)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid equipment ID: {eq_id}")
            
            logger.info(f"Processed equipment IDs for update: {equipment_ids}")
        
        # Store current fixed equipment for comparison - use IDs for accurate comparison
        previous_equipment_ids = set(room.fixed_equipment.values_list('equipment_id', flat=True))
        logger.info(f"Previous fixed equipment IDs: {previous_equipment_ids}")
        
        with transaction.atomic():
            room_type = get_object_or_404(RoomType, room_type_id=data['room_type'])
            
            # Update room basic info
            room.name = data['name']
            room.capacity = data['capacity']
            room.room_type = room_type
            room.description = data.get('description', '')
            room.save()  # Save basic info first
            
            # Handle fixed equipment changes if provided in data
            if 'fixed_equipment' in data:
                # Calculate additions and removals
                new_equipment_ids = set(equipment_ids)
                removed_ids = previous_equipment_ids - new_equipment_ids
                added_ids = new_equipment_ids - previous_equipment_ids
                
                logger.info(f"Equipment changes - Removing: {removed_ids}, Adding: {added_ids}")
                
                # Get equipment objects for the new set
                new_equipment_list = list(Equipment.objects.filter(equipment_id__in=equipment_ids))
                
                if not new_equipment_list and equipment_ids:
                    logger.warning(f"No equipment found for IDs: {equipment_ids}")
                else:
                    logger.info(f"New equipment to set: {[eq.code for eq in new_equipment_list]}")
                
                # Set the new equipment (this automatically handles additions and removals)
                room.fixed_equipment.set(new_equipment_list)
                room.save()  # Explicitly save after M2M changes
                
                # Update equipment status - Handle removed equipment
                if removed_ids:
                    removed_equipment = Equipment.objects.filter(equipment_id__in=removed_ids)
                    for equipment in removed_equipment:
                        try:
                            if equipment.status == 'FIXED':
                                equipment.status = 'READY'
                                equipment.save()
                                logger.info(f"Reset status to READY for equipment {equipment.code}")
                        except Exception as e:
                            logger.error(f"Error resetting equipment status for {equipment.code}: {e}", exc_info=True)
                
                # Update equipment status - Handle added equipment
                if added_ids:
                    added_equipment = Equipment.objects.filter(equipment_id__in=added_ids)
                    for equipment in added_equipment:
                        try:
                            if equipment.status != 'FIXED':
                                equipment.status = 'FIXED'
                                equipment.save()
                                logger.info(f"Updated status to FIXED for equipment {equipment.code}")
                        except Exception as e:
                            logger.error(f"Error setting equipment status for {equipment.code}: {e}", exc_info=True)
            
            # Final verification
            updated_equipment = list(room.fixed_equipment.all())
            logger.info(f"Final equipment after update: {[eq.code for eq in updated_equipment]}")
        
        # Get final data for response
        updated_equipment_ids = list(room.fixed_equipment.values_list('equipment_id', flat=True))
        logger.info(f"Final equipment IDs: {updated_equipment_ids}")
        
        return JsonResponse({
            'status': 'success',
            'message': _('Room updated successfully!'),
            'room': {
                'id': room.room_id,
                'name': room.name,
                'type': room.room_type.name,
                'capacity': room.capacity,
                'fixed_equipment': [
                    {'id': eq.equipment_id, 'name': eq.name, 'code': eq.code}
                    for eq in room.fixed_equipment.all()
                ]
            }
        })
    except Exception as e:
        logger.error(f"Error updating room {room_id}: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["POST"])
def deactivate_room(request, room_id):
    room = get_object_or_404(Room, room_id=room_id)
    try:
        room.is_active = False
        room.save()
        return JsonResponse({
            'status': 'success',
            'message': _('Room deactivated successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["POST"])
def delete_room(request, room_id):
    room = get_object_or_404(Room, room_id=room_id)
    try:
        room.delete()
        return JsonResponse({
            'status': 'success',
            'message': _('Room deleted successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@login_required
@staff_member_required
def sessions_view(request):
    # Get active courses, trainers, rooms, and events from database
    active_sessions = Session.objects.filter(is_active=True).select_related('course', 'room').prefetch_related('trainers', 'trainers__user', 'required_equipment')
    
    # Filter to get only courses assigned to trainers
    active_courses = Course.objects.filter(
        is_active=True,
        trainer__isnull=False
    ).distinct()
    
    active_trainers = Trainer.objects.all().select_related('user')
    active_rooms = Room.objects.filter(is_active=True)
    active_events = Event.objects.filter(is_active=True)
    event_types = EventType.objects.all()
    room_types = RoomType.objects.filter(is_active=True)
    
    # For each course, serialize the equipment_categories and session_room_types JSON for template
    for course in active_courses:
        # Handle equipment_categories
        if course.equipment_categories:
            # The equipment_categories field is already a dictionary,
            # we just need to ensure it's properly formatted for the template
            course.equipment_categories_json = json.dumps(course.equipment_categories)
        else:
            course.equipment_categories_json = '{}'
        
        # Handle session_room_types
        if course.session_room_types:
            try:
                # Check if it's already a dict or needs to be parsed from JSON string
                if isinstance(course.session_room_types, str):
                    # Parse the JSON string to ensure it's valid
                    session_room_types_dict = json.loads(course.session_room_types)
                else:
                    # It's already a dict, use it directly
                    session_room_types_dict = course.session_room_types
                
                # Dump it back to a properly formatted JSON string
                course.session_room_types_json = json.dumps(session_room_types_dict)
            except (json.JSONDecodeError, TypeError) as e:
                # Handle any errors by setting to empty dict
                print(f"Error processing session_room_types for course {course.name_en}: {e}")
                course.session_room_types_json = '{}'
        else:
            course.session_room_types_json = '{}'
    
    # Filter equipment based on their status directly from the Equipment model
    # Only include equipment with READY or BUSY status
    equipment_list = Equipment.objects.filter(
        is_active=True,
        status__in=['READY', 'BUSY']
    ).distinct()
    
    context = {
        'sessions': active_sessions,
        'courses': active_courses,
        'trainers': active_trainers,
        'rooms': active_rooms,
        'events': active_events,
        'event_types': event_types,
        'room_types': room_types,
        'equipment_list': equipment_list,
    }
    
    return render(request, 'website/sessions/index.html', context)


@staff_member_required
@require_http_methods(["POST"])
def delete_session(request, session_id):
    try:
        session = get_object_or_404(Session, session_id=session_id)
        session.delete()
        return JsonResponse({
            'status': 'success',
            'message': _('Session deleted successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@login_required
@staff_member_required
def create_event(request, event_id=None):
    """Create or update an event"""
    # Handle GET request - return form data or specific event data
    if request.method == 'GET':
        # Check if an event_id is provided (for editing)
        # First check if it was passed as a URL parameter
        if not event_id:
            # Then check if it was passed as a query parameter
            event_id = request.GET.get('event_id')
        
        if event_id:
            try:
                event = Event.objects.get(event_id=event_id)
                include_all = request.GET.get('include_all') == 'true'
                response_data = {
                    'status': 'success',
                    'event': {
                        'name': event.name,
                        'description': event.description,
                        'capacity': event.capacity,
                        'event_type': str(event.event_type.event_type_id) if event.event_type else '',
                        'room': str(event.room.room_id) if event.room else '',
                        'room_name': event.room.name if event.room else '',
                        'required_equipment': [str(eq.equipment_id) for eq in event.required_equipment.all()],
                        'start_time': event.start_time.isoformat() if event.start_time else '',
                        'end_time': event.end_time.isoformat() if event.end_time else ''
                    }
                }
                
                # If include_all is true, include additional data needed for the form
                if include_all:
                    try:
                        # Get equipment details
                        equipment_names = []
                        for eq in event.required_equipment.all():
                            equipment_names.append({
                                'id': eq.equipment_id,
                                'name': f"{eq.name} ({eq.code})",
                                'code': eq.code
                            })
                        response_data['event']['equipment_names'] = equipment_names
                        
                        # Get available rooms for the event time period
                        start_time = event.start_time
                        end_time = event.end_time
                        
                        if start_time and end_time:
                            # Get all active rooms
                            all_rooms = Room.objects.filter(is_active=True)
                            
                            # Check which rooms are available
                            available_rooms = []
                            for room in all_rooms:
                                # Check if room is already booked for this time
                                # (excluding this event's booking)
                                is_available = not RoomAvailability.objects.filter(
                                    room=room,
                                    start_date__lt=end_time,
                                    end_date__gt=start_time
                                ).exclude(event=event).exists()
                                
                                if is_available or room.room_id == event.room_id:
                                    available_rooms.append({
                                        'id': str(room.room_id),
                                        'name': room.name
                                    })
                            
                            response_data['available_rooms'] = available_rooms
                            
                            # Get available equipment for the event time period
                            all_equipment = Equipment.objects.filter(is_active=True)
                            available_equipment = []
                            
                            for equipment in all_equipment:
                                # Check if equipment is already booked for this time
                                # (excluding this event's booking)
                                is_available = not EquipmentAvailability.objects.filter(
                                    equipment=equipment,
                                    start_date__lt=end_time,
                                    end_date__gt=start_time
                                ).exclude(event=event).exists()
                                
                                if is_available or event.required_equipment.filter(equipment_id=equipment.equipment_id).exists():
                                    available_equipment.append({
                                        'id': str(equipment.equipment_id),
                                        'name': equipment.name,
                                        'code': equipment.code
                                    })
                            
                            response_data['available_equipment'] = available_equipment
                            
                            # Include event types for completeness
                            event_types = EventType.objects.all()
                            response_data['event_types'] = [
                                {'id': str(et.event_type_id), 'type': et.type}
                                for et in event_types
                            ]
                    except Exception as e:
                        logger.error(f"Error fetching additional data: {e}", exc_info=True)
                        # We'll still return the basic event data even if the additional data fails
                        response_data['warning'] = f"Some additional data could not be loaded: {str(e)}"
                    
                return JsonResponse(response_data)
            except Event.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Event not found')
                }, status=404)
            except Exception as e:
                logger.error(f"Error retrieving event: {e}", exc_info=True)
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
        
        # If no event_id, return form data for creating a new event
        try:
            rooms = Room.objects.filter(is_active=True)
            equipment = Equipment.objects.filter(is_active=True)
            event_types = EventType.objects.all()
            
            return JsonResponse({
                'status': 'success',
                'rooms': [{'id': str(room.room_id), 'name': room.name} for room in rooms],
                'equipment': [{'id': str(eq.equipment_id), 'name': eq.name, 'code': eq.code} for eq in equipment],
                'event_types': [{'id': str(et.event_type_id), 'type': et.type} for et in event_types]
            })
        except Exception as e:
            logger.error(f"Error retrieving form data: {e}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': f'Error loading form data: {str(e)}'
            }, status=500)
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Check if this is a delete request
            action = data.get('action')
            if action == 'delete':
                event_id = data.get('event_id')
                if not event_id:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Event ID is required for deletion')
                    }, status=400)
                
                try:
                    event = Event.objects.get(event_id=event_id)
                    event.delete()  # The model's delete method will handle cleaning up availabilities
                    return JsonResponse({
                        'status': 'success',
                        'message': _('Event deleted successfully')
                    })
                except Event.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Event not found')
                    }, status=404)
                except Exception as e:
                    logger.error(f"Error deleting event: {e}", exc_info=True)
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Error deleting event: {str(e)}'
                    }, status=500)
            
            event_id = data.get('event_id')
            name = data.get('name')
            event_type_id = data.get('event_type')
            room_id = data.get('room')
            capacity = data.get('capacity', 0)
            description = data.get('description', '')
            # Update this line to ensure we're using the 'equipment' field consistently with the frontend
            required_equipment = data.get('equipment', [])
            
            # Debug logging to ensure we're receiving equipment data correctly
            logger.debug(f"Event data: name={name}, event_type={event_type_id}, room={room_id}")
            logger.debug(f"Equipment received: {required_equipment}")
            
            start_time_str = data.get('start_time')
            end_time_str = data.get('end_time')
            force_save = data.get('force_save', False)
            
            # Validate required fields
            if not all([name, event_type_id, room_id, start_time_str, end_time_str]):
                return JsonResponse({
                    'status': 'error',
                    'message': _('Missing required fields')
                }, status=400)
            
            try:
                # Convert string times to datetime objects
                start_time = datetime.fromisoformat(start_time_str.replace('Z', ''))
                end_time = datetime.fromisoformat(end_time_str.replace('Z', ''))
                
                # Make datetimes naive if they're timezone-aware
                if timezone.is_aware(start_time):
                    start_time = timezone.make_naive(start_time)
                if timezone.is_aware(end_time):
                    end_time = timezone.make_naive(end_time)
                
                if end_time <= start_time:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('End time must be after start time')
                    }, status=400)
            except ValueError as e:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Invalid date or time format')
                }, status=400)
            
            # Get or create event instance
            if event_id:
                try:
                    event = Event.objects.get(event_id=event_id)
                except Event.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Event not found')
                    }, status=404)
            else:
                event = Event()
            
            # Get related objects
            try:
                event_type = EventType.objects.get(event_type_id=event_type_id)
                room = Room.objects.get(room_id=room_id)
            except (EventType.DoesNotExist, Room.DoesNotExist):
                return JsonResponse({
                    'status': 'error',
                    'message': _('Invalid event type or room')
                }, status=400)
            
            # Update event fields
            event.name = name
            event.event_type = event_type
            event.room = room
            event.capacity = capacity
            event.description = description
            event.start_time = start_time
            event.end_time = end_time
            
            # Get previous equipment for comparison if updating an existing event
            previous_equipment = []
            if event_id:
                previous_equipment = list(event.required_equipment.all())
            
            # Check availability
            is_available = True
            warnings = []
            room_conflicts = False
            equipment_conflicts = []
            
            try:
                # Check room availability
                overlapping_slots = RoomAvailability.objects.filter(
                    room=room,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Exclude the current event if updating
                if event_id:
                    overlapping_slots = overlapping_slots.exclude(event__event_id=event_id)
                
                if overlapping_slots.exists() and not force_save:
                    is_available = False
                    room_conflicts = True
                    conflict = overlapping_slots.first()
                    
                    conflict_type = "session" if conflict.session else "event"
                    conflict_name = conflict.session.course.name_en if conflict.session else (conflict.event.name if conflict.event else "Unknown")
                    conflict_start = conflict.start_date.strftime("%Y-%m-%d %H:%M")
                    conflict_end = conflict.end_date.strftime("%Y-%m-%d %H:%M")
                    
                    conflict_msg = _(
                        "Room is already booked for another {0} ({1}) from {2} to {3}"
                    ).format(conflict_type, conflict_name, conflict_start, conflict_end)
                    
                    warnings.append(conflict_msg)
            except Exception as e:
                logger.error(f"Error checking room availability: {e}", exc_info=True)
                warnings.append(f"Room availability check error: {str(e)}")
            
            # Check equipment availability if equipment is selected
            if required_equipment:
                try:
                    equipment_objects = Equipment.objects.filter(equipment_id__in=required_equipment)
                    
                    for equipment in equipment_objects:
                        try:
                            # Check for overlapping equipment availability records
                            overlapping_equipment_slots = EquipmentAvailability.objects.filter(
                                equipment=equipment,
                                start_date__lt=end_time,
                                end_date__gt=start_time
                            )
                            
                            # Exclude current event's slots if updating
                            if event_id:
                                overlapping_equipment_slots = overlapping_equipment_slots.exclude(event__event_id=event_id)
                            
                            if overlapping_equipment_slots.exists():
                                is_available = False
                                conflict = overlapping_equipment_slots.first()
                                conflict_info = {
                                    'equipment_name': f"{equipment.name} ({equipment.code})",
                                    'start': conflict.start_date.strftime("%Y-%m-%d %H:%M"),
                                    'end': conflict.end_date.strftime("%Y-%m-%d %H:%M")
                                }
                                equipment_conflicts.append(conflict_info)
                                
                                warnings.append(
                                    _("Equipment '{0}' is not available from {1} to {2}").format(
                                        equipment.name, conflict_info['start'], conflict_info['end']
                                    )
                                )
                        except Exception as e:
                            logger.error(f"Error checking equipment {equipment.code} availability: {e}", exc_info=True)
                            continue
                except Exception as e:
                    logger.error(f"Error in equipment availability checks: {e}", exc_info=True)
                    warnings.append(f"Equipment availability check error: {str(e)}")
            
            # If resources are not available and force_save is not specified, return conflicts
            if not is_available and not force_save:
                return JsonResponse({
                    'status': 'warning',
                    'message': _('Availability conflicts detected'),
                    'warnings': warnings,
                    'room_conflicts': room_conflicts,
                    'equipment_conflicts': equipment_conflicts,
                    'needs_confirmation': True
                })
            
            try:
                # Save the event
                event.save()
                
                # Create or update room availability
                room_availability, created = RoomAvailability.objects.get_or_create(
                    room=room,
                    event=event,
                    defaults={
                        'start_date': start_time,
                        'end_date': end_time
                    }
                )
                
                if not created:
                    room_availability.start_date = start_time
                    room_availability.end_date = end_time
                    room_availability.save()
                
                # Update required equipment and create availability records
                if required_equipment:
                    logger.debug(f"Setting {len(required_equipment)} equipment items for event {event.event_id}")
                    
                    try:
                        # Clear any existing equipment first to avoid duplicates
                        event.required_equipment.clear()
                        
                        # Get the equipment objects - handle both string and integer IDs
                        equipment_ids = [int(eq_id) for eq_id in required_equipment]
                        equipment_objects = Equipment.objects.filter(equipment_id__in=equipment_ids)
                        
                        # Set the new equipment
                        event.required_equipment.set(equipment_objects)
                        
                        logger.debug(f"Successfully set {equipment_objects.count()} equipment items for event {event.event_id}")
                        
                        # Create equipment availability records
                        for equipment in equipment_objects:
                            try:
                                # Create or update equipment availability record
                                equipment_availability, created = EquipmentAvailability.objects.get_or_create(
                                    equipment=equipment,
                                    event=event,
                                    defaults={
                                        'start_date': start_time,
                                        'end_date': end_time,
                                        'booking_id': str(event.event_id),
                                        'booking_type': 'EVENT'
                                    }
                                )
                                
                                if not created:
                                    # Update the dates if the record already exists
                                    equipment_availability.start_date = start_time
                                    equipment_availability.end_date = end_time
                                    equipment_availability.save()
                                
                                # Update equipment status to BUSY
                                if equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                                    equipment.status = 'BUSY'
                                    equipment.save(update_fields=['status'])
                                    logger.debug(f"Set equipment {equipment.code} status to BUSY")
                            except Exception as e:
                                logger.error(f"Error updating equipment availability for {equipment.code}: {e}", exc_info=True)
                                warnings.append(f"Error updating equipment {equipment.code} availability: {str(e)}")
                        
                        # Handle equipment no longer associated with this event
                        if event_id:
                            removed_equipment = [eq for eq in previous_equipment if eq not in equipment_objects]
                            for equipment in removed_equipment:
                                try:
                                    # Delete equipment availability records for this event
                                    EquipmentAvailability.objects.filter(
                                        equipment=equipment,
                                        event=event
                                    ).delete()
                                    
                                    # Check if equipment has any other bookings
                                    has_other_bookings = EquipmentAvailability.objects.filter(
                                        equipment=equipment
                                    ).exists()
                                    
                                    # If no other bookings and not in special status, set to READY
                                    if not has_other_bookings and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                                        equipment.status = 'READY'
                                        equipment.save(update_fields=['status'])
                                        logger.debug(f"Set equipment {equipment.code} status to READY as no longer used in event")
                                except Exception as e:
                                    logger.error(f"Error updating removed equipment {equipment.code}: {e}", exc_info=True)
                                    warnings.append(f"Error updating removed equipment {equipment.code}: {str(e)}")
                    except Exception as e:
                        logger.error(f"Error setting required equipment: {e}", exc_info=True)
                        warnings.append(f"Error setting equipment: {str(e)}")
                else:
                    # No equipment - clear any existing assignments
                    event.required_equipment.clear()
                    logger.debug(f"Cleared all equipment for event {event.event_id}")
                    
                    # If updating an event, handle equipment that was previously assigned but now removed
                    if event_id and previous_equipment:
                        for equipment in previous_equipment:
                            try:
                                # Delete equipment availability records for this event
                                EquipmentAvailability.objects.filter(
                                    equipment=equipment,
                                    event=event
                                ).delete()
                                
                                # Check if equipment has any other bookings
                                has_other_bookings = EquipmentAvailability.objects.filter(
                                    equipment=equipment
                                ).exists()
                                
                                # If no other bookings and not in special status, set to READY
                                if not has_other_bookings and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                                    equipment.status = 'READY'
                                    equipment.save(update_fields=['status'])
                                    logger.debug(f"Set equipment {equipment.code} status to READY as no longer used in event")
                            except Exception as e:
                                logger.error(f"Error updating removed equipment {equipment.code}: {e}", exc_info=True)
                                warnings.append(f"Error updating removed equipment {equipment.code}: {str(e)}")
                
                # Return success response with event details and any warnings
                return JsonResponse({
                    'status': 'success',
                    'message': _('Event {} successfully').format('updated' if event_id else 'created'),
                    'event_id': str(event.event_id),
                    'warnings': warnings if warnings else None
                })
                
            except Exception as e:
                logger.error(f"Error saving event: {e}", exc_info=True)
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
                
        except json.JSONDecodeError:
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid JSON data')
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in create_event: {e}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    # Method not allowed
    return JsonResponse({
        'status': 'error',
        'message': _('Method not allowed')
    }, status=405)

def is_room_available(room, start_time, end_time, exclude_event_id=None, exclude_session_id=None):
    """
    Check if a room is available during the specified time range.
    
    Args:
        room: Room object to check
        start_time: Start datetime
        end_time: End datetime
        exclude_event_id: Event ID to exclude from availability check (for editing existing events)
        exclude_session_id: Session ID to exclude from availability check (for editing existing sessions)
        
    Returns:
        bool: True if room is available, False otherwise
    """
    print(f"Checking if room {room.name} is available from {start_time} to {end_time}, excluding event_id={exclude_event_id}, session_id={exclude_session_id}")
    
    # Check for overlapping events in the Event model
    query = Q(
        room=room,
        is_active=True
    ) & (
        # Event starts during our time frame
        (Q(start_time__gte=start_time) & Q(start_time__lt=end_time)) |
        # Event ends during our time frame
        (Q(end_time__gt=start_time) & Q(end_time__lte=end_time)) |
        # Event spans our entire time frame
        (Q(start_time__lte=start_time) & Q(end_time__gte=end_time))
    )
    
    overlapping_events = Event.objects.filter(query)
    
    # Exclude the event being edited from the availability check
    if exclude_event_id:
        try:
            # Ensure exclude_event_id is a string and not empty
            exclude_event_id_str = str(exclude_event_id).strip()
            if exclude_event_id_str:
                overlapping_events = overlapping_events.exclude(event_id=exclude_event_id_str)
                print(f"Excluding event {exclude_event_id_str} from availability check")
        except Exception as e:
            print(f"Error excluding event: {e}")
    
    # Check for conflict with busy slots in RoomAvailability
    busy_slots_conflict = False
    conflicting_slots = []
    
    try:
        # Check for overlapping availability slots
        overlapping_slots = RoomAvailability.objects.filter(
            room=room,
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        if exclude_event_id:
            # Ensure exclude_event_id is a string
            exclude_event_id_str = str(exclude_event_id)
            # Exclude slots related to the event being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='EVENT',
                event__event_id=exclude_event_id_str
            )
            
        if exclude_session_id:
            # Exclude slots related to the session being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='SESSION',
                session__session_id=exclude_session_id
            )
        
        if overlapping_slots.exists():
            busy_slots_conflict = True
            
            # Collect conflict information
            for slot in overlapping_slots:
                slot_info = {
                    'start_time': slot.start_date.isoformat(),
                    'end_time': slot.end_date.isoformat(),
                    'type': slot.booking_type,
                    'id': slot.booking_id
                }
                conflicting_slots.append(slot_info)
    except Exception as e:
        print(f"Error checking room availability slots: {e}")
    
    # Check if any conflicts found
    is_available = not (overlapping_events.exists() or busy_slots_conflict)
    
    if not is_available:
        print(f"Room {room.name} is NOT available for the requested time")
        
        # Print overlapping events from Event model
        if overlapping_events.exists():
            print(f"Found {overlapping_events.count()} overlapping events in Event model:")
            for event in overlapping_events:
                print(f"  - Event: {event.name}, Time: {event.start_time} to {event.end_time}")
        
        # Print conflicting busy slots
        if conflicting_slots:
            print(f"Found {len(conflicting_slots)} conflicting busy slots:")
            for slot in conflicting_slots:
                print(f"  - {slot['type']} {slot['id']}, Time: {slot['start_time']} to {slot['end_time']}")
    else:
        print(f"Room {room.name} IS available for the requested time")
    
    return is_available

@login_required
def check_room_availability(request):
    """
    Check if a room is available for booking at a specific time range.
    """
    if request.method != 'POST':
        return JsonResponse({
            'status': 'error',
            'message': _('Method not allowed')
        }, status=405)
    
    try:
        data = json.loads(request.body)
        room_id = data.get('room_id')
        start_time_str = data.get('start_time')
        end_time_str = data.get('end_time')
        event_id = data.get('event_id', '')
        get_available = data.get('get_available', False)
        
        print(f"Checking room availability: room={room_id}, start={start_time_str}, end={end_time_str}, event_id={event_id}, get_available={get_available}")
        
        if not all([start_time_str, end_time_str]):
            return JsonResponse({
                'status': 'error',
                'message': _('Missing required fields')
            })
        
        try:
            start_time = datetime.fromisoformat(start_time_str.replace('Z', ''))
            end_time = datetime.fromisoformat(end_time_str.replace('Z', ''))
            
            if timezone.is_aware(start_time):
                start_time = timezone.make_naive(start_time)
            if timezone.is_aware(end_time):
                end_time = timezone.make_naive(end_time)
        except ValueError as e:
            print(f"Date parsing error in availability check: {e}")
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid date or time format')
            }, status=400)
        
        # Validate time range
        if end_time <= start_time:
            return JsonResponse({
                'status': 'error',
                'message': _('End time must be after start time')
            })
            
        # If we're requesting available rooms, find all available rooms
        if get_available:
            # Get all active rooms
            all_rooms = Room.objects.filter(is_active=True)
            available_rooms = []
            
            for room in all_rooms:
                is_available = is_room_available(room, start_time, end_time, exclude_event_id=event_id)
                if is_available:
                    available_rooms.append({
                        'id': str(room.room_id),
                        'name': room.name,
                        'capacity': room.capacity
                    })
            
            return JsonResponse({
                'status': 'success',
                'available_rooms': available_rooms
            })
            
        # If checking a specific room
        try:
            room = Room.objects.get(room_id=room_id)
        except Room.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Room not found')
            })
        
        # Get conflict information
        conflict_info = get_room_conflict_details(room, start_time, end_time, exclude_event_id=event_id)
        
        # Check availability
        is_available = is_room_available(room, start_time, end_time, exclude_event_id=event_id)
        
        if is_available:
            return JsonResponse({
                'status': 'success',
                'available': True,
                'message': _('Room is available for the selected time')
            })
        else:
            # Format conflict message
            conflict_message = _('Room is not available at the selected time. ')
            
            if conflict_info['events'] or conflict_info['sessions']:
                conflict_message += _('The room is booked for: ')
                
                if conflict_info['events']:
                    event_names = [f"{e['name']} ({format_time_range(e['start'], e['end'])})" for e in conflict_info['events'][:3]]
                    conflict_message += ', '.join(event_names)
                    
                    if len(conflict_info['events']) > 3:
                        conflict_message += _(' and {} more events').format(len(conflict_info['events']) - 3)
                
                if conflict_info['events'] and conflict_info['sessions']:
                    conflict_message += '; '
                
                if conflict_info['sessions']:
                    session_names = [f"{s['id']} ({format_time_range(s['start'], s['end'])})" for s in conflict_info['sessions'][:3]]
                    conflict_message += ', '.join(session_names)
                    
                    if len(conflict_info['sessions']) > 3:
                        conflict_message += _(' and {} more sessions').format(len(conflict_info['sessions']) - 3)
            
            conflict_message += _(' Please select a different time or date.')
            
            return JsonResponse({
                'status': 'success',
                'available': False,
                'message': conflict_message,
                'conflicts': conflict_info
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        })
    except Exception as e:
        print(f"Error in check_room_availability: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })

def get_room_conflict_details(room, start_time, end_time, exclude_event_id=None, exclude_session_id=None):
    """
    Get detailed information about conflicting events and sessions for a room.
    
    Args:
        room: Room object to check
        start_time: Start datetime
        end_time: End datetime
        exclude_event_id: Event ID to exclude from conflicts
        exclude_session_id: Session ID to exclude from conflicts
        
    Returns:
        dict: Information about conflicting events and sessions
    """
    conflict_info = {
        'events': [],
        'sessions': []
    }
    
    # Check for overlapping events
    query = Q(
        room=room,
        is_active=True
    ) & (
        # Event starts during our time frame
        (Q(start_time__gte=start_time) & Q(start_time__lt=end_time)) |
        # Event ends during our time frame
        (Q(end_time__gt=start_time) & Q(end_time__lte=end_time)) |
        # Event spans our entire time frame
        (Q(start_time__lte=start_time) & Q(end_time__gte=end_time))
    )
    
    overlapping_events = Event.objects.filter(query)
    
    # Exclude the event being edited
    if exclude_event_id and exclude_event_id.strip():
        try:
            overlapping_events = overlapping_events.exclude(event_id=exclude_event_id)
        except Exception as e:
            print(f"Error excluding event: {e}")
    
    # Add event conflicts
    for event in overlapping_events:
        conflict_info['events'].append({
            'id': str(event.event_id),
            'name': event.name,
            'start': event.start_time.isoformat(),
            'end': event.end_time.isoformat(),
            'type': 'event'
        })
    
    # Check room availability for busy slots
    try:
        # Query for overlapping availability slots
        overlapping_slots = RoomAvailability.objects.filter(
            room=room,
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        if exclude_event_id:
            # Exclude slots related to the event being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='EVENT',
                event__event_id=exclude_event_id
            )
            
        if exclude_session_id:
            # Exclude slots related to the session being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='SESSION',
                session__session_id=exclude_session_id
            )
        
        # Process each overlapping slot
        for slot in overlapping_slots:
            if slot.booking_type == 'SESSION' and slot.session:
                conflict_info['sessions'].append({
                    'id': slot.session.session_id,
                    'start': slot.start_date.isoformat(),
                    'end': slot.end_date.isoformat(),
                    'type': 'session',
                    'time': format_time_range(slot.start_date.isoformat(), slot.end_date.isoformat())
                })
            elif slot.booking_type == 'EVENT' and slot.event:
                conflict_info['events'].append({
                    'id': str(slot.event.event_id),
                    'name': slot.event.name,
                    'start_time': slot.start_date.isoformat(),
                    'end_time': slot.end_date.isoformat(),
                    'type': 'event',
                    'time': format_time_range(slot.start_date.isoformat(), slot.end_date.isoformat())
                })
    except Exception as e:
        print(f"Error checking room availability slots: {e}")
    
    return conflict_info

def format_time_range(start_iso, end_iso):
    """Format a time range for display in conflict messages"""
    try:
        start = datetime.fromisoformat(start_iso.replace('Z', '+00:00'))
        end = datetime.fromisoformat(end_iso.replace('Z', '+00:00'))
        
        # If same day, only show time for end
        if start.date() == end.date():
            return f"{start.strftime('%b %d, %H:%M')} - {end.strftime('%H:%M')}"
        else:
            return f"{start.strftime('%b %d, %H:%M')} - {end.strftime('%b %d, %H:%M')}"
    except:
        # Fallback if parsing fails
        return f"{start_iso} - {end_iso}"

@staff_member_required
@require_http_methods(["POST"])
def create_equipment(request):
    try:
        data = json.loads(request.body)
        
        # Check if code exists if provided
        code = data.get('code')
        if code and Equipment.objects.filter(code=code).exists():
            return JsonResponse({
                'status': 'error',
                'message': _('Equipment code already exists')
            }, status=400)
        
        # Get the category, brand, and model instances
        try:
            category = Category.objects.get(category_id=data['category'])
            brand = Brand.objects.get(brand_id=data['brand'])
            model = Model.objects.get(model_id=data['model'])
            
            # Verify that the model belongs to the selected brand
            if model.brand != brand:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected model does not belong to the selected brand')
                }, status=400)
            
            # Check if model has available stock
            if model.stock <= 0:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected model is out of stock')
                }, status=400)
            
        except (Category.DoesNotExist, Brand.DoesNotExist, Model.DoesNotExist) as e:
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid category, brand, or model selected')
            }, status=400)
        
        # Create the equipment and decrease model stock in a transaction
        with transaction.atomic():
            # Decrease model stock
            if not model.decrease_stock():
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected model is out of stock')
                }, status=400)
            
            # Create the equipment
            equipment = Equipment.objects.create(
                name=data['name'],
                category=category,
                brand=brand,
                model=model,
                description=data.get('description', ''),
                status='READY',  # Set initial status to READY
                is_active=True,
                code=code if code else None  # Let model generate code if not provided
            )
            
            # Create availability record
            EquipmentAvailability.objects.create(
                equipment=equipment
            )
        
        return JsonResponse({
            'status': 'success',
            'message': _('Equipment created successfully'),
            'equipment': {
                'id': equipment.equipment_id,
                'code': equipment.code,
                'name': equipment.name
            }
        })
    except KeyError as e:
        return JsonResponse({
            'status': 'error',
            'message': _('Missing required field: {}').format(str(e))
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
def equipment_view(request):
    equipment_list = Equipment.objects.all()
    categories = Category.objects.filter(is_active=True).order_by('name')
    brands = Brand.objects.filter(is_active=True).order_by('name')
    models = Model.objects.filter(is_active=True).select_related('brand').order_by('brand__name', 'name')
    
    context = {
        'equipment_list': equipment_list,
        'categories': categories,
        'brands': brands,
        'models': models,
    }
    return render(request, 'website/rooms/index.html', context)

@staff_member_required
@require_http_methods(["POST"])
def delete_equipment(request, equipment_id):
    try:
        equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
        equipment.delete()
        return JsonResponse({
            'status': 'success',
            'message': _('Equipment deleted successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["GET", "PUT", "DELETE"])
def equipment_detail(request, equipment_id):
    try:
        equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
        
        if request.method == "GET":
            return JsonResponse({
                'status': 'success',
                'equipment': {
                    'equipment_id': equipment.equipment_id,
                    'code': equipment.code,
                    'name': equipment.name,
                    'category': equipment.category.category_id,
                    'brand': equipment.brand.brand_id,
                    'model': equipment.model.model_id,
                    'status': equipment.status,
                    'description': equipment.description
                }
            })
            
        elif request.method == "PUT":
            data = json.loads(request.body)
            
            # Validate required fields
            required_fields = ['name', 'category', 'brand', 'model', 'status']
            if not all(field in data for field in required_fields):
                return JsonResponse({
                    'status': 'error',
                    'message': _('Missing required fields')
                }, status=400)
            
            try:
                category = Category.objects.get(category_id=data['category'])
                brand = Brand.objects.get(brand_id=data['brand'])
                model = Model.objects.get(model_id=data['model'])
                
                # Validate that model belongs to brand
                if model.brand != brand:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Selected model does not belong to the selected brand')
                    }, status=400)
                
                # Update equipment fields
                equipment.name = data['name']
                equipment.category = category
                equipment.brand = brand
                equipment.model = model
                equipment.status = data['status']
                equipment.description = data.get('description', '')
                
                if 'code' in data and data['code'] != equipment.code:
                    # Check if new code is unique
                    if Equipment.objects.filter(code=data['code']).exclude(equipment_id=equipment_id).exists():
                        return JsonResponse({
                            'status': 'error',
                            'message': _('Equipment code already exists')
                        }, status=400)
                    equipment.code = data['code']
                
                equipment.save()
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Equipment updated successfully')
                })
                
            except (Category.DoesNotExist, Brand.DoesNotExist, Model.DoesNotExist) as e:
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=400)
                
        elif request.method == "DELETE":
            equipment.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Equipment deleted successfully')
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@trainer_required
def duplicate_course(request, course_id):
    """Trainers can duplicate their own courses, supervisors can duplicate any course"""
    original_course = get_object_or_404(Course, course_id=course_id)
    is_supervisor = request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and original_course not in request.user.trainer.courses.all():
        messages.error(request, _('You do not have permission to duplicate this course.'))
        return redirect('website:courses')
    
    try:
        # Create a new course with the same attributes as the original
        new_course = Course.objects.create(
            name_en=f"{original_course.name_en} (Copied)",
            name_ar=f"{original_course.name_ar} (نسخة)",
            description_en=original_course.description_en,
            description_ar=original_course.description_ar,
            category=original_course.category,
            location=original_course.location,
            session_type=original_course.session_type,
            num_of_sessions=original_course.num_of_sessions,
            capacity=original_course.capacity,
            prerequisite=original_course.prerequisite,
            icon_svg=original_course.icon_svg,
            order=original_course.order,
            equipment_categories=original_course.equipment_categories,
            session_room_types=original_course.session_room_types,
            status='ACTIVE',
            is_active=True
        )
        
        # Add the new course to trainer's courses
        request.user.trainer.courses.add(new_course)
            
        messages.success(request, _('Course duplicated successfully!'))
        return redirect('website:course_detail', course_id=new_course.course_id)
    except Exception as e:
        messages.error(request, _(f'Failed to duplicate course. Error: {str(e)}'))
        return redirect('website:course_detail', course_id=course_id)

@staff_member_required
@require_http_methods(["GET"])
def check_availability(request):
    """API endpoint to check resource availability for a time period"""
    logger = logging.getLogger(__name__)
    try:
        start_time_str = request.GET.get('start_time')
        end_time_str = request.GET.get('end_time')
        session_id = request.GET.get('session_id')
        
        logger.debug(f"Checking availability for time range: {start_time_str} to {end_time_str}, session_id: {session_id}")
        
        if not start_time_str or not end_time_str:
            return JsonResponse({
                'status': 'error',
                'message': 'Missing start_time or end_time parameters'
            }, status=400)
            
        # Parse the date strings (handle both ISO format and URL-encoded format)
        try:
            # First try parsing as ISO format
            try:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            except ValueError:
                # If that fails, try parse_datetime
                start_time = parse_datetime(start_time_str)
                end_time = parse_datetime(end_time_str)
                
                if start_time is None or end_time is None:
                    raise ValueError('Could not parse datetime strings')
                    
            # Make sure times are timezone aware
            if timezone.is_naive(start_time):
                start_time = timezone.make_aware(start_time)
            if timezone.is_naive(end_time):
                end_time = timezone.make_aware(end_time)
                
            # Convert to naive datetime for database queries
            start_time = timezone.make_naive(start_time)
            end_time = timezone.make_naive(end_time)
                
        except (ValueError, TypeError) as e:
            logger.error(f"Error parsing dates: {e}, start_time={start_time_str}, end_time={end_time_str}")
            return JsonResponse({
                'status': 'error',
                'message': f'Error parsing dates: {str(e)}'
            }, status=400)
            
        # Validate time period
        if end_time <= start_time:
            return JsonResponse({
                'status': 'error',
                'message': 'End time must be after start time'
            }, status=400)
            
        # Get all trainers
        all_trainers = Trainer.objects.all()
        logger.debug(f"Found {all_trainers.count()} trainers")
        
        # Find busy trainers during this time period by checking availability records
        busy_trainers = set()
        
        for trainer in all_trainers:
            try:
                # Skip checking the current session's availability if we're editing
                if session_id:
                    try:
                        session = Session.objects.get(session_id=session_id)
                        if trainer in session.trainers.all():
                            logger.debug(f"Skipping trainer {trainer.trainer_id} as they are assigned to session {session_id}")
                            continue  # Skip this trainer, they're already assigned to this session
                    except Session.DoesNotExist:
                        logger.warning(f"Session {session_id} not found")
                        pass
                
                # Check if trainer has overlapping availability slots
                query = TrainerAvailability.objects.filter(
                    trainer=trainer,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Only add the exclude if session_id is provided
                if session_id:
                    query = query.exclude(session__session_id=session_id)
                    
                has_conflict = query.exists()
                
                if has_conflict:
                    busy_trainers.add(trainer.trainer_id)
                    logger.debug(f"Trainer {trainer.trainer_id} is busy during requested time")
                    
            except Exception as e:
                logger.error(f"Error checking trainer {trainer.trainer_id} availability: {e}", exc_info=True)
                continue
                
        # Get available trainers (those not in busy_trainers)
        available_trainers = [trainer.trainer_id for trainer in all_trainers if trainer.trainer_id not in busy_trainers]
        logger.debug(f"Found {len(available_trainers)} available trainers")
        
        # Get all rooms
        all_rooms = Room.objects.filter(is_active=True)
        logger.debug(f"Found {all_rooms.count()} rooms")
        
        # Find busy rooms during this time period by checking availability records
        busy_rooms = set()
        
        for room in all_rooms:
            try:
                # Skip checking the current session's availability if we're editing
                if session_id:
                    try:
                        session = Session.objects.get(session_id=session_id)
                        if session.room and session.room.room_id == room.room_id:
                            logger.debug(f"Skipping room {room.room_id} as it is assigned to session {session_id}")
                            continue  # Skip this room, it's already assigned to this session
                    except Session.DoesNotExist:
                        logger.warning(f"Session {session_id} not found")
                        pass
                
                # Check if room has overlapping availability slots
                query = RoomAvailability.objects.filter(
                    room=room,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Only add the exclude if session_id is provided
                if session_id:
                    query = query.exclude(session__session_id=session_id)
                    
                has_conflict = query.exists()
                
                if has_conflict:
                    busy_rooms.add(room.room_id)
                    logger.debug(f"Room {room.room_id} is busy during requested time")
                    
            except Exception as e:
                logger.error(f"Error checking room {room.room_id} availability: {e}", exc_info=True)
                continue
                
        # Get available rooms (those not in busy_rooms)
        available_rooms = [room.room_id for room in all_rooms if room.room_id not in busy_rooms]
        logger.debug(f"Found {len(available_rooms)} available rooms")
        
        # Return available resources
        return JsonResponse({
            'status': 'success',
            'available_resources': {
                'trainers': available_trainers,
                'rooms': available_rooms
            },
            'time_range': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"Error in check_availability: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["GET"])
def check_equipment_code(request):
    code = request.GET.get('code')
    if not code:
        return JsonResponse({
            'status': 'error',
            'message': _('Code is required')
        }, status=400)
        
    exists = Equipment.objects.filter(code=code).exists()
    return JsonResponse({
        'status': 'success',
        'exists': exists
    })

@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def category_api(request, category_id=None):
    """API endpoint for managing categories"""
    print(f"\nCategory API called with method: {request.method}")
    print(f"Request path: {request.path}")
    print(f"Content type: {request.content_type}")
    
    try:
        if request.method == "DELETE" and category_id:
            # Delete category
            category = get_object_or_404(Category, category_id=category_id)
            category.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Category deleted successfully')
            })
        elif request.method == "GET":
            if category_id:
                # Get specific category
                category = get_object_or_404(Category, category_id=category_id)
                return JsonResponse({
                    'status': 'success',
                    'category': {
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    }
                })
            else:
                # List all categories
                categories = Category.objects.filter(is_active=True)
                return JsonResponse({
                    'status': 'success',
                    'categories': [{
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    } for category in categories]
                })
        
        elif request.method == "PUT" and category_id:
            # Update existing category
            print("\nProcessing PUT request")
            print("Request body:", request.body.decode('utf-8'))
            
            try:
                # Print request content type for debugging
                logger.info(f"Request Content-Type: {request.content_type}")
                logger.info(f"Request body: {request.body.decode('utf-8')}")
                
                # Handle both application/json and form data
                if request.content_type == 'application/json':
                    try:
                        data = json.loads(request.body)
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON decode error: {e}")
                        return JsonResponse({
                            'success': False, 
                            'message': f'Invalid JSON data: {str(e)}'
                        }, status=400)
                else:
                    # Try to get data from POST
                    data = request.POST.dict()
                    
                # If still no data, try to get it from body
                if not data and request.body:
                    try:
                        data = json.loads(request.body)
                    except json.JSONDecodeError:
                        logger.error("Failed to parse request body as JSON")
                        
                logger.info(f"Parsed data: {data}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                return JsonResponse({
                    'success': False, 
                    'message': f'Invalid JSON data: {str(e)}'
                }, status=400)
            except Exception as e:
                logger.error(f"Error parsing request data: {e}", exc_info=True)
                return JsonResponse({
                    'success': False,
                    'message': f'Error parsing request data: {str(e)}'
                }, status=400)
            
            name = data.get('name')
            print(f"Parsed name: {name}")
            
            if not name:
                print("Error: Name is required")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Category name is required')
                }, status=400)
            
            # Get the category to update
            category = get_object_or_404(Category, category_id=category_id)
            
            # Check if another category with this name exists
            if Category.objects.filter(name=name).exclude(category_id=category_id).exists():
                print(f"Error: Category '{name}' already exists")
                return JsonResponse({
                    'status': 'error',
                    'message': _('A category with this name already exists')
                }, status=400)
            
            try:
                # Update the category
                category.name = name
                category.save()
                
                print(f"Updated category: {category.category_id} - {category.name}")
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Category updated successfully'),
                    'category': {
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    }
                })
            except Exception as e:
                print(f"Error updating category: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
        
        elif request.method == "POST":
            print("\nProcessing POST request")
            print("Request body:", request.body.decode('utf-8'))
            
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw request body: {request.body}")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Invalid JSON data')
                }, status=400)
            
            name = data.get('name')
            print(f"Parsed name: {name}")
            
            if not name:
                print("Error: Name is required")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Category name is required')
                }, status=400)
            
            # Check if category with this name already exists
            if Category.objects.filter(name=name).exists():
                print(f"Error: Category '{name}' already exists")
                return JsonResponse({
                    'status': 'error',
                    'message': _('A category with this name already exists')
                }, status=400)
            
            try:
                # Create the category
                category = Category.objects.create(
                    name=name,
                    is_active=True
                )
                
                print(f"Created category: {category.category_id} - {category.name}")
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Category created successfully'),
                    'category': {
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    }
                })
            except Exception as e:
                print(f"Error creating category: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
            
    except Exception as e:
        print(f"Unexpected error in category_api: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def brand_api(request, brand_id=None):
    """API endpoint for managing brands"""
    try:
        if request.method == "DELETE" and brand_id:
            # Delete brand
            brand = get_object_or_404(Brand, brand_id=brand_id)
            brand.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Brand deleted successfully')
            })
        elif request.method == "GET":
            if brand_id:
                brand = get_object_or_404(Brand, brand_id=brand_id)
                return JsonResponse({
                    'status': 'success',
                    'brand': {
                        'id': brand.brand_id,
                        'name': brand.name,
                        'is_active': brand.is_active
                    }
                })
            else:
                brands = Brand.objects.filter(is_active=True)
                return JsonResponse({
                    'status': 'success',
                    'brands': [{
                        'id': brand.brand_id,
                        'name': brand.name,
                        'is_active': brand.is_active
                    } for brand in brands]
                })
        
        elif request.method == "POST":
            data = json.loads(request.body)
            name = data.get('name')
            
            if not name:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Brand name is required')
                }, status=400)
            
            # Check if brand with this name already exists
            if Brand.objects.filter(name=name).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A brand with this name already exists')
                }, status=400)
            
            brand = Brand.objects.create(
                name=name,
                is_active=True
            )
            
            return JsonResponse({
                'status': 'success',
                'message': _('Brand created successfully'),
                'brand': {
                    'id': brand.brand_id,
                    'name': brand.name,
                    'is_active': brand.is_active
                }
            })
            
        elif request.method == "PUT":
            if not brand_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Brand ID is required')
                }, status=400)
                
            brand = get_object_or_404(Brand, brand_id=brand_id)
            data = json.loads(request.body)
            
            name = data.get('name')
            if not name:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Brand name is required')
                }, status=400)
            
            # Check if another brand with this name exists
            if Brand.objects.filter(name=name).exclude(brand_id=brand_id).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A brand with this name already exists')
                }, status=400)
            
            brand.name = name
            brand.save()
            
            return JsonResponse({
                'status': 'success',
                'message': _('Brand updated successfully'),
                'brand': {
                    'id': brand.brand_id,
                    'name': brand.name,
                    'is_active': brand.is_active
                }
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def model_api(request, model_id=None):
    """API endpoint for managing models"""
    try:
        if request.method == "DELETE" and model_id:
            # Delete model
            model = get_object_or_404(Model, model_id=model_id)
            model.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Model deleted successfully')
            })
        elif request.method == "GET":
            if model_id:
                model = get_object_or_404(Model, model_id=model_id)
                return JsonResponse({
                    'status': 'success',
                    'model': {
                        'id': model.model_id,
                        'name': model.name,
                        'brand': model.brand.brand_id,
                        'brand_name': model.brand.name,
                        'stock': model.stock,
                        'is_active': model.is_active
                    }
                })
            else:
                models = Model.objects.filter(is_active=True).select_related('brand')
                return JsonResponse({
                    'status': 'success',
                    'models': [{
                        'id': model.model_id,
                        'name': model.name,
                        'brand': model.brand.brand_id,
                        'brand_name': model.brand.name,
                        'stock': model.stock,
                        'is_active': model.is_active
                    } for model in models]
                })
        
        elif request.method == "POST":
            data = json.loads(request.body)
            name = data.get('name')
            brand_id = data.get('brand')
            stock = data.get('stock', 1)  # Default to 1 if not provided
            
            if not name or not brand_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Model name and brand are required')
                }, status=400)
            
            try:
                brand = Brand.objects.get(brand_id=brand_id)
            except Brand.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected brand does not exist')
                }, status=400)
            
            # Check if model with this name already exists for this brand
            if Model.objects.filter(name=name, brand=brand).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A model with this name already exists for this brand')
                }, status=400)
            
            model = Model.objects.create(
                name=name,
                brand=brand,
                stock=stock,
                is_active=True
            )
            
            return JsonResponse({
                'status': 'success',
                'message': _('Model created successfully'),
                'model': {
                    'id': model.model_id,
                    'name': model.name,
                    'brand': model.brand.brand_id,
                    'brand_name': model.brand.name,
                    'stock': model.stock,
                    'is_active': model.is_active
                }
            })
            
        elif request.method == "PUT":
            if not model_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Model ID is required')
                }, status=400)
                
            model = get_object_or_404(Model, model_id=model_id)
            data = json.loads(request.body)
            
            name = data.get('name')
            brand_id = data.get('brand')
            stock = data.get('stock')
            
            if not name or not brand_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Model name and brand are required')
                }, status=400)
            
            try:
                brand = Brand.objects.get(brand_id=brand_id)
            except Brand.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected brand does not exist')
                }, status=400)
            
            # Check if another model with this name exists for this brand
            if Model.objects.filter(name=name, brand=brand).exclude(model_id=model_id).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A model with this name already exists for this brand')
                }, status=400)
            
            model.name = name
            model.brand = brand
            if stock is not None:
                model.stock = stock
            model.save()
            
            return JsonResponse({
                'status': 'success',
                'message': _('Model updated successfully'),
                'model': {
                    'id': model.model_id,
                    'name': model.name,
                    'brand': model.brand.brand_id,
                    'brand_name': model.brand.name,
                    'stock': model.stock,
                    'is_active': model.is_active
                }
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

def check_equipment_availability(equipment, start_time, end_time, exclude_event_id=None, exclude_session_id=None):
    """
    Check if equipment is available for a given time slot.
    Returns a list of conflicts if any, otherwise returns None.
    
    NOTE: Currently modified to always return None (no conflicts) to allow equipment selection
    to override availability checks during event rescheduling.
    """
    # TEMPORARY FIX: Always return None (no conflicts)
    # This allows equipment that appears in the dropdown to be selected without additional checks
    return None
    
    # Original code below is kept for reference but not executed
    """
    try:
        # Check if equipment status prevents booking
        if equipment.status in ['MAINTENANCE', 'SCRAP', 'FIXED']:
            return [{
                'type': 'status',
                'status': equipment.status,
                'message': f"Equipment is {equipment.get_status_display()}"
            }]
            
        # Make times timezone naive if they are aware
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
        
        # Check for overlapping availability slots
        conflicts = []
        overlapping_slots = EquipmentAvailability.objects.filter(
            equipment=equipment,
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        # Exclude the event being edited
        if exclude_event_id:
            # Ensure exclude_event_id is a string
            exclude_event_id_str = str(exclude_event_id)
            overlapping_slots = overlapping_slots.exclude(
                booking_type='EVENT',
                event__event_id=exclude_event_id_str
            )
        
        # Exclude the session being edited
        if exclude_session_id:
            overlapping_slots = overlapping_slots.exclude(
                booking_type='SESSION',
                session__session_id=exclude_session_id
            )
        
        for slot in overlapping_slots:
            conflict = {
                'type': slot.booking_type.lower() if slot.booking_type else 'unknown',
                'id': slot.booking_id,
                'start': slot.start_date.isoformat(),
                'end': slot.end_date.isoformat(),
                'message': f"Equipment is booked from {slot.start_date.strftime('%Y-%m-%d %H:%M')} to {slot.end_date.strftime('%Y-%m-%d %H:%M')}"
            }
            conflicts.append(conflict)
        
        return conflicts if conflicts else None
        
    except Exception as e:
        print(f"Error checking equipment availability: {e}")
        return None
    """

@login_required
@require_http_methods(["POST"])
def check_equipment_availability_view(request):
    """Check equipment availability for a given time slot."""
    logger = logging.getLogger(__name__)
    try:
        data = json.loads(request.body)
        start_time_str = data.get('start_time')
        end_time_str = data.get('end_time')
        event_id = data.get('event_id')
        session_id = data.get('session_id')
        
        logger.debug(f"Equipment availability check request: {data}")
        
        if not all([start_time_str, end_time_str]):
            return JsonResponse({
                'status': 'error',
                'message': _('Start time and end time are required')
            }, status=400)
        
        try:
            # Convert string times to datetime objects
            start_time = datetime.fromisoformat(start_time_str.replace('Z', ''))
            end_time = datetime.fromisoformat(end_time_str.replace('Z', ''))
            
            # Make datetimes naive if they're timezone-aware
            if timezone.is_aware(start_time):
                start_time = timezone.make_naive(start_time)
            if timezone.is_aware(end_time):
                end_time = timezone.make_naive(end_time)
            
            if end_time <= start_time:
                return JsonResponse({
                    'status': 'error',
                    'message': _('End time must be after start time')
                }, status=400)
        except ValueError as e:
            logger.error(f"Invalid date format: {e}, start_time={start_time_str}, end_time={end_time_str}")
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid date or time format: ') + str(e)
            }, status=400)
        
        # Get only equipment with READY or BUSY availability status
        all_equipment = Equipment.objects.filter(
            is_active=True,
            status__in=['READY', 'BUSY']
        ).distinct()
        
        available_equipment = []
        conflicts = []
        
        for equipment in all_equipment:
            # Check equipment availability
            equipment_conflicts = check_equipment_availability(
                equipment,
                start_time,
                end_time,
                exclude_event_id=event_id,
                exclude_session_id=session_id
            )
            
            if not equipment_conflicts:
                available_equipment.append({
                    'id': str(equipment.equipment_id),
                    'name': equipment.name,
                    'code': equipment.code
                })
            else:
                conflicts.append({
                    'equipment_id': str(equipment.equipment_id),
                    'equipment_name': f"{equipment.name} ({equipment.code})",
                    'conflicts': equipment_conflicts
                })
        
        return JsonResponse({
            'status': 'success',
            'available_equipment': available_equipment,
            'conflicts': conflicts if conflicts else None
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        logger.error(f"Error checking equipment availability: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
def edit_room(request, room_id):
    """View function to retrieve room details for editing"""
    room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment'), room_id=room_id)
    logger = logging.getLogger(__name__)
    logger.info(f"Fetching room {room_id} for editing")
    
    # Format fixed equipment as array of objects with id property
    fixed_equipment = [{'id': eq_id} for eq_id in room.fixed_equipment.values_list('equipment_id', flat=True)]
    logger.info(f"Fixed equipment for room {room_id}: {fixed_equipment}")
    
    return JsonResponse({
        'room': {
            'name': room.name,
            'capacity': room.capacity,
            'room_type': room.room_type.room_type_id,
            'description': room.description,
            'fixed_equipment': fixed_equipment
        }
    })

@staff_member_required
def room_detail(request, room_id):
    room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment', 'fixed_equipment__category'), room_id=room_id)
    return render(request, 'website/rooms/detail.html', {
        'room': room,
        'equipment_list': Equipment.objects.filter(is_active=True).order_by('code'),
        'room_types': RoomType.objects.filter(is_active=True)
    })

@staff_member_required
@require_http_methods(["GET"])
def room_available_equipment(request, room_id):
    """API endpoint to get available equipment for a room (READY status + already assigned equipment)"""
    try:
        room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment'), room_id=room_id)
        
        # Get IDs of equipment already fixed in this room
        fixed_equipment_ids = list(room.fixed_equipment.values_list('equipment_id', flat=True))
        
        # Get equipment with READY status from equipment availability
        ready_equipment = Equipment.objects.filter(
            status='READY',
            is_active=True
        ).exclude(equipment_id__in=fixed_equipment_ids)
        
        # Get equipment already fixed in this room
        fixed_equipment = room.fixed_equipment.all()
        
        # Combine both lists
        all_equipment = []
        
        # Add fixed equipment (already assigned to this room)
        for equipment in fixed_equipment:
            all_equipment.append({
                'equipment_id': equipment.equipment_id,
                'name': equipment.name,
                'code': equipment.code,
                'is_fixed': True
            })
        
        # Add ready equipment (available to be assigned)
        for equipment in ready_equipment:
            all_equipment.append({
                'equipment_id': equipment.equipment_id,
                'name': equipment.name,
                'code': equipment.code,
                'is_fixed': False
            })
        
        return JsonResponse({
            'equipment': all_equipment
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def calendar_events_api(request):
    """API endpoint to get events for the calendar"""
    from django.http import JsonResponse
    from django.utils import timezone
    from django.db.models import Count
    import json
    import logging
    from datetime import timedelta, datetime
    
    logger = logging.getLogger(__name__)
    logger.info("Calendar API called with params: %s", request.GET)
    
    try:
        # Initialize empty result array
        events = []
        
        # Check if Session model has any records at all
        total_session_count = Session.objects.count()
        total_event_count = Event.objects.count()
        logger.info("Total records in database: %d Sessions, %d Events", total_session_count, total_event_count)
        
        if total_session_count == 0 and total_event_count == 0:
            logger.warning("No sessions or events exist in the database at all!")
            # Just return an empty list with a clear message
            return JsonResponse({
                'events': [],
                'message': 'No events or sessions found in the database'
            })
        
        # Get filter parameters from request
        room_id = request.GET.get('room_id')
        trainer_id = request.GET.get('trainer_id')
        equipment_id = request.GET.get('equipment_id')
        with_equipment = request.GET.get('with_equipment', 'false').lower() == 'true'
        
        if room_id:
            logger.info(f"Filtering events for room_id: {room_id}")
        if trainer_id:
            logger.info(f"Filtering events for trainer_id: {trainer_id}")
        if equipment_id:
            logger.info(f"Filtering events for equipment_id: {equipment_id}")
        if with_equipment:
            logger.info("Filtering to only show events/sessions with equipment assigned")
        
        # Get sessions, filtered by parameters if specified
        try:
            # Start with basic query using select_related to optimize
            sessions_query = Session.objects.select_related('course', 'room')
            
            # If trainer filtering is used, make sure we also optimize that relationship
            if trainer_id:
                sessions_query = sessions_query.filter(trainers__trainer_id=trainer_id)
                sessions_query = sessions_query.select_related('course', 'room').prefetch_related('trainers', 'trainers__user')
            else:
                # When no specific trainer filter, still prefetch trainers for efficiency
                sessions_query = sessions_query.select_related('course', 'room').prefetch_related('trainers', 'trainers__user')
            
            # Apply other filters
            if room_id:
                sessions_query = sessions_query.filter(room__room_id=room_id)
            if equipment_id:
                sessions_query = sessions_query.filter(required_equipment__equipment_id=equipment_id)
            elif with_equipment:
                # Only show sessions that have equipment assigned
                # Use a more direct approach to find sessions with equipment
                sessions_query = sessions_query.annotate(
                    equipment_count=models.Count('required_equipment')
                ).filter(equipment_count__gt=0)

            # Filter sessions to only show those from published course instances (TRAINER users only)
            if request.user.user_type and request.user.user_type.code == 'TRAINER':
                # Filter sessions to only show those from published course instances
                published_session_ids = CourseInstance.objects.filter(
                    published=True,
                    sessions__in=sessions_query
                ).values_list('sessions', flat=True)
                
                # Then filter the sessions query to only include these sessions
                sessions_query = sessions_query.filter(session_id__in=published_session_ids)

            logger.info(f"Found {sessions_query.count()} sessions for the query")
            
            # Add sessions to events array
            for session in sessions_query:
                try:
                    if session.start_date and session.end_date:
                        start_iso = session.start_date.isoformat()
                        end_iso = session.end_date.isoformat()
                        
                        course_name = session.course.name_en if session.course else 'Session'
                        
                        # Get trainer name from the first trainer (if any)
                        trainer_name = 'No Trainer'
                        if session.trainers.exists():
                            trainer = session.trainers.first()
                            if trainer and trainer.user:
                                trainer_name = trainer.user.get_full_name() or trainer.user.username
                        
                        room_name = session.room.name if session.room else 'No Room'
                        
                        title = f"{course_name} - {trainer_name} - {room_name}"
                        
                        events.append({
                            'id': session.session_id,
                            'title': title,
                            'start': start_iso,
                            'end': end_iso,
                            'type': 'session',  # Explicitly set type to 'session'
                            'resourceId': session.room.room_id if session.room else None,
                            'trainerId': session.trainers.first().trainer_id if session.trainers.exists() else None,
                            'is_active': session.is_active,
                            'color': 'green-600'  # Set color for sessions
                        })
                        logger.info(f"Added session: {title} from {start_iso} to {end_iso}")
                    else:
                        logger.warning(f"Session {session.session_id} missing dates")
                except Exception as e:
                    logger.error(f"Error processing session {session.session_id}: {e}")
                    # Continue with next session
                    continue
        except Exception as e:
            logger.error(f"Error retrieving sessions: {str(e)}", exc_info=True)
        
        # Get events, filtered by parameters if specified
        try:
            events_query = Event.objects.all().select_related('room', 'event_type')
            
            # Apply filters
            if room_id:
                events_query = events_query.filter(room__room_id=room_id)
            if equipment_id:
                events_query = events_query.filter(required_equipment__equipment_id=equipment_id)
            elif with_equipment:
                # Only show events that have equipment assigned
                # Use a more direct approach to find events with equipment
                events_query = events_query.annotate(
                    equipment_count=models.Count('required_equipment')
                ).filter(equipment_count__gt=0)
            # Note: Event model doesn't have a direct trainer relationship
            # This would need to be added to the Event model to fully support trainer filtering
            # TODO: Add trainer relationship to Event model
                
            logger.info(f"Found {events_query.count()} events for the query")
            
            # Add events to events array
            for event in events_query:
                try:
                    if event.start_time and event.end_time:
                        start_iso = event.start_time.isoformat()
                        end_iso = event.end_time.isoformat()
                        
                        event_type = event.event_type.type if event.event_type else 'Event'
                        room_name = event.room.name if event.room else 'No Room'
                        
                        title = f"{event.name} ({event_type}) - {room_name}"
                        
                        events.append({
                            'id': str(event.event_id),
                            'title': title,
                            'start': start_iso,
                            'end': end_iso,
                            'type': 'event',  # Explicitly set type to 'event'
                            'resourceId': event.room.room_id if event.room else None,
                            'is_active': event.is_active,
                            'color': 'blue-500'  # Set color for events
                        })
                        logger.info(f"Added event: {title} from {start_iso} to {end_iso}")
                    else:
                        logger.warning(f"Event {event.event_id} missing dates: start={event.start_time}, end={event.end_time}")
                except Exception as e:
                    logger.error(f"Error processing event {event.event_id}: {e}")
                    # Continue with next event
                    continue
        except Exception as e:
            logger.error(f"Error retrieving events: {str(e)}", exc_info=True)
        
        # Return found events
        logger.info(f"Returning {len(events)} filtered events/sessions to calendar")
        
        # Add holidays to events
        try:
            # Check if holidays should be included
            include_holidays = request.GET.get('include_holidays', 'true').lower() == 'true'
            
            # Don't include holidays if viewing rooms or equipment tabs
            if room_id or equipment_id:
                include_holidays = False
                logger.info("Holidays excluded for room or equipment view")
            
            if include_holidays:
                # Get all active holidays
                holidays = Holiday.objects.filter(
                    Q(is_active=True) | Q(is_active__isnull=True)
                )
                logger.info(f"Including {holidays.count()} holidays in calendar events")
                
                for holiday in holidays:
                    # Format the date to ISO format
                    holiday_date_str = holiday.date.strftime('%Y-%m-%d')
                    
                    # Add holiday as an all-day event
                    events.append({
                        'id': f"holiday-{holiday.holiday_id}",
                        'title': f"🎉 {holiday.name}",
                        'start': holiday_date_str,
                        'end': holiday_date_str,
                        'allDay': True,
                        'type': 'holiday',
                        'description': holiday.description,
                        'color': 'red-500',  # Red color for holidays
                        'textColor': 'white',
                        'borderColor': 'red-700'
                    })
        except Exception as e:
            logger.error(f"Error adding holidays to calendar: {str(e)}", exc_info=True)
            # Continue with returning events even if holiday addition fails
        
        return JsonResponse({
            'events': events,
            'total_sessions': total_session_count,
            'total_events': total_event_count,
            'filtered_count': len(events),
            'message': f'Found {len(events)} events/sessions to display'
        })
        
    except Exception as e:
        # Handle any unexpected errors
        logger.error(f"Calendar API error: {str(e)}", exc_info=True)
        return JsonResponse({'events': [], 'error': str(e)}, status=200)
@login_required
def calendar_resources_api(request):
    """API endpoint to get resources (rooms, trainers, equipment) for the calendar"""
    from django.http import JsonResponse
    
    resource_type = request.GET.get('type', 'all')
    
    if resource_type == 'rooms' or resource_type == 'all':
        rooms = [{
            'id': room.room_id,
            'title': room.name,
            'capacity': room.capacity,
            'status': room.status
        } for room in Room.objects.filter(is_active=True)]
    else:
        rooms = []
        
    if resource_type == 'trainers' or resource_type == 'all':
        trainers = [{
            'id': trainer.trainer_id,
            'title': trainer.user.get_full_name() or trainer.user.username,
            'status': trainer.status
        } for trainer in Trainer.objects.all().select_related('user')]
    else:
        trainers = []
        
    if resource_type == 'equipment' or resource_type == 'all':
        equipment = [{
            'id': item.equipment_id,
            'title': f"{item.code} - {item.name}",
            'status': item.status
        } for item in Equipment.objects.filter(is_active=True)]
    else:
        equipment = []
    
    return JsonResponse({
        'rooms': rooms,
        'trainers': trainers,
        'equipment': equipment
    })

@login_required
@staff_member_required
@require_http_methods(["POST"])
def delete_trainer_availability(request):
    """
    Delete trainer availability record for a specific trainer and session
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Get JSON data from request
        data = json.loads(request.body)
        trainer_id = data.get('trainer_id')
        session_id = data.get('session_id')
        
        if not trainer_id or not session_id:
            return JsonResponse({
                'status': 'error',
                'message': 'Missing required parameters: trainer_id and session_id'
            }, status=400)
        
        # Get the trainer
        try:
            trainer = Trainer.objects.get(trainer_id=trainer_id)
        except Trainer.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': f'Trainer with ID {trainer_id} not found'
            }, status=404)
        
        # Get the session
        try:
            session = Session.objects.get(session_id=session_id)
        except Session.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': f'Session with ID {session_id} not found'
            }, status=404)
        
        # Delete the availability record
        deleted, _ = TrainerAvailability.objects.filter(trainer=trainer, session=session).delete()
        
        # Remove trainer from session.trainers
        session.trainers.remove(trainer)
        
        if deleted > 0:
            logger.info(f"Deleted {deleted} trainer availability record(s) for trainer {trainer_id} in session {session_id}")
            
            # Update trainer status if they have no more availability records
            if not TrainerAvailability.objects.filter(trainer=trainer).exists():
                trainer.status = 'READY'
                trainer.save(update_fields=['status'])
                logger.info(f"Updated trainer {trainer_id} status to READY as no other availability records exist")
            
            return JsonResponse({
                'status': 'success',
                'message': f'Deleted {deleted} trainer availability record(s)'
            })
        else:
            logger.info(f"No trainer availability records found to delete for trainer {trainer_id} in session {session_id}")
            return JsonResponse({
                'status': 'success',
                'message': 'No records found to delete'
            })
    
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error deleting trainer availability: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
def debug_course_data(request, course_id):
    """Debug view to examine the course data structure"""
    import json
    from django.http import HttpResponse
    
    course = get_object_or_404(Course, course_id=course_id)
    
    # Prepare the debug information
    debug_info = {
        'course_id': course.course_id,
        'name': course.name_en,
        'equipment_categories': course.equipment_categories,
        'equipment_categories_type': str(type(course.equipment_categories)),
        'session_room_types': course.session_room_types,
        'session_room_types_type': str(type(course.session_room_types)),
    }
    
    # Format as JSON with indentation for readability
    formatted_json = json.dumps(debug_info, indent=4)
    
    # Return as a simple text response
    return HttpResponse(f"<pre>{formatted_json}</pre>", content_type="text/html")

@trainer_required
def edit_course(request, course_id):
    """Trainers can edit their own courses, supervisors can edit any course"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} editing course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to edit this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES, instance=course)
        if form.is_valid():
            form.save()
            messages.success(request, _('Course updated successfully!'))
            return redirect('website:course_detail', course_id=course.course_id)
    else:
        form = CourseForm(instance=course)
    
    # Get all active room types
    room_types = RoomType.objects.filter(is_active=True).order_by('name')
    # Get all active categories
    categories = Category.objects.filter(is_active=True).order_by('name')
    
    context = {
        'form': form,
        'title': _('Edit Course'),
        'submit_text': _('Update Course'),
        'course': course,
        'room_types': room_types,
        'categories': categories,
    }
    return render(request, 'website/courses/course_form.html', context)

@trainer_required
def cancel_course(request, course_id):
    """Trainers can cancel their own courses, supervisors can cancel any course"""
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} cancelling course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to cancel this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        # Update the course status to CANCELLED
        course.status = 'CANCELLED'
        course.save()
        
        messages.success(request, _('Course cancelled successfully!'))
        return redirect('website:course_detail', course_id=course.course_id)
    
    context = {
        'course': course,
    }
    return render(request, 'website/courses/cancel_course.html', context)

@trainer_required
def reactivate_course(request, course_id):
    """Trainers can reactivate their own cancelled courses, supervisors can reactivate any course"""
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} reactivating course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to reactivate this course.'))
        return redirect('website:courses')
    
    if course.status != 'CANCELLED':
        messages.error(request, _('Only cancelled courses can be reactivated.'))
        return redirect('website:course_detail', course_id=course.course_id)
    
    # Update the course status to ACTIVE
    try:
        course.status = 'ACTIVE'
        course.save()
        
        messages.success(request, _('Course reactivated successfully!'))
        return redirect('website:course_detail', course_id=course.course_id)
    except Exception as e:
        messages.error(request, _(f'Failed to reactivate course. Error: {str(e)}'))
        return redirect('website:course_detail', course_id=course.course_id)
    
@trainer_required
def delete_course(request, course_id):
    """Trainers can delete their own cancelled courses, supervisors can delete any course"""
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} deleting course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to delete this course.'))
        return redirect('website:courses')
    
    # Regular trainers can only delete cancelled courses, supervisors can delete any course
    if not is_supervisor and course.status != 'CANCELLED':
        messages.error(request, _('Only cancelled courses can be deleted.'))
        return redirect('website:course_detail', course_id=course.course_id)

    try:
        course.delete()
        messages.success(request, _('Course deleted successfully!'))
        return redirect('website:courses')
    except Exception as e:
        messages.error(request, _('Failed to delete course.'))
        return redirect('website:course_detail', course_id=course.course_id)


@login_required
def course_detail(request, course_id):
    """View a course's details"""
    course = get_object_or_404(Course, course_id=course_id)
    is_trainer = hasattr(request.user, 'trainer')
    is_supervisor = is_trainer and request.user.trainer.is_supervisor
    is_admin = request.user.is_staff
    can_manage = False
    has_paid = False
    has_upcoming_reservation = False
    
    # Check if the user is an external individual
    is_external_individual = request.user.user_type and request.user.user_type.code == 'EXTERNAL_INDIVIDUAL'
    
    # Check if the user can manage this course
    if is_trainer:
        # Supervisors can manage any course
        if is_supervisor:
            can_manage = True
        # Regular trainers can only manage their own courses
        elif course in request.user.trainer.courses.all():
            can_manage = True
    # Admins can manage courses too
    elif is_admin:
        can_manage = True
    else:
        # For normal users, check if they have paid for this course
        has_paid = Reservation.objects.filter(
            user=request.user,
            course_instance__course=course,
            status__in=['IN_PROGRESS', 'COMPLETED']
        ).exists()
        
        # Check if they have an upcoming reservation
        has_upcoming_reservation = Reservation.objects.filter(
            user=request.user,
            course_instance__course=course,
            status='UPCOMING'
        ).exists()
    
    # Get all room types for room type display
    room_types = RoomType.objects.filter(is_active=True)
    
    # Process session room types for display
    session_room_types = []
    if course.session_room_types:
        # Get all room types as a lookup dictionary
        room_type_dict = {str(rt.room_type_id): rt.name for rt in room_types}
        
        # Convert the session_room_types JsonField to a Python dictionary if needed
        if isinstance(course.session_room_types, str):
            try:
                session_room_types_dict = json.loads(course.session_room_types)
            except json.JSONDecodeError:
                session_room_types_dict = {}
        else:
            session_room_types_dict = course.session_room_types
        
        # Sort session numbers numerically
        session_numbers = sorted(session_room_types_dict.keys(), key=lambda x: int(x) if x.isdigit() else float('inf'))
        
        # Build a list of session room types with relevant information
        for session_num in session_numbers:
            room_type_id = session_room_types_dict[session_num]
            room_type_id_str = str(room_type_id)
            
            # Check if we found a matching room type
            found = room_type_id_str in room_type_dict
            room_type_name = room_type_dict.get(room_type_id_str, f"Room Type #{room_type_id}")
            
            session_room_types.append({
                'session_num': session_num,
                'room_type_id': room_type_id,
                'room_type_name': room_type_name,
                'found': found
            })
    
    # Get course instances for external individuals
    course_instances = []
    now = timezone.now()
    
    if is_external_individual:
        course_instances = CourseInstance.objects.filter(
            course=course,
            is_active=True,
            course_type='INDIVIDUAL'  # Only show INDIVIDUAL course types to individual users
        ).order_by('start_date')
        
        # Add session count for each instance and mark instances where cancellation deadline has passed
        for instance in course_instances:
            instance.session_count = instance.sessions.count()
            
            # Check if course has already started
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth import authenticate, login
from django.contrib import messages
from django.urls import reverse, reverse_lazy
from .models import (
    Attendance, CustomUser, UserType, Section, Feature, Course, ContactInfo, 
    Trainer, Room, RoomAvailability, Session, TrainerAvailability, 
    Event, EventType, RoomType, Equipment, EquipmentAvailability, 
    Brand, Category, Model, Reservation, CourseInstance,
    EmergencyCancellationRequest, Payment, Notification, Holiday,
    CourseContent, Corporate, Survey, Questionnaire, CorporateAdminRequest
)
from .forms import (
    UserTypeSelectionForm, AccountRegistrationForm, 
    PersonalInfoForm, CorporateInfoForm, CourseForm,
    CourseContentForm
)
from django.contrib.auth.decorators import login_required, user_passes_test
from datetime import datetime, timedelta, time
from django.utils.translation import gettext_lazy as _
from django.contrib.admin.views.decorators import staff_member_required
import uuid
from django.utils import timezone
from django.http import JsonResponse, HttpResponse
from django.views.decorators.http import require_http_methods
import json
from django.db import transaction
import logging
from django.core.exceptions import ValidationError
from django.http import Http404
from django.template.loader import render_to_string
from django.db.models import Q
from django.db import IntegrityError
from django.utils.dateparse import parse_datetime
from django.http import HttpResponse
from functools import wraps
from django.conf import settings
from django.db import models
from django.db.models import Count
from django.db import connection
import traceback

# Import views from corporate_views.py
from .corporate_views import (
    corporate_admins_view, corporate_admin_dashboard, corporate_reservations_view,
    list_corporates, corporate_detail, create_corporate, update_corporate, delete_corporate,
    get_corporate_users, process_corporate_form, update_corporate_form, course_request_form,
    course_requests_list, update_course_request_status, course_requests_api,
    corporate_admin_calendar, corporate_calendar_events_api,
    my_corporate_requests_view, my_corporate_requests_api,
    my_corporate_users_view, corporate_users_api, add_corporate_user, delete_corporate_user
)

# Configure logger
logger = logging.getLogger(__name__)

def trainer_required(view_func):
    """
    Decorator for views that checks that the user is a trainer.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, _('Please log in first.'))
            return redirect('login')
        if not hasattr(request.user, 'trainer') and request.user.user_type.code != 'SUPER_ADMIN':
            messages.error(request, _('You must be a trainer to access this page.'))
            return redirect('website:courses')
        return view_func(request, *args, **kwargs)
    return _wrapped_view

def trainer_supervisor_required(view_func):
    """
    Decorator for views that checks that the user is a trainer supervisor.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        if not request.user.is_authenticated:
            messages.error(request, _('Please log in first.'))
            return redirect('login')
        if not hasattr(request.user, 'trainer') and request.user.user_type.code != 'SUPER_ADMIN':
            messages.error(request, _('You must be a trainer to access this page.'))
            return redirect('website:courses')
        # First check if user is SUPER_ADMIN
        if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == 'SUPER_ADMIN':
            # SUPER_ADMIN can access without being a trainer supervisor
            pass
        # Otherwise, must be a trainer supervisor
        elif not hasattr(request.user, 'trainer') or not request.user.trainer.is_supervisor:
            messages.error(request, _('You must be a trainer supervisor to access this page.'))
            return redirect('website:courses')
        return view_func(request, *args, **kwargs)
    return _wrapped_view

# Create your views here.


@login_required
def main_view(request):
    """
    Main view that redirects users to appropriate dashboards based on their role
    """
    # Use the redirect_to_dashboard function from corporate_views
    from .corporate_views import redirect_to_dashboard
    return redirect_to_dashboard(request)

def register_step1(request):
    # Get the previous registration type from session if it exists
    initial_type = request.session.get('registration_user_type')
    initial_corporate = False
    if initial_type:
        try:
            user_type = UserType.objects.get(code=initial_type)
            initial_corporate = user_type.code == 'EXTERNAL_CORPORATE_ADMIN'
        except UserType.DoesNotExist:
            pass

    if request.method == 'POST':
        form = UserTypeSelectionForm(request.POST)
        if form.is_valid():
            user_type = form.cleaned_data['user_type']
            request.session['registration_user_type'] = user_type.code
            request.session['registration_step'] = 2
            request.session['total_steps'] = 4 if user_type.code == 'EXTERNAL_CORPORATE_ADMIN' else 3
            return redirect('website:register_step2')
    else:
        form = UserTypeSelectionForm(initial={'user_type': initial_type} if initial_type else None)
    
    return render(request, 'website/registration/step1.html', {
        'form': form,
        'current_step': 1,
        'initial_type': initial_type,
        'initial_corporate': initial_corporate
    })

def register_step2(request):
    user_type_code = request.session.get('registration_user_type')
    if not user_type_code:
        return redirect('website:register_step1')
    
    is_corporate = user_type_code == 'EXTERNAL_CORPORATE_ADMIN'
    total_steps = request.session.get('total_steps', 4 if is_corporate else 3)
    
    if request.method == 'POST':
        form = AccountRegistrationForm(request.POST)
        if form.is_valid():
            # Check if email already exists
            email = form.cleaned_data['email']
            if CustomUser.objects.filter(email=email).exists():
                form.add_error('email', 'This email is already registered.')
                return render(request, 'website/registration/step2.html', {
                    'form': form,
                    'current_step': 2,
                    'show_corporate': is_corporate,
                    'total_steps': total_steps
                })
            
            # Store account info in session
            request.session['account_info'] = form.cleaned_data
            request.session['registration_step'] = 3
            return redirect('website:register_step3')
    else:
        form = AccountRegistrationForm()
    
    return render(request, 'website/registration/step2.html', {
        'form': form,
        'current_step': 2,
        'show_corporate': is_corporate,
        'total_steps': total_steps
    })

def register_step3(request):
    user_type_code = request.session.get('registration_user_type')
    account_info = request.session.get('account_info')
    
    if not user_type_code or not account_info:
        return redirect('website:register_step1')
    
    is_corporate = user_type_code == 'EXTERNAL_CORPORATE_ADMIN'
    total_steps = request.session.get('total_steps', 4 if is_corporate else 3)
    
    if request.method == 'POST':
        form = PersonalInfoForm(request.POST)
        if form.is_valid():
            # Store personal info in session
            personal_info = form.cleaned_data.copy()
            if personal_info.get('date_of_birth'):
                personal_info['date_of_birth'] = personal_info['date_of_birth'].isoformat()
            request.session['personal_info'] = personal_info
            if is_corporate:
                request.session['registration_step'] = 4
                return redirect('website:register_step4')
            else:
                # Create user for individual registration
                user = create_user(request)
                if user is None:
                    return redirect('website:register_step1')
                login(request, user, backend='website.backends.EmailOrUsernameModelBackend')
                messages.success(request, 'Registration successful!')
                return redirect('website:main')
    else:
        form = PersonalInfoForm()
    
    return render(request, 'website/registration/step3.html', {
        'form': form,
        'current_step': 3,
        'show_corporate': is_corporate,
        'total_steps': total_steps
    })

def register_step4(request):
    user_type_code = request.session.get('registration_user_type')
    account_info = request.session.get('account_info')
    personal_info = request.session.get('personal_info')
    
    if not all([user_type_code, account_info, personal_info]) or user_type_code != 'EXTERNAL_CORPORATE_ADMIN':
        return redirect('website:register_step1')
    
    if request.method == 'POST':
        form = CorporateInfoForm(request.POST)
        if form.is_valid():
            # Store corporate info and create user
            request.session['corporate_info'] = form.cleaned_data
            user = create_user(request)
            if user is None:
                return redirect('website:register_step1')
            login(request, user, backend='website.backends.EmailOrUsernameModelBackend')
            messages.success(request, 'Registration successful!')
            return redirect('website:main')
    else:
        form = CorporateInfoForm()
    
    return render(request, 'website/registration/step4.html', {
        'form': form,
        'current_step': 4,
        'show_corporate': True,
        'total_steps': 4
    })

def registration_timeline(request):
    """AJAX view to update the registration timeline."""
    show_corporate = request.GET.get('show_corporate') == 'true'
    current_step = int(request.GET.get('current_step', 1))
    total_steps = 4 if show_corporate else 3
    
    return render(request, 'website/registration/timeline.html', {
        'show_corporate': show_corporate,
        'current_step': current_step,
        'total_steps': total_steps
    })

def create_user(request):
    try:
        user_type_code = request.session.get('registration_user_type')
        account_info = request.session.get('account_info')
        personal_info = request.session.get('personal_info').copy()
        corporate_info = request.session.get('corporate_info')
        
        # Double check if email is still available
        email = account_info['email']
        if CustomUser.objects.filter(email=email).exists():
            messages.error(request, 'This email is already registered. Please try again with a different email.')
            return None
        
        # Convert date string back to date object
        if personal_info and personal_info.get('date_of_birth'):
            personal_info['date_of_birth'] = datetime.fromisoformat(personal_info['date_of_birth']).date()
        
        # Get the UserType instance
        try:
            user_type = UserType.objects.get(code=user_type_code)
        except UserType.DoesNotExist:
            messages.error(request, 'Invalid user type selected.')
            return None
        
        user = CustomUser.objects.create_user(
            email=account_info['email'],
            username=account_info['username'],
            password=account_info['password1'],
            user_type=user_type,
            **personal_info
        )
        
        if corporate_info:
            for key, value in corporate_info.items():
                setattr(user, key, value)
            user.save()
            
            # Create Corporate record if this is a corporate admin
            if user_type_code == 'EXTERNAL_CORPORATE_ADMIN':
                corporate = Corporate.objects.create(
                    corporate_admin=user,
                    legal_name=corporate_info.get('company_name', ''),
                    address=personal_info.get('address', ''),
                    tax_registration_number=corporate_info.get('tax_registration_number', ''),
                    commercial_registration_number=corporate_info.get('commercial_registration_number', ''),
                    phone_number=personal_info.get('phone_number', ''),
                    capacity=corporate_info.get('capacity', 10),
                    category=corporate_info.get('category', 'A')
                )
                
                # Create CorporateAdmin record
                from .models import CorporateAdmin
                CorporateAdmin.objects.create(
                    user=user,
                    corporate=corporate,
                    position=corporate_info.get('position', 'Admin'),
                    department=corporate_info.get('department', ''),
                    direct_phone=personal_info.get('phone_number', ''),
                    is_active=True
                )
        
        # Clear session data
        keys_to_delete = ['registration_user_type', 'registration_step', 
                         'account_info', 'personal_info', 'corporate_info']
        for key in keys_to_delete:
            request.session.pop(key, None)
        
        return user
    except Exception as e:
        messages.error(request, f'An error occurred during registration: {str(e)}')
        return None

@login_required
def profile_view(request):
    return render(request, 'website/profile.html')

@login_required
def settings_view(request):
    if request.method == 'POST':
        if 'full_name' in request.POST:
            # Handle profile settings form
            first_name = request.POST.get('full_name').split()[0]
            last_name = ' '.join(request.POST.get('full_name').split()[1:]) if len(request.POST.get('full_name').split()) > 1 else ''
            request.user.first_name = first_name
            request.user.last_name = last_name
            request.user.email = request.POST.get('email')
            request.user.save()
            messages.success(request, _('Profile updated successfully'))
        
        elif 'current_password' in request.POST:
            # Handle password change form
            current_password = request.POST.get('current_password')
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')
            
            if not request.user.check_password(current_password):
                messages.error(request, _('Current password is incorrect'))
            elif new_password != confirm_password:
                messages.error(request, _('New passwords do not match'))
            else:
                request.user.set_password(new_password)
                request.user.save()
                messages.success(request, _('Password updated successfully'))
                # Re-authenticate the user to prevent logout
                user = authenticate(username=request.user.username, password=new_password)
                if user:
                    login(request, user)
        
        elif 'email_notifications' in request.POST:
            # Handle notification settings
            # You can save these to user preferences model if you have one
            messages.success(request, _('Notification preferences updated'))
        
        return redirect('website:settings')
    
    return render(request, 'website/settings.html')



@login_required
def calendar_view(request):
    # Check if the user is an external individual and redirect to their personal calendar
    if request.user.user_type and request.user.user_type.code == 'EXTERNAL_INDIVIDUAL':
        return redirect('website:user_calendar')
    
    # Check if the user is a corporate admin and redirect to corporate admin calendar
    if request.user.user_type and request.user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN':
        return redirect('website:corporate_admin_calendar')
    
    # Check if the user is a trainer and redirect to trainer calendar
    if request.user.user_type and request.user.user_type.code == 'TRAINER':
        return redirect('website:trainer_calendar')
    
    # Only staff members (system admins and super admins) can access the system calendar
    if not request.user.is_staff:
        messages.warning(request, _('You do not have permission to access the system calendar.'))
        return redirect('website:main')
        
    # Get all active rooms
    rooms = Room.objects.filter(is_active=True)
    
    # Get all active trainers
    trainers = Trainer.objects.all().select_related('user')
    
    # Get all active equipment
    equipment = Equipment.objects.filter(is_active=True)
    
    # Get room availability data
    room_availability = RoomAvailability.objects.filter(
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('room', 'session', 'event')
    
    # Get trainer availability data
    trainer_availability = TrainerAvailability.objects.filter(
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('trainer', 'trainer__user', 'session', 'event')
    
    # Get equipment availability data
    equipment_availability = EquipmentAvailability.objects.filter(
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('equipment', 'session', 'event')
    
    # Prepare context
    context = {
        'rooms': rooms,
        'trainers': trainers,
        'equipment': equipment,
        'room_availability': room_availability,
        'trainer_availability': trainer_availability,
        'equipment_availability': equipment_availability,
    }
    
    return render(request, 'website/calendar/index.html', context) 
@login_required
def trainer_calendar_view(request):
    # Check if the user is a trainer
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        # If the user is not a trainer, redirect to the main calendar view
        messages.warning(request, _('You do not have trainer privileges.'))
        return redirect('website:calendar')
    
    # Get trainer availability data
    trainer_availability = TrainerAvailability.objects.filter(
        trainer=trainer,
        start_date__isnull=False,
        end_date__isnull=False
    ).select_related('session', 'event')
    
    # Prepare context
    context = {
        'trainer': trainer,
        'trainer_availability': trainer_availability,
    }
    
    return render(request, 'website/calendar/trainer_calendar.html', context)

@staff_member_required
def rooms_view(request):
    rooms = Room.objects.prefetch_related('availability_slots', 'fixed_equipment').select_related('room_type').all()
    room_types = RoomType.objects.filter(is_active=True)
    
    # Get all active equipment
    equipment_list = Equipment.objects.filter(
        is_active=True
    ).order_by('code')
    
    # Get all active categories, brands, and models
    categories = Category.objects.filter(is_active=True).order_by('name')
    brands = Brand.objects.filter(is_active=True).order_by('name')
    models = Model.objects.filter(is_active=True).select_related('brand').order_by('brand__name', 'name')
    
    context = {
        'rooms': rooms,
        'room_types': room_types,
        'equipment_list': equipment_list,
        'categories': categories,
        'brands': brands,
        'models': models,
    }
    return render(request, 'website/rooms/index.html', context)

@staff_member_required
def trainers_view(request):
    # Get all trainers with their related user information
    trainers = Trainer.objects.select_related('user', 'user__user_type').all()
    
    context = {
        'trainers': trainers,
    }
    return render(request, 'website/trainers.html', context)

@staff_member_required
def get_users(request):
    # Get all users who are students (EXTERNAL_INDIVIDUAL)
    users = CustomUser.objects.filter(
        user_type__code='EXTERNAL_INDIVIDUAL'
    ).values('id', 'first_name', 'last_name', 'email')
    return JsonResponse(list(users), safe=False)

@staff_member_required
@require_http_methods(["POST"])
def promote_to_trainer(request):
    data = json.loads(request.body)
    user_id = data.get('user_id')
    
    try:
        user = CustomUser.objects.get(id=user_id)
        trainer_type = UserType.objects.get(code='TRAINER')
        user.user_type = trainer_type
        user.save()
        
        # Create Trainer profile if it doesn't exist
        trainer, created = Trainer.objects.get_or_create(user=user)
        
        # Create trainer availability if trainer was just created
        if created:
            TrainerAvailability.objects.create(
                trainer=trainer,
                status='ACTIVE',
                start_time=timezone.now()
            )
        
        messages.success(request, _('User successfully promoted to trainer.'))
        return JsonResponse({'status': 'success'})
    except CustomUser.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('User not found.')
        }, status=404)
    except UserType.DoesNotExist:
        return JsonResponse({
            'status': 'error', 
            'message': _('Trainer user type not found.')
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
def trainer_detail(request, trainer_id):
    trainer = get_object_or_404(Trainer, id=trainer_id)
    context = {
        'trainer': trainer,
    }
    return render(request, 'website/trainer_detail.html', context)

@staff_member_required
def edit_trainer(request, trainer_id):
    trainer = get_object_or_404(Trainer, id=trainer_id)
    if request.method == 'POST':
        # Handle form submission
        # Add your form handling logic here
        messages.success(request, _('Trainer updated successfully!'))
        return redirect('website:trainer_detail', trainer_id=trainer.id)
    
    context = {
        'trainer': trainer,
    }
    return render(request, 'website/edit_trainer.html', context)

@staff_member_required
@require_http_methods(["POST"])
def create_trainer(request):
    data = json.loads(request.body)
    try:
        # Start a transaction
        with transaction.atomic():
            # Check if username or email already exists
            if CustomUser.objects.filter(username=data['username']).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('Username already exists. Please choose a different username.')
                }, status=400)
            
            if CustomUser.objects.filter(email=data['email']).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('Email already exists. Please use a different email address.')
                }, status=400)
            
            # Create the user
            user = CustomUser.objects.create_user(
                username=data['username'],
                email=data['email'],
                password=data['password'],
                first_name=data['first_name'],
                last_name=data['last_name']
            )
            
            # Set user type to trainer
            trainer_type = UserType.objects.get(code='TRAINER')
            user.user_type = trainer_type
            user.save()
            
            # Create trainer profile
            trainer = Trainer.objects.create(user=user)
            
            # Create trainer availability
            TrainerAvailability.objects.create(
                trainer=trainer,
                status='READY'
            )
            
            messages.success(request, _('Trainer created successfully!'))
            return JsonResponse({'status': 'success'})
    except UserType.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('Trainer user type not found. Please configure user types first.')
        }, status=400)
    except Exception as e:
        # If any error occurs, rollback the transaction
        if 'user' in locals():
            user.delete()  # This will cascade delete trainer and availability
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["POST"])
def create_room(request):
    try:
        data = json.loads(request.body)
        logger.info(f"CREATE ROOM: Received data: {json.dumps(data, indent=2)}")
        
        # Get room type
        try:
            room_type = RoomType.objects.get(room_type_id=data['room_type'])
            logger.info(f"CREATE ROOM: Found room type: {room_type.name} (ID: {room_type.room_type_id})")
        except RoomType.DoesNotExist:
            logger.error(f"CREATE ROOM: Room type with ID {data['room_type']} does not exist")
            return JsonResponse({
                'status': 'error',
                'message': _('Selected room type does not exist')
            }, status=400)
        
        # Create the room in a transaction - without equipment
        logger.info("CREATE ROOM: Starting transaction for room creation")
        with transaction.atomic():
            # Create the room
            logger.info(f"CREATE ROOM: Creating room with name='{data['name']}', capacity={data.get('capacity', 0)}")
            room = Room.objects.create(
                name=data['name'],
                capacity=data.get('capacity', 0),
                description=data.get('description', ''),
                room_type=room_type,
            )
            room.save()
            logger.info(f"CREATE ROOM: Room created with ID: {room.room_id}")
        
        # Return response with the created room
        response_data = {
            'status': 'success',
            'message': _('Room created successfully'),
            'room': {
                'id': room.room_id,
                'name': room.name,
                'type': room.room_type.name,
                'capacity': room.capacity,
                'fixed_equipment': []  # Empty list as no equipment is added during creation
            }
        }
        logger.info(f"CREATE ROOM: Sending response: {json.dumps(response_data, default=str)}")
        return JsonResponse(response_data)
        
    except IntegrityError as e:
        logger.error(f"CREATE ROOM: Integrity error creating room: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('A room with this name already exists')
        }, status=400)
    except Exception as e:
        logger.error(f"CREATE ROOM: Error creating room: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["PUT"])
def update_room(request, room_id):
    room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment'), room_id=room_id)
    try:
        data = json.loads(request.body)
        logger.info(f"Updating room {room_id} with data: {data}")
        
        # Process equipment IDs outside of transaction
        equipment_ids = []
        if 'fixed_equipment' in data:
            for eq_id in data['fixed_equipment']:
                try:
                    if isinstance(eq_id, str):
                        equipment_ids.append(int(eq_id))
                    elif isinstance(eq_id, int):
                        equipment_ids.append(eq_id)
                except (ValueError, TypeError):
                    logger.warning(f"Invalid equipment ID: {eq_id}")
            
            logger.info(f"Processed equipment IDs for update: {equipment_ids}")
        
        # Store current fixed equipment for comparison - use IDs for accurate comparison
        previous_equipment_ids = set(room.fixed_equipment.values_list('equipment_id', flat=True))
        logger.info(f"Previous fixed equipment IDs: {previous_equipment_ids}")
        
        with transaction.atomic():
            room_type = get_object_or_404(RoomType, room_type_id=data['room_type'])
            
            # Update room basic info
            room.name = data['name']
            room.capacity = data['capacity']
            room.room_type = room_type
            room.description = data.get('description', '')
            room.save()  # Save basic info first
            
            # Handle fixed equipment changes if provided in data
            if 'fixed_equipment' in data:
                # Calculate additions and removals
                new_equipment_ids = set(equipment_ids)
                removed_ids = previous_equipment_ids - new_equipment_ids
                added_ids = new_equipment_ids - previous_equipment_ids
                
                logger.info(f"Equipment changes - Removing: {removed_ids}, Adding: {added_ids}")
                
                # Get equipment objects for the new set
                new_equipment_list = list(Equipment.objects.filter(equipment_id__in=equipment_ids))
                
                if not new_equipment_list and equipment_ids:
                    logger.warning(f"No equipment found for IDs: {equipment_ids}")
                else:
                    logger.info(f"New equipment to set: {[eq.code for eq in new_equipment_list]}")
                
                # Set the new equipment (this automatically handles additions and removals)
                room.fixed_equipment.set(new_equipment_list)
                room.save()  # Explicitly save after M2M changes
                
                # Update equipment status - Handle removed equipment
                if removed_ids:
                    removed_equipment = Equipment.objects.filter(equipment_id__in=removed_ids)
                    for equipment in removed_equipment:
                        try:
                            if equipment.status == 'FIXED':
                                equipment.status = 'READY'
                                equipment.save()
                                logger.info(f"Reset status to READY for equipment {equipment.code}")
                        except Exception as e:
                            logger.error(f"Error resetting equipment status for {equipment.code}: {e}", exc_info=True)
                
                # Update equipment status - Handle added equipment
                if added_ids:
                    added_equipment = Equipment.objects.filter(equipment_id__in=added_ids)
                    for equipment in added_equipment:
                        try:
                            if equipment.status != 'FIXED':
                                equipment.status = 'FIXED'
                                equipment.save()
                                logger.info(f"Updated status to FIXED for equipment {equipment.code}")
                        except Exception as e:
                            logger.error(f"Error setting equipment status for {equipment.code}: {e}", exc_info=True)
            
            # Final verification
            updated_equipment = list(room.fixed_equipment.all())
            logger.info(f"Final equipment after update: {[eq.code for eq in updated_equipment]}")
        
        # Get final data for response
        updated_equipment_ids = list(room.fixed_equipment.values_list('equipment_id', flat=True))
        logger.info(f"Final equipment IDs: {updated_equipment_ids}")
        
        return JsonResponse({
            'status': 'success',
            'message': _('Room updated successfully!'),
            'room': {
                'id': room.room_id,
                'name': room.name,
                'type': room.room_type.name,
                'capacity': room.capacity,
                'fixed_equipment': [
                    {'id': eq.equipment_id, 'name': eq.name, 'code': eq.code}
                    for eq in room.fixed_equipment.all()
                ]
            }
        })
    except Exception as e:
        logger.error(f"Error updating room {room_id}: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["POST"])
def deactivate_room(request, room_id):
    room = get_object_or_404(Room, room_id=room_id)
    try:
        room.is_active = False
        room.save()
        return JsonResponse({
            'status': 'success',
            'message': _('Room deactivated successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["POST"])
def delete_room(request, room_id):
    room = get_object_or_404(Room, room_id=room_id)
    try:
        room.delete()
        return JsonResponse({
            'status': 'success',
            'message': _('Room deleted successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@login_required
@staff_member_required
def sessions_view(request):
    # Get active courses, trainers, rooms, and events from database
    active_sessions = Session.objects.filter(is_active=True).select_related('course', 'room').prefetch_related('trainers', 'trainers__user', 'required_equipment')
    
    # Filter to get only courses assigned to trainers
    active_courses = Course.objects.filter(
        is_active=True,
        trainer__isnull=False
    ).distinct()
    
    active_trainers = Trainer.objects.all().select_related('user')
    active_rooms = Room.objects.filter(is_active=True)
    active_events = Event.objects.filter(is_active=True)
    event_types = EventType.objects.all()
    room_types = RoomType.objects.filter(is_active=True)
    
    # For each course, serialize the equipment_categories and session_room_types JSON for template
    for course in active_courses:
        # Handle equipment_categories
        if course.equipment_categories:
            # The equipment_categories field is already a dictionary,
            # we just need to ensure it's properly formatted for the template
            course.equipment_categories_json = json.dumps(course.equipment_categories)
        else:
            course.equipment_categories_json = '{}'
        
        # Handle session_room_types
        if course.session_room_types:
            try:
                # Check if it's already a dict or needs to be parsed from JSON string
                if isinstance(course.session_room_types, str):
                    # Parse the JSON string to ensure it's valid
                    session_room_types_dict = json.loads(course.session_room_types)
                else:
                    # It's already a dict, use it directly
                    session_room_types_dict = course.session_room_types
                
                # Dump it back to a properly formatted JSON string
                course.session_room_types_json = json.dumps(session_room_types_dict)
            except (json.JSONDecodeError, TypeError) as e:
                # Handle any errors by setting to empty dict
                print(f"Error processing session_room_types for course {course.name_en}: {e}")
                course.session_room_types_json = '{}'
        else:
            course.session_room_types_json = '{}'
    
    # Filter equipment based on their status directly from the Equipment model
    # Only include equipment with READY or BUSY status
    equipment_list = Equipment.objects.filter(
        is_active=True,
        status__in=['READY', 'BUSY']
    ).distinct()
    
    context = {
        'sessions': active_sessions,
        'courses': active_courses,
        'trainers': active_trainers,
        'rooms': active_rooms,
        'events': active_events,
        'event_types': event_types,
        'room_types': room_types,
        'equipment_list': equipment_list,
    }
    
    return render(request, 'website/sessions/index.html', context)

@staff_member_required
@require_http_methods(["GET", "POST", "PUT"])
def create_session(request):
    logger = logging.getLogger(__name__)
    
    if request.method == "GET":
        # Handle GET request to fetch session details
        session_id = request.GET.get('session_id')
        if session_id:
            try:
                session = Session.objects.get(session_id=session_id)
                return JsonResponse({
                    'status': 'success',
                    'session': {
                        'course': session.course.course_id,
                        'trainers': [trainer.trainer_id for trainer in session.trainers.all()],
                        'room': session.room.room_id if session.room else None,
                        'equipment': [eq.equipment_id for eq in session.required_equipment.all()],
                        'start_date': session.start_date.strftime('%Y-%m-%dT%H:%M'),
                        'end_date': session.end_date.strftime('%Y-%m-%dT%H:%M'),
                        'location': session.location,
                        'slot_type': session.slot_type
                    }
                })
            except Session.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Session not found'
                }, status=404)
    
    try:
        data = json.loads(request.body)
        logger.info(f"Received session data: {data}")
        
        # Handle deletion request
        if data.get('action') == 'delete':
            session_id = data.get('session_id')
            if session_id:
                try:
                    session = Session.objects.get(session_id=session_id)
                    session.delete()
                    return JsonResponse({
                        'status': 'success',
                        'message': 'Session deleted successfully!'
                    })
                except Session.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Session not found'
                    }, status=404)
        
        # Validate required fields for create/update
        required_fields = ['course', 'trainers', 'location', 'slot_type', 'start_time', 'end_time', 'capacity']
        if data.get('location') == 'PHYSICAL':
            required_fields.append('room')
            
        missing_fields = [field for field in required_fields if not data.get(field)]
        
        if missing_fields:
            logger.warning(f"Missing required fields: {missing_fields}")
            return JsonResponse({
                'status': 'error',
                'message': f'Missing required fields: {", ".join(missing_fields)}'
            }, status=400)
        
        # Get the objects
        try:
            course = Course.objects.get(course_id=data['course'])
            course_type = data.get('course_type', 'INDIVIDUAL')
            corporate = None
            if course_type == 'CORPORATE':
                corporate = Corporate.objects.get(corporate_id=data['corporate_id'])
                logger.info(f"Found corporate: {corporate.legal_name}")
            else:
                logger.info("Course type is not corporate")
                
            trainers = Trainer.objects.filter(trainer_id__in=data['trainers'])
            if not trainers.exists():
                raise Trainer.DoesNotExist("No trainers found with the provided IDs")
            
            room = None
            if data['location'] == 'PHYSICAL':
                room = Room.objects.get(room_id=data['room'])
            
            # Get equipment if specified
            equipment_list = []
            if data.get('equipment'):
                logger.info(f"Processing equipment IDs: {data['equipment']}")
                equipment_list = list(Equipment.objects.filter(equipment_id__in=data['equipment']))
                logger.info(f"Found equipment: {[f'{eq.code} - {eq.name}' for eq in equipment_list]}")
            
            logger.info(f"Found course: {course}, trainers: {trainers}, room: {room}, equipment count: {len(equipment_list)}")
        except (Course.DoesNotExist, Trainer.DoesNotExist, Room.DoesNotExist) as e:
            logger.error(f"Error finding related objects: {e}")
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=404)
        except Equipment.DoesNotExist as e:
            logger.error(f"Error finding equipment: {e}")
            return JsonResponse({
                'status': 'error',
                'message': f'One or more equipment items not found: {str(e)}'
            }, status=404)
        
        # Parse and validate dates
        try:
            start_time = timezone.make_aware(datetime.fromisoformat(data['start_time']))
            end_time = timezone.make_aware(datetime.fromisoformat(data['end_time']))
            
            # Convert to naive datetime for database queries
            start_time = timezone.make_naive(start_time)
            end_time = timezone.make_naive(end_time)
            
            if end_time <= start_time:
                return JsonResponse({
                    'status': 'error',
                    'message': 'End time must be after start time'
                }, status=400)
        except ValueError as e:
            return JsonResponse({
                'status': 'error',
                'message': f'Invalid date format: {str(e)}'
            }, status=400)
        
        # Check if we're updating an existing session (PUT request)
        if request.method == "PUT" and data.get('session_id'):
            try:
                existing_session = Session.objects.get(session_id=data['session_id'])
                logger.info(f"Updating existing session: {existing_session.session_id}")
                
                with transaction.atomic():
                    # Store previous equipment and trainers for later comparison
                    previous_equipment = list(existing_session.required_equipment.all())
                    previous_trainers = list(existing_session.trainers.all())
                    
                    # Update session fields
                    existing_session.course = course
                    existing_session.room = room
                    existing_session.location = data['location']
                    existing_session.slot_type = data['slot_type']
                    
                    # Clear existing equipment availability
                    EquipmentAvailability.remove_busy_slots(equipment=None, session_id=existing_session.session_id)
                    
                    # Clear existing trainer availability
                    TrainerAvailability.remove_busy_slots(trainer=None, session_id=existing_session.session_id)
                    
                    # Clear existing room availability
                    RoomAvailability.objects.filter(session=existing_session).delete()
                    
                    # Update times
                    existing_session.start_date = start_time
                    existing_session.end_date = end_time
                    
                    # Validate
                    existing_session.full_clean()
                    
                    # Save
                    existing_session.save()
                    
                    # Update trainers and create availability records
                    existing_session.trainers.set(trainers)
                    for trainer in trainers:
                        try:
                            TrainerAvailability.add_busy_slot(
                                trainer=trainer,
                                start_time=start_time,
                                end_time=end_time,
                                session=existing_session
                            )
                            logger.info(f"Created availability record for trainer {trainer.user.username}")
                        except Exception as e:
                            logger.error(f"Error creating trainer availability: {e}")
                            continue
                    
                    # Update equipment
                    existing_session.required_equipment.set(equipment_list)
                    
                    # Add new equipment availability slots
                    for equipment in equipment_list:
                        try:
                            EquipmentAvailability.add_busy_slot(
                                equipment=equipment,
                                start_time=existing_session.start_date,
                                end_time=existing_session.end_date,
                                session=existing_session
                            )
                            logger.info(f"Added availability slot for equipment {equipment.code}")
                        except Exception as e:
                            logger.error(f"Error adding equipment availability for {equipment.code}: {e}")
                            continue
                
                return JsonResponse({
                    'status': 'success',
                    'message': 'Session updated successfully',
                    'session_id': existing_session.session_id
                })
                
            except Session.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Session not found'
                }, status=404)
            except ValidationError as e:
                logger.error(f"Validation error while updating session: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=400)
            except Exception as e:
                logger.error(f"Error updating session: {str(e)}", exc_info=True)
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=400)
        
        # Creating new session (POST)
        try:
            with transaction.atomic():
                # Create session object
                session = Session(
                    course=course,
                    room=room,
                    location=data['location'],
                    slot_type=data['slot_type'],
                    start_date=start_time,
                    end_date=end_time
                )
                
                # Validate session
                try:
                    session.full_clean()
                except ValidationError as e:
                    logger.error(f"Validation error: {e}")
                    raise
                
                # Save session
                session.save()
                
                # Add trainers to session and create availability records
                session.trainers.set(trainers)
                for trainer in trainers:
                    try:
                        TrainerAvailability.add_busy_slot(
                            trainer=trainer,
                            start_time=start_time,
                            end_time=end_time,
                            session=session
                        )
                        logger.info(f"Created availability record for trainer {trainer.user.username}")
                    except Exception as e:
                        logger.error(f"Error creating trainer availability: {e}")
                        continue
                
                # Add equipment to session
                if equipment_list:
                    logger.info(f"Adding equipment to session {session.session_id}")
                    session.required_equipment.set(equipment_list)
                    
                    # Update equipment availability
                    for equipment in equipment_list:
                        try:
                            EquipmentAvailability.add_busy_slot(
                                equipment=equipment,
                                start_time=session.start_date,
                                end_time=session.end_date,
                                session=session
                            )
                            logger.info(f"Updated availability for equipment {equipment.code}")
                        except Exception as e:
                            logger.error(f"Error updating equipment availability for {equipment.code}: {e}")
                            continue
                
                # Create or update CourseInstance
                try:
                    # Check if a batch_id was provided to group related sessions
                    batch_id = data.get('batch_id')
                    course_instance = None
                    
                    if batch_id:
                        # Try to find an existing CourseInstance for this batch
                        course_instance = CourseInstance.objects.filter(
                            course=course,
                            batch_id=batch_id
                        ).first()
                        
                        # If no existing instance with this batch ID, create a new one
                        if not course_instance:
                            # Get cancellation deadline from data or use default
                            cancellation_deadline = int(data.get('cancellation_deadline', 72))
                            capacity = int(data.get('capacity'))
                            published = data.get('published', False)
                            
                            course_instance = CourseInstance.objects.create(
                                course=course,
                                batch_id=batch_id,
                                course_type=course_type,
                                corporate=corporate,
                                start_date=start_time,
                                end_date=end_time,
                                capacity=capacity,
                                cancellation_deadline_hours=cancellation_deadline,
                                published=published,
                                is_active=True
                            )
                            logger.info(f"Created new CourseInstance for course {course.name_en} with batch {batch_id}")
                        elif data.get('cancellation_deadline') or data.get('capacity'):
                            update_fields = []
                            if 'cancellation_deadline' in data:
                                cancellation_deadline = int(data.get('cancellation_deadline', 72))
                                course_instance.cancellation_deadline_hours = cancellation_deadline
                                update_fields.append('cancellation_deadline_hours')
                            
                            if 'capacity' in data:
                                capacity = int(data.get('capacity'))
                                course_instance.capacity = capacity
                                update_fields.append('capacity')

                            if update_fields:
                                course_instance.save(update_fields=update_fields)
                    else:
                        # If no batch_id is provided, we must create a new instance
                        cancellation_deadline = int(data.get('cancellation_deadline', 72))
                        capacity = int(data.get('capacity'))
                        published = data.get('published', False)
                        
                        course_instance = CourseInstance.objects.create(
                            course=course,
                            course_type=course_type,
                            corporate=corporate,
                            start_date=start_time,
                            end_date=end_time,
                            capacity=capacity,
                            cancellation_deadline_hours=cancellation_deadline,
                            published=published,
                            is_active=True
                        )
                        logger.info(f"Created new CourseInstance for course {course.name_en} without batch_id")
                    
                    # Add this session to the instance
                    if course_instance:
                        course_instance.sessions.add(session)
                        
                        # Update start and end dates based on all sessions
                        all_sessions = list(course_instance.sessions.all())
                        if all_sessions:
                            # Find earliest start date and latest end date
                            earliest_start = min([s.start_date for s in all_sessions])
                            latest_end = max([s.end_date for s in all_sessions])
                            
                            # Update course instance dates
                            course_instance.start_date = earliest_start
                            course_instance.end_date = latest_end
                            course_instance.save()
                            
                            logger.info(f"Updated CourseInstance dates: start={earliest_start}, end={latest_end}")
                    
                except Exception as e:
                    logger.error(f"Error creating/updating CourseInstance: {e}", exc_info=True)
                    # Don't fail the whole transaction if just the CourseInstance part fails
                
                logger.info(f"Successfully created session {session.session_id}")
                return JsonResponse({
                    'status': 'success',
                    'message': f'Successfully created session',
                    'session': {
                        'session_id': session.session_id,
                        'start_date': session.start_date.strftime('%Y-%m-%d %H:%M'),
                        'end_date': session.end_date.strftime('%Y-%m-%d %H:%M'),
                        'equipment': [eq.equipment_id for eq in session.required_equipment.all()]
                    }
                })
            
        except ValidationError as e:
            logger.error(f"Validation error while creating session: {e}")
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)
        except Exception as e:
            logger.error(f"Error creating session: {str(e)}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=400)
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        logger.error(f"Unexpected error in create_session: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["POST"])
def delete_session(request, session_id):
    try:
        session = get_object_or_404(Session, session_id=session_id)
        session.delete()
        return JsonResponse({
            'status': 'success',
            'message': _('Session deleted successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@login_required
@staff_member_required
def create_event(request, event_id=None):
    """Create or update an event"""
    # Handle GET request - return form data or specific event data
    if request.method == 'GET':
        # Check if an event_id is provided (for editing)
        # First check if it was passed as a URL parameter
        if not event_id:
            # Then check if it was passed as a query parameter
            event_id = request.GET.get('event_id')
        
        if event_id:
            try:
                event = Event.objects.get(event_id=event_id)
                include_all = request.GET.get('include_all') == 'true'
                response_data = {
                    'status': 'success',
                    'event': {
                        'name': event.name,
                        'description': event.description,
                        'capacity': event.capacity,
                        'event_type': str(event.event_type.event_type_id) if event.event_type else '',
                        'room': str(event.room.room_id) if event.room else '',
                        'room_name': event.room.name if event.room else '',
                        'required_equipment': [str(eq.equipment_id) for eq in event.required_equipment.all()],
                        'start_time': event.start_time.isoformat() if event.start_time else '',
                        'end_time': event.end_time.isoformat() if event.end_time else ''
                    }
                }
                
                # If include_all is true, include additional data needed for the form
                if include_all:
                    try:
                        # Get equipment details
                        equipment_names = []
                        for eq in event.required_equipment.all():
                            equipment_names.append({
                                'id': eq.equipment_id,
                                'name': f"{eq.name} ({eq.code})",
                                'code': eq.code
                            })
                        response_data['event']['equipment_names'] = equipment_names
                        
                        # Get available rooms for the event time period
                        start_time = event.start_time
                        end_time = event.end_time
                        
                        if start_time and end_time:
                            # Get all active rooms
                            all_rooms = Room.objects.filter(is_active=True)
                            
                            # Check which rooms are available
                            available_rooms = []
                            for room in all_rooms:
                                # Check if room is already booked for this time
                                # (excluding this event's booking)
                                is_available = not RoomAvailability.objects.filter(
                                    room=room,
                                    start_date__lt=end_time,
                                    end_date__gt=start_time
                                ).exclude(event=event).exists()
                                
                                if is_available or room.room_id == event.room_id:
                                    available_rooms.append({
                                        'id': str(room.room_id),
                                        'name': room.name
                                    })
                            
                            response_data['available_rooms'] = available_rooms
                            
                            # Get available equipment for the event time period
                            all_equipment = Equipment.objects.filter(is_active=True)
                            available_equipment = []
                            
                            for equipment in all_equipment:
                                # Check if equipment is already booked for this time
                                # (excluding this event's booking)
                                is_available = not EquipmentAvailability.objects.filter(
                                    equipment=equipment,
                                    start_date__lt=end_time,
                                    end_date__gt=start_time
                                ).exclude(event=event).exists()
                                
                                if is_available or event.required_equipment.filter(equipment_id=equipment.equipment_id).exists():
                                    available_equipment.append({
                                        'id': str(equipment.equipment_id),
                                        'name': equipment.name,
                                        'code': equipment.code
                                    })
                            
                            response_data['available_equipment'] = available_equipment
                            
                            # Include event types for completeness
                            event_types = EventType.objects.all()
                            response_data['event_types'] = [
                                {'id': str(et.event_type_id), 'type': et.type}
                                for et in event_types
                            ]
                    except Exception as e:
                        logger.error(f"Error fetching additional data: {e}", exc_info=True)
                        # We'll still return the basic event data even if the additional data fails
                        response_data['warning'] = f"Some additional data could not be loaded: {str(e)}"
                    
                return JsonResponse(response_data)
            except Event.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Event not found')
                }, status=404)
            except Exception as e:
                logger.error(f"Error retrieving event: {e}", exc_info=True)
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
        
        # If no event_id, return form data for creating a new event
        try:
            rooms = Room.objects.filter(is_active=True)
            equipment = Equipment.objects.filter(is_active=True)
            event_types = EventType.objects.all()
            
            return JsonResponse({
                'status': 'success',
                'rooms': [{'id': str(room.room_id), 'name': room.name} for room in rooms],
                'equipment': [{'id': str(eq.equipment_id), 'name': eq.name, 'code': eq.code} for eq in equipment],
                'event_types': [{'id': str(et.event_type_id), 'type': et.type} for et in event_types]
            })
        except Exception as e:
            logger.error(f"Error retrieving form data: {e}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': f'Error loading form data: {str(e)}'
            }, status=500)
    
    elif request.method == 'POST':
        try:
            data = json.loads(request.body)
            
            # Check if this is a delete request
            action = data.get('action')
            if action == 'delete':
                event_id = data.get('event_id')
                if not event_id:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Event ID is required for deletion')
                    }, status=400)
                
                try:
                    event = Event.objects.get(event_id=event_id)
                    event.delete()  # The model's delete method will handle cleaning up availabilities
                    return JsonResponse({
                        'status': 'success',
                        'message': _('Event deleted successfully')
                    })
                except Event.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Event not found')
                    }, status=404)
                except Exception as e:
                    logger.error(f"Error deleting event: {e}", exc_info=True)
                    return JsonResponse({
                        'status': 'error',
                        'message': f'Error deleting event: {str(e)}'
                    }, status=500)
            
            event_id = data.get('event_id')
            name = data.get('name')
            event_type_id = data.get('event_type')
            room_id = data.get('room')
            capacity = data.get('capacity', 0)
            description = data.get('description', '')
            # Update this line to ensure we're using the 'equipment' field consistently with the frontend
            required_equipment = data.get('equipment', [])
            
            # Debug logging to ensure we're receiving equipment data correctly
            logger.debug(f"Event data: name={name}, event_type={event_type_id}, room={room_id}")
            logger.debug(f"Equipment received: {required_equipment}")
            
            start_time_str = data.get('start_time')
            end_time_str = data.get('end_time')
            force_save = data.get('force_save', False)
            
            # Validate required fields
            if not all([name, event_type_id, room_id, start_time_str, end_time_str]):
                return JsonResponse({
                    'status': 'error',
                    'message': _('Missing required fields')
                }, status=400)
            
            try:
                # Convert string times to datetime objects
                start_time = datetime.fromisoformat(start_time_str.replace('Z', ''))
                end_time = datetime.fromisoformat(end_time_str.replace('Z', ''))
                
                # Make datetimes naive if they're timezone-aware
                if timezone.is_aware(start_time):
                    start_time = timezone.make_naive(start_time)
                if timezone.is_aware(end_time):
                    end_time = timezone.make_naive(end_time)
                
                if end_time <= start_time:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('End time must be after start time')
                    }, status=400)
            except ValueError as e:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Invalid date or time format')
                }, status=400)
            
            # Get or create event instance
            if event_id:
                try:
                    event = Event.objects.get(event_id=event_id)
                except Event.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Event not found')
                    }, status=404)
            else:
                event = Event()
            
            # Get related objects
            try:
                event_type = EventType.objects.get(event_type_id=event_type_id)
                room = Room.objects.get(room_id=room_id)
            except (EventType.DoesNotExist, Room.DoesNotExist):
                return JsonResponse({
                    'status': 'error',
                    'message': _('Invalid event type or room')
                }, status=400)
            
            # Update event fields
            event.name = name
            event.event_type = event_type
            event.room = room
            event.capacity = capacity
            event.description = description
            event.start_time = start_time
            event.end_time = end_time
            
            # Get previous equipment for comparison if updating an existing event
            previous_equipment = []
            if event_id:
                previous_equipment = list(event.required_equipment.all())
            
            # Check availability
            is_available = True
            warnings = []
            room_conflicts = False
            equipment_conflicts = []
            
            try:
                # Check room availability
                overlapping_slots = RoomAvailability.objects.filter(
                    room=room,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Exclude the current event if updating
                if event_id:
                    overlapping_slots = overlapping_slots.exclude(event__event_id=event_id)
                
                if overlapping_slots.exists() and not force_save:
                    is_available = False
                    room_conflicts = True
                    conflict = overlapping_slots.first()
                    
                    conflict_type = "session" if conflict.session else "event"
                    conflict_name = conflict.session.course.name_en if conflict.session else (conflict.event.name if conflict.event else "Unknown")
                    conflict_start = conflict.start_date.strftime("%Y-%m-%d %H:%M")
                    conflict_end = conflict.end_date.strftime("%Y-%m-%d %H:%M")
                    
                    conflict_msg = _(
                        "Room is already booked for another {0} ({1}) from {2} to {3}"
                    ).format(conflict_type, conflict_name, conflict_start, conflict_end)
                    
                    warnings.append(conflict_msg)
            except Exception as e:
                logger.error(f"Error checking room availability: {e}", exc_info=True)
                warnings.append(f"Room availability check error: {str(e)}")
            
            # Check equipment availability if equipment is selected
            if required_equipment:
                try:
                    equipment_objects = Equipment.objects.filter(equipment_id__in=required_equipment)
                    
                    for equipment in equipment_objects:
                        try:
                            # Check for overlapping equipment availability records
                            overlapping_equipment_slots = EquipmentAvailability.objects.filter(
                                equipment=equipment,
                                start_date__lt=end_time,
                                end_date__gt=start_time
                            )
                            
                            # Exclude current event's slots if updating
                            if event_id:
                                overlapping_equipment_slots = overlapping_equipment_slots.exclude(event__event_id=event_id)
                            
                            if overlapping_equipment_slots.exists():
                                is_available = False
                                conflict = overlapping_equipment_slots.first()
                                conflict_info = {
                                    'equipment_name': f"{equipment.name} ({equipment.code})",
                                    'start': conflict.start_date.strftime("%Y-%m-%d %H:%M"),
                                    'end': conflict.end_date.strftime("%Y-%m-%d %H:%M")
                                }
                                equipment_conflicts.append(conflict_info)
                                
                                warnings.append(
                                    _("Equipment '{0}' is not available from {1} to {2}").format(
                                        equipment.name, conflict_info['start'], conflict_info['end']
                                    )
                                )
                        except Exception as e:
                            logger.error(f"Error checking equipment {equipment.code} availability: {e}", exc_info=True)
                            continue
                except Exception as e:
                    logger.error(f"Error in equipment availability checks: {e}", exc_info=True)
                    warnings.append(f"Equipment availability check error: {str(e)}")
            
            # If resources are not available and force_save is not specified, return conflicts
            if not is_available and not force_save:
                return JsonResponse({
                    'status': 'warning',
                    'message': _('Availability conflicts detected'),
                    'warnings': warnings,
                    'room_conflicts': room_conflicts,
                    'equipment_conflicts': equipment_conflicts,
                    'needs_confirmation': True
                })
            
            try:
                # Save the event
                event.save()
                
                # Create or update room availability
                room_availability, created = RoomAvailability.objects.get_or_create(
                    room=room,
                    event=event,
                    defaults={
                        'start_date': start_time,
                        'end_date': end_time
                    }
                )
                
                if not created:
                    room_availability.start_date = start_time
                    room_availability.end_date = end_time
                    room_availability.save()
                
                # Update required equipment and create availability records
                if required_equipment:
                    logger.debug(f"Setting {len(required_equipment)} equipment items for event {event.event_id}")
                    
                    try:
                        # Clear any existing equipment first to avoid duplicates
                        event.required_equipment.clear()
                        
                        # Get the equipment objects - handle both string and integer IDs
                        equipment_ids = [int(eq_id) for eq_id in required_equipment]
                        equipment_objects = Equipment.objects.filter(equipment_id__in=equipment_ids)
                        
                        # Set the new equipment
                        event.required_equipment.set(equipment_objects)
                        
                        logger.debug(f"Successfully set {equipment_objects.count()} equipment items for event {event.event_id}")
                        
                        # Create equipment availability records
                        for equipment in equipment_objects:
                            try:
                                # Create or update equipment availability record
                                equipment_availability, created = EquipmentAvailability.objects.get_or_create(
                                    equipment=equipment,
                                    event=event,
                                    defaults={
                                        'start_date': start_time,
                                        'end_date': end_time,
                                        'booking_id': str(event.event_id),
                                        'booking_type': 'EVENT'
                                    }
                                )
                                
                                if not created:
                                    # Update the dates if the record already exists
                                    equipment_availability.start_date = start_time
                                    equipment_availability.end_date = end_time
                                    equipment_availability.save()
                                
                                # Update equipment status to BUSY
                                if equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                                    equipment.status = 'BUSY'
                                    equipment.save(update_fields=['status'])
                                    logger.debug(f"Set equipment {equipment.code} status to BUSY")
                            except Exception as e:
                                logger.error(f"Error updating equipment availability for {equipment.code}: {e}", exc_info=True)
                                warnings.append(f"Error updating equipment {equipment.code} availability: {str(e)}")
                        
                        # Handle equipment no longer associated with this event
                        if event_id:
                            removed_equipment = [eq for eq in previous_equipment if eq not in equipment_objects]
                            for equipment in removed_equipment:
                                try:
                                    # Delete equipment availability records for this event
                                    EquipmentAvailability.objects.filter(
                                        equipment=equipment,
                                        event=event
                                    ).delete()
                                    
                                    # Check if equipment has any other bookings
                                    has_other_bookings = EquipmentAvailability.objects.filter(
                                        equipment=equipment
                                    ).exists()
                                    
                                    # If no other bookings and not in special status, set to READY
                                    if not has_other_bookings and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                                        equipment.status = 'READY'
                                        equipment.save(update_fields=['status'])
                                        logger.debug(f"Set equipment {equipment.code} status to READY as no longer used in event")
                                except Exception as e:
                                    logger.error(f"Error updating removed equipment {equipment.code}: {e}", exc_info=True)
                                    warnings.append(f"Error updating removed equipment {equipment.code}: {str(e)}")
                    except Exception as e:
                        logger.error(f"Error setting required equipment: {e}", exc_info=True)
                        warnings.append(f"Error setting equipment: {str(e)}")
                else:
                    # No equipment - clear any existing assignments
                    event.required_equipment.clear()
                    logger.debug(f"Cleared all equipment for event {event.event_id}")
                    
                    # If updating an event, handle equipment that was previously assigned but now removed
                    if event_id and previous_equipment:
                        for equipment in previous_equipment:
                            try:
                                # Delete equipment availability records for this event
                                EquipmentAvailability.objects.filter(
                                    equipment=equipment,
                                    event=event
                                ).delete()
                                
                                # Check if equipment has any other bookings
                                has_other_bookings = EquipmentAvailability.objects.filter(
                                    equipment=equipment
                                ).exists()
                                
                                # If no other bookings and not in special status, set to READY
                                if not has_other_bookings and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                                    equipment.status = 'READY'
                                    equipment.save(update_fields=['status'])
                                    logger.debug(f"Set equipment {equipment.code} status to READY as no longer used in event")
                            except Exception as e:
                                logger.error(f"Error updating removed equipment {equipment.code}: {e}", exc_info=True)
                                warnings.append(f"Error updating removed equipment {equipment.code}: {str(e)}")
                
                # Return success response with event details and any warnings
                return JsonResponse({
                    'status': 'success',
                    'message': _('Event {} successfully').format('updated' if event_id else 'created'),
                    'event_id': str(event.event_id),
                    'warnings': warnings if warnings else None
                })
                
            except Exception as e:
                logger.error(f"Error saving event: {e}", exc_info=True)
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
                
        except json.JSONDecodeError:
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid JSON data')
            }, status=400)
        except Exception as e:
            logger.error(f"Unexpected error in create_event: {e}", exc_info=True)
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
    
    # Method not allowed
    return JsonResponse({
        'status': 'error',
        'message': _('Method not allowed')
    }, status=405)

def is_room_available(room, start_time, end_time, exclude_event_id=None, exclude_session_id=None):
    """
    Check if a room is available during the specified time range.
    
    Args:
        room: Room object to check
        start_time: Start datetime
        end_time: End datetime
        exclude_event_id: Event ID to exclude from availability check (for editing existing events)
        exclude_session_id: Session ID to exclude from availability check (for editing existing sessions)
        
    Returns:
        bool: True if room is available, False otherwise
    """
    print(f"Checking if room {room.name} is available from {start_time} to {end_time}, excluding event_id={exclude_event_id}, session_id={exclude_session_id}")
    
    # Check for overlapping events in the Event model
    query = Q(
        room=room,
        is_active=True
    ) & (
        # Event starts during our time frame
        (Q(start_time__gte=start_time) & Q(start_time__lt=end_time)) |
        # Event ends during our time frame
        (Q(end_time__gt=start_time) & Q(end_time__lte=end_time)) |
        # Event spans our entire time frame
        (Q(start_time__lte=start_time) & Q(end_time__gte=end_time))
    )
    
    overlapping_events = Event.objects.filter(query)
    
    # Exclude the event being edited from the availability check
    if exclude_event_id:
        try:
            # Ensure exclude_event_id is a string and not empty
            exclude_event_id_str = str(exclude_event_id).strip()
            if exclude_event_id_str:
                overlapping_events = overlapping_events.exclude(event_id=exclude_event_id_str)
                print(f"Excluding event {exclude_event_id_str} from availability check")
        except Exception as e:
            print(f"Error excluding event: {e}")
    
    # Check for conflict with busy slots in RoomAvailability
    busy_slots_conflict = False
    conflicting_slots = []
    
    try:
        # Check for overlapping availability slots
        overlapping_slots = RoomAvailability.objects.filter(
            room=room,
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        if exclude_event_id:
            # Ensure exclude_event_id is a string
            exclude_event_id_str = str(exclude_event_id)
            # Exclude slots related to the event being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='EVENT',
                event__event_id=exclude_event_id_str
            )
            
        if exclude_session_id:
            # Exclude slots related to the session being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='SESSION',
                session__session_id=exclude_session_id
            )
        
        if overlapping_slots.exists():
            busy_slots_conflict = True
            
            # Collect conflict information
            for slot in overlapping_slots:
                slot_info = {
                    'start_time': slot.start_date.isoformat(),
                    'end_time': slot.end_date.isoformat(),
                    'type': slot.booking_type,
                    'id': slot.booking_id
                }
                conflicting_slots.append(slot_info)
    except Exception as e:
        print(f"Error checking room availability slots: {e}")
    
    # Check if any conflicts found
    is_available = not (overlapping_events.exists() or busy_slots_conflict)
    
    if not is_available:
        print(f"Room {room.name} is NOT available for the requested time")
        
        # Print overlapping events from Event model
        if overlapping_events.exists():
            print(f"Found {overlapping_events.count()} overlapping events in Event model:")
            for event in overlapping_events:
                print(f"  - Event: {event.name}, Time: {event.start_time} to {event.end_time}")
        
        # Print conflicting busy slots
        if conflicting_slots:
            print(f"Found {len(conflicting_slots)} conflicting busy slots:")
            for slot in conflicting_slots:
                print(f"  - {slot['type']} {slot['id']}, Time: {slot['start_time']} to {slot['end_time']}")
    else:
        print(f"Room {room.name} IS available for the requested time")
    
    return is_available

@login_required
def check_room_availability(request):
    """
    Check if a room is available for booking at a specific time range.
    """
    if request.method != 'POST':
        return JsonResponse({
            'status': 'error',
            'message': _('Method not allowed')
        }, status=405)
    
    try:
        data = json.loads(request.body)
        room_id = data.get('room_id')
        start_time_str = data.get('start_time')
        end_time_str = data.get('end_time')
        event_id = data.get('event_id', '')
        get_available = data.get('get_available', False)
        
        print(f"Checking room availability: room={room_id}, start={start_time_str}, end={end_time_str}, event_id={event_id}, get_available={get_available}")
        
        if not all([start_time_str, end_time_str]):
            return JsonResponse({
                'status': 'error',
                'message': _('Missing required fields')
            })
        
        try:
            start_time = datetime.fromisoformat(start_time_str.replace('Z', ''))
            end_time = datetime.fromisoformat(end_time_str.replace('Z', ''))
            
            if timezone.is_aware(start_time):
                start_time = timezone.make_naive(start_time)
            if timezone.is_aware(end_time):
                end_time = timezone.make_naive(end_time)
        except ValueError as e:
            print(f"Date parsing error in availability check: {e}")
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid date or time format')
            }, status=400)
        
        # Validate time range
        if end_time <= start_time:
            return JsonResponse({
                'status': 'error',
                'message': _('End time must be after start time')
            })
            
        # If we're requesting available rooms, find all available rooms
        if get_available:
            # Get all active rooms
            all_rooms = Room.objects.filter(is_active=True)
            available_rooms = []
            
            for room in all_rooms:
                is_available = is_room_available(room, start_time, end_time, exclude_event_id=event_id)
                if is_available:
                    available_rooms.append({
                        'id': str(room.room_id),
                        'name': room.name,
                        'capacity': room.capacity
                    })
            
            return JsonResponse({
                'status': 'success',
                'available_rooms': available_rooms
            })
            
        # If checking a specific room
        try:
            room = Room.objects.get(room_id=room_id)
        except Room.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Room not found')
            })
        
        # Get conflict information
        conflict_info = get_room_conflict_details(room, start_time, end_time, exclude_event_id=event_id)
        
        # Check availability
        is_available = is_room_available(room, start_time, end_time, exclude_event_id=event_id)
        
        if is_available:
            return JsonResponse({
                'status': 'success',
                'available': True,
                'message': _('Room is available for the selected time')
            })
        else:
            # Format conflict message
            conflict_message = _('Room is not available at the selected time. ')
            
            if conflict_info['events'] or conflict_info['sessions']:
                conflict_message += _('The room is booked for: ')
                
                if conflict_info['events']:
                    event_names = [f"{e['name']} ({format_time_range(e['start'], e['end'])})" for e in conflict_info['events'][:3]]
                    conflict_message += ', '.join(event_names)
                    
                    if len(conflict_info['events']) > 3:
                        conflict_message += _(' and {} more events').format(len(conflict_info['events']) - 3)
                
                if conflict_info['events'] and conflict_info['sessions']:
                    conflict_message += '; '
                
                if conflict_info['sessions']:
                    session_names = [f"{s['id']} ({format_time_range(s['start'], s['end'])})" for s in conflict_info['sessions'][:3]]
                    conflict_message += ', '.join(session_names)
                    
                    if len(conflict_info['sessions']) > 3:
                        conflict_message += _(' and {} more sessions').format(len(conflict_info['sessions']) - 3)
            
            conflict_message += _(' Please select a different time or date.')
            
            return JsonResponse({
                'status': 'success',
                'available': False,
                'message': conflict_message,
                'conflicts': conflict_info
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        })
    except Exception as e:
        print(f"Error in check_room_availability: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        })

def get_room_conflict_details(room, start_time, end_time, exclude_event_id=None, exclude_session_id=None):
    """
    Get detailed information about conflicting events and sessions for a room.
    
    Args:
        room: Room object to check
        start_time: Start datetime
        end_time: End datetime
        exclude_event_id: Event ID to exclude from conflicts
        exclude_session_id: Session ID to exclude from conflicts
        
    Returns:
        dict: Information about conflicting events and sessions
    """
    conflict_info = {
        'events': [],
        'sessions': []
    }
    
    # Check for overlapping events
    query = Q(
        room=room,
        is_active=True
    ) & (
        # Event starts during our time frame
        (Q(start_time__gte=start_time) & Q(start_time__lt=end_time)) |
        # Event ends during our time frame
        (Q(end_time__gt=start_time) & Q(end_time__lte=end_time)) |
        # Event spans our entire time frame
        (Q(start_time__lte=start_time) & Q(end_time__gte=end_time))
    )
    
    overlapping_events = Event.objects.filter(query)
    
    # Exclude the event being edited
    if exclude_event_id and exclude_event_id.strip():
        try:
            overlapping_events = overlapping_events.exclude(event_id=exclude_event_id)
        except Exception as e:
            print(f"Error excluding event: {e}")
    
    # Add event conflicts
    for event in overlapping_events:
        conflict_info['events'].append({
            'id': str(event.event_id),
            'name': event.name,
            'start': event.start_time.isoformat(),
            'end': event.end_time.isoformat(),
            'type': 'event'
        })
    
    # Check room availability for busy slots
    try:
        # Query for overlapping availability slots
        overlapping_slots = RoomAvailability.objects.filter(
            room=room,
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        if exclude_event_id:
            # Exclude slots related to the event being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='EVENT',
                event__event_id=exclude_event_id
            )
            
        if exclude_session_id:
            # Exclude slots related to the session being edited
            overlapping_slots = overlapping_slots.exclude(
                booking_type='SESSION',
                session__session_id=exclude_session_id
            )
        
        # Process each overlapping slot
        for slot in overlapping_slots:
            if slot.booking_type == 'SESSION' and slot.session:
                conflict_info['sessions'].append({
                    'id': slot.session.session_id,
                    'start': slot.start_date.isoformat(),
                    'end': slot.end_date.isoformat(),
                    'type': 'session',
                    'time': format_time_range(slot.start_date.isoformat(), slot.end_date.isoformat())
                })
            elif slot.booking_type == 'EVENT' and slot.event:
                conflict_info['events'].append({
                    'id': str(slot.event.event_id),
                    'name': slot.event.name,
                    'start_time': slot.start_date.isoformat(),
                    'end_time': slot.end_date.isoformat(),
                    'type': 'event',
                    'time': format_time_range(slot.start_date.isoformat(), slot.end_date.isoformat())
                })
    except Exception as e:
        print(f"Error checking room availability slots: {e}")
    
    return conflict_info

def format_time_range(start_iso, end_iso):
    """Format a time range for display in conflict messages"""
    try:
        start = datetime.fromisoformat(start_iso.replace('Z', '+00:00'))
        end = datetime.fromisoformat(end_iso.replace('Z', '+00:00'))
        
        # If same day, only show time for end
        if start.date() == end.date():
            return f"{start.strftime('%b %d, %H:%M')} - {end.strftime('%H:%M')}"
        else:
            return f"{start.strftime('%b %d, %H:%M')} - {end.strftime('%b %d, %H:%M')}"
    except:
        # Fallback if parsing fails
        return f"{start_iso} - {end_iso}"

@staff_member_required
@require_http_methods(["POST"])
def create_equipment(request):
    try:
        data = json.loads(request.body)
        
        # Check if code exists if provided
        code = data.get('code')
        if code and Equipment.objects.filter(code=code).exists():
            return JsonResponse({
                'status': 'error',
                'message': _('Equipment code already exists')
            }, status=400)
        
        # Get the category, brand, and model instances
        try:
            category = Category.objects.get(category_id=data['category'])
            brand = Brand.objects.get(brand_id=data['brand'])
            model = Model.objects.get(model_id=data['model'])
            
            # Verify that the model belongs to the selected brand
            if model.brand != brand:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected model does not belong to the selected brand')
                }, status=400)
            
            # Check if model has available stock
            if model.stock <= 0:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected model is out of stock')
                }, status=400)
            
        except (Category.DoesNotExist, Brand.DoesNotExist, Model.DoesNotExist) as e:
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid category, brand, or model selected')
            }, status=400)
        
        # Create the equipment and decrease model stock in a transaction
        with transaction.atomic():
            # Decrease model stock
            if not model.decrease_stock():
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected model is out of stock')
                }, status=400)
            
            # Create the equipment
            equipment = Equipment.objects.create(
                name=data['name'],
                category=category,
                brand=brand,
                model=model,
                description=data.get('description', ''),
                status='READY',  # Set initial status to READY
                is_active=True,
                code=code if code else None  # Let model generate code if not provided
            )
            
            # Create availability record
            EquipmentAvailability.objects.create(
                equipment=equipment
            )
        
        return JsonResponse({
            'status': 'success',
            'message': _('Equipment created successfully'),
            'equipment': {
                'id': equipment.equipment_id,
                'code': equipment.code,
                'name': equipment.name
            }
        })
    except KeyError as e:
        return JsonResponse({
            'status': 'error',
            'message': _('Missing required field: {}').format(str(e))
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
def equipment_view(request):
    equipment_list = Equipment.objects.all()
    categories = Category.objects.filter(is_active=True).order_by('name')
    brands = Brand.objects.filter(is_active=True).order_by('name')
    models = Model.objects.filter(is_active=True).select_related('brand').order_by('brand__name', 'name')
    
    context = {
        'equipment_list': equipment_list,
        'categories': categories,
        'brands': brands,
        'models': models,
    }
    return render(request, 'website/rooms/index.html', context)

@staff_member_required
@require_http_methods(["POST"])
def delete_equipment(request, equipment_id):
    try:
        equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
        equipment.delete()
        return JsonResponse({
            'status': 'success',
            'message': _('Equipment deleted successfully!')
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)

@staff_member_required
@require_http_methods(["GET", "PUT", "DELETE"])
def equipment_detail(request, equipment_id):
    try:
        equipment = get_object_or_404(Equipment, equipment_id=equipment_id)
        
        if request.method == "GET":
            return JsonResponse({
                'status': 'success',
                'equipment': {
                    'equipment_id': equipment.equipment_id,
                    'code': equipment.code,
                    'name': equipment.name,
                    'category': equipment.category.category_id,
                    'brand': equipment.brand.brand_id,
                    'model': equipment.model.model_id,
                    'status': equipment.status,
                    'description': equipment.description
                }
            })
            
        elif request.method == "PUT":
            data = json.loads(request.body)
            
            # Validate required fields
            required_fields = ['name', 'category', 'brand', 'model', 'status']
            if not all(field in data for field in required_fields):
                return JsonResponse({
                    'status': 'error',
                    'message': _('Missing required fields')
                }, status=400)
            
            try:
                category = Category.objects.get(category_id=data['category'])
                brand = Brand.objects.get(brand_id=data['brand'])
                model = Model.objects.get(model_id=data['model'])
                
                # Validate that model belongs to brand
                if model.brand != brand:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('Selected model does not belong to the selected brand')
                    }, status=400)
                
                # Update equipment fields
                equipment.name = data['name']
                equipment.category = category
                equipment.brand = brand
                equipment.model = model
                equipment.status = data['status']
                equipment.description = data.get('description', '')
                
                if 'code' in data and data['code'] != equipment.code:
                    # Check if new code is unique
                    if Equipment.objects.filter(code=data['code']).exclude(equipment_id=equipment_id).exists():
                        return JsonResponse({
                            'status': 'error',
                            'message': _('Equipment code already exists')
                        }, status=400)
                    equipment.code = data['code']
                
                equipment.save()
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Equipment updated successfully')
                })
                
            except (Category.DoesNotExist, Brand.DoesNotExist, Model.DoesNotExist) as e:
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=400)
                
        elif request.method == "DELETE":
            equipment.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Equipment deleted successfully')
            })
            
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@trainer_required
def duplicate_course(request, course_id):
    """Trainers can duplicate their own courses, supervisors can duplicate any course"""
    original_course = get_object_or_404(Course, course_id=course_id)
    is_supervisor = request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and original_course not in request.user.trainer.courses.all():
        messages.error(request, _('You do not have permission to duplicate this course.'))
        return redirect('website:courses')
    
    try:
        # Create a new course with the same attributes as the original
        new_course = Course.objects.create(
            name_en=f"{original_course.name_en} (Copied)",
            name_ar=f"{original_course.name_ar} (نسخة)",
            description_en=original_course.description_en,
            description_ar=original_course.description_ar,
            category=original_course.category,
            location=original_course.location,
            session_type=original_course.session_type,
            num_of_sessions=original_course.num_of_sessions,
            capacity=original_course.capacity,
            prerequisite=original_course.prerequisite,
            icon_svg=original_course.icon_svg,
            order=original_course.order,
            equipment_categories=original_course.equipment_categories,
            session_room_types=original_course.session_room_types,
            status='ACTIVE',
            is_active=True
        )
        
        # Add the new course to trainer's courses
        request.user.trainer.courses.add(new_course)
            
        messages.success(request, _('Course duplicated successfully!'))
        return redirect('website:course_detail', course_id=new_course.course_id)
    except Exception as e:
        messages.error(request, _(f'Failed to duplicate course. Error: {str(e)}'))
        return redirect('website:course_detail', course_id=course_id)

@staff_member_required
@require_http_methods(["GET"])
def check_availability(request):
    """API endpoint to check resource availability for a time period"""
    logger = logging.getLogger(__name__)
    try:
        start_time_str = request.GET.get('start_time')
        end_time_str = request.GET.get('end_time')
        session_id = request.GET.get('session_id')
        
        logger.debug(f"Checking availability for time range: {start_time_str} to {end_time_str}, session_id: {session_id}")
        
        if not start_time_str or not end_time_str:
            return JsonResponse({
                'status': 'error',
                'message': 'Missing start_time or end_time parameters'
            }, status=400)
            
        # Parse the date strings (handle both ISO format and URL-encoded format)
        try:
            # First try parsing as ISO format
            try:
                start_time = datetime.fromisoformat(start_time_str.replace('Z', '+00:00'))
                end_time = datetime.fromisoformat(end_time_str.replace('Z', '+00:00'))
            except ValueError:
                # If that fails, try parse_datetime
                start_time = parse_datetime(start_time_str)
                end_time = parse_datetime(end_time_str)
                
                if start_time is None or end_time is None:
                    raise ValueError('Could not parse datetime strings')
                    
            # Make sure times are timezone aware
            if timezone.is_naive(start_time):
                start_time = timezone.make_aware(start_time)
            if timezone.is_naive(end_time):
                end_time = timezone.make_aware(end_time)
                
            # Convert to naive datetime for database queries
            start_time = timezone.make_naive(start_time)
            end_time = timezone.make_naive(end_time)
                
        except (ValueError, TypeError) as e:
            logger.error(f"Error parsing dates: {e}, start_time={start_time_str}, end_time={end_time_str}")
            return JsonResponse({
                'status': 'error',
                'message': f'Error parsing dates: {str(e)}'
            }, status=400)
            
        # Validate time period
        if end_time <= start_time:
            return JsonResponse({
                'status': 'error',
                'message': 'End time must be after start time'
            }, status=400)
            
        # Get all trainers
        all_trainers = Trainer.objects.all()
        logger.debug(f"Found {all_trainers.count()} trainers")
        
        # Find busy trainers during this time period by checking availability records
        busy_trainers = set()
        
        for trainer in all_trainers:
            try:
                # Skip checking the current session's availability if we're editing
                if session_id:
                    try:
                        session = Session.objects.get(session_id=session_id)
                        if trainer in session.trainers.all():
                            logger.debug(f"Skipping trainer {trainer.trainer_id} as they are assigned to session {session_id}")
                            continue  # Skip this trainer, they're already assigned to this session
                    except Session.DoesNotExist:
                        logger.warning(f"Session {session_id} not found")
                        pass
                
                # Check if trainer has overlapping availability slots
                query = TrainerAvailability.objects.filter(
                    trainer=trainer,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Only add the exclude if session_id is provided
                if session_id:
                    query = query.exclude(session__session_id=session_id)
                    
                has_conflict = query.exists()
                
                if has_conflict:
                    busy_trainers.add(trainer.trainer_id)
                    logger.debug(f"Trainer {trainer.trainer_id} is busy during requested time")
                    
            except Exception as e:
                logger.error(f"Error checking trainer {trainer.trainer_id} availability: {e}", exc_info=True)
                continue
                
        # Get available trainers (those not in busy_trainers)
        available_trainers = [trainer.trainer_id for trainer in all_trainers if trainer.trainer_id not in busy_trainers]
        logger.debug(f"Found {len(available_trainers)} available trainers")
        
        # Get all rooms
        all_rooms = Room.objects.filter(is_active=True)
        logger.debug(f"Found {all_rooms.count()} rooms")
        
        # Find busy rooms during this time period by checking availability records
        busy_rooms = set()
        
        for room in all_rooms:
            try:
                # Skip checking the current session's availability if we're editing
                if session_id:
                    try:
                        session = Session.objects.get(session_id=session_id)
                        if session.room and session.room.room_id == room.room_id:
                            logger.debug(f"Skipping room {room.room_id} as it is assigned to session {session_id}")
                            continue  # Skip this room, it's already assigned to this session
                    except Session.DoesNotExist:
                        logger.warning(f"Session {session_id} not found")
                        pass
                
                # Check if room has overlapping availability slots
                query = RoomAvailability.objects.filter(
                    room=room,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Only add the exclude if session_id is provided
                if session_id:
                    query = query.exclude(session__session_id=session_id)
                    
                has_conflict = query.exists()
                
                if has_conflict:
                    busy_rooms.add(room.room_id)
                    logger.debug(f"Room {room.room_id} is busy during requested time")
                    
            except Exception as e:
                logger.error(f"Error checking room {room.room_id} availability: {e}", exc_info=True)
                continue
                
        # Get available rooms (those not in busy_rooms)
        available_rooms = [room.room_id for room in all_rooms if room.room_id not in busy_rooms]
        logger.debug(f"Found {len(available_rooms)} available rooms")
        
        # Return available resources
        return JsonResponse({
            'status': 'success',
            'available_resources': {
                'trainers': available_trainers,
                'rooms': available_rooms
            },
            'time_range': {
                'start': start_time.isoformat(),
                'end': end_time.isoformat()
            }
        })
        
    except Exception as e:
        logger.error(f"Error in check_availability: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["GET"])
def check_equipment_code(request):
    code = request.GET.get('code')
    if not code:
        return JsonResponse({
            'status': 'error',
            'message': _('Code is required')
        }, status=400)
        
    exists = Equipment.objects.filter(code=code).exists()
    return JsonResponse({
        'status': 'success',
        'exists': exists
    })

@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def category_api(request, category_id=None):
    """API endpoint for managing categories"""
    print(f"\nCategory API called with method: {request.method}")
    print(f"Request path: {request.path}")
    print(f"Content type: {request.content_type}")
    
    try:
        if request.method == "DELETE" and category_id:
            # Delete category
            category = get_object_or_404(Category, category_id=category_id)
            category.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Category deleted successfully')
            })
        elif request.method == "GET":
            if category_id:
                # Get specific category
                category = get_object_or_404(Category, category_id=category_id)
                return JsonResponse({
                    'status': 'success',
                    'category': {
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    }
                })
            else:
                # List all categories
                categories = Category.objects.filter(is_active=True)
                return JsonResponse({
                    'status': 'success',
                    'categories': [{
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    } for category in categories]
                })
        
        elif request.method == "PUT" and category_id:
            # Update existing category
            print("\nProcessing PUT request")
            print("Request body:", request.body.decode('utf-8'))
            
            try:
                # Print request content type for debugging
                logger.info(f"Request Content-Type: {request.content_type}")
                logger.info(f"Request body: {request.body.decode('utf-8')}")
                
                # Handle both application/json and form data
                if request.content_type == 'application/json':
                    try:
                        data = json.loads(request.body)
                    except json.JSONDecodeError as e:
                        logger.error(f"JSON decode error: {e}")
                        return JsonResponse({
                            'success': False, 
                            'message': f'Invalid JSON data: {str(e)}'
                        }, status=400)
                else:
                    # Try to get data from POST
                    data = request.POST.dict()
                    
                # If still no data, try to get it from body
                if not data and request.body:
                    try:
                        data = json.loads(request.body)
                    except json.JSONDecodeError:
                        logger.error("Failed to parse request body as JSON")
                        
                logger.info(f"Parsed data: {data}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                return JsonResponse({
                    'success': False, 
                    'message': f'Invalid JSON data: {str(e)}'
                }, status=400)
            except Exception as e:
                logger.error(f"Error parsing request data: {e}", exc_info=True)
                return JsonResponse({
                    'success': False,
                    'message': f'Error parsing request data: {str(e)}'
                }, status=400)
            
            name = data.get('name')
            print(f"Parsed name: {name}")
            
            if not name:
                print("Error: Name is required")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Category name is required')
                }, status=400)
            
            # Get the category to update
            category = get_object_or_404(Category, category_id=category_id)
            
            # Check if another category with this name exists
            if Category.objects.filter(name=name).exclude(category_id=category_id).exists():
                print(f"Error: Category '{name}' already exists")
                return JsonResponse({
                    'status': 'error',
                    'message': _('A category with this name already exists')
                }, status=400)
            
            try:
                # Update the category
                category.name = name
                category.save()
                
                print(f"Updated category: {category.category_id} - {category.name}")
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Category updated successfully'),
                    'category': {
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    }
                })
            except Exception as e:
                print(f"Error updating category: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
        
        elif request.method == "POST":
            print("\nProcessing POST request")
            print("Request body:", request.body.decode('utf-8'))
            
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError as e:
                print(f"JSON decode error: {e}")
                print(f"Raw request body: {request.body}")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Invalid JSON data')
                }, status=400)
            
            name = data.get('name')
            print(f"Parsed name: {name}")
            
            if not name:
                print("Error: Name is required")
                return JsonResponse({
                    'status': 'error',
                    'message': _('Category name is required')
                }, status=400)
            
            # Check if category with this name already exists
            if Category.objects.filter(name=name).exists():
                print(f"Error: Category '{name}' already exists")
                return JsonResponse({
                    'status': 'error',
                    'message': _('A category with this name already exists')
                }, status=400)
            
            try:
                # Create the category
                category = Category.objects.create(
                    name=name,
                    is_active=True
                )
                
                print(f"Created category: {category.category_id} - {category.name}")
                
                return JsonResponse({
                    'status': 'success',
                    'message': _('Category created successfully'),
                    'category': {
                        'id': category.category_id,
                        'name': category.name,
                        'is_active': category.is_active
                    }
                })
            except Exception as e:
                print(f"Error creating category: {e}")
                return JsonResponse({
                    'status': 'error',
                    'message': str(e)
                }, status=500)
            
    except Exception as e:
        print(f"Unexpected error in category_api: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def brand_api(request, brand_id=None):
    """API endpoint for managing brands"""
    try:
        if request.method == "DELETE" and brand_id:
            # Delete brand
            brand = get_object_or_404(Brand, brand_id=brand_id)
            brand.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Brand deleted successfully')
            })
        elif request.method == "GET":
            if brand_id:
                brand = get_object_or_404(Brand, brand_id=brand_id)
                return JsonResponse({
                    'status': 'success',
                    'brand': {
                        'id': brand.brand_id,
                        'name': brand.name,
                        'is_active': brand.is_active
                    }
                })
            else:
                brands = Brand.objects.filter(is_active=True)
                return JsonResponse({
                    'status': 'success',
                    'brands': [{
                        'id': brand.brand_id,
                        'name': brand.name,
                        'is_active': brand.is_active
                    } for brand in brands]
                })
        
        elif request.method == "POST":
            data = json.loads(request.body)
            name = data.get('name')
            
            if not name:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Brand name is required')
                }, status=400)
            
            # Check if brand with this name already exists
            if Brand.objects.filter(name=name).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A brand with this name already exists')
                }, status=400)
            
            brand = Brand.objects.create(
                name=name,
                is_active=True
            )
            
            return JsonResponse({
                'status': 'success',
                'message': _('Brand created successfully'),
                'brand': {
                    'id': brand.brand_id,
                    'name': brand.name,
                    'is_active': brand.is_active
                }
            })
            
        elif request.method == "PUT":
            if not brand_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Brand ID is required')
                }, status=400)
                
            brand = get_object_or_404(Brand, brand_id=brand_id)
            data = json.loads(request.body)
            
            name = data.get('name')
            if not name:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Brand name is required')
                }, status=400)
            
            # Check if another brand with this name exists
            if Brand.objects.filter(name=name).exclude(brand_id=brand_id).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A brand with this name already exists')
                }, status=400)
            
            brand.name = name
            brand.save()
            
            return JsonResponse({
                'status': 'success',
                'message': _('Brand updated successfully'),
                'brand': {
                    'id': brand.brand_id,
                    'name': brand.name,
                    'is_active': brand.is_active
                }
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def model_api(request, model_id=None):
    """API endpoint for managing models"""
    try:
        if request.method == "DELETE" and model_id:
            # Delete model
            model = get_object_or_404(Model, model_id=model_id)
            model.delete()
            return JsonResponse({
                'status': 'success',
                'message': _('Model deleted successfully')
            })
        elif request.method == "GET":
            if model_id:
                model = get_object_or_404(Model, model_id=model_id)
                return JsonResponse({
                    'status': 'success',
                    'model': {
                        'id': model.model_id,
                        'name': model.name,
                        'brand': model.brand.brand_id,
                        'brand_name': model.brand.name,
                        'stock': model.stock,
                        'is_active': model.is_active
                    }
                })
            else:
                models = Model.objects.filter(is_active=True).select_related('brand')
                return JsonResponse({
                    'status': 'success',
                    'models': [{
                        'id': model.model_id,
                        'name': model.name,
                        'brand': model.brand.brand_id,
                        'brand_name': model.brand.name,
                        'stock': model.stock,
                        'is_active': model.is_active
                    } for model in models]
                })
        
        elif request.method == "POST":
            data = json.loads(request.body)
            name = data.get('name')
            brand_id = data.get('brand')
            stock = data.get('stock', 1)  # Default to 1 if not provided
            
            if not name or not brand_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Model name and brand are required')
                }, status=400)
            
            try:
                brand = Brand.objects.get(brand_id=brand_id)
            except Brand.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected brand does not exist')
                }, status=400)
            
            # Check if model with this name already exists for this brand
            if Model.objects.filter(name=name, brand=brand).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A model with this name already exists for this brand')
                }, status=400)
            
            model = Model.objects.create(
                name=name,
                brand=brand,
                stock=stock,
                is_active=True
            )
            
            return JsonResponse({
                'status': 'success',
                'message': _('Model created successfully'),
                'model': {
                    'id': model.model_id,
                    'name': model.name,
                    'brand': model.brand.brand_id,
                    'brand_name': model.brand.name,
                    'stock': model.stock,
                    'is_active': model.is_active
                }
            })
            
        elif request.method == "PUT":
            if not model_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Model ID is required')
                }, status=400)
                
            model = get_object_or_404(Model, model_id=model_id)
            data = json.loads(request.body)
            
            name = data.get('name')
            brand_id = data.get('brand')
            stock = data.get('stock')
            
            if not name or not brand_id:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Model name and brand are required')
                }, status=400)
            
            try:
                brand = Brand.objects.get(brand_id=brand_id)
            except Brand.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Selected brand does not exist')
                }, status=400)
            
            # Check if another model with this name exists for this brand
            if Model.objects.filter(name=name, brand=brand).exclude(model_id=model_id).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': _('A model with this name already exists for this brand')
                }, status=400)
            
            model.name = name
            model.brand = brand
            if stock is not None:
                model.stock = stock
            model.save()
            
            return JsonResponse({
                'status': 'success',
                'message': _('Model updated successfully'),
                'model': {
                    'id': model.model_id,
                    'name': model.name,
                    'brand': model.brand.brand_id,
                    'brand_name': model.brand.name,
                    'stock': model.stock,
                    'is_active': model.is_active
                }
            })
            
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

def check_equipment_availability(equipment, start_time, end_time, exclude_event_id=None, exclude_session_id=None):
    """
    Check if equipment is available for a given time slot.
    Returns a list of conflicts if any, otherwise returns None.
    
    NOTE: Currently modified to always return None (no conflicts) to allow equipment selection
    to override availability checks during event rescheduling.
    """
    # TEMPORARY FIX: Always return None (no conflicts)
    # This allows equipment that appears in the dropdown to be selected without additional checks
    return None
    
    # Original code below is kept for reference but not executed
    """
    try:
        # Check if equipment status prevents booking
        if equipment.status in ['MAINTENANCE', 'SCRAP', 'FIXED']:
            return [{
                'type': 'status',
                'status': equipment.status,
                'message': f"Equipment is {equipment.get_status_display()}"
            }]
            
        # Make times timezone naive if they are aware
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
        
        # Check for overlapping availability slots
        conflicts = []
        overlapping_slots = EquipmentAvailability.objects.filter(
            equipment=equipment,
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        # Exclude the event being edited
        if exclude_event_id:
            # Ensure exclude_event_id is a string
            exclude_event_id_str = str(exclude_event_id)
            overlapping_slots = overlapping_slots.exclude(
                booking_type='EVENT',
                event__event_id=exclude_event_id_str
            )
        
        # Exclude the session being edited
        if exclude_session_id:
            overlapping_slots = overlapping_slots.exclude(
                booking_type='SESSION',
                session__session_id=exclude_session_id
            )
        
        for slot in overlapping_slots:
            conflict = {
                'type': slot.booking_type.lower() if slot.booking_type else 'unknown',
                'id': slot.booking_id,
                'start': slot.start_date.isoformat(),
                'end': slot.end_date.isoformat(),
                'message': f"Equipment is booked from {slot.start_date.strftime('%Y-%m-%d %H:%M')} to {slot.end_date.strftime('%Y-%m-%d %H:%M')}"
            }
            conflicts.append(conflict)
        
        return conflicts if conflicts else None
        
    except Exception as e:
        print(f"Error checking equipment availability: {e}")
        return None
    """

@login_required
@require_http_methods(["POST"])
def check_equipment_availability_view(request):
    """Check equipment availability for a given time slot."""
    logger = logging.getLogger(__name__)
    try:
        data = json.loads(request.body)
        start_time_str = data.get('start_time')
        end_time_str = data.get('end_time')
        event_id = data.get('event_id')
        session_id = data.get('session_id')
        
        logger.debug(f"Equipment availability check request: {data}")
        
        if not all([start_time_str, end_time_str]):
            return JsonResponse({
                'status': 'error',
                'message': _('Start time and end time are required')
            }, status=400)
        
        try:
            # Convert string times to datetime objects
            start_time = datetime.fromisoformat(start_time_str.replace('Z', ''))
            end_time = datetime.fromisoformat(end_time_str.replace('Z', ''))
            
            # Make datetimes naive if they're timezone-aware
            if timezone.is_aware(start_time):
                start_time = timezone.make_naive(start_time)
            if timezone.is_aware(end_time):
                end_time = timezone.make_naive(end_time)
            
            if end_time <= start_time:
                return JsonResponse({
                    'status': 'error',
                    'message': _('End time must be after start time')
                }, status=400)
        except ValueError as e:
            logger.error(f"Invalid date format: {e}, start_time={start_time_str}, end_time={end_time_str}")
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid date or time format: ') + str(e)
            }, status=400)
        
        # Get only equipment with READY or BUSY availability status
        all_equipment = Equipment.objects.filter(
            is_active=True,
            status__in=['READY', 'BUSY']
        ).distinct()
        
        available_equipment = []
        conflicts = []
        
        for equipment in all_equipment:
            # Check equipment availability
            equipment_conflicts = check_equipment_availability(
                equipment,
                start_time,
                end_time,
                exclude_event_id=event_id,
                exclude_session_id=session_id
            )
            
            if not equipment_conflicts:
                available_equipment.append({
                    'id': str(equipment.equipment_id),
                    'name': equipment.name,
                    'code': equipment.code
                })
            else:
                conflicts.append({
                    'equipment_id': str(equipment.equipment_id),
                    'equipment_name': f"{equipment.name} ({equipment.code})",
                    'conflicts': equipment_conflicts
                })
        
        return JsonResponse({
            'status': 'success',
            'available_equipment': available_equipment,
            'conflicts': conflicts if conflicts else None
        })
        
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _('Invalid JSON data')
        }, status=400)
    except Exception as e:
        logger.error(f"Error checking equipment availability: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
def edit_room(request, room_id):
    """View function to retrieve room details for editing"""
    room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment'), room_id=room_id)
    logger = logging.getLogger(__name__)
    logger.info(f"Fetching room {room_id} for editing")
    
    # Format fixed equipment as array of objects with id property
    fixed_equipment = [{'id': eq_id} for eq_id in room.fixed_equipment.values_list('equipment_id', flat=True)]
    logger.info(f"Fixed equipment for room {room_id}: {fixed_equipment}")
    
    return JsonResponse({
        'room': {
            'name': room.name,
            'capacity': room.capacity,
            'room_type': room.room_type.room_type_id,
            'description': room.description,
            'fixed_equipment': fixed_equipment
        }
    })

@staff_member_required
def room_detail(request, room_id):
    room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment', 'fixed_equipment__category'), room_id=room_id)
    return render(request, 'website/rooms/detail.html', {
        'room': room,
        'equipment_list': Equipment.objects.filter(is_active=True).order_by('code'),
        'room_types': RoomType.objects.filter(is_active=True)
    })

@staff_member_required
@require_http_methods(["GET"])
def room_available_equipment(request, room_id):
    """API endpoint to get available equipment for a room (READY status + already assigned equipment)"""
    try:
        room = get_object_or_404(Room.objects.prefetch_related('fixed_equipment'), room_id=room_id)
        
        # Get IDs of equipment already fixed in this room
        fixed_equipment_ids = list(room.fixed_equipment.values_list('equipment_id', flat=True))
        
        # Get equipment with READY status from equipment availability
        ready_equipment = Equipment.objects.filter(
            status='READY',
            is_active=True
        ).exclude(equipment_id__in=fixed_equipment_ids)
        
        # Get equipment already fixed in this room
        fixed_equipment = room.fixed_equipment.all()
        
        # Combine both lists
        all_equipment = []
        
        # Add fixed equipment (already assigned to this room)
        for equipment in fixed_equipment:
            all_equipment.append({
                'equipment_id': equipment.equipment_id,
                'name': equipment.name,
                'code': equipment.code,
                'is_fixed': True
            })
        
        # Add ready equipment (available to be assigned)
        for equipment in ready_equipment:
            all_equipment.append({
                'equipment_id': equipment.equipment_id,
                'name': equipment.name,
                'code': equipment.code,
                'is_fixed': False
            })
        
        return JsonResponse({
            'equipment': all_equipment
        })
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def calendar_resources_api(request):
    """API endpoint to get resources (rooms, trainers, equipment) for the calendar"""
    from django.http import JsonResponse
    
    resource_type = request.GET.get('type', 'all')
    
    if resource_type == 'rooms' or resource_type == 'all':
        rooms = [{
            'id': room.room_id,
            'title': room.name,
            'capacity': room.capacity,
            'status': room.status
        } for room in Room.objects.filter(is_active=True)]
    else:
        rooms = []
        
    if resource_type == 'trainers' or resource_type == 'all':
        trainers = [{
            'id': trainer.trainer_id,
            'title': trainer.user.get_full_name() or trainer.user.username,
            'status': trainer.status
        } for trainer in Trainer.objects.all().select_related('user')]
    else:
        trainers = []
        
    if resource_type == 'equipment' or resource_type == 'all':
        equipment = [{
            'id': item.equipment_id,
            'title': f"{item.code} - {item.name}",
            'status': item.status
        } for item in Equipment.objects.filter(is_active=True)]
    else:
        equipment = []
    
    return JsonResponse({
        'rooms': rooms,
        'trainers': trainers,
        'equipment': equipment
    })

@login_required
@staff_member_required
@require_http_methods(["POST"])
def delete_trainer_availability(request):
    """
    Delete trainer availability record for a specific trainer and session
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Get JSON data from request
        data = json.loads(request.body)
        trainer_id = data.get('trainer_id')
        session_id = data.get('session_id')
        
        if not trainer_id or not session_id:
            return JsonResponse({
                'status': 'error',
                'message': 'Missing required parameters: trainer_id and session_id'
            }, status=400)
        
        # Get the trainer
        try:
            trainer = Trainer.objects.get(trainer_id=trainer_id)
        except Trainer.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': f'Trainer with ID {trainer_id} not found'
            }, status=404)
        
        # Get the session
        try:
            session = Session.objects.get(session_id=session_id)
        except Session.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': f'Session with ID {session_id} not found'
            }, status=404)
        
        # Delete the availability record
        deleted, _ = TrainerAvailability.objects.filter(trainer=trainer, session=session).delete()
        
        # Remove trainer from session.trainers
        session.trainers.remove(trainer)
        
        if deleted > 0:
            logger.info(f"Deleted {deleted} trainer availability record(s) for trainer {trainer_id} in session {session_id}")
            
            # Update trainer status if they have no more availability records
            if not TrainerAvailability.objects.filter(trainer=trainer).exists():
                trainer.status = 'READY'
                trainer.save(update_fields=['status'])
                logger.info(f"Updated trainer {trainer_id} status to READY as no other availability records exist")
            
            return JsonResponse({
                'status': 'success',
                'message': f'Deleted {deleted} trainer availability record(s)'
            })
        else:
            logger.info(f"No trainer availability records found to delete for trainer {trainer_id} in session {session_id}")
            return JsonResponse({
                'status': 'success',
                'message': 'No records found to delete'
            })
    
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid JSON data'
        }, status=400)
    except Exception as e:
        logger.error(f"Error deleting trainer availability: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@staff_member_required
def debug_course_data(request, course_id):
    """Debug view to examine the course data structure"""
    import json
    from django.http import HttpResponse
    
    course = get_object_or_404(Course, course_id=course_id)
    
    # Prepare the debug information
    debug_info = {
        'course_id': course.course_id,
        'name': course.name_en,
        'equipment_categories': course.equipment_categories,
        'equipment_categories_type': str(type(course.equipment_categories)),
        'session_room_types': course.session_room_types,
        'session_room_types_type': str(type(course.session_room_types)),
    }
    
    # Format as JSON with indentation for readability
    formatted_json = json.dumps(debug_info, indent=4)
    
    # Return as a simple text response
    return HttpResponse(f"<pre>{formatted_json}</pre>", content_type="text/html")

@trainer_required
def create_course(request):
    """Only trainers can create courses"""
    logger = logging.getLogger(__name__)
    
    # Debug information about the user attempting to create course
    user_type_code = getattr(request.user.user_type, 'code', 'No user type') if hasattr(request.user, 'user_type') else 'No user_type attribute'
    is_trainer = hasattr(request.user, 'trainer')
    logger.info(f"User {request.user.username} (ID: {request.user.id}) attempting to create course")
    logger.info(f"User type: {user_type_code}")
    logger.info(f"Is trainer: {is_trainer}")
    
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES)
        if form.is_valid():
            # Save the course
            course = form.save(commit=False)
            course.is_active = True
            course.save()
            
            # Add the course to trainer's courses if user is a trainer
            if hasattr(request.user, 'trainer'):
                request.user.trainer.courses.add(course)
                logger.info(f"Course {course.course_id} added to trainer {request.user.username}'s courses")
            else:
                # For SUPER_ADMIN, find the first supervisor trainer and assign the course
                try:
                    supervisor_trainer = Trainer.objects.filter(is_supervisor=True).first()
                    if supervisor_trainer:
                        supervisor_trainer.courses.add(course)
                        logger.info(f"SUPER_ADMIN: Course {course.course_id} assigned to supervisor trainer {supervisor_trainer.user.username}")
                    else:
                        logger.warning(f"SUPER_ADMIN: Could not find a supervisor trainer to assign course {course.course_id}")
                except Exception as e:
                    logger.error(f"SUPER_ADMIN: Error assigning course to supervisor: {str(e)}")
            
            # Process multiple course content items
            content_added = False
            if 'content_ids' in request.POST and request.POST['content_ids']:
                content_ids = request.POST['content_ids'].split(',')
                supported_extensions = CourseContentForm.SUPPORTED_EXTENSIONS
                
                for content_id in content_ids:
                    # Get content fields with the content_id prefix
                    try:
                        title = request.POST.get(f'content-{content_id}-title')
                        content_type = request.POST.get(f'content-{content_id}-content_type')
                        description = request.POST.get(f'content-{content_id}-description', '')
                        
                        # Check if we have a file for this content item
                        if f'content-{content_id}-file' in request.FILES:
                            content_file = request.FILES[f'content-{content_id}-file']
                            
                            # Validate file extension
                            file_ext = content_file.name.split('.')[-1].lower()
                            if file_ext not in supported_extensions:
                                messages.error(
                                    request, 
                                    _('Unsupported file format for "{0}". Supported formats are: {1}').format(
                                        title,
                                        ', '.join([ext.upper() for ext in supported_extensions])
                                    )
                                )
                                continue
                            
                            # Create the course content
                            course_content = CourseContent(
                                course=course,
                                title=title,
                                description=description,
                                content_type=content_type,
                                file=content_file,
                                created_by=request.user
                            )
                            course_content.save()
                            content_added = True
                            logger.info(f"Added course content: {title} for course {course.course_id}")
                    except Exception as e:
                        logger.error(f"Error adding course content item {content_id}: {str(e)}")
                        continue
            
            if content_added:
                messages.success(request, _('Course created successfully with content!'))
            else:
                messages.success(request, _('Course created successfully!'))
                
            return redirect('website:course_detail', course_id=course.course_id)
        else:
            messages.error(request, _('Please correct the errors below.'))
    else:
        form = CourseForm()
    
    # Get all active room types
    room_types = RoomType.objects.filter(is_active=True).order_by('name')
    # Get all active categories
    categories = Category.objects.filter(is_active=True).order_by('name')
    
    # Get content type choices for the content form
    content_form = CourseContentForm()
    
    context = {
        'form': form,
        'content_form': content_form,
        'title': _('Create Course'),
        'submit_text': _('Create Course'),
        'room_types': room_types,
        'categories': categories,
        'show_content_section': True,
    }
    return render(request, 'website/courses/course_form.html', context)
@trainer_required
def edit_course(request, course_id):
    """Trainers can edit their own courses, supervisors can edit any course"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} editing course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to edit this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        form = CourseForm(request.POST, request.FILES, instance=course)
        if form.is_valid():
            form.save()
            messages.success(request, _('Course updated successfully!'))
            return redirect('website:course_detail', course_id=course.course_id)
    else:
        form = CourseForm(instance=course)
    
    # Get all active room types
    room_types = RoomType.objects.filter(is_active=True).order_by('name')
    # Get all active categories
    categories = Category.objects.filter(is_active=True).order_by('name')
    
    context = {
        'form': form,
        'title': _('Edit Course'),
        'submit_text': _('Update Course'),
        'course': course,
        'room_types': room_types,
        'categories': categories,
    }
    return render(request, 'website/courses/course_form.html', context)

@trainer_required
def cancel_course(request, course_id):
    """Trainers can cancel their own courses, supervisors can cancel any course"""
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} cancelling course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to cancel this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        # Update the course status to CANCELLED
        course.status = 'CANCELLED'
        course.save()
        
        messages.success(request, _('Course cancelled successfully!'))
        return redirect('website:course_detail', course_id=course.course_id)
    
    context = {
        'course': course,
    }
    return render(request, 'website/courses/cancel_course.html', context)

@trainer_required
def reactivate_course(request, course_id):
    """Trainers can reactivate their own cancelled courses, supervisors can reactivate any course"""
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} reactivating course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to reactivate this course.'))
        return redirect('website:courses')
    
    if course.status != 'CANCELLED':
        messages.error(request, _('Only cancelled courses can be reactivated.'))
        return redirect('website:course_detail', course_id=course.course_id)
    
    # Update the course status to ACTIVE
    try:
        course.status = 'ACTIVE'
        course.save()
        
        messages.success(request, _('Course reactivated successfully!'))
        return redirect('website:course_detail', course_id=course.course_id)
    except Exception as e:
        messages.error(request, _(f'Failed to reactivate course. Error: {str(e)}'))
        return redirect('website:course_detail', course_id=course.course_id)
    
@trainer_required
def delete_course(request, course_id):
    """Trainers can delete their own cancelled courses, supervisors can delete any course"""
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check if user is SUPER_ADMIN or has trainer attribute
    if hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN":
        is_supervisor = True  # Treat SUPER_ADMIN as supervisor
        logger.info(f"SUPER_ADMIN user {request.user.username} deleting course {course_id}")
    else:
        is_supervisor = hasattr(request.user, 'trainer') and request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and (not hasattr(request.user, 'trainer') or course not in request.user.trainer.courses.all()):
        messages.error(request, _('You do not have permission to delete this course.'))
        return redirect('website:courses')
    
    # Regular trainers can only delete cancelled courses, supervisors can delete any course
    if not is_supervisor and course.status != 'CANCELLED':
        messages.error(request, _('Only cancelled courses can be deleted.'))
        return redirect('website:course_detail', course_id=course.course_id)

    try:
        course.delete()
        messages.success(request, _('Course deleted successfully!'))
        return redirect('website:courses')
    except Exception as e:
        messages.error(request, _('Failed to delete course.'))
        return redirect('website:course_detail', course_id=course.course_id)

@trainer_supervisor_required
def assign_course_to_trainers(request):
    """Allow supervisor trainers to assign courses to other trainers"""
    logger = logging.getLogger(__name__)
    
    # Debug information about the user attempting to assign courses
    user_type_code = getattr(request.user.user_type, 'code', 'No user type') if hasattr(request.user, 'user_type') else 'No user_type attribute'
    is_trainer = hasattr(request.user, 'trainer')
    # Check if user is SUPER_ADMIN - they don't need to be a trainer supervisor
    is_super_admin = hasattr(request.user, 'user_type') and request.user.user_type and request.user.user_type.code == "SUPER_ADMIN"
    is_supervisor = (is_trainer and request.user.trainer.is_supervisor) if is_trainer else False
    
    logger.info(f"User {request.user.username} (ID: {request.user.id}) attempting to assign courses")
    logger.info(f"User type: {user_type_code}")
    logger.info(f"Is trainer: {is_trainer}")
    logger.info(f"Is supervisor: {is_supervisor}")
    logger.info(f"Is super admin: {is_super_admin}")
    
    # If the user is not a trainer and not a SUPER_ADMIN, they shouldn't be here
    if not is_trainer and not is_super_admin:
        logger.warning(f"Non-trainer, non-SUPER_ADMIN user {request.user.username} tried to access assign_course_to_trainers")
        messages.error(request, _('You do not have permission to assign courses.'))
        return redirect('website:courses')
    
    # Check if a course was pre-selected from the URL
    preselected_course_id = request.GET.get('course')
    preselected_course = None
    
    if preselected_course_id:
        try:
            preselected_course = Course.objects.get(course_id=preselected_course_id)
        except Course.DoesNotExist:
            messages.warning(request, _('The selected course was not found.'))
    
    if request.method == 'POST':
        course_id = request.POST.get('course')
        trainer_ids = request.POST.getlist('trainers')
        
        if not course_id:
            messages.error(request, _('Please select a course.'))
            return redirect('website:courses')
            
        if not trainer_ids:
            messages.error(request, _('Please select at least one trainer.'))
            return redirect('website:courses')
            
        try:
            course = Course.objects.get(course_id=course_id)
            
            # Add course to selected trainers
            for trainer_id in trainer_ids:
                trainer = Trainer.objects.get(trainer_id=trainer_id)
                trainer.courses.add(course)
                
            messages.success(request, _('Course assigned to trainers successfully!'))
            return redirect('website:course_detail', course_id=course.course_id)
        except Course.DoesNotExist:
            messages.error(request, _('Course not found.'))
            return redirect('website:courses')
        except Trainer.DoesNotExist:
            messages.error(request, _('One or more trainers not found.'))
            return redirect('website:courses')
        except Exception as e:
            logger.error(f"Error assigning course to trainers: {e}", exc_info=True)
            messages.error(request, _('An error occurred while assigning the course.'))
            return redirect('website:courses')
    
    # For GET requests, provide necessary context
    courses = Course.objects.filter(is_active=True)
    trainers = Trainer.objects.all()
    
    context = {
        'courses': courses,
        'trainers': trainers,
        'preselected_course': preselected_course,
    }
    
    return render(request, 'website/courses/assign_course.html', context)
@login_required
def course_detail(request, course_id):
    """View a course's details"""
    course = get_object_or_404(Course, course_id=course_id)
    is_trainer = hasattr(request.user, 'trainer')
    is_supervisor = is_trainer and request.user.trainer.is_supervisor
    is_admin = request.user.is_staff
    can_manage = False
    has_paid = False
    has_upcoming_reservation = False

    # Check if the user is an external individual
    is_external_individual = request.user.user_type and request.user.user_type.code == 'EXTERNAL_INDIVIDUAL'

    # Get referrer information for back navigation
    referrer = request.GET.get('ref')
    back_url = None
    back_text = _('Back to Courses')

    if referrer == 'admin-reservations':
        back_url = reverse('website:admin_reservations')
        back_text = _('Back to Admin Reservations')
    elif referrer == 'corporate-reservations':
        back_url = reverse('website:corporate_admin_reservations')
        back_text = _('Back to Corporate Reservations')
    elif referrer == 'user-reservations':
        back_url = reverse('website:user_reservations')
        back_text = _('Back to My Reservations')
    else:
        back_url = reverse('website:courses')
        back_text = _('Back to Courses')
    
    # Check if the user can manage this course
    if is_trainer:
        # Supervisors can manage any course
        if is_supervisor:
            can_manage = True
        # Regular trainers can only manage their own courses
        elif course in request.user.trainer.courses.all():
            can_manage = True
    # Admins can manage courses too
    elif is_admin:
        can_manage = True
    else:
        # For normal users, check if they have paid for this course
        has_paid = Reservation.objects.filter(
            user=request.user,
            course_instance__course=course,
            status__in=['IN_PROGRESS', 'COMPLETED']
        ).exists()
        
        # Check if they have an upcoming reservation
        has_upcoming_reservation = Reservation.objects.filter(
            user=request.user,
            course_instance__course=course,
            status='UPCOMING'
        ).exists()
    
    # Get all room types for room type display
    room_types = RoomType.objects.filter(is_active=True)
    
    # Process session room types for display
    session_room_types = []
    if course.session_room_types:
        # Get all room types as a lookup dictionary
        room_type_dict = {str(rt.room_type_id): rt.name for rt in room_types}
        
        # Convert the session_room_types JsonField to a Python dictionary if needed
        if isinstance(course.session_room_types, str):
            try:
                session_room_types_dict = json.loads(course.session_room_types)
            except json.JSONDecodeError:
                session_room_types_dict = {}
        else:
            session_room_types_dict = course.session_room_types
        
        # Sort session numbers numerically
        session_numbers = sorted(session_room_types_dict.keys(), key=lambda x: int(x) if x.isdigit() else float('inf'))
        
        # Build a list of session room types with relevant information
        for session_num in session_numbers:
            room_type_id = session_room_types_dict[session_num]
            room_type_id_str = str(room_type_id)
            
            # Check if we found a matching room type
            found = room_type_id_str in room_type_dict
            room_type_name = room_type_dict.get(room_type_id_str, f"Room Type #{room_type_id}")
            
            session_room_types.append({
                'session_num': session_num,
                'room_type_id': room_type_id,
                'room_type_name': room_type_name,
                'found': found
            })
    
    # Get course instances for external individuals
    course_instances = []
    now = timezone.now()
    
    if is_external_individual:
        course_instances = CourseInstance.objects.filter(
            course=course,
            is_active=True,
            course_type='INDIVIDUAL'  # Only show INDIVIDUAL course types to individual users
        ).order_by('start_date')
        
        # Add session count for each instance and mark instances where cancellation deadline has passed
        for instance in course_instances:
            instance.session_count = instance.sessions.count()
            
            # Check if course has already started
            instance.course_started = now > instance.start_date
            
            # Calculate remaining time until course starts (for UI purposes)
            if not instance.course_started:
                time_until_start = instance.start_date - now
                days = time_until_start.days
                hours = (time_until_start.seconds // 3600)
                minutes = (time_until_start.seconds % 3600) // 60
                if days > 0:
                    instance.time_until_deadline = f"{days} days, {hours} hours"
                else:
                    instance.time_until_deadline = f"{hours} hours, {minutes} minutes" 
            else:
                instance.time_until_deadline = "Deadline has passed"
            
            # No need to set these properties, just accessing them ensures they're available in the template
            # The properties available_seats and waiting_list_count are calculated dynamically
            # and don't need to be assigned to themselves
    
    context = {
        'course': course,
        'can_manage': can_manage,
        'is_trainer': is_trainer,
        'is_supervisor': is_supervisor,
        'is_admin': is_admin,
        'has_paid': has_paid,
        'has_upcoming_reservation': has_upcoming_reservation,
        'room_types': room_types,
        'session_room_types': session_room_types,
        'is_external_individual': is_external_individual,
        'course_instances': course_instances,
        'now': now,
        'back_url': back_url,
        'back_text': back_text
    }
    return render(request, 'website/courses/course_detail.html', context)

@trainer_required
def course_content_list(request, course_id):
    """View to list all content associated with a course"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    
    # Check user permissions
    is_trainer = hasattr(request.user, 'trainer')
    is_supervisor = is_trainer and request.user.trainer.is_supervisor
    is_admin = request.user.is_staff
    
    # For trainers, check if they own the course or are supervisors
    if is_trainer:
        if not (is_supervisor or course in request.user.trainer.courses.all()):
            messages.error(request, _('You do not have permission to view contents for this course.'))
            return redirect('website:courses')
    # For admins, allow access
    elif is_admin:
        pass  # Admin can access content
    else:
        # For regular users, check if they have a PAID reservation
        has_paid = Reservation.objects.filter(
            user=request.user,
            course_instance__course=course,
            status__in=['IN_PROGRESS', 'COMPLETED']
        ).exists()
        
        if not has_paid:
            messages.error(request, _('You need to complete your payment to access course content.'))
            return redirect('website:course_detail', course_id=course.course_id)
    
    # Get all course content
    course_contents = CourseContent.objects.filter(course=course, is_active=True).order_by('-created_at')
    
    # Get all questionnaires for this course
    questionnaires = Questionnaire.objects.filter(course=course, is_active=True).order_by('-created_at')
    logger.debug(f"Fetched {questionnaires.count()} questionnaires for course {course_id}")
    
    context = {
        'course': course,
        'course_contents': course_contents,
        'questionnaires': questionnaires,
        'is_supervisor': is_supervisor,
        'is_trainer': is_trainer,
        'is_admin': is_admin,
    }
    return render(request, 'website/courses/course_content_list.html', context)

@trainer_required
def add_course_content(request, course_id):
    """View to add new content to a course"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    is_supervisor = request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and course not in request.user.trainer.courses.all():
        messages.error(request, _('You do not have permission to add content to this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        form = CourseContentForm(request.POST, request.FILES)
        if form.is_valid():
            course_content = form.save(commit=False)
            course_content.course = course
            course_content.created_by = request.user
            course_content.save()
            
            messages.success(request, _('Content added successfully!'))
            return redirect('website:course_content_list', course_id=course.course_id)
        else:
            # Check specifically for file extension errors
            if 'file' in form.errors:
                for error in form.errors['file']:
                    # Check if the error is about unsupported file format
                    if 'Unsupported file format' in error:
                        messages.error(request, error)
                    else:
                        messages.error(request, error)
            else:
                messages.error(request, _('Please correct the errors below.'))
    else:
        form = CourseContentForm()
    
    context = {
        'form': form,
        'course': course,
        'title': _('Add Course Content'),
        'submit_text': _('Add Content'),
    }
    return render(request, 'website/courses/course_content_form.html', context)

@trainer_required
def edit_course_content(request, course_id, content_id):
    """View to edit existing course content"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    course_content = get_object_or_404(CourseContent, content_id=content_id, course=course)
    is_supervisor = request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and course not in request.user.trainer.courses.all():
        messages.error(request, _('You do not have permission to edit content for this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        form = CourseContentForm(request.POST, request.FILES, instance=course_content)
        if form.is_valid():
            form.save()
            
            messages.success(request, _('Content updated successfully!'))
            return redirect('website:course_content_list', course_id=course.course_id)
        else:
            # Check specifically for file extension errors
            if 'file' in form.errors:
                for error in form.errors['file']:
                    # Check if the error is about unsupported file format
                    if 'Unsupported file format' in error:
                        messages.error(request, error)
                    else:
                        messages.error(request, error)
            else:
                messages.error(request, _('Please correct the errors below.'))
    else:
        form = CourseContentForm(instance=course_content)
    
    context = {
        'form': form,
        'course': course,
        'course_content': course_content,
        'title': _('Edit Course Content'),
        'submit_text': _('Update Content'),
    }
    return render(request, 'website/courses/course_content_form.html', context)

@trainer_required
def delete_course_content(request, course_id, content_id):
    """View to delete course content"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    course_content = get_object_or_404(CourseContent, content_id=content_id, course=course)
    is_supervisor = request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and course not in request.user.trainer.courses.all():
        messages.error(request, _('You do not have permission to delete content for this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        # Soft delete by setting is_active to False
        course_content.is_active = False
        course_content.save()
        
        messages.success(request, _('Content deleted successfully!'))
        return redirect('website:course_content_list', course_id=course.course_id)
    
    context = {
        'course': course,
        'course_content': course_content,
    }
    return render(request, 'website/courses/delete_course_content.html', context)

@login_required
def download_course_content(request, course_id, content_id):
    """View to download course content file"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    course_content = get_object_or_404(CourseContent, content_id=content_id, course=course, is_active=True)
    
    # Check if user is allowed to download the content
    # 1. User is a trainer who owns the course
    # 2. User is a trainer supervisor
    # 3. User is an admin (staff member)
    # 4. User has PAID for the course (status is UPCOMING, IN_PROGRESS, or COMPLETED)
    
    is_trainer = hasattr(request.user, 'trainer')
    is_supervisor = is_trainer and request.user.trainer.is_supervisor
    is_admin = request.user.is_staff
    can_access = False
    
    if is_trainer:
        if is_supervisor or course in request.user.trainer.courses.all():
            can_access = True
    elif is_admin:
        can_access = True
    else:
        # Check if user has PAID for the course
        has_paid = Reservation.objects.filter(
            user=request.user,
            course_instance__course=course,
            status__in=['IN_PROGRESS', 'COMPLETED']
        ).exists()
        
        if has_paid:
            can_access = True
    
    if not can_access:
        messages.error(request, _('You need to complete your payment to download course content.'))
        return redirect('website:course_detail', course_id=course.course_id)
    
    # Serve the file
    from django.http import FileResponse
    from django.utils.encoding import smart_str
    
    try:
        response = FileResponse(course_content.file)
        file_name = smart_str(course_content.file.name.split('/')[-1])
        response['Content-Disposition'] = f'attachment; filename="{file_name}"'
        return response
    except Exception as e:
        logger.error(f"Error downloading content: {e}", exc_info=True)
        messages.error(request, _('Error downloading content. Please try again later.'))
        return redirect('website:course_detail', course_id=course.course_id)

@login_required
def enroll_in_instance(request, instance_id):
    """Handle enrollment in a course instance"""
    # Verify user is an external individual
    if not request.user.user_type or request.user.user_type.code != 'EXTERNAL_INDIVIDUAL':
        messages.error(request, _('Only individual users can enroll in courses.'))
        return redirect('website:courses')
    
    try:
        # Get the course instance
        instance = CourseInstance.objects.get(instance_id=instance_id, is_active=True)
        
        # Check that this is an INDIVIDUAL course type
        if instance.course_type != 'INDIVIDUAL':
            messages.error(request, _('This course is available for individual enrollment.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)
        
        # Check if the course instance is published
        if not instance.published:
            messages.error(request, _('This course instance is not available for enrollment at this time.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)
        
        # Check if the course has already started
        now = timezone.now()
        
        if now > instance.start_date:
            messages.error(request, _('Enrollment for this course instance is closed because the course has already started.'))
            
            # Create a notification in the database
            Notification.objects.create(
                user=request.user,
                message=_('You cannot enroll in course: {} ({}). The course has already started.').format(
                    instance.course.name_en,
                    instance.start_date.strftime('%Y-%m-%d')
                ),
                delivered_at=timezone.now()
            )
            
            return redirect('website:course_detail', course_id=instance.course.course_id)
        
        # Check if user is already enrolled in this instance (excluding cancelled reservations)
        if Reservation.objects.filter(
            user=request.user, 
            course_instance=instance
        ).exclude(
            status='CANCELLED'
        ).exists():
            messages.warning(request, _('You are already enrolled in this course instance.'))
            
            # Create a notification in the database
            Notification.objects.create(
                user=request.user,
                message=_('You are already enrolled in course: {} ({}). You cannot enroll twice.').format(
                    instance.course.name_en,
                    instance.start_date.strftime('%Y-%m-%d')
                ),
                delivered_at=timezone.now()
            )
            
            return redirect('website:course_detail', course_id=instance.course.course_id)
        
        # Check if there are available seats
        if instance.available_seats <= 0:
            # Instead of error, offer to join waitlist
            if request.GET.get('waitlist') == 'yes':
                # User confirmed they want to join the waitlist
                with transaction.atomic():
                    # Check if this user previously had a cancelled reservation for this course
                    existing_cancelled = Reservation.objects.filter(
                        user=request.user,
                        course_instance=instance,
                        status='CANCELLED'
                    ).first()
                    
                    if existing_cancelled:
                        # Update existing cancelled reservation to WAITING_LIST status
                        existing_cancelled.status = 'WAITING_LIST'
                        existing_cancelled.save(update_fields=['status'])
                        reservation = existing_cancelled
                    else:
                        # Create a reservation with WAITING_LIST status
                        reservation = Reservation.objects.create(
                            user=request.user,
                            course_instance=instance,
                            status='WAITING_LIST'
                        )
                    
                    # Create a notification about joining the waitlist
                    Notification.objects.create(
                        user=request.user,
                        message=_('You have been added to the waiting list for course: {} ({}). We will notify you if a spot becomes available.').format(
                            instance.course.name_en,
                            instance.start_date.strftime('%Y-%m-%d')
                        ),
                        delivered_at=timezone.now()
                    )
                    
                    messages.success(request, _('You have been added to the waiting list for this course. We will notify you if a spot becomes available.'))
                    return redirect('website:course_detail', course_id=instance.course.course_id)
            else:
                # Ask user if they want to join waitlist
                messages.warning(request, _('This course instance is fully booked. Would you like to join the waiting list?'))
                
                return render(request, 'website/courses/waitlist_confirmation.html', {
                    'course_instance': instance,
                    'waitlist_url': request.path + '?waitlist=yes'
                })
        
        # Create a reservation for this course instance
        with transaction.atomic():
            # Always use WAITING_TO_PAY regardless of date
            status = 'WAITING_TO_PAY'
            
            # Check if this user previously had a cancelled reservation for this course
            existing_cancelled = Reservation.objects.filter(
                user=request.user,
                course_instance=instance,
                status='CANCELLED'
            ).first()
            
            if existing_cancelled:
                # Update the existing cancelled reservation instead of creating a new one
                existing_cancelled.status = status
                existing_cancelled.save(update_fields=['status'])
                reservation = existing_cancelled
            else:
                # Create a new reservation with the appropriate status
                reservation = Reservation.objects.create(
                    user=request.user,
                    course_instance=instance,
                    status=status
                )
            
            # Create a success notification
            Notification.objects.create(
                user=request.user,
                message=_('You have successfully enrolled in course: {} ({}). Please proceed to payment to secure your seat.').format(
                    instance.course.name_en,
                    instance.start_date.strftime('%Y-%m-%d')
                ),
                delivered_at=timezone.now()
            )
            
            messages.success(request, _('Successfully enrolled in the course! Please proceed to payment to secure your seat.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)
            
    except CourseInstance.DoesNotExist:
        messages.error(request, _('Course instance not found.'))
        return redirect('website:courses')
    except Exception as e:
        messages.error(request, _('An error occurred during enrollment. Please try again.'))
        logger.error(f"Error during enrollment: {e}", exc_info=True)
        return redirect('website:courses')

@login_required
def user_calendar_view(request):
    """
    Display calendar view for individual users showing only 
    sessions from course instances they've reserved
    """
    # Check if the user is an individual (not staff, trainer, etc.)
    if request.user.is_staff or hasattr(request.user, 'trainer'):
        # Redirect to the appropriate calendar view for staff or trainers
        if hasattr(request.user, 'trainer'):
            return redirect('website:trainer_calendar')
        return redirect('website:calendar')
    
    # Get all the user's reservations to find their course instances
    user_reservations = Reservation.objects.filter(
        user=request.user,
        course_instance__course_type='INDIVIDUAL'  # Only show INDIVIDUAL course types to individual users
    ).exclude(status='CANCELLED').select_related('course_instance')
    
    # Collect all course instances the user is enrolled in
    enrolled_instance_ids = [res.course_instance_id for res in user_reservations if res.course_instance_id]
    
    # Get all sessions belonging to these course instances
    user_sessions = Session.objects.filter(
        course_instance__instance_id__in=enrolled_instance_ids
    ).select_related('course', 'room').prefetch_related('trainers', 'trainers__user')
    
    context = {
        'is_user_calendar': True,
        'user_sessions': user_sessions,
        'enrolled_instance_ids': enrolled_instance_ids,
        'user_reservations': user_reservations,
    }
    
    return render(request, 'website/calendar/user_calendar.html', context)

@login_required
def user_calendar_events_api(request):
    """API endpoint to get only events for courses the user has enrolled in"""
    from django.http import JsonResponse
    import logging
    
    logger = logging.getLogger(__name__)
    logger.info("User Calendar API called for user: %s", request.user.username)
    
    try:
        # Initialize empty result array
        events = []
        
        # Add holidays first
        try:
            holidays_query = Holiday.objects.filter(
                Q(is_active=True) | Q(is_active__isnull=True)
            )
            
            # Get date range from request if provided
            start_param = request.GET.get('start')
            end_param = request.GET.get('end')
            
            if start_param and end_param:
                try:
                    from datetime import datetime
                    start_date = datetime.fromisoformat(start_param.split('T')[0])
                    end_date = datetime.fromisoformat(end_param.split('T')[0])
                    holidays_query = holidays_query.filter(date__gte=start_date, date__lte=end_date)
                except ValueError:
                    logger.warning("Invalid start/end parameters for holiday fetching.")
            
            logger.info(f"Including {holidays_query.count()} holidays in user calendar events")
            
            for holiday in holidays_query:
                # Format the date to ISO format  
                holiday_date_str = holiday.date.strftime('%Y-%m-%d')
                
                # Add holiday as an all-day event
                events.append({
                    'id': f"holiday-{holiday.holiday_id}",
                    'title': f"🎉 {holiday.name}",
                    'start': holiday_date_str,
                    'end': holiday_date_str,
                    'allDay': True,
                    'type': 'holiday',
                    'description': holiday.description,
                    'color': 'red-500',  # Red color for holidays
                    'textColor': 'white',
                    'borderColor': 'red-700'
                })
        except Exception as e:
            logger.error(f"Error fetching holidays: {e}", exc_info=True)
        
        # Get all the user's reservations to find their course instances
        # Filter out CANCELLED reservations
        user_reservations = Reservation.objects.filter(
            user=request.user,
            course_instance__course_type='INDIVIDUAL'  # Only show INDIVIDUAL course types to individual users
        ).exclude(status='CANCELLED').select_related('course_instance')
        
        # Collect all course instances the user is enrolled in
        enrolled_instance_ids = [res.course_instance_id for res in user_reservations if res.course_instance_id]
        
        if not enrolled_instance_ids:
            logger.info("User has no course reservations")
            return JsonResponse({
                'events': events,
                'message': 'You have not enrolled in any courses yet'
            })
        
        # Get all sessions belonging to these course instances
        user_sessions = Session.objects.filter(
            course_instance__in=enrolled_instance_ids
        ).select_related('course', 'room').prefetch_related('trainers', 'trainers__user')
        
        logger.info(f"Found {user_sessions.count()} sessions for user's enrolled courses")
        
        # Add sessions to events array
        for session in user_sessions:
            try:
                if session.start_date and session.end_date:
                    start_iso = session.start_date.isoformat()
                    end_iso = session.end_date.isoformat()
                    
                    course_name = session.course.name_en if session.course else 'Session'
                    
                    # Get trainer name from the first trainer (if any)
                    trainer_name = 'No Trainer'
                    if session.trainers.exists():
                        trainer = session.trainers.first()
                        if trainer and trainer.user:
                            trainer_name = trainer.user.get_full_name() or trainer.user.username
                    
                    room_name = session.room.name if session.room else 'No Room'
                    
                    title = f"{course_name} - {trainer_name} - {room_name}"
                    
                    events.append({
                        'id': session.session_id,
                        'title': title,
                        'start': start_iso,
                        'end': end_iso,
                        'type': 'session',
                        'resourceId': session.room.room_id if session.room else None,
                        'trainerId': session.trainers.first().trainer_id if session.trainers.exists() else None,
                        'is_active': session.is_active,
                        'color': 'green-600'
                    })
                    logger.info(f"Added session: {title} from {start_iso} to {end_iso}")
                else:
                    logger.warning(f"Session {session.session_id} missing dates")
            except Exception as e:
                logger.error(f"Error processing session {session.session_id}: {e}")
                continue
        
        # Return found events
        return JsonResponse({
            'events': events,
            'total_sessions': len([e for e in events if e.get('type') == 'session']),
            'message': f'Found {len([e for e in events if e.get("type") == "session"])} sessions for your enrolled courses'
        })
        
    except Exception as e:
        # Handle any unexpected errors
        logger.error(f"User Calendar API error: {str(e)}", exc_info=True)
        return JsonResponse({'events': [], 'error': str(e)}, status=200)

def auto_cancel_expired_reservations(user=None):
    """
    Automatically cancel reservations with status 'WAITING_TO_PAY' or 'WAITING_LIST'
    when the course has already started.
    If user is provided, only check that user's reservations, otherwise check all.
    """
    now = timezone.now()
    logger.info(f"Running auto cancellation check at {now}")
    
    # Build the query to find reservations that need to be cancelled
    query = Q(status__in=['WAITING_TO_PAY', 'WAITING_LIST'])
    
    # If user is provided, only check that user's reservations
    if user:
        query &= Q(user=user)
    
    # Get all relevant reservations
    reservations = Reservation.objects.filter(query).select_related('course_instance', 'course_instance__course')
    
    cancelled_count = 0
    for reservation in reservations:
        course_instance = reservation.course_instance
        
        # Skip if there's no course instance
        if not course_instance:
            continue
        
        # If the current time is after the course start date, cancel the reservation
        if now > course_instance.start_date:
            # Update the reservation status
            previous_status = reservation.status
            reservation.status = 'CANCELLED'
            reservation.save(update_fields=['status'])
            cancelled_count += 1
            
            # Create a notification
            Notification.objects.create(
                user=reservation.user,
                message=_('Your reservation for course: {} ({}) has been automatically cancelled as the course has already started.').format(
                    course_instance.course.name_en,
                    course_instance.start_date.strftime('%Y-%m-%d')
                ),
                delivered_at=timezone.now()
            )
            
            logger.info(f"Auto-cancelled reservation {reservation.reservation_id} (previous status: {previous_status}) for user {reservation.user.id} as course has already started")
    
    return cancelled_count

@login_required
@require_http_methods(["GET"])
def user_attendance_api(request):
    """
    API endpoint to get attendance data for a user's course instance
    """
    try:
        course_instance_id = request.GET.get('course_instance_id')
        
        if not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance ID is required')
            })
        
        try:
            course_instance = CourseInstance.objects.get(
                instance_id=course_instance_id
            )
            
            # Get all sessions for this course instance
            sessions = list(course_instance.sessions.filter(is_active=True).order_by('start_date'))
            
            # Get attendance records for this user and these sessions
            attendance_records = {}
            for attendance in Attendance.objects.filter(user=request.user, session__in=sessions):
                attendance_records[str(attendance.session_id)] = {
                    'first_half': attendance.first_half,
                    'second_half': attendance.second_half
                }
            
            sessions_data = []
            now = timezone.now()
            
            for session in sessions:
                # Determine session status
                if now < session.start_date:
                    status = 'upcoming'
                elif now > session.end_date:
                    status = 'completed'
                else:
                    status = 'in_progress'
                
                # Get attendance for this session
                attendance = attendance_records.get(str(session.session_id), {'first_half': False, 'second_half': False})
                attended = attendance['first_half'] or attendance['second_half']
                
                # Format session data
                room_name = session.room.name if session.room else _('Online')
                
                sessions_data.append({
                    'session_id': str(session.session_id),
                    'date': session.start_date.strftime('%b %d, %Y'),
                    'time': f"{session.start_date.strftime('%H:%M')} - {session.end_date.strftime('%H:%M')}",
                    'room': room_name,
                    'attended': attended,
                    'first_half': attendance['first_half'],
                    'second_half': attendance['second_half'],
                    'status': status
                })
            
            return JsonResponse({
                'status': 'success',
                'course_name': course_instance.course.name_en,
                'sessions': sessions_data
            })
            
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance not found')
            })
            
    except Exception as e:
        logger.error(f"Error in user_attendance_api: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching attendance data')
        })

@login_required
@staff_member_required
@require_http_methods(["GET"])
def attendance_api(request):
    """
    API endpoint to get attendance data for any user's course instance - admin only
    """
    try:
        course_instance_id = request.GET.get('course_instance_id')
        user_id = request.GET.get('user_id')
        
        if not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance ID is required')
            })
        
        if not user_id:
            return JsonResponse({
                'status': 'error',
                'message': _('User ID is required')
            })
        
        try:
            # Get the course instance
            course_instance = CourseInstance.objects.get(
                instance_id=course_instance_id
            )
            
            # Get the user
            user = CustomUser.objects.get(id=user_id)
            
            # Get all sessions for this course instance
            sessions = list(course_instance.sessions.filter(is_active=True).order_by('start_date'))
            
            # Get attendance records for this user and these sessions
            attendance_records = {}
            try:
                from .models import Attendance
                for attendance in Attendance.objects.filter(user=user, session__in=sessions):
                    attendance_records[str(attendance.session_id)] = {
                        'first_half': attendance.first_half,
                        'second_half': attendance.second_half
                    }
            except Exception as attendance_error:
                logger.warning(f"Error fetching attendance records: {str(attendance_error)}")
                # Continue with empty records if there was an error
            
            sessions_data = []
            now = timezone.now()
            
            for session in sessions:
                # Determine session status
                if now < session.start_date:
                    status = 'upcoming'
                elif now > session.end_date:
                    status = 'completed'
                else:
                    status = 'in_progress'
                
                # Get attendance for this session
                attendance = attendance_records.get(str(session.session_id), {'first_half': False, 'second_half': False})
                
                # Format session data
                room_name = session.room.name if session.room else _('Online')
                
                sessions_data.append({
                    'session_id': str(session.session_id),
                    'date': session.start_date.strftime('%b %d, %Y'),
                    'time': f"{session.start_date.strftime('%H:%M')} - {session.end_date.strftime('%H:%M')}",
                    'room': room_name,
                    'status': status,
                    'attendance': attendance
                })
            
            return JsonResponse({
                'status': 'success',
                'course_name': course_instance.course.name_en,
                'user_name': f"{user.first_name} {user.last_name}",
                'sessions': sessions_data
            })
            
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance not found')
            })
        except CustomUser.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('User not found')
            })
            
    except Exception as e:
        logger.error(f"Error in attendance_api: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching attendance data')
        })

@login_required
@staff_member_required
def admin_reservations_view(request):
    """
    Admin view to display and manage all user reservations
    """
    # Get filter parameters from request
    course_id = request.GET.get('course_id')
    instance_id = request.GET.get('instance_id')
    user_id = request.GET.get('user_id')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    status = request.GET.get('status')
    
    # Start with all reservations
    all_reservations = Reservation.objects.all().select_related(
        'user',
        'course_instance', 
        'course_instance__course'
    )
    
    # Apply filters
    if course_id:
        all_reservations = all_reservations.filter(course_instance__course__course_id=course_id)
    
    if instance_id:
        all_reservations = all_reservations.filter(course_instance__instance_id=instance_id)
    
    if user_id:
        all_reservations = all_reservations.filter(user__id=user_id)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            all_reservations = all_reservations.filter(course_instance__start_date__gte=start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            all_reservations = all_reservations.filter(course_instance__end_date__lte=end_date_obj)
        except ValueError:
            pass
    
    if status:
        all_reservations = all_reservations.filter(status=status)
    
    # Final ordering
    all_reservations = all_reservations.order_by('course_instance__start_date')
    
    # Get available courses and users for filter dropdowns
    courses = Course.objects.filter(is_active=True).order_by('name_en')
    users = CustomUser.objects.filter(is_active=True).order_by('username')
    
    # Get course instances for filter dropdown
    course_instances = CourseInstance.objects.filter(is_active=True).select_related('course').order_by('-start_date')
    
    now = timezone.now()
    
    # Try to update reservation statuses based on current time
    try:
        for reservation in all_reservations:
            # Skip if the status is already CANCELLED, WAITING_LIST, or WAITING_TO_PAY
            if hasattr(reservation, 'status') and reservation.status in ['CANCELLED', 'WAITING_LIST', 'WAITING_TO_PAY']:
                continue
                
            # Determine the correct status based on dates
            if reservation.course_instance.start_date > now:
                new_status = 'UPCOMING'
            elif reservation.course_instance.end_date < now:
                new_status = 'COMPLETED'
            else:
                new_status = 'IN_PROGRESS'
                
            # Update if status has changed and the field exists
            if hasattr(reservation, 'status') and reservation.status != new_status:
                reservation.status = new_status
                reservation.save(update_fields=['status'])
    except Exception as e:
        # Log the error but continue - field might not exist yet
        logger.error(f"Error updating reservation statuses: {e}", exc_info=True)
    
    context = {
        'all_reservations': all_reservations,
        'now': now,  # Add current time for template
        'courses': courses,
        'course_instances': course_instances,
        'users': users,
        'filters': {
            'course_id': course_id,
            'instance_id': instance_id,
            'user_id': user_id,
            'start_date': start_date,
            'end_date': end_date,
            'status': status
        }
    }
    return render(request, 'website/reservations/admin_reservations.html', context)

@login_required
@staff_member_required
def update_reservation(request, reservation_id):
    """
    View function for updating a reservation's status by an admin
    """
    # Get the reservation object or return 404
    reservation = get_object_or_404(Reservation, reservation_id=reservation_id)
    
    if request.method == 'POST':
        # Get the new status from the form
        new_status = request.POST.get('status')
        
        # Validate the status is one of the valid choices
        valid_statuses = dict(Reservation.STATUS_CHOICES).keys()
        if new_status in valid_statuses:
            # Update the status
            reservation.status = new_status
            reservation.save()
            
            # Add a success message
            messages.success(request, _('Reservation status updated successfully.'))
        else:
            messages.error(request, _('Invalid status provided.'))
            
    # Redirect back to the admin reservations page
    return redirect('website:admin_reservations')

@login_required
@staff_member_required
def admin_update_attendance(request):
    """
    View function for Super admin user to update attendance records for any user
    """
    if request.user.user_type.code != "SUPER_ADMIN":
        messages.error(request, _('You are not authorized to update attendance records.'))
        return redirect('website:admin_reservations')
    
    if request.method == 'POST':
        # Get the course instance and user IDs from the form
        course_instance_id = request.POST.get('course_instance_id')
        user_id = request.POST.get('user_id')
        
        # Debug logging
        logger.debug(f"Admin attendance update request received: {request.POST}")
        logger.debug(f"Course instance ID: {course_instance_id}, User ID: {user_id}")
        
        # Validate required parameters
        if not course_instance_id or not user_id:
            error_msg = _('Course instance and user IDs are required.')
            logger.error(f"Attendance update failed: {error_msg}")
            messages.error(request, error_msg)
            return JsonResponse({'status': 'error', 'message': error_msg}) if request.headers.get('X-Requested-With') == 'XMLHttpRequest' else redirect('website:admin_reservations')
        
        try:
            # Get the user and course instance
            user = CustomUser.objects.get(id=user_id)
            course_instance = CourseInstance.objects.get(instance_id=course_instance_id)
            
            # Log the found user and course instance
            logger.debug(f"Found user: {user.username} (ID: {user.id})")
            logger.debug(f"Found course instance: {course_instance.course.name_en} (ID: {course_instance.instance_id})")
            
            # Process each session attendance
            sessions = course_instance.sessions.all()
            logger.debug(f"Found {sessions.count()} sessions for course instance")
            
            updated_count = 0
            session_ids = []
            
            # Log all POST keys for debugging
            for key in request.POST.keys():
                if key.startswith('session_id_'):
                    session_ids.append(key.replace('session_id_', ''))
            
            logger.debug(f"Session IDs in POST data: {session_ids}")
            
            for session in sessions:
                session_id_str = str(session.session_id)
                logger.debug(f"Processing session: {session_id_str} (Date: {session.start_date})")
                
                # Check if session ID is in the form
                if f'session_id_{session_id_str}' not in request.POST:
                    logger.debug(f"Session ID {session_id_str} not found in POST data, skipping")
                    continue
                
                # Get attendance status for first and second half
                first_half = f'first_half_{session_id_str}' in request.POST
                second_half = f'second_half_{session_id_str}' in request.POST
                
                logger.debug(f"Session {session_id_str}: first_half={first_half}, second_half={second_half}")
                
                try:
                    # Try to get the existing attendance record
                    attendance, created = Attendance.objects.get_or_create(
                        session=session,
                        user=user,
                        defaults={
                            'first_half': first_half,
                            'second_half': second_half
                        }
                    )
                    
                    # If not created, update the existing record
                    if not created:
                        attendance.first_half = first_half
                        attendance.second_half = second_half
                        attendance.save()
                    
                    logger.debug(f"Attendance {'created' if created else 'updated'} for session {session_id_str}")
                    updated_count += 1
                except Exception as e:
                    logger.error(f"Error updating attendance for session {session_id_str}: {str(e)}")
                    messages.error(request, _(f"Error updating attendance for session on {session.start_date.strftime('%b %d, %Y')}"))
            
            # Add success message
            if updated_count > 0:
                success_msg = _(f'Attendance updated for {updated_count} sessions.')
                messages.success(request, success_msg)
                logger.debug(success_msg)
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'status': 'success', 'message': success_msg, 'updated_count': updated_count})
            else:
                warning_msg = _('No attendance records were updated.')
                messages.warning(request, warning_msg)
                logger.warning(warning_msg)
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return JsonResponse({'status': 'warning', 'message': warning_msg})
                
        except CustomUser.DoesNotExist:
            error_msg = _(f'User with ID {user_id} not found.')
            logger.error(error_msg)
            messages.error(request, error_msg)
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'message': error_msg})
            
        except CourseInstance.DoesNotExist:
            error_msg = _(f'Course instance with ID {course_instance_id} not found.')
            logger.error(error_msg)
            messages.error(request, error_msg)
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'message': error_msg})
            
        except Exception as e:
            error_msg = _(f'An error occurred while updating attendance: {str(e)}')
            logger.error(f"Unexpected error in admin_update_attendance: {str(e)}", exc_info=True)
            messages.error(request, error_msg)
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return JsonResponse({'status': 'error', 'message': error_msg})
    
    # Redirect back to the reservations page
    return redirect('website:admin_reservations')

@login_required
@staff_member_required
@require_http_methods(["GET"])
def course_instance_details(request, instance_id):
    """
    API endpoint to get details about a course instance including available seats and waiting list
    """
    # Get the course instance or return 404
    course_instance = get_object_or_404(CourseInstance, instance_id=instance_id)
    
    # Get the necessary information
    capacity = course_instance.capacity
    available_seats = course_instance.available_seats
    waiting_list_count = course_instance.waiting_list_count
    
    # Return as JSON
    data = {
        'capacity': capacity,
        'available_seats': available_seats,
        'waiting_list_count': waiting_list_count,
        'instance_id': instance_id
    }
    
    return JsonResponse(data)

def is_weekend(date):
    """Check if a date falls on a weekend (Friday or Saturday)"""
    # Get the weekday (0=Monday, 1=Tuesday, ... 6=Sunday)
    weekday = date.weekday()
    # In this system, Friday(4) and Saturday(5) are considered weekend days
    return weekday == 4 or weekday == 5

def is_holiday(date):
    """Check if a date is a holiday in the system"""
    return Holiday.objects.filter(date=date, is_active=True).exists()

def calculate_business_days_deadline(start_date, business_days):
    """
    Calculate the deadline date that is N business days before the start_date.
    Business days exclude weekends (Friday and Saturday) and holidays.
    
    Args:
        start_date: The reference date (course start date)
        business_days: Number of business days before the start_date
    
    Returns:
        datetime: The deadline datetime
    """
    # Start with the initial date
    current_date = start_date.date()
    days_counted = 0
    
    while days_counted < business_days:
        # Move one day back
        current_date = current_date - timezone.timedelta(days=1)
        
        # Skip if it's a weekend
        if is_weekend(current_date):
            continue
            
        # Skip if it's a holiday
        if is_holiday(current_date):
            continue
            
        # If we get here, it's a business day
        days_counted += 1
    
    # Create a datetime at the same time as the start_date but on the deadline date
    deadline_time = timezone.datetime.combine(
        current_date, 
        start_date.time(),
        tzinfo=start_date.tzinfo
    )
    
    return deadline_time

@login_required
@require_http_methods(["GET"])
def get_cancellation_deadline(request):
    """
    API endpoint to get the remaining time until cancellation deadline
    Returns remaining time in hours and minutes, or zero if deadline has passed
    """
    reservation_id = request.GET.get('reservation_id')
    if not reservation_id:
        return JsonResponse({
            'status': 'error',
            'message': _('Reservation ID is required')
        }, status=400)
    
    try:
        # Ensure the user can only get their own reservation deadlines
        reservation = Reservation.objects.get(
            reservation_id=reservation_id,
            user=request.user
        )
        
        course_instance = reservation.course_instance
        now = timezone.now()
        
        # Get the business days for cancellation (default to 3 if not specified)
        business_days = getattr(course_instance, 'cancellation_deadline_business_days', 3)
        
        # Calculate the deadline based on business days
        deadline_time = calculate_business_days_deadline(course_instance.start_date, business_days)
        
        # Calculate remaining time until deadline
        if now >= deadline_time:
            # Deadline has passed
            hours_remaining = 0
            minutes_remaining = 0
            can_cancel = False
        else:
            # Calculate time difference
            time_diff = deadline_time - now
            total_seconds = time_diff.total_seconds()
            hours_remaining = int(total_seconds // 3600)
            minutes_remaining = int((total_seconds % 3600) // 60)
            can_cancel = True
        
        # Check if there's already an emergency cancellation request for this reservation
        has_emergency_request = EmergencyCancellationRequest.objects.filter(
            reservation=reservation
        ).exists()
        
        return JsonResponse({
            'status': 'success',
            'can_cancel': can_cancel,
            'hours_remaining': hours_remaining,
            'minutes_remaining': minutes_remaining,
            'cancellation_deadline_business_days': business_days,
            'has_emergency_request': has_emergency_request
        })
        
    except Reservation.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('Reservation not found or you do not have permission to view it')
        }, status=404)
    except Exception as e:
        logger.error(f"Error calculating cancellation deadline: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
def emergency_cancellation_request(request):
    """
    Handle emergency cancellation requests when the regular cancellation deadline has passed
    but before the course starts
    """
    logger = logging.getLogger(__name__)
    
    reservation_id = request.POST.get('reservation_id')
    reason = request.POST.get('reason')
    
    if not reservation_id or not reason:
        messages.error(request, _('Reservation ID and reason are required.'))
        return redirect('website:user_reservations')
    
    try:
        # Ensure the user can only cancel their own reservations
        reservation = Reservation.objects.get(
            reservation_id=reservation_id,
            user=request.user
        )
        
        course_instance = reservation.course_instance
        now = timezone.now()
        
        # Check if course has already started
        if course_instance.start_date <= now:
            messages.error(
                request,
                _('Cannot submit an emergency cancellation request for a course that has already started.')
            )
            return redirect('website:user_reservations')
        
        # Check if there's already a pending request for this reservation
        existing_request = EmergencyCancellationRequest.objects.filter(
            reservation=reservation,
            status='PENDING'
        ).exists()
        
        if existing_request:
            messages.warning(
                request,
                _('You already have a pending emergency cancellation request for this reservation.')
            )
            return redirect('website:user_reservations')
        
        # Create the emergency cancellation request
        cancellation_request = EmergencyCancellationRequest(
            reservation=reservation,
            reason=reason
        )
        
        # Handle file attachment if provided
        attachment = request.FILES.get('attachment')
        if attachment:
            cancellation_request.attachment = attachment
        
        cancellation_request.save()
        
        # Create a notification for the user
        Notification.objects.create(
            user=request.user,
            message=_('Your emergency cancellation request for {} has been submitted and is pending review.').format(
                course_instance.course.name_en
            ),
            delivered_at=timezone.now()
        )
        
        # Log the emergency cancellation request
        logger.info(f"Emergency cancellation request submitted by user {request.user.username} for reservation {reservation_id}")
        
        messages.success(
            request,
            _('Your emergency cancellation request has been submitted and is pending review by administration.')
        )
        
    except Reservation.DoesNotExist:
        messages.error(request, _('Reservation not found.'))
        logger.warning(f"User {request.user.username} attempted to submit emergency cancellation for non-existent reservation {reservation_id}")
    except Exception as e:
        messages.error(request, _('An error occurred. Please try again.'))
        logger.error(f"Error processing emergency cancellation request: {e}", exc_info=True)
    
    return redirect('website:user_reservations')

@staff_member_required
@require_http_methods(["GET", "POST"])
def admin_cancellation_requests(request):
    """
    Admin view to manage emergency cancellation requests, with filtering options
    """
    if request.method == "POST":
        request_id = request.POST.get('request_id')
        action = request.POST.get('action')  # 'approve' or 'reject'
        admin_notes = request.POST.get('admin_notes', '')
        
        if not request_id or not action:
            messages.error(request, _('Request ID and action are required.'))
            return redirect('website:admin_cancellation_requests')
        
        try:
            cancellation_request = EmergencyCancellationRequest.objects.get(request_id=request_id)
            
            # Update the request status based on the action
            if action == 'approve':
                cancellation_request.status = 'APPROVED'
                
                # Update the reservation status to CANCELLED
                reservation = cancellation_request.reservation
                reservation.status = 'CANCELLED'
                reservation.save()
                
                # Get the course instance
                course_instance = reservation.course_instance
                
                # Log the action
                logger.info(f"Emergency cancellation approved for reservation {reservation.reservation_id}, course instance {course_instance.instance_id}")
                
                # Move all users on the waiting list to waiting to pay
                waiting_list_reservations = Reservation.objects.filter(
                    course_instance=course_instance,
                    status='WAITING_LIST'
                )
                
                logger.info(f"Moving {waiting_list_reservations.count()} users from waiting list to WAITING_TO_PAY for course instance {course_instance.instance_id}")
                
                # Update all waiting list reservations
                for res in waiting_list_reservations:
                    res.status = 'WAITING_TO_PAY'
                    res.save(update_fields=['status'])
                    
                    # Notify the user
                    Notification.objects.create(
                        user=res.user,
                        message=_('A spot has become available in the course: {} ({}). You and others from the waiting list can now pay to secure a spot. Available seats will be assigned to whoever pays first.').format(
                            course_instance.course.name_en,
                            course_instance.start_date.strftime('%Y-%m-%d')
                        ),
                        delivered_at=timezone.now()
                    )
                
                # Notify the user who requested the cancellation
                Notification.objects.create(
                    user=reservation.user,
                    message=_('Your emergency cancellation request for {} has been approved.').format(
                        reservation.course_instance.course.name_en
                    ),
                    delivered_at=timezone.now()
                )
                
                messages.success(request, _('Cancellation request approved successfully.'))
                
            elif action == 'reject':
                cancellation_request.status = 'REJECTED'
                
                # Notify the user
                Notification.objects.create(
                    user=cancellation_request.reservation.user,
                    message=_('Your emergency cancellation request for {} has been rejected. Please contact administration for more information.').format(
                        cancellation_request.reservation.course_instance.course.name_en
                    ),
                    delivered_at=timezone.now()
                )
                
                messages.success(request, _('Cancellation request rejected.'))
            
            # Update admin notes and processed_by
            cancellation_request.admin_notes = admin_notes
            cancellation_request.processed_by = request.user
            cancellation_request.save()
            
        except EmergencyCancellationRequest.DoesNotExist:
            messages.error(request, _('Cancellation request not found.'))
        except Exception as e:
            messages.error(request, _('An error occurred while processing the request.'))
            logger.error(f"Error processing admin action on cancellation request: {e}", exc_info=True)
    
    # GET request handling for filtering
    user_type_filter = request.GET.get('user_type', '')
    user_id_filter = request.GET.get('user_id', '')
    
    # Base querysets
    pending_requests_qs = EmergencyCancellationRequest.objects.filter(status='PENDING').select_related(
        'reservation', 'reservation__user', 'reservation__course_instance', 'reservation__course_instance__course'
    ).order_by('-created_at')
    
    processed_requests_qs = EmergencyCancellationRequest.objects.exclude(status='PENDING').select_related(
        'reservation', 'reservation__user', 'reservation__course_instance', 'reservation__course_instance__course',
        'processed_by'
    ).order_by('-updated_at')
    
    # Apply filters
    if user_type_filter:
        pending_requests_qs = pending_requests_qs.filter(reservation__user__user_type__code=user_type_filter)
        processed_requests_qs = processed_requests_qs.filter(reservation__user__user_type__code=user_type_filter)
        
    if user_id_filter:
        pending_requests_qs = pending_requests_qs.filter(reservation__user__id=user_id_filter)
        processed_requests_qs = processed_requests_qs.filter(reservation__user__id=user_id_filter)
        
    # Fetch data for filter dropdowns
    user_types = UserType.objects.filter(is_active=True).order_by('name')
    all_users = CustomUser.objects.filter(is_active=True).order_by('first_name', 'last_name')
    
    context = {
        'pending_requests': pending_requests_qs,
        'processed_requests': processed_requests_qs[:50],  # Limit processed requests
        'user_types': user_types,
        'all_users': all_users,
        'selected_user_type': user_type_filter,
        'selected_user_id': user_id_filter,
    }
    
    return render(request, 'website/reservations/admin_cancellation_requests.html', context)

@staff_member_required
@require_http_methods(["GET"])
def emergency_cancellation_api(request):
    """API endpoint to get pending emergency cancellation requests"""
    try:
        # Get pending requests
        pending_requests = EmergencyCancellationRequest.objects.filter(status='PENDING').select_related(
            'reservation', 'reservation__user', 'reservation__course_instance', 'reservation__course_instance__course'
        ).order_by('-created_at')
        
        # Format response data
        data = []
        for req in pending_requests:
            data.append({
                'request_id': req.request_id,
                'user': {
                    'id': req.reservation.user.id,
                    'username': req.reservation.user.username,
                    'full_name': req.reservation.user.get_full_name(),
                    'email': req.reservation.user.email
                },
                'course': {
                    'id': req.reservation.course_instance.course.course_id,
                    'name': req.reservation.course_instance.course.name_en,
                    'category': req.reservation.course_instance.course.get_category_display()
                },
                'course_instance': {
                    'id': req.reservation.course_instance.instance_id,
                    'start_date': req.reservation.course_instance.start_date.strftime('%Y-%m-%dT%H:%M:%S'),
                    'end_date': req.reservation.course_instance.end_date.strftime('%Y-%m-%dT%H:%M:%S')
                },
                'reason': req.reason,
                'has_attachment': bool(req.attachment),
                'attachment_url': req.attachment.url if req.attachment else None,
                'created_at': req.created_at.strftime('%Y-%m-%dT%H:%M:%S')
            })
        
        return JsonResponse({
            'status': 'success',
            'count': len(data),
            'requests': data
        })
        
    except Exception as e:
        logger.error(f"Error in emergency_cancellation_api: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["GET", "POST"])
def check_before_payment(request, reservation_id):
    """
    Check if seats are still available before proceeding to payment.
    If no seats are available, present options to join waiting list or cancel.
    """
    try:
        # Get the reservation
        reservation = get_object_or_404(
            Reservation, 
            reservation_id=reservation_id,
            user=request.user,
            status='WAITING_TO_PAY'
        )
        
        course_instance = reservation.course_instance
        
        # Check if seats are still available
        if course_instance.available_seats <= 0:
            # No seats available, render template with options to join waiting list or cancel
            return render(request, 'website/reservations/join_waiting_list.html', {
                'reservation': reservation,
                'course_instance': course_instance
            })
        
        # Seats are available, process the payment directly
        if request.method == "POST":
            # Determine the new status based on the current date
            now = timezone.now()
            
            if course_instance.start_date > now:
                new_status = 'UPCOMING'
            elif course_instance.end_date < now:
                new_status = 'COMPLETED'
            else:
                new_status = 'IN_PROGRESS'
            
            # Update the reservation status
            reservation.status = new_status
            reservation.save(update_fields=['status'])
            
            # Create a payment record
            payment = Payment.objects.create(
                user=request.user,
                amount=0.00,  # Set the amount based on your requirements
                payment_method='CREDIT_CARD',  # Set default or get from form
                status='COMPLETED',
                payment_source='INDIVIDUAL'
            )
            
            # Create a notification
            Notification.objects.create(
                user=request.user,
                message=_('Your payment for course: {} ({}) has been confirmed. Your seat is now secured.').format(
                    course_instance.course.name_en,
                    course_instance.start_date.strftime('%Y-%m-%d')
                ),
                delivered_at=timezone.now()
            )
            
            # Users stay in WAITING_TO_PAY status even if no seats are available
            # This allows users who were on the waiting list to continue trying to pay
            # even after all current seats are filled
            
            messages.success(request, _('Payment successful! Your seat in the course is now secured.'))
            return redirect('website:user_reservations')
        else:
            # For GET requests, redirect to user_reservations with a message
            messages.info(request, _('Please use the Pay Now button to complete your payment.'))
            return redirect('website:user_reservations')
        
    except Reservation.DoesNotExist:
        messages.error(request, _('Reservation not found or already paid.'))
        return redirect('website:user_reservations')
    except Exception as e:
        logger.error(f"Error checking reservation: {e}", exc_info=True)
        messages.error(request, _('An error occurred. Please try again or contact support.'))
        return redirect('website:user_reservations')

@require_http_methods(["POST"])
def confirm_payment(request, reservation_id):
    """
    Handle confirmation of payment for a reservation.
    This changes the status from WAITING_TO_PAY to UPCOMING or IN_PROGRESS
    depending on the current date.
    """
    # This function handles the payment confirmation process
    try:
        # Get the reservation
        reservation = get_object_or_404(
            Reservation, 
            reservation_id=reservation_id,
            user=request.user,
            status='WAITING_TO_PAY'
        )
        
        course_instance = reservation.course_instance
        
        # Double-check available seats at the time of payment
        if course_instance.available_seats <= 0:
            # Handle according to user's choice
            if request.POST.get('join_waiting_list') == 'yes':
                # Move to waiting list
                reservation.status = 'WAITING_LIST'
                reservation.save(update_fields=['status'])
                
                # Create a notification
                Notification.objects.create(
                    user=request.user,
                    message=_('You have been added to the waiting list for course: {} ({}). We will notify you if a spot becomes available.').format(
                        course_instance.course.name_en,
                        course_instance.start_date.strftime('%Y-%m-%d')
                    ),
                    delivered_at=timezone.now()
                )
                
                messages.success(request, _('You have been added to the waiting list. We will notify you if a spot becomes available.'))
                return redirect('website:user_reservations')
            else:
                # User chose to cancel
                reservation.status = 'CANCELLED'
                reservation.save(update_fields=['status'])
                
                # Create a notification
                Notification.objects.create(
                    user=request.user,
                    message=_('Your reservation for course: {} ({}) has been cancelled.').format(
                        course_instance.course.name_en,
                        course_instance.start_date.strftime('%Y-%m-%d')
                    ),
                    delivered_at=timezone.now()
                )
                
                messages.info(request, _('Your reservation has been cancelled.'))
                return redirect('website:user_reservations')
        
        # Determine the new status based on the current date
        now = timezone.now()
        
        if course_instance.start_date > now:
            new_status = 'UPCOMING'
        elif course_instance.end_date < now:
            new_status = 'COMPLETED'
        else:
            new_status = 'IN_PROGRESS'
        
        # Update the reservation status
        reservation.status = new_status
        reservation.save(update_fields=['status'])
        
        # Create a payment record
        payment = Payment.objects.create(
            user=request.user,
            amount=0.00,  # Set the amount based on your requirements
            payment_method='CREDIT_CARD',  # Set default or get from form
            status='COMPLETED',
            payment_source='INDIVIDUAL'
        )
        
        # Create a notification
        Notification.objects.create(
            user=request.user,
            message=_('Your payment for course: {} ({}) has been confirmed. Your seat is now secured.').format(
                course_instance.course.name_en,
                course_instance.start_date.strftime('%Y-%m-%d')
            ),
            delivered_at=timezone.now()
        )
        
        # Users stay in WAITING_TO_PAY status even if no seats are available
        # This allows all users who were on the waiting list to continue trying to pay
        # even after all current seats are filled
        
        messages.success(request, _('Payment successful! Your seat in the course is now secured.'))
        return redirect('website:user_reservations')
        
    except Reservation.DoesNotExist:
        messages.error(request, _('Reservation not found or already paid.'))
        return redirect('website:user_reservations')
    except Exception as e:
        logger.error(f"Error confirming payment: {e}", exc_info=True)
        messages.error(request, _('An error occurred during payment. Please try again or contact support.'))
        return redirect('website:user_reservations')

@login_required
@staff_member_required
@require_http_methods(["POST", "PUT", "DELETE"])
def add_holiday(request, holiday_id=None):
    """Create, update, or delete a holiday"""
    from django.http import JsonResponse
    import json
    import logging
    from datetime import datetime, time
    
    logger = logging.getLogger(__name__)
    
    try:
        # DELETE request - remove holiday
        if request.method == "DELETE" and holiday_id:
            try:
                holiday = Holiday.objects.get(holiday_id=holiday_id)
                holiday_name = holiday.name
                holiday.delete()
                return JsonResponse({
                    'success': True,
                    'message': f'Holiday "{holiday_name}" has been deleted'
                })
            except Holiday.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': 'Holiday not found'
                }, status=404)
                
        # Parse request data for POST and PUT
        if request.method in ["POST", "PUT"]:
            # Log request details for debugging
            logger.info(f"Request Content-Type: {request.content_type}")
            if request.body:
                logger.info(f"Request body: {request.body.decode('utf-8', errors='replace')}")
                
            # Parse data based on content type
            data = {}
            try:
                if request.content_type and 'application/json' in request.content_type:
                    data = json.loads(request.body)
                else:
                    # Try to get data from POST
                    data = request.POST.dict()
                    
                    # If still no data, try to parse body as JSON
                    if not data and request.body:
                        data = json.loads(request.body)
                        
                logger.info(f"Parsed data: {data}")
            except json.JSONDecodeError as e:
                logger.error(f"JSON decode error: {e}")
                return JsonResponse({
                    'success': False, 
                    'message': f'Invalid JSON data: {str(e)}'
                }, status=400)
            
            name = data.get('name')
            description = data.get('description', '')
            date_str = data.get('date')
            
            # Validate required fields
            if not name or not date_str:
                return JsonResponse({
                    'success': False,
                    'message': 'Name and date are required fields'
                }, status=400)
                
            # Parse date
            try:
                holiday_date = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                return JsonResponse({
                    'success': False,
                    'message': 'Invalid date format. Use YYYY-MM-DD'
                }, status=400)
            
            # Check if holiday date is not in the past
            from datetime import date
            today = date.today()
            if holiday_date < today:
                return JsonResponse({
                    'success': False,
                    'message': 'Cannot add holiday for past dates. Please select today or a future date.'
                }, status=400)
            
            # Check for conflicts with sessions and events
            from django.db.models import Q
            
            # Create datetime objects for the start and end of the holiday
            holiday_start = datetime.combine(holiday_date, time.min)
            holiday_end = datetime.combine(holiday_date, time.max)
            
            # Find sessions that overlap with the holiday date
            conflicting_sessions = Session.objects.filter(
                Q(start_date__date=holiday_date) | 
                Q(end_date__date=holiday_date) |
                (Q(start_date__date__lt=holiday_date) & Q(end_date__date__gt=holiday_date))
            ).filter(is_active=True)
            
            # Find events that overlap with the holiday date
            conflicting_events = Event.objects.filter(
                Q(start_time__date=holiday_date) | 
                Q(end_time__date=holiday_date) |
                (Q(start_time__date__lt=holiday_date) & Q(end_time__date__gt=holiday_date))
            ).filter(is_active=True)
            
            # Count conflicts
            sessions_count = conflicting_sessions.count()
            events_count = conflicting_events.count()
            total_conflicts = sessions_count + events_count
            
            # Format conflict data for API response
            conflicting_sessions_data = []
            for session in conflicting_sessions:
                # ** DEBUG LOG: Check session room and room type details **
                room_name = session.room.name if session.room else "None"
                room_type_id_val = None
                if session.room and hasattr(session.room, 'room_type') and session.room.room_type:
                     room_type_id_val = session.room.room_type.room_type_id
                logger.debug(f"Processing session {session.session_id}: Room='{room_name}', RoomTypeID={room_type_id_val}")
                
                # Get trainer names
                trainer_names = ", ".join([t.user.get_full_name() for t in session.trainers.all()])
                # Get equipment names/codes
                equipment_names = ", ".join([eq.name for eq in session.required_equipment.all()])

                conflicting_sessions_data.append({
                    'id': session.session_id,
                    'course_name': session.course.name_en,
                    'start_time': session.start_date.isoformat(),
                    'end_time': session.end_date.isoformat(),
                    'room': room_name, # Use variable from above
                    'room_id': session.room.room_id if session.room else None,
                    'room_type_id': room_type_id_val, # Use variable from above
                    'trainers': trainer_names, # Use variable from above
                    'trainer_ids': [t.trainer_id for t in session.trainers.all()], # Add trainer IDs
                    'equipment': equipment_names if equipment_names else "None" # Add equipment info
                })
            
            conflicting_events_data = []
            for event in conflicting_events:
                conflicting_events_data.append({
                    'id': str(event.event_id),
                    'name': event.name,
                    'start_time': event.start_time.isoformat() if event.start_time else None,
                    'end_time': event.end_time.isoformat() if event.end_time else None,
                    'room': event.room.name if event.room else 'No room assigned'
                })
            
            # If there are conflicts, return them and don't create/update the holiday
            if total_conflicts > 0 and request.method == "POST":
                return JsonResponse({
                    'success': False,
                    'message': 'Cannot add holiday. There are conflicts with existing sessions or events.',
                    'has_conflicts': True,
                    'conflicts': {
                        'total': total_conflicts,
                        'sessions_count': sessions_count,
                        'events_count': events_count,
                        'sessions': conflicting_sessions_data,
                        'events': conflicting_events_data
                    }
                }, status=409)  # HTTP 409 Conflict
            
            # UPDATE existing holiday
            if request.method == "PUT" and holiday_id:
                try:
                    holiday = Holiday.objects.get(holiday_id=holiday_id)
                    
                    # Only check conflicts if the date is being changed
                    if holiday.date != holiday_date and total_conflicts > 0:
                        return JsonResponse({
                            'success': False,
                            'message': 'Cannot change holiday date. There are conflicts with existing sessions or events.',
                            'has_conflicts': True,
                            'conflicts': {
                                'total': total_conflicts,
                                'sessions_count': sessions_count,
                                'events_count': events_count,
                                'sessions': conflicting_sessions_data,
                                'events': conflicting_events_data
                            }
                        }, status=409)  # HTTP 409 Conflict
                    
                    holiday.name = name
                    holiday.description = description
                    holiday.date = holiday_date
                    holiday.save()
                    
                    return JsonResponse({
                        'success': True,
                        'message': f'Holiday "{name}" has been updated',
                        'holiday': {
                            'id': holiday.holiday_id,
                            'name': holiday.name,
                            'description': holiday.description,
                            'date': holiday.date.strftime('%Y-%m-%d')
                        }
                    })
                except Holiday.DoesNotExist:
                    return JsonResponse({
                        'success': False,
                        'message': 'Holiday not found'
                    }, status=404)
            
            # CREATE new holiday
            elif request.method == "POST":
                try:
                    # Log the data we're about to use
                    logger.info(f"Creating holiday with: name={name}, date={holiday_date}, description={description}")
                    
                    # Create the holiday record with only required fields
                    # to avoid errors if some fields don't exist in the table yet
                    holiday_data = {
                        'name': name,
                        'description': description,
                        'date': holiday_date,
                    }
                    
                    # Try to add is_active field only if it exists in the model
                    try:
                        holiday = Holiday(
                            name=name,
                            description=description,
                            date=holiday_date,
                            is_active=True  # Try with is_active
                        )
                        holiday.save()
                    except Exception as field_error:
                        # If is_active causes an error, try without it
                        if 'is_active' in str(field_error):
                            logger.warning("is_active field is missing, creating without it")
                            holiday = Holiday(
                                name=name,
                                description=description,
                                date=holiday_date
                            )
                            holiday.save()
                        else:
                            # Re-raise if it's not an is_active issue
                            raise field_error
                    
                    logger.info(f"Successfully created holiday with ID: {holiday.holiday_id}")
                    
                    return JsonResponse({
                        'success': True,
                        'message': f'Holiday "{name}" has been created',
                        'holiday': {
                            'id': holiday.holiday_id,
                            'name': holiday.name,
                            'description': holiday.description,
                            'date': holiday.date.strftime('%Y-%m-%d')
                        }
                    })
                except Exception as e:
                    logger.error(f"Error creating holiday: {e}", exc_info=True)
                    return JsonResponse({
                        'success': False,
                        'message': f'Error creating holiday: {str(e)}'
                    }, status=500)
        
        # If we get here, method not allowed
        return JsonResponse({
            'success': False,
            'message': 'Method not allowed'
        }, status=405)
        
    except Exception as e:
        logger.error(f"Error handling holiday: {str(e)}", exc_info=True)
        # Log additional information to help debug
        logger.error(f"Request method: {request.method}, Content type: {request.content_type}")
        if request.body:
            logger.error(f"Request body: {request.body.decode('utf-8', errors='replace')}")
        return JsonResponse({
            'success': False,
            'error': str(e),
            'details': 'Check server logs for more information'
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_holidays_api(request):
    """API endpoint to get all holidays"""
    from django.http import JsonResponse
    import logging
    from django.db.models import Q
    from datetime import datetime, time
    
    logger = logging.getLogger(__name__)
    
    try:
        # Get all active holidays
        holidays = Holiday.objects.filter(is_active=True).order_by('date')
        
        # Format holidays for API response
        holidays_data = []
        for holiday in holidays:
            # Check for conflicts with sessions and events
            holiday_date = holiday.date
            holiday_start = datetime.combine(holiday_date, time.min)
            holiday_end = datetime.combine(holiday_date, time.max)
            
            # Find sessions that overlap with the holiday date
            conflicting_sessions = Session.objects.filter(
                Q(start_date__date=holiday_date) | 
                Q(end_date__date=holiday_date) |
                (Q(start_date__date__lt=holiday_date) & Q(end_date__date__gt=holiday_date))
            ).filter(is_active=True)
            
            # Find events that overlap with the holiday date
            conflicting_events = Event.objects.filter(
                Q(start_time__date=holiday_date) | 
                Q(end_time__date=holiday_date) |
                (Q(start_time__date__lt=holiday_date) & Q(end_time__date__gt=holiday_date))
            ).filter(is_active=True)
            
            sessions_count = conflicting_sessions.count()
            events_count = conflicting_events.count()
            total_conflicts = sessions_count + events_count
            
            # Format conflict details for display
            conflicting_sessions_data = []
            if sessions_count > 0:
                for session in conflicting_sessions:
                    trainer_names = ", ".join([f"{trainer.user.first_name} {trainer.user.last_name}" 
                                          for trainer in session.trainers.all()])
                    conflicting_sessions_data.append({
                        'id': session.session_id,
                        'course_name': session.course.name_en,
                        'start_time': session.start_date.isoformat(),
                        'end_time': session.end_date.isoformat(),
                        'room': session.room.name if session.room else 'No room assigned',
                        'trainers': trainer_names
                    })
            
            conflicting_events_data = []
            if events_count > 0:
                for event in conflicting_events:
                    conflicting_events_data.append({
                        'id': str(event.event_id),
                        'name': event.name,
                        'start_time': event.start_time.isoformat() if event.start_time else None,
                        'end_time': event.end_time.isoformat() if event.end_time else None,
                        'room': event.room.name if event.room else 'No room assigned'
                    })
            
            holidays_data.append({
                'id': holiday.holiday_id,
                'name': holiday.name,
                'description': holiday.description,
                'date': holiday.date.strftime('%Y-%m-%d'),
                'has_conflicts': total_conflicts > 0,
                'conflicts_count': total_conflicts,
                'conflicts': {
                    'sessions_count': sessions_count,
                    'events_count': events_count,
                    'sessions': conflicting_sessions_data,
                    'events': conflicting_events_data
                } if total_conflicts > 0 else None
            })
        
        return JsonResponse({
            'success': True,
            'holidays': holidays_data
        })
        
    except Exception as e:
        logger.error(f"Error fetching holidays: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f"Error: {str(e)}"
        }, status=500)

def reschedule_session(session, new_start_date, new_end_date, new_room=None):
    """
    Reschedules a session to a new time/date and updates all related availability records.
    Instead of updating the existing session, this deletes the old session and creates a new one
    with the same session_id.
    
    Args:
        session: The Session object to reschedule
        new_start_date: New start datetime
        new_end_date: New end datetime
        new_room: Optional new Room object
    
    Returns:
        Updated session object
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Rescheduling session {session.session_id} to {new_start_date} - {new_end_date}")
    
    # Ensure datetime objects are naive if timezone support is disabled
    if timezone.is_aware(new_start_date):
        new_start_date = timezone.make_naive(new_start_date)
    if timezone.is_aware(new_end_date):
        new_end_date = timezone.make_naive(new_end_date)
    
    try:
        with transaction.atomic():
            # Store original session data
            session_id = session.session_id
            course = session.course
            trainers = list(session.trainers.all())
            equipment_list = list(session.required_equipment.all())
            location = session.location
            slot_type = session.slot_type
            is_active = session.is_active
            created_at = session.created_at
            updated_at = timezone.now()
            
            # Store course instance relationships
            course_instances = list(CourseInstance.objects.filter(sessions=session))
            
            # Clear existing equipment and trainer availability records
            EquipmentAvailability.remove_busy_slots(equipment=None, session_id=session.session_id)
            TrainerAvailability.remove_busy_slots(trainer=None, session_id=session.session_id)
            
            # Find the existing room availability record
            old_room = session.room
            existing_room_availability = None
            
            if old_room and (new_room is None or old_room.room_id == new_room.room_id):
                # Get the existing room availability record
                existing_room_availability = RoomAvailability.objects.filter(session=session).first()
                if existing_room_availability:
                    logger.info(f"Found existing room availability for session {session_id}, will update it")
                    # We'll update this record after new session creation
            else:
                # If room has changed or no previous room, delete old record
                RoomAvailability.objects.filter(session=session).delete()
            
            # Delete old session
            session.delete()
            
            # Create new session with same ID
            new_session = Session(
                session_id=session_id,
                course=course,
                start_date=new_start_date,
                end_date=new_end_date,
                room=new_room if new_room else old_room,  # Use new_room if provided, otherwise keep old_room
                location=location,
                slot_type=slot_type,
                is_active=is_active,
                created_at=created_at,
                updated_at=updated_at
            )
            
            # Add a custom attribute to skip automatic room availability creation in the save method
            new_session._skip_room_availability = True
            
            # Validate and save the new session
            new_session.full_clean()
            new_session.save()
            
            # Re-add trainers to the session
            new_session.trainers.set(trainers)
            
            # Re-add equipment to the session
            new_session.required_equipment.set(equipment_list)
            
            # Re-add the session to course instances
            for instance in course_instances:
                instance.sessions.add(new_session)
            
            # Update the existing room availability record or create a new one
            if existing_room_availability and new_session.room:
                # Update existing record
                existing_room_availability.session = new_session  # Update the session reference
                existing_room_availability.start_date = new_start_date
                existing_room_availability.end_date = new_end_date
                existing_room_availability.save()
                logger.info(f"Updated existing room availability for session {session_id}")
            elif new_session.room and not existing_room_availability:
                # Create a new room availability record
                room_availability = RoomAvailability(
                    room=new_session.room,
                    start_date=new_session.start_date,
                    end_date=new_session.end_date,
                    booking_type='SESSION',
                    booking_id=str(session_id),
                    session=new_session
                )
                room_availability.save()
                logger.info(f"Created new room availability for session {session_id} with room {new_session.room.name}")
            
            # Re-apply trainer availability
            for trainer in trainers:
                TrainerAvailability.add_busy_slot(
                    trainer=trainer,
                    start_time=new_start_date,
                    end_time=new_end_date,
                    session=new_session
                )
            
            # Re-apply equipment availability
            for equipment in equipment_list:
                EquipmentAvailability.add_busy_slot(
                    equipment=equipment,
                    start_time=new_start_date,
                    end_time=new_end_date,
                    session=new_session
                )
            
            # Update course instance dates if needed
            for instance in course_instances:
                # Refresh instance to get updated sessions
                instance.refresh_from_db()
                # Check if instance dates need updating
                instance_sessions = instance.sessions.all()
                if instance_sessions.count() > 0:
                    # Get earliest start and latest end date from all sessions
                    earliest_start = min(s.start_date for s in instance_sessions)
                    latest_end = max(s.end_date for s in instance_sessions)
                    
                    # Update instance dates if needed
                    if instance.start_date != earliest_start or instance.end_date != latest_end:
                        instance.start_date = earliest_start
                        instance.end_date = latest_end
                        instance.save()
            
            logger.info(f"Successfully rescheduled session {new_session.session_id}")
            return new_session
            
    except Exception as e:
        logger.error(f"Error rescheduling session: {e}", exc_info=True)
        raise

def reschedule_event(event, new_start_time, new_end_time, new_room=None):
    """
    Reschedules an event to a new time/date and updates all related availability records.
    Instead of updating the existing event, this deletes the old event and creates a new one
    
    Args:
        event: The Event object to reschedule
        new_start_time: New start datetime
        new_end_time: New end datetime
        new_room: Optional new Room object
    
    Returns:
        Updated event object
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Rescheduling event {event.event_id} to {new_start_time} - {new_end_time}")
    
    # Ensure datetime objects are naive if timezone support is disabled
    if timezone.is_aware(new_start_time):
        new_start_time = timezone.make_naive(new_start_time)
    if timezone.is_aware(new_end_time):
        new_end_time = timezone.make_naive(new_end_time)
    
    try:
        with transaction.atomic():
            # Store original event data
            event_id = event.event_id
            name = event.name
            description = event.description
            capacity = event.capacity
            event_type = event.event_type
            old_room = event.room
            equipment_list = list(event.required_equipment.all())
            is_active = event.is_active
            created_at = event.created_at
            updated_at = timezone.now()
            
            # Clear existing equipment availability records
            EquipmentAvailability.remove_busy_slots(equipment=None, event_id=event_id)
            
            # Find the existing room availability record
            existing_room_availability = None
            
            if old_room and (new_room is None or old_room.room_id == new_room.room_id):
                # Get the existing room availability record
                existing_room_availability = RoomAvailability.objects.filter(event=event).first()
                if existing_room_availability:
                    logger.info(f"Found existing room availability for event {event_id}, will update it")
                    # We'll update this record after new event creation
            else:
                # If room has changed or no previous room, delete old record
                RoomAvailability.objects.filter(event=event).delete()
            
            # Delete old event
            event.delete()
            
            # Create new event with same ID
            new_event = Event(
                event_id=event_id,
                name=name,
                description=description,
                capacity=capacity,
                event_type=event_type,
                room=new_room if new_room else old_room,  # Use new_room if provided, otherwise keep old_room
                start_time=new_start_time,
                end_time=new_end_time,
                is_active=is_active,
                created_at=created_at,
                updated_at=updated_at
            )
            
            # Validate and save the new event
            new_event.full_clean()
            
            # Add a custom attribute to skip automatic room availability creation in the save method
            new_event._skip_room_availability = True
            
            new_event.save()
            
            # Re-add equipment to the event
            new_event.required_equipment.set(equipment_list)
            
            # Update the existing room availability record or create a new one
            if existing_room_availability and new_event.room:
                # Update existing record
                existing_room_availability.event = new_event  # Update the event reference
                existing_room_availability.start_date = new_start_time
                existing_room_availability.end_date = new_end_time
                existing_room_availability.save()
                logger.info(f"Updated existing room availability for event {event_id}")
            elif new_event.room and not existing_room_availability:
                # Create a new room availability record
                room_availability = RoomAvailability(
                    room=new_event.room,
                    start_date=new_event.start_time,
                    end_date=new_event.end_time,
                    booking_type='EVENT',
                    booking_id=str(event_id),
                    event=new_event
                )
                room_availability.save()
                logger.info(f"Created new room availability for event {event_id} with room {new_event.room.name}")
            
            # Re-apply equipment availability
            for equipment in equipment_list:
                EquipmentAvailability.add_busy_slot(
                    equipment=equipment,
                    start_time=new_start_time,
                    end_time=new_end_time,
                    event=new_event
                )
            
            logger.info(f"Successfully rescheduled event {new_event.event_id}")
            return new_event
            
    except Exception as e:
        logger.error(f"Error rescheduling event: {e}", exc_info=True)
        raise

@login_required
@staff_member_required
@require_http_methods(["POST"])
def reschedule_session_api(request):
    """API endpoint to reschedule a session from holiday conflicts"""
    from django.http import JsonResponse
    import json
    import logging
    from datetime import datetime
    import traceback
    
    logger = logging.getLogger(__name__)
    
    try:
        # Parse request data
        data = json.loads(request.body)
        
        # Log the complete request data for debugging
        logger.info(f"Reschedule session request received with data: {data}")
        
        session_id = data.get('session_id')
        new_start_date_str = data.get('new_start_date')
        new_end_date_str = data.get('new_end_date')
        new_room_id = data.get('new_room_id')
        new_equipment_id = data.get('new_equipment_id')  # New parameter for equipment
        new_trainer_id = data.get('new_trainer_id')  # Optional new trainer parameter
        
        # Log complete request data for debugging
        logger.debug(f"Reschedule session request data: {data}")
        
        # Validate required fields
        if not all([session_id, new_start_date_str, new_end_date_str]):
            return JsonResponse({
                'success': False,
                'message': 'Missing required fields: session_id, new_start_date, new_end_date'
            }, status=400)
        
        # Parse dates
        try:
            # Parse the ISO datetime strings
            new_start_date = datetime.fromisoformat(new_start_date_str.replace('Z', '+00:00'))
            new_end_date = datetime.fromisoformat(new_end_date_str.replace('Z', '+00:00'))
            
            # Make datetime objects timezone-naive for SQLite compatibility
            # This is needed because SQLite doesn't support timezone-aware datetimes when USE_TZ is False
            if timezone.is_aware(new_start_date):
                new_start_date = timezone.make_naive(new_start_date)
            if timezone.is_aware(new_end_date):
                new_end_date = timezone.make_naive(new_end_date)
                
            logger.debug(f"Parsed dates (made naive): start={new_start_date}, end={new_end_date}")
        except ValueError as e:
            return JsonResponse({
                'success': False,
                'message': f'Invalid date format: {str(e)}'
            }, status=400)
        
        # Get session
        try:
            session = Session.objects.get(session_id=session_id)
            logger.debug(f"Found session: {session.session_id} for course: {session.course.name_en}")
        except Session.DoesNotExist:
            return JsonResponse({
                'success': False,
                'message': f'Session with ID {session_id} not found'
            }, status=404)
        
        # Get new room if specified
        new_room = None
        if new_room_id:
            try:
                new_room = Room.objects.get(room_id=new_room_id)
                logger.debug(f"Found new room: {new_room.name} (ID: {new_room.room_id})")
            except Room.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'Room with ID {new_room_id} not found'
                }, status=404)
        
        # Get new equipment if specified
        new_equipment = None
        if new_equipment_id and new_equipment_id != "none":
            try:
                new_equipment = Equipment.objects.get(equipment_id=new_equipment_id)
                logger.debug(f"Found new equipment: {new_equipment.name} (ID: {new_equipment.equipment_id})")
            except Equipment.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'Equipment with ID {new_equipment_id} not found'
                }, status=404)
        
        # Get new trainer if specified
        new_trainer = None
        if new_trainer_id:
            try:
                new_trainer = Trainer.objects.get(trainer_id=new_trainer_id)
                logger.debug(f"Found new trainer: {new_trainer.user.username} (ID: {new_trainer.trainer_id})")
            except Trainer.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'Trainer with ID {new_trainer_id} not found'
                }, status=404)
        
        # Check availability before rescheduling
        if new_room:
            room_is_available = is_room_available(new_room, new_start_date, new_end_date, exclude_session_id=session_id)
            if not room_is_available:
                # Get detailed conflict information for better error messages
                conflicts = get_room_conflict_details(new_room, new_start_date, new_end_date, exclude_session_id=session_id)
                conflict_details = ", ".join([f"{c['type']} at {c['time']}" for c in conflicts[:3]])
                
                return JsonResponse({
                    'success': False,
                    'message': f'Room {new_room.name} is not available during the selected time. Conflicts: {conflict_details}'
                }, status=409)
        elif session.room:
            # Using same room, check availability
            room_is_available = is_room_available(session.room, new_start_date, new_end_date, exclude_session_id=session_id)
            if not room_is_available:
                conflicts = get_room_conflict_details(session.room, new_start_date, new_end_date, exclude_session_id=session_id)
                conflict_details = ", ".join([f"{c['type']} at {c['time']}" for c in conflicts[:3]])
                
                return JsonResponse({
                    'success': False,
                    'message': f'Room {session.room.name} is not available during the selected time. Conflicts: {conflict_details}'
                }, status=409)
        
        # Check trainer availability
        trainers_to_check = session.trainers.all()
        if new_trainer:
            trainers_to_check = [new_trainer]
            
        for trainer in trainers_to_check:
            trainer_is_available = trainer.is_available(new_start_date, new_end_date, exclude_session_id=session_id)
            if not trainer_is_available:
                return JsonResponse({
                    'success': False,
                    'message': f'Trainer {trainer.user.username} is not available during the selected time'
                }, status=409)
        
        # Check equipment availability if we're keeping current equipment
        if not new_equipment and new_equipment_id != "none":
            for equipment in session.required_equipment.all():
                equipment_is_available = check_equipment_availability(equipment, new_start_date, new_end_date, exclude_session_id=session_id)
                if not equipment_is_available:
                    return JsonResponse({
                        'success': False,
                        'message': f'Equipment {equipment.code} is not available during the selected time'
                    }, status=409)
        # Check new equipment availability if changing
        elif new_equipment:
            equipment_conflicts = check_equipment_availability(new_equipment, new_start_date, new_end_date, exclude_session_id=session_id)
            if equipment_conflicts:
                return JsonResponse({
                    'success': False,
                    'message': f'New equipment {new_equipment.code} is not available during the selected time'
                }, status=409)
        
        logger.info(f"All availability checks passed, proceeding with rescheduling session {session_id}")
        
        # Reschedule session
        try:
            with transaction.atomic():
                logger.debug(f"Beginning rescheduling transaction for session {session_id}")
                
                # First reschedule the session with time/room changes
                rescheduled_session = reschedule_session(
                    session=session,
                    new_start_date=new_start_date,
                    new_end_date=new_end_date,
                    new_room=new_room
                )
                
                logger.debug(f"Session rescheduled successfully: {rescheduled_session.session_id}")
                
                # Handle trainer changes if specified
                if new_trainer:
                    logger.debug(f"Updating trainer from {[t.user.username for t in rescheduled_session.trainers.all()]} to {new_trainer.user.username}")
                    # Clear existing trainers
                    for trainer in rescheduled_session.trainers.all():
                        TrainerAvailability.remove_busy_slots(
                            trainer=trainer,
                            session_id=rescheduled_session.session_id
                        )
                    rescheduled_session.trainers.clear()
                    
                    # Add the new trainer
                    rescheduled_session.trainers.add(new_trainer)
                    
                    # Create new availability record
                    TrainerAvailability.add_busy_slot(
                        trainer=new_trainer,
                        start_time=new_start_date,
                        end_time=new_end_date,
                        session=rescheduled_session
                    )
                
                # If new equipment is specified and not "none"
                if new_equipment:
                    logger.debug(f"Updating equipment to {new_equipment.name}")
                    # Clear existing equipment first
                    for equipment in rescheduled_session.required_equipment.all():
                        EquipmentAvailability.remove_busy_slots(
                            equipment=equipment,
                            session_id=rescheduled_session.session_id
                        )
                    rescheduled_session.required_equipment.clear()
                    
                    # Add the new equipment
                    rescheduled_session.required_equipment.add(new_equipment)
                    
                    # Create new availability record
                    EquipmentAvailability.add_busy_slot(
                        equipment=new_equipment,
                        start_time=new_start_date,
                        end_time=new_end_date,
                        session=rescheduled_session
                    )
                elif new_equipment_id == "none":
                    logger.debug("Removing all equipment from session")
                    # If "none" is specified, remove all equipment
                    for equipment in rescheduled_session.required_equipment.all():
                        EquipmentAvailability.remove_busy_slots(
                            equipment=equipment,
                            session_id=rescheduled_session.session_id
                        )
                    rescheduled_session.required_equipment.clear()
            
            logger.info(f"Session {session_id} rescheduled successfully")
            
            return JsonResponse({
                'success': True,
                'message': f'Session {session_id} has been rescheduled successfully',
                'session': {
                    'id': rescheduled_session.session_id,
                    'start_date': rescheduled_session.start_date.isoformat(),
                    'end_date': rescheduled_session.end_date.isoformat(),
                    'room_id': rescheduled_session.room.room_id if rescheduled_session.room else None,
                    'room_name': rescheduled_session.room.name if rescheduled_session.room else None,
                    'trainers': [{'id': t.trainer_id, 'name': t.user.get_full_name() or t.user.username} 
                               for t in rescheduled_session.trainers.all()],
                    'equipment': [{'id': eq.equipment_id, 'name': eq.name, 'code': eq.code} 
                                 for eq in rescheduled_session.required_equipment.all()]
                }
            })
        except Exception as e:
            # Log detailed exception traceback
            tb = traceback.format_exc()
            logger.error(f"Error rescheduling session: {e}\n{tb}")
            
            return JsonResponse({
                'success': False,
                'message': f'Error rescheduling session: {str(e)}',
                'details': str(tb) if settings.DEBUG else None
            }, status=500)
            
    except Exception as e:
        # Log detailed exception traceback for the outer try-except
        tb = traceback.format_exc()
        logger.error(f"Error in reschedule_session_api: {e}\n{tb}")
        
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}',
            'details': str(tb) if settings.DEBUG else None
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def reschedule_event_api(request):
    """API endpoint to reschedule an event from holiday conflicts"""
    from django.http import JsonResponse
    import json
    import logging
    from datetime import datetime
    import uuid
    import traceback
    
    logger = logging.getLogger(__name__)
    
    try:
        # Parse request data
        data = json.loads(request.body)
        
        # Log the complete request data for debugging
        logger.info(f"Reschedule event request received with data: {data}")
        
        event_id = data.get('event_id')
        new_start_time_str = data.get('new_start_time')
        new_end_time_str = data.get('new_end_time')
        new_room_id = data.get('new_room_id')
        new_equipment_id = data.get('new_equipment_id')  # New parameter for equipment
        
        # Validate required fields
        if not all([event_id, new_start_time_str, new_end_time_str]):
            return JsonResponse({
                'success': False,
                'message': 'Missing required fields: event_id, new_start_time, new_end_time'
            }, status=400)
        
        # Parse dates
        try:
            new_start_time = datetime.fromisoformat(new_start_time_str.replace('Z', '+00:00'))
            new_end_time = datetime.fromisoformat(new_end_time_str.replace('Z', '+00:00'))
            
            # Make datetime objects timezone-naive for SQLite compatibility
            if timezone.is_aware(new_start_time):
                new_start_time = timezone.make_naive(new_start_time)
            if timezone.is_aware(new_end_time):
                new_end_time = timezone.make_naive(new_end_time)
                
            logger.debug(f"Parsed event dates (made naive): start={new_start_time}, end={new_end_time}")
        except ValueError as e:
            return JsonResponse({
                'success': False,
                'message': f'Invalid date format: {str(e)}'
            }, status=400)
        
        # Get event
        try:
            # Convert event_id to UUID if it's a string
            try:
                if isinstance(event_id, str):
                    event_id = uuid.UUID(event_id)
            except ValueError as e:
                logger.error(f"Error converting event_id to UUID: {e}")
                return JsonResponse({
                    'success': False,
                    'message': f'Invalid event ID format: {str(e)}'
                }, status=400)
                
            event = Event.objects.get(event_id=event_id)
            logger.debug(f"Found event: {event.name} (ID: {event.event_id})")
        except (Event.DoesNotExist, ValueError) as e:
            return JsonResponse({
                'success': False,
                'message': f'Event not found or invalid ID: {str(e)}'
            }, status=404)
        
        # Get new room if specified
        new_room = None
        if new_room_id:
            try:
                new_room = Room.objects.get(room_id=new_room_id)
            except Room.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'Room with ID {new_room_id} not found'
                }, status=404)
        
        # Get new equipment if specified
        new_equipment = None
        if new_equipment_id and new_equipment_id != "none":
            try:
                new_equipment = Equipment.objects.get(equipment_id=new_equipment_id)
                logger.debug(f"Found new equipment: {new_equipment.name} (ID: {new_equipment.equipment_id})")
            except Equipment.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': f'Equipment with ID {new_equipment_id} not found'
                }, status=404)
        
        # Check availability before rescheduling
        if new_room:
            # Convert UUID to string for is_room_available
            event_id_str = str(event_id)
            room_is_available = is_room_available(new_room, new_start_time, new_end_time, exclude_event_id=event_id_str)
            if not room_is_available:
                return JsonResponse({
                    'success': False,
                    'message': f'Room {new_room.name} is not available during the selected time'
                }, status=409)
        elif event.room:
            # Using same room, check availability
            event_id_str = str(event_id)
            room_is_available = is_room_available(event.room, new_start_time, new_end_time, exclude_event_id=event_id_str)
            if not room_is_available:
                return JsonResponse({
                    'success': False,
                    'message': f'Room {event.room.name} is not available during the selected time'
                }, status=409)
        
        # Check equipment availability if new equipment is specified
        if new_equipment:
            event_id_str = str(event_id)
            # Just check availability but don't return error, as per client request
            equipment_is_available = check_equipment_availability(new_equipment, new_start_time, new_end_time, exclude_event_id=event_id_str)
            logger.info(f"Equipment {new_equipment.code} availability check: {equipment_is_available}")
            # The following code block has been removed to bypass equipment availability check
            # if not equipment_is_available:
            #     return JsonResponse({
            #         'success': False,
            #         'message': f'Equipment {new_equipment.code} is not available during the selected time'
            #     }, status=409)
        
        # Reschedule event
        try:
            with transaction.atomic():
                logger.debug(f"Beginning rescheduling transaction for event {event_id}")
                
                # First reschedule the event with time/room changes
                rescheduled_event = reschedule_event(
                    event=event,
                    new_start_time=new_start_time,
                    new_end_time=new_end_time,
                    new_room=new_room
                )
                
                logger.debug(f"Event rescheduled successfully: {rescheduled_event.event_id}")
                
                # Handle equipment changes if specified
                if new_equipment:
                    logger.debug(f"Updating equipment to {new_equipment.name}")
                    # Clear existing equipment first
                    for equipment in rescheduled_event.required_equipment.all():
                        EquipmentAvailability.remove_busy_slots(
                            equipment=equipment,
                            event_id=str(rescheduled_event.event_id)
                        )
                    rescheduled_event.required_equipment.clear()
                    
                    # Add the new equipment
                    rescheduled_event.required_equipment.add(new_equipment)
                    
                    # Create new availability record
                    EquipmentAvailability.add_busy_slot(
                        equipment=new_equipment,
                        start_time=new_start_time,
                        end_time=new_end_time,
                        event=rescheduled_event
                    )
                elif new_equipment_id == "none":
                    logger.debug("Removing all equipment from event")
                    # If "none" is specified, remove all equipment
                    for equipment in rescheduled_event.required_equipment.all():
                        EquipmentAvailability.remove_busy_slots(
                            equipment=equipment,
                            event_id=str(rescheduled_event.event_id)
                        )
                    rescheduled_event.required_equipment.clear()
            
            logger.info(f"Event {event_id} rescheduled successfully")
            
            # Get equipment info for response
            equipment_info = [
                {'id': eq.equipment_id, 'name': eq.name, 'code': eq.code} 
                for eq in rescheduled_event.required_equipment.all()
            ]
            
            return JsonResponse({
                'success': True,
                'message': f'Event {event_id} has been rescheduled successfully',
                'event': {
                    'id': str(rescheduled_event.event_id),
                    'start_time': rescheduled_event.start_time.isoformat() if rescheduled_event.start_time else None,
                    'end_time': rescheduled_event.end_time.isoformat() if rescheduled_event.end_time else None,
                    'room_id': rescheduled_event.room.room_id if rescheduled_event.room else None,
                    'room_name': rescheduled_event.room.name if rescheduled_event.room else None,
                    'equipment': equipment_info
                }
            })
        except Exception as e:
            # Log detailed exception traceback
            tb = traceback.format_exc()
            logger.error(f"Error rescheduling event: {e}\n{tb}")
            
            return JsonResponse({
                'success': False,
                'message': f'Error rescheduling event: {str(e)}',
                'details': str(tb) if settings.DEBUG else None
            }, status=500)
            
    except Exception as e:
        logger.error(f"Error in reschedule_event_api: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}'
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def check_holiday_conflicts(request):
    """
    Checks if adding a holiday on a given date conflicts with existing sessions or events.
    Returns a list of conflicting items if any.
    """
    # ... (imports and logger setup) ...
    try:
        data = json.loads(request.body)
        date_str = data.get('date')
        # name = data.get('name') # Name and description not needed for conflict check
        # description = data.get('description')
        
        if not date_str:
            return JsonResponse({'success': False, 'message': 'Date is required'}, status=400)
            
        try:
            holiday_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return JsonResponse({'success': False, 'message': 'Invalid date format. Use YYYY-MM-DD'}, status=400)
            
        # Define start and end of the holiday date for checking overlaps
        start_of_day = timezone.make_aware(datetime.combine(holiday_date, datetime.min.time()))
        end_of_day = timezone.make_aware(datetime.combine(holiday_date, datetime.max.time()))

        # If USE_TZ is False, work with naive datetimes
        if not settings.USE_TZ:
            start_of_day = timezone.make_naive(start_of_day)
            end_of_day = timezone.make_naive(end_of_day)

        # Find conflicting sessions
        conflicting_sessions = Session.objects.filter(
            Q(start_date__lt=end_of_day) & Q(end_date__gt=start_of_day)
        ).select_related(
            'course', 'room', 'room__room_type'
        ).prefetch_related(
            'trainers__user', 'required_equipment' # Prefetch equipment too
        )
        
        # Find conflicting events
        conflicting_events = Event.objects.filter(
             Q(start_time__lt=end_of_day) & Q(end_time__gt=start_of_day)
        ).select_related('room', 'event_type')

        # Prepare response data
        conflicting_sessions_data = []
        for session in conflicting_sessions:
            # ** DEBUG LOG: Check session room and room type details **
            room_name = session.room.name if session.room else "None"
            room_type_id_val = None
            if session.room and hasattr(session.room, 'room_type') and session.room.room_type:
                 room_type_id_val = session.room.room_type.room_type_id
            logger.debug(f"Processing session {session.session_id}: Room='{room_name}', RoomTypeID={room_type_id_val}")
            
            # Get trainer names
            trainer_names = ", ".join([t.user.get_full_name() for t in session.trainers.all()])
            # Get equipment names/codes
            equipment_names = ", ".join([eq.name for eq in session.required_equipment.all()])

            conflicting_sessions_data.append({
                'id': session.session_id,
                'course_name': session.course.name_en,
                'start_time': session.start_date.isoformat(),
                'end_time': session.end_date.isoformat(),
                'room': room_name, # Use variable from above
                'room_id': session.room.room_id if session.room else None,
                'room_type_id': room_type_id_val, # Use variable from above
                'trainers': trainer_names, # Use variable from above
                'trainer_ids': [t.trainer_id for t in session.trainers.all()], # Add trainer IDs
                'equipment': equipment_names if equipment_names else "None" # Add equipment info
            })
        
        conflicting_events_data = []
        for event in conflicting_events:
            conflicting_events_data.append({
                'id': str(event.event_id),
                'name': event.name,
                'start_time': event.start_time.isoformat() if event.start_time else None,
                'end_time': event.end_time.isoformat() if event.end_time else None,
                'room': event.room.name if event.room else 'No room assigned'
            })
            
        sessions_count = len(conflicting_sessions_data)
        events_count = len(conflicting_events_data)
        total_conflicts = sessions_count + events_count
        
        response_status = 200 if total_conflicts == 0 else 409 # Return 409 if conflicts exist

        return JsonResponse({
            'success': True, # Indicate the check itself was successful
            'has_conflicts': total_conflicts > 0,
            'conflicts': {
                'total': total_conflicts,
                'sessions_count': sessions_count,
                'events_count': events_count,
                'sessions': conflicting_sessions_data,
                'events': conflicting_events_data
            }
        }, status=response_status)
        
    except json.JSONDecodeError:
         return JsonResponse({'success': False, 'message': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error checking holiday conflicts: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}'
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def available_rooms_api(request):
    """
    API endpoint to get available rooms for a specific time slot.
    Used by the holiday rescheduling modals.
    Accepts start_datetime and end_datetime in YYYY-MM-DDTHH:mm format (local time).
    Optionally accepts required_room_type_id to filter rooms by type.
    """
    from django.http import JsonResponse
    import logging
    from datetime import datetime
    from django.utils import timezone # Import timezone
    
    logger = logging.getLogger(__name__)
    
    try:
        # Log all GET parameters for debugging
        logger.debug(f"available_rooms_api called with params: {dict(request.GET.items())}")
        
        start_datetime_str = request.GET.get('start_datetime')
        end_datetime_str = request.GET.get('end_datetime')
        required_room_type_id = request.GET.get('required_room_type_id') # Get optional room type filter

        if not start_datetime_str or not end_datetime_str:
             return JsonResponse({
                'success': False,
                'message': 'Missing required parameters: start_datetime and end_datetime'
            }, status=400)

        # Rest of datetime parsing code...
        try:
            start_datetime = datetime.fromisoformat(start_datetime_str)
            end_datetime = datetime.fromisoformat(end_datetime_str)
            
            if timezone.is_aware(start_datetime):
                start_datetime = timezone.make_naive(start_datetime)
            if timezone.is_aware(end_datetime):
                end_datetime = timezone.make_naive(end_datetime)

        except ValueError as e:
            logger.warning(f"Invalid date format received: start='{start_datetime_str}', end='{end_datetime_str}', error={str(e)}")
            return JsonResponse({
                'success': False,
                'message': f'Invalid date format. Expected YYYY-MM-DDTHH:mm. Received start: {start_datetime_str}, end: {end_datetime_str}'
            }, status=400)
        
        # Find available rooms for this time period
        available_rooms_data = []
        all_rooms = Room.objects.filter(is_active=True)
        logger.debug(f"Initial room count before filtering: {all_rooms.count()}")

        # Apply room type filter if provided
        room_type_filter_applied = False
        if required_room_type_id:
            logger.debug(f"Received required_room_type_id={required_room_type_id}, type={type(required_room_type_id)}")
            try:
                room_type_id_int = int(required_room_type_id)
                original_count = all_rooms.count()
                all_rooms = all_rooms.filter(room_type_id=room_type_id_int)
                filtered_count = all_rooms.count()
                room_type_filter_applied = True
                logger.debug(f"Filtered rooms by room_type_id={room_type_id_int}: {original_count} -> {filtered_count} rooms")
                
                # Check if we have any rooms of this type at all
                if filtered_count == 0:
                    logger.warning(f"No rooms found with room_type_id={room_type_id_int}")
                    # Optional: Do a query to see if ANY rooms with this type exist, regardless of availability
                    all_rooms_of_type = Room.objects.filter(room_type_id=room_type_id_int).count()
                    logger.debug(f"Total rooms in database with room_type_id={room_type_id_int}: {all_rooms_of_type}")
                    
                    # For debugging, log the raw SQL query and the available room types
                    with connection.cursor() as cursor:
                        cursor.execute("SELECT room_id, name, room_type_id FROM website_room WHERE is_active = TRUE")
                        rows = cursor.fetchall()
                        logger.debug(f"Active rooms: {rows}")
                        
                        cursor.execute("SELECT room_type_id, name FROM website_roomtype")
                        room_types = cursor.fetchall()
                        logger.debug(f"Room types: {room_types}")
                
            except (ValueError, TypeError) as e:
                logger.warning(f"Invalid required_room_type_id received: {required_room_type_id}. Error: {str(e)}")
                # We'll continue without the filter
                pass 
        
        logger.debug(f"Checking availability for {all_rooms.count()} rooms between {start_datetime} and {end_datetime}")
        # Log the actual SQL query to help debug the filter
        if room_type_filter_applied:
            logger.debug(f"Room filter query: {all_rooms.query}")
            
        for room in all_rooms:
            # Pass naive datetimes to is_room_available (assuming it handles them)
            if is_room_available(room, start_datetime, end_datetime):
                room_data = {
                    'id': room.room_id,
                    'name': room.name,
                    'capacity': room.capacity,
                    'room_type_id': room.room_type_id,
                    'room_type_name': room.room_type.name if hasattr(room, 'room_type') and room.room_type else None
                }
                available_rooms_data.append(room_data)
                logger.debug(f"Room {room.name} (id={room.room_id}, type={getattr(room, 'room_type_id', 'unknown')}) is available")
            else:
                logger.debug(f"Room {room.name} (id={room.room_id}) is NOT available")
        
        logger.debug(f"Found {len(available_rooms_data)} available rooms.")
        return JsonResponse({
            'success': True,
            'available_rooms': available_rooms_data,
            'applied_room_type_filter': room_type_filter_applied,
            'required_room_type_id': room_type_id_int if room_type_filter_applied else None
        })
            
    except Exception as e:
        logger.error(f"Error in available_rooms_api: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}'
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def suggested_dates_api(request):
    """
    API endpoint to get suggested alternative dates for rescheduling a session.
    Used by the holiday rescheduling modals.
    """
    from django.http import JsonResponse
    import logging
    from datetime import datetime, timedelta
    
    logger = logging.getLogger(__name__)
    
    try:
        session_id = request.GET.get('session_id')
        holiday_date_str = request.GET.get('holiday_date')
        
        if not session_id or not holiday_date_str:
            return JsonResponse({
                'success': False,
                'message': 'Missing required parameters: session_id and holiday_date'
            }, status=400)
            
        try:
            holiday_date = datetime.strptime(holiday_date_str, '%Y-%m-%d').date()
            session = Session.objects.get(session_id=session_id)
        except (ValueError, Session.DoesNotExist) as e:
            return JsonResponse({
                'success': False,
                'message': f'Invalid input: {str(e)}'
            }, status=400)
            
        # Get session duration
        session_duration = session.end_date - session.start_date
        
        # Generate suggested dates (next 3 days after the holiday)
        suggested_dates = []
        for i in range(1, 4):
            potential_date = datetime.combine(holiday_date + timedelta(days=i), datetime.min.time())
            potential_end_date = potential_date + session_duration
            
            # Check if trainers are available
            trainers_available = True
            for trainer in session.trainers.all():
                if not trainer.is_available(potential_date, potential_end_date):
                    trainers_available = False
                    break
                    
            # Check if room is available (if a room is assigned)
            room_available = True
            if session.room:
                room_available = is_room_available(session.room, potential_date, potential_end_date)
                
            # Check if equipment is available
            equipment_available = True
            for equipment in session.required_equipment.all():
                if not check_equipment_availability(equipment, potential_date, potential_end_date):
                    equipment_available = False
                    break
                    
            # If all resources are available, add to suggestions
            if trainers_available and room_available and equipment_available:
                suggested_dates.append({
                    'start_date': potential_date.isoformat(),
                    'end_date': potential_end_date.isoformat(),
                })
                
        return JsonResponse({
            'success': True,
            'suggested_dates': suggested_dates
        })
            
    except Exception as e:
        logger.error(f"Error in suggested_dates_api: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}'
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def available_trainers_api(request):
    """
    API endpoint to get available trainers for a specific time slot.
    Used by the session rescheduling modals.
    Accepts start_datetime and end_datetime in YYYY-MM-DDTHH:mm format (local time).
    Returns trainers who are not already booked during the specified time slot.
    """
    from django.http import JsonResponse
    import logging
    from datetime import datetime
    from django.utils import timezone
    from django.db.models import Q

    logger = logging.getLogger(__name__)
    
    try:
        # Log all GET parameters for debugging
        logger.debug(f"available_trainers_api called with params: {dict(request.GET.items())}")
        
        start_datetime_str = request.GET.get('start_datetime')
        end_datetime_str = request.GET.get('end_datetime')
        exclude_session_id = request.GET.get('exclude_session_id')  # Optional: to exclude conflicts with the session being rescheduled

        if not start_datetime_str or not end_datetime_str:
             return JsonResponse({
                'success': False,
                'message': 'Missing required parameters: start_datetime and end_datetime'
            }, status=400)

        # Parse datetime strings
        try:
            start_datetime = datetime.fromisoformat(start_datetime_str)
            end_datetime = datetime.fromisoformat(end_datetime_str)
            
            if timezone.is_aware(start_datetime):
                start_datetime = timezone.make_naive(start_datetime)
            if timezone.is_aware(end_datetime):
                end_datetime = timezone.make_naive(end_datetime)

        except ValueError as e:
            logger.warning(f"Invalid date format received: start='{start_datetime_str}', end='{end_datetime_str}', error={str(e)}")
            return JsonResponse({
                'success': False,
                'message': f'Invalid date format. Expected YYYY-MM-DDTHH:mm. Received start: {start_datetime_str}, end: {end_datetime_str}'
            }, status=400)
        
        # Start with all active trainers
        all_trainers = Trainer.objects.select_related('user').filter(user__is_active=True)
        logger.debug(f"Total active trainers before checking availability: {all_trainers.count()}")
        
        # Get all trainer availability slots that conflict with our time period
        # A conflict exists if:
        # 1. The slot starts before our end and ends after our start
        conflicting_slots = TrainerAvailability.objects.filter(
            Q(start_date__lt=end_datetime) & Q(end_date__gt=start_datetime)
        )
        
        # Exclude the session being rescheduled if provided
        if exclude_session_id:
            try:
                conflicting_slots = conflicting_slots.exclude(session_id=exclude_session_id)
                logger.debug(f"Excluding conflicts with session_id={exclude_session_id}")
            except ValueError:
                logger.warning(f"Invalid exclude_session_id: {exclude_session_id}")
        
        # Get the trainer IDs that have conflicts
        trainer_ids_with_conflicts = conflicting_slots.values_list('trainer_id', flat=True).distinct()
        logger.debug(f"Found {len(trainer_ids_with_conflicts)} trainers with scheduling conflicts")
        
        # Get trainers without conflicts
        available_trainers = all_trainers.exclude(trainer_id__in=trainer_ids_with_conflicts)
        logger.debug(f"Available trainers count: {available_trainers.count()}")
        
        # Format the response
        available_trainers_data = []
        for trainer in available_trainers:
            trainer_data = {
                'id': trainer.trainer_id,
                'name': f"{trainer.user.first_name} {trainer.user.last_name}".strip() or trainer.user.username,
                'username': trainer.user.username,
            }
            available_trainers_data.append(trainer_data)
            logger.debug(f"Trainer {trainer_data['name']} (id={trainer.trainer_id}) is available")
        
        # For debugging, log the trainers with conflicts too
        trainers_with_conflicts = all_trainers.filter(trainer_id__in=trainer_ids_with_conflicts)
        for trainer in trainers_with_conflicts:
            logger.debug(f"Trainer {trainer.user.first_name} {trainer.user.last_name} (id={trainer.trainer_id}) has conflicts")
            # Log the specific conflicts for advanced debugging
            conflicts = conflicting_slots.filter(trainer_id=trainer.trainer_id)
            for conflict in conflicts:
                logger.debug(f" - Conflict: {conflict.start_date} to {conflict.end_date}, type: {conflict.booking_type}")
        
        logger.debug(f"Returning {len(available_trainers_data)} available trainers")
        return JsonResponse({
            'success': True,
            'available_trainers': available_trainers_data
        })
            
    except Exception as e:
        logger.error(f"Error in available_trainers_api: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}'
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def available_equipment_api(request):
    """
    API endpoint to get available equipment for a specific time slot.
    Used by the session rescheduling modals.
    Accepts start_datetime and end_datetime in YYYY-MM-DDTHH:mm format (local time).
    Returns equipment that is not already booked during the specified time slot.
    """
    from django.http import JsonResponse
    import logging
    from datetime import datetime
    from django.utils import timezone
    from django.db.models import Q
    
    logger = logging.getLogger(__name__)
    
    try:
        # Log all GET parameters for debugging
        logger.debug(f"available_equipment_api called with params: {dict(request.GET.items())}")
        
        start_datetime_str = request.GET.get('start_datetime')
        end_datetime_str = request.GET.get('end_datetime')
        exclude_session_id = request.GET.get('exclude_session_id')  # Optional: to exclude conflicts with the session being rescheduled

        if not start_datetime_str or not end_datetime_str:
             return JsonResponse({
                'success': False,
                'message': 'Missing required parameters: start_datetime and end_datetime'
            }, status=400)

        # Parse datetime strings
        try:
            start_datetime = datetime.fromisoformat(start_datetime_str)
            end_datetime = datetime.fromisoformat(end_datetime_str)
            
            if timezone.is_aware(start_datetime):
                start_datetime = timezone.make_naive(start_datetime)
            if timezone.is_aware(end_datetime):
                end_datetime = timezone.make_naive(end_datetime)

        except ValueError as e:
            logger.warning(f"Invalid date format received: start='{start_datetime_str}', end='{end_datetime_str}', error={str(e)}")
            return JsonResponse({
                'success': False,
                'message': f'Invalid date format. Expected YYYY-MM-DDTHH:mm. Received start: {start_datetime_str}, end: {end_datetime_str}'
            }, status=400)
        
        # Start with all active equipment - MODIFIED: expanded status filter to include more statuses
        # Before: status__in=['AVAILABLE', 'READY']
        available_statuses = ['AVAILABLE', 'READY', 'BUSY', 'BOOKED']
        all_equipment = Equipment.objects.filter(is_active=True)
        
        # Log counts at each filtering stage for debugging
        logger.debug(f"Total active equipment before status filtering: {all_equipment.count()}")
        
        # Apply status filter
        all_equipment = all_equipment.filter(status__in=available_statuses)
        logger.debug(f"Equipment after status filtering: {all_equipment.count()}")
        
        # Get all equipment availability slots that conflict with our time period
        conflicting_slots = EquipmentAvailability.objects.filter(
            Q(start_date__lt=end_datetime) & Q(end_date__gt=start_datetime)
        )
        
        # Count conflicting slots for debugging
        conflicting_count = conflicting_slots.count()
        logger.debug(f"Found {conflicting_count} conflicting time slots in EquipmentAvailability")
        
        # Exclude the session being rescheduled if provided
        if exclude_session_id:
            try:
                conflicting_slots = conflicting_slots.exclude(session_id=exclude_session_id)
                new_conflicting_count = conflicting_slots.count()
                logger.debug(f"Excluding conflicts with session_id={exclude_session_id}, reduced conflicts from {conflicting_count} to {new_conflicting_count}")
            except ValueError:
                logger.warning(f"Invalid exclude_session_id: {exclude_session_id}")
        
        # Get the equipment IDs that have conflicts
        equipment_ids_with_conflicts = conflicting_slots.values_list('equipment_id', flat=True).distinct()
        logger.debug(f"Found {len(equipment_ids_with_conflicts)} equipment items with scheduling conflicts")
        
        # Debugging: list the conflicting equipment IDs
        if equipment_ids_with_conflicts:
            logger.debug(f"Conflicting equipment IDs: {list(equipment_ids_with_conflicts)}")
        
        # Get equipment without conflicts
        available_equipment = all_equipment.exclude(equipment_id__in=equipment_ids_with_conflicts)
        logger.debug(f"Available equipment count after conflict filtering: {available_equipment.count()}")
        
        # Format the response
        available_equipment_data = []
        for equipment in available_equipment:
            equipment_data = {
                'id': equipment.equipment_id,
                'name': equipment.name,
                'code': equipment.code,
                'status': equipment.status,
                'category': equipment.category.name if equipment.category else '',
                'brand': equipment.brand.name if equipment.brand else '',
                'model': equipment.model.name if equipment.model else ''
            }
            available_equipment_data.append(equipment_data)
            logger.debug(f"Equipment {equipment.name} (id={equipment.equipment_id}, status={equipment.status}) is available")
        
        # For debugging, log equipment count by status
        from django.db.models import Count
        status_counts = all_equipment.values('status').annotate(count=Count('status'))
        logger.debug(f"Equipment counts by status: {[s for s in status_counts]}")
        
        # Also log all equipment for reference
        all_equipment_list = Equipment.objects.all()
        logger.debug(f"Total equipment in database (all statuses): {all_equipment_list.count()}")
        
        # If no equipment is available, check why
        if not available_equipment_data:
            logger.warning("No available equipment found. This could be due to filtering or no equipment in the database.")
            # Check if there's any equipment in the database
            if Equipment.objects.count() == 0:
                logger.warning("There are no equipment items in the database.")
            else:
                # List some available statuses for clarity
                available_status_count = Equipment.objects.filter(status__in=available_statuses).count()
                logger.warning(f"There are {available_status_count} equipment items with allowed statuses.")
        
        logger.debug(f"Returning {len(available_equipment_data)} available equipment items")
        return JsonResponse({
            'success': True,
            'available_equipment': available_equipment_data
        })
            
    except Exception as e:
        logger.error(f"Error in available_equipment_api: {e}", exc_info=True)
        return JsonResponse({
            'success': False,
            'message': f'Server error: {str(e)}'
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def get_corporates_api(request):
    try:
        corporates = Corporate.objects.all()
        return JsonResponse({
            'status': 'success',
            'corporates': [
                {
                    'id': corporate.corporate_id,
                    'name': corporate.legal_name
                }
                for corporate in corporates
            ]
        })
    except Exception as e:
        logger.error(f"Error fetching corporates: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
        
        
@login_required
@staff_member_required
def surveys_view(request):
    """
    View for displaying all surveys
    """
    return render(request, 'website/survays/surveys.html')

@login_required
@staff_member_required
@require_http_methods(["GET"])
def surveys_api(request):
    """
    API endpoint for retrieving surveys with filtering and pagination
    """
    try:
        # Get query parameters
        search_query = request.GET.get('search', '')
        is_active = request.GET.get('is_active', None)
        start_date_order = request.GET.get('start_date_order', None)
        name_order = request.GET.get('name_order', None)
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 10))

        # Start with all surveys
        surveys = Survey.objects.all()

        # Apply filters
        if search_query:
            surveys = surveys.filter(title__icontains=search_query)
        
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            surveys = surveys.filter(is_active=is_active_bool)

        # Apply ordering
        if start_date_order:
            order_param = '-start_date' if start_date_order == 'desc' else 'start_date'
            surveys = surveys.order_by(order_param)
        
        if name_order:
            order_param = '-title' if name_order == 'desc' else 'title'
            surveys = surveys.order_by(order_param)
        
        # Default ordering if no ordering specified
        if not start_date_order and not name_order:
            surveys = surveys.order_by('-created_at')

        # Count total before pagination
        total_count = surveys.count()
        total_pages = (total_count + per_page - 1) // per_page  # Ceiling division

        # Apply pagination
        start_idx = (page - 1) * per_page
        end_idx = page * per_page
        surveys_page = surveys[start_idx:end_idx]

        # Prepare response data
        survey_data = []
        for survey in surveys_page:
            course_instance_name = None
            if survey.course_instance:
                course_name = survey.course_instance.course.name_en
                course_instance_name = f"{course_name} ({survey.course_instance.start_date.strftime('%b %d, %Y')})"
            
            survey_data.append({
                'survey_id': survey.survey_id,
                'title': survey.title,
                'course_instance_name': course_instance_name,
                'start_date': survey.start_date.isoformat() if survey.start_date else None,
                'is_active': survey.is_active,
                'created_at': survey.created_at.isoformat(),
                'updated_at': survey.updated_at.isoformat()
            })

        return JsonResponse({
            'status': 'success',
            'surveys': survey_data,
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': total_pages
        })
    except Exception as e:
        logger.error(f"Error fetching surveys: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["DELETE"])
def survey_delete_api(request, survey_id):
    """
    API endpoint for deleting a specific survey
    """
    try:
        survey = get_object_or_404(Survey, survey_id=survey_id)
        survey.delete()
        return JsonResponse({
            'status': 'success',
            'message': 'Survey deleted successfully'
        })
    except Exception as e:
        logger.error(f"Error deleting survey: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def survey_bulk_action_api(request, action):
    """
    API endpoint for performing bulk actions on surveys
    """
    try:
        data = json.loads(request.body)
        survey_ids = data.get('ids', [])
        
        if not survey_ids:
            return JsonResponse({
                'status': 'error',
                'message': 'No surveys selected'
            }, status=400)
        
        surveys = Survey.objects.filter(survey_id__in=survey_ids)
        
        if action == 'activate':
            surveys.update(is_active=True)
            message = 'Surveys activated successfully'
        elif action == 'deactivate':
            surveys.update(is_active=False)
            message = 'Surveys deactivated successfully'
        elif action == 'delete':
            surveys.delete()
            message = 'Surveys deleted successfully'
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid action'
            }, status=400)
        
        return JsonResponse({
            'status': 'success',
            'message': message
        })
    except Exception as e:
        logger.error(f"Error performing bulk action on surveys: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
        

@login_required
@staff_member_required
@require_http_methods(["GET"])
def course_instances_api(request):
    """
    API endpoint for retrieving available course instances for surveys
    """
    try:
        # Get active course instances
        course_instances = CourseInstance.objects.filter(is_active=True).order_by('-start_date')
        
        # Format response
        instances_data = []
        for instance in course_instances:
            course_name = instance.course.name_en if instance.course else "Unknown Course"
            start_date = instance.start_date.strftime("%b %d, %Y") if instance.start_date else "No date"
            
            # Format the name to include course name and date
            instance_name = f"{course_name} ({start_date})"
            
            instances_data.append({
                'id': instance.instance_id,
                'name': instance_name,
                'course_id': instance.course.course_id if instance.course else None,
                'start_date': instance.start_date.isoformat() if instance.start_date else None
            })
        
        return JsonResponse({
            'status': 'success',
            'course_instances': instances_data
        })
    except Exception as e:
        logger.error(f"Error fetching course instances: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def create_survey_api(request):
    """
    API endpoint for creating a new survey with questions or copying from existing survey
    """
    try:
        # Parse request data
        data = json.loads(request.body)
        title = data.get('title')
        course_instance_id = data.get('course_instance_id')
        questions = data.get('questions', [])
        existing_survey_id = data.get('existing_survey_id')  # New field for copying from existing survey
        
        # Validate required fields
        if not title or not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': 'Title and course instance are required'
            }, status=400)
        
        # If copying from existing survey, get questions from that survey
        if existing_survey_id:
            try:
                existing_survey = Survey.objects.get(survey_id=existing_survey_id)
                questions = existing_survey.questions or []
                if not questions:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'Selected survey has no questions'
                    }, status=400)
            except Survey.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': 'Selected survey not found'
                }, status=404)
        elif not questions:
            return JsonResponse({
                'status': 'error',
                'message': 'At least one question is required'
            }, status=400)
        
        # Get course instance
        try:
            course_instance = CourseInstance.objects.get(instance_id=course_instance_id)
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'Course instance not found'
            }, status=404)
        
        # Create survey with a transaction
        with transaction.atomic():
            # Create the survey
            survey = Survey.objects.create(
                title=title,
                course_instance=course_instance,
                is_active=True,
                start_date = course_instance.end_date
            )
            
            # Format the questions JSON
            survey_questions = []
            for i, question in enumerate(questions):
                q_data = {
                    'id': i + 1,
                    'text': question.get('text', ''),
                    'type': question.get('type', 'rating'),
                }
                
                # Add options for multiple choice questions
                if question.get('type') == 'multiple_choice' and 'options' in question:
                    q_data['options'] = []
                    for j, option_text in enumerate(question['options']):
                        q_data['options'].append({
                            'id': j + 1,
                            'text': option_text
                        })
                        
                survey_questions.append(q_data)
            
            # Save questions to survey
            survey.questions = survey_questions
            survey.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Survey created successfully',
                'survey_id': survey.survey_id
            })
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid JSON in request body'
        }, status=400)
    except Exception as e:
        logger.error(f"Error creating survey: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def view_survey_api(request, survey_id):
    """
    API endpoint for viewing a specific survey's details
    """
    try:
        survey = get_object_or_404(Survey, survey_id=survey_id)
        
        # Format response with survey details
        survey_data = {
            'survey_id': survey.survey_id,
            'title': survey.title,
            'course_instance': {
                'id': survey.course_instance.instance_id,
                'name': survey.course_instance.course.name_en if survey.course_instance.course else "Unknown Course",
                'start_date': survey.course_instance.start_date.isoformat() if survey.course_instance.start_date else None
            },
            'questions': survey.questions,
            'start_date': survey.start_date.isoformat() if survey.start_date else None,
            'is_active': survey.is_active,
            'created_at': survey.created_at.isoformat(),
            'updated_at': survey.updated_at.isoformat()
        }
        
        return JsonResponse({
            'status': 'success',
            'survey': survey_data
        })
    except Exception as e:
        logger.error(f"Error fetching survey details: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def edit_survey_api(request, survey_id):
    """
    API endpoint for editing a specific survey
    """
    try:
        survey = get_object_or_404(Survey, survey_id=survey_id)
        
        # Parse request data
        data = json.loads(request.body)
        title = data.get('title')
        course_instance_id = data.get('course_instance_id')
        questions = data.get('questions', [])
        is_active = data.get('is_active', survey.is_active)
        
        # Validate required fields
        if not title or not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': 'Title and course instance are required'
            }, status=400)
        
        if not questions:
            return JsonResponse({
                'status': 'error',
                'message': 'At least one question is required'
            }, status=400)
        
        # Get course instance
        try:
            course_instance = CourseInstance.objects.get(instance_id=course_instance_id)
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'Course instance not found'
            }, status=404)
        
        # Update survey with a transaction
        with transaction.atomic():
            # Update survey fields
            survey.title = title
            survey.course_instance = course_instance
            survey.is_active = is_active
            survey.start_date = course_instance.end_date
            
            # Format the questions JSON
            survey_questions = []
            for i, question in enumerate(questions):
                q_data = {
                    'id': i + 1,
                    'text': question.get('text', ''),
                    'type': question.get('type', 'rating'),
                }
                
                # Add options for multiple choice questions
                if question.get('type') == 'multiple_choice' and 'options' in question:
                    q_data['options'] = []
                    for j, option_text in enumerate(question['options']):
                        q_data['options'].append({
                            'id': j + 1,
                            'text': option_text
                        })
                        
                survey_questions.append(q_data)
            
            # Save updated questions to survey
            survey.questions = survey_questions
            survey.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Survey updated successfully',
                'survey_id': survey.survey_id
            })
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid JSON in request body'
        }, status=400)
    except Exception as e:
        logger.error(f"Error updating survey: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
        

@login_required
@staff_member_required
@require_http_methods(["GET", "POST", "PUT", "DELETE"])
def course_instance_api(request, instance_id=None):
    """API endpoint for managing course instances"""
    if request.method == "GET":
        if instance_id:
            instance = get_object_or_404(CourseInstance, instance_id=instance_id)
            return JsonResponse({
                'course_id': instance.course.course_id,
                'course_type': instance.course_type,
                'capacity': instance.capacity,
                'start_date': instance.start_date.strftime('%Y-%m-%d'),
                'end_date': instance.end_date.strftime('%Y-%m-%d'),
                'published': instance.published,
            })
        else:
            instances = CourseInstance.objects.select_related('course').all()
            return JsonResponse({
                'instances': [{
                    'instance_id': instance.instance_id,
                    'course_name': instance.course.name_en,
                    'course_type': instance.course_type,
                    'start_date': instance.start_date.strftime('%Y-%m-%d'),
                    'end_date': instance.end_date.strftime('%Y-%m-%d'),
                    'capacity': instance.capacity,
                    'enrolled_count': instance.reservation_set.filter(status__in=['UPCOMING', 'IN_PROGRESS']).count(),
                    'published': instance.published,
                } for instance in instances]
            })
    
    elif request.method == "POST":
        try:
            course = get_object_or_404(Course, course_id=request.POST.get('course'))
            instance = CourseInstance.objects.create(
                course=course,
                course_type=request.POST.get('course_type'),
                capacity=request.POST.get('capacity'),
                start_date=request.POST.get('start_date'),
                end_date=request.POST.get('end_date'),
                published=request.POST.get('published') == 'on',
            )
            return JsonResponse({'success': True, 'instance_id': instance.instance_id})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    elif request.method == "PUT":
        try:
            instance = get_object_or_404(CourseInstance, instance_id=instance_id)
            course = get_object_or_404(Course, course_id=request.POST.get('course'))
            
            instance.course = course
            instance.course_type = request.POST.get('course_type')
            instance.capacity = request.POST.get('capacity')
            instance.start_date = request.POST.get('start_date')
            instance.end_date = request.POST.get('end_date')
            instance.published = request.POST.get('published') == 'on'
            instance.save()
            
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)
    
    elif request.method == "DELETE":
        try:
            instance = get_object_or_404(CourseInstance, instance_id=instance_id)
            instance.delete()
            return JsonResponse({'success': True})
        except Exception as e:
            return JsonResponse({'success': False, 'error': str(e)}, status=400)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def toggle_publish_status(request, instance_id):
    """Toggle the published status of a course instance"""
    try:
        instance = get_object_or_404(CourseInstance, instance_id=instance_id)
        instance.published = not instance.published
        instance.save()
        return JsonResponse({'success': True, 'published': instance.published})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)}, status=400)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def get_existing_surveys_api(request):
    """
    API endpoint for getting existing surveys to choose from when creating new survey
    """
    try:
        # Get all active surveys
        surveys = Survey.objects.filter(is_active=True).select_related('course_instance__course').order_by('-created_at')
        
        survey_data = []
        for survey in surveys:
            course_name = survey.course_instance.course.name_en if survey.course_instance and survey.course_instance.course else "Unknown Course"
            survey_data.append({
                'survey_id': survey.survey_id,
                'title': survey.title,
                'course_name': course_name,
                'questions_count': len(survey.questions) if survey.questions else 0,
                'created_at': survey.created_at.isoformat()
            })
        
        return JsonResponse({
            'status': 'success',
            'surveys': survey_data
        })
    except Exception as e:
        logger.error(f"Error fetching existing surveys: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
