{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Questionnaires" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    .questionnaire-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .questionnaire-card:hover {
        border-left-color: #10b981;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }
    
    .tab-button {
        transition: all 0.2s ease;
    }
    
    .tab-button.active {
        background-color: rgba(16, 185, 129, 0.9) !important;
        border-color: rgba(52, 211, 153, 1) !important;
        color: white !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            {% trans "My Questionnaires" %}
        </h1>
        <p class="text-green-300 mt-2">{% trans "View and complete your course questionnaires" %}</p>
    </div>

    <!-- Tab Navigation -->
    <div class="flex border-b border-green-600/30 mb-6">
        <button id="pending-tab" class="tab-button active px-4 py-2 bg-green-800/50 text-white font-medium rounded-t-lg mr-2">
            {% trans "Pending Questionnaires" %}
            <span id="pending-count" class="ml-2 px-2 py-1 bg-green-600 text-white text-xs rounded-full">{{ pending_responses.count }}</span>
        </button>
        <button id="review-tab" class="tab-button px-4 py-2 bg-green-900/40 text-gray-300 font-medium rounded-t-lg mr-2">
            {% trans "Pending Review" %}
            <span id="review-count" class="ml-2 px-2 py-1 bg-yellow-600 text-white text-xs rounded-full">{{ pending_review_responses.count }}</span>
        </button>
        <button id="completed-tab" class="tab-button px-4 py-2 bg-green-900/40 text-gray-300 font-medium rounded-t-lg">
            {% trans "Completed" %}
            <span id="completed-count" class="ml-2 px-2 py-1 bg-gray-600 text-white text-xs rounded-full">{{ completed_responses.count }}</span>
        </button>
    </div>

    <!-- Pending Questionnaires Section -->
    <div id="pending-questionnaires" class="space-y-4">
        {% if pending_responses %}
            {% for response in pending_responses %}
                <div class="questionnaire-card bg-green-900/50 backdrop-blur-md rounded-lg border border-green-600/40 p-5 shadow-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-white">{{ response.questionnaire.title }}</h3>
                            <p class="text-green-300 mt-1">
                                {% trans "Course" %}: {{ response.course.name_en }}
                            </p>
                            <p class="text-gray-400 text-sm mt-1">
                                {% trans "Session" %}: {{ response.session_number }}
                            </p>
                            <div class="mt-3 flex items-center text-sm">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {% trans "Pending Response" %}
                                </span>
                                <span class="ml-2 text-gray-400">
                                    {{ response.questionnaire.questions|length }} {% trans "questions" %}
                                </span>
                            </div>
                        </div>
                        <a href="{% url 'website:take_questionnaire' questionnaire_id=response.questionnaire.questionnaire_id %}" 
                           class="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg shadow-md transition-all transform hover:scale-105">
                            {% trans "Complete Questionnaire" %}
                        </a>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-10 bg-green-900/30 backdrop-blur-sm rounded-lg border border-green-600/20 shadow-lg">
                <svg class="mx-auto h-12 w-12 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-white">{% trans "All Caught Up!" %}</h3>
                <p class="mt-2 text-gray-300">{% trans "You have no pending questionnaires to complete." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Pending Trainer Review Section -->
    <div id="review-questionnaires" class="space-y-4 hidden">
        {% if pending_review_responses %}
            {% for response in pending_review_responses %}
                <div class="questionnaire-card bg-yellow-900/50 backdrop-blur-md rounded-lg border border-yellow-600/40 p-5 shadow-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-white">{{ response.questionnaire.title }}</h3>
                            <p class="text-yellow-300 mt-1">
                                {% trans "Course" %}: {{ response.course.name_en }}
                            </p>
                            <p class="text-gray-300 text-sm mt-1">
                                {% trans "Session" %}: {{ response.session_number }}
                            </p>
                            <div class="mt-3 flex items-center text-sm">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/30 text-yellow-300">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    {% trans "Pending Trainer Review" %}
                                </span>
                                <span class="ml-2 text-gray-400">
                                    {{ response.questionnaire.questions|length }} {% trans "questions" %}
                                </span>
                            </div>
                            
                            <!-- Score information -->
                            <div class="mt-3 border-t border-yellow-600/30 pt-3">
                                {% if response.status == 'COMPLETED_TRAINER_REVIEWED' %}
                                <div class="flex items-center justify-between">
                                    <span class="text-white">{% trans "Your Score" %}:</span>
                                    <span class="text-yellow-300 font-semibold">{{ response.total_score }} / {{ response.max_total_score }}</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                                    <div class="bg-yellow-500 h-2.5 rounded-full" style="width: {% widthratio response.total_score response.max_total_score 100 %}%"></div>
                                </div>
                                {% else %}
                                <div class="text-center py-2">
                                    <span class="text-yellow-300">{% trans "Score will be available after trainer review" %}</span>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="text-yellow-300 text-sm bg-yellow-900/60 p-3 rounded-lg border border-yellow-600/40">
                            {% trans "Submitted on" %}: {{ response.completed_at|date:"F j, Y" }}
                        </div>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-10 bg-yellow-900/30 backdrop-blur-sm rounded-lg border border-yellow-600/20 shadow-lg">
                <svg class="mx-auto h-12 w-12 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-white">{% trans "No Pending Reviews" %}</h3>
                <p class="mt-2 text-gray-300">{% trans "You don't have any questionnaires waiting for trainer review." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Completed Questionnaires Section (Hidden by Default) -->
    <div id="completed-questionnaires" class="space-y-4 hidden">
        {% if completed_responses %}
            {% for response in completed_responses %}
                <div class="questionnaire-card bg-gray-800/70 backdrop-blur-md rounded-lg border border-gray-700 p-5 shadow-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-white">{{ response.questionnaire.title }}</h3>
                            <p class="text-green-300 mt-1">
                                {% trans "Course" %}: {{ response.course.name_en }}
                            </p>
                            <div class="mt-3 flex items-center text-sm">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {% trans "Completed" %}
                                </span>
                                <span class="ml-4 text-gray-400">
                                    {% trans "Completed on" %}: {{ response.completed_at|date:"F j, Y" }}
                                </span>
                            </div>
                            
                            <!-- Add score information for completed responses -->
                            {% if response.max_total_score > 0 %}
                            <div class="mt-3 border-t border-gray-700 pt-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-white">{% trans "Score" %}:</span>
                                    <span class="text-green-300 font-semibold">{{ response.total_score }} / {{ response.max_total_score }}</span>
                                </div>
                                <div class="w-full bg-gray-700 rounded-full h-2.5 mt-2">
                                    <div class="bg-green-500 h-2.5 rounded-full" style="width: {% widthratio response.total_score response.max_total_score 100 %}%"></div>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                        <span class="text-gray-400 text-sm">
                            {{ response.questionnaire.questions|length }} {% trans "questions answered" %}
                        </span>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-10 bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 shadow-lg">
                <svg class="mx-auto h-12 w-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-white">{% trans "No Completed Questionnaires" %}</h3>
                <p class="mt-2 text-gray-300">{% trans "You haven't completed any questionnaires yet." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Return Button -->
    <div class="mt-8 flex justify-center">
        <a href="{% url 'website:corporate_user_reservations' %}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md shadow transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            {% trans "Back to My Reservations" %}
        </a>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching functionality
        const pendingTab = document.getElementById('pending-tab');
        const reviewTab = document.getElementById('review-tab');
        const completedTab = document.getElementById('completed-tab');
        
        const pendingQuestionnaires = document.getElementById('pending-questionnaires');
        const reviewQuestionnaires = document.getElementById('review-questionnaires');
        const completedQuestionnaires = document.getElementById('completed-questionnaires');
        
        // Function to reset all tabs to inactive state
        function resetTabs() {
            [pendingTab, reviewTab, completedTab].forEach(tab => {
                tab.classList.remove('active', 'bg-green-800/50');
                tab.classList.add('bg-green-900/40', 'text-gray-300');
            });
            
            [pendingQuestionnaires, reviewQuestionnaires, completedQuestionnaires].forEach(section => {
                section.classList.add('hidden');
            });
        }
        
        pendingTab.addEventListener('click', function() {
            resetTabs();
            pendingTab.classList.add('active', 'bg-green-800/50');
            pendingTab.classList.remove('bg-green-900/40', 'text-gray-300');
            pendingQuestionnaires.classList.remove('hidden');
        });
        
        reviewTab.addEventListener('click', function() {
            resetTabs();
            reviewTab.classList.add('active', 'bg-green-800/50');
            reviewTab.classList.remove('bg-green-900/40', 'text-gray-300');
            reviewQuestionnaires.classList.remove('hidden');
        });
        
        completedTab.addEventListener('click', function() {
            resetTabs();
            completedTab.classList.add('active', 'bg-green-800/50');
            completedTab.classList.remove('bg-green-900/40', 'text-gray-300');
            completedQuestionnaires.classList.remove('hidden');
        });
    });
</script>
{% endblock %} 