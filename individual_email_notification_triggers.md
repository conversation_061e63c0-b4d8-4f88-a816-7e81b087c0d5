# Email Notification Triggers in individual_views.py

Based on the analysis of the individual_views.py file, the system uses an in-app notification system rather than direct email notifications. These notifications are created using the `Notification` model, which stores messages that are displayed to users within the application interface.

## Notification Model

The notification model is defined as:

```python
class Notification(models.Model):
    notification_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    message = models.TextField(_('Message'))
    status = models.BooleanField(_('Read Status'), default=False)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    delivered_at = models.DateTimeField(_('Delivered At'), null=True, blank=True)
```

## Notification Triggers in individual_views.py

### 1. Reservation Cancellation Notification
**Function**: `user_reservations_view` (Line ~445)
- **Sender**: System
- **Receiver**: Individual User
- **Topic**: Reservation Cancellation Confirmation
- **Message**: "Your reservation for course: {course_name} ({start_date}) has been cancelled."
- **Context**: When an individual user cancels their own reservation before the cancellation deadline.

## Implementation Recommendations

The individual_views.py file contains only one notification trigger, which is sent when a user cancels their own reservation. However, there are several other events in this file that could benefit from notifications:

### Potential Additional Notification Triggers

1. **Questionnaire Submission Notification**
   - When a user submits a questionnaire, a notification could be sent to the trainer for review
   - Function: `individual_create_questionnaire_response_api`

2. **Questionnaire Review Notification**
   - When a trainer reviews a questionnaire, a notification could be sent to the user
   - This would need to be implemented in a trainer view function

3. **Survey Completion Reminder**
   - Automated notifications to remind users about pending surveys
   - Could be triggered by a scheduled task that checks for pending surveys

Similar to the recommendation in the corporate views analysis, these notifications could be extended to include email notifications. Here's how that could be implemented:

1. Create an email notification utility function in a separate file (e.g., `email_utils.py`):

```python
from django.core.mail import EmailMessage
from django.template.loader import render_to_string
from django.conf import settings

def send_notification_email(recipient_email, subject, template, context):
    """
    Send email notification to user.
    
    Args:
        recipient_email: Email address of the recipient
        subject: Email subject
        template: Path to the email template
        context: Dictionary with template variables
    """
    html_content = render_to_string(template, context)
    email = EmailMessage(
        subject=subject,
        body=html_content,
        from_email=settings.DEFAULT_FROM_EMAIL,
        to=[recipient_email]
    )
    email.content_subtype = "html"
    return email.send()
```

2. Modify the notification creation to also send emails:

```python
# Example for reservation cancellation notification
Notification.objects.create(
    user=request.user,
    message=_('Your reservation for course: {} ({}) has been cancelled.').format(
        reservation.course_instance.course.name_en,
        reservation.course_instance.start_date.strftime('%Y-%m-%d')
    ),
    delivered_at=timezone.now()
)

# Add email notification
send_notification_email(
    recipient_email=request.user.email,
    subject=_('Reservation Cancellation Confirmation'),
    template='emails/reservation_cancelled.html',
    context={
        'user_name': request.user.get_full_name(),
        'course_name': reservation.course_instance.course.name_en,
        'start_date': reservation.course_instance.start_date.strftime('%Y-%m-%d')
    }
)
```

This implementation would allow the system to send both in-app notifications and email notifications for the same events. 