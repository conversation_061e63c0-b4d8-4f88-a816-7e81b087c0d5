{% load i18n %}

<!-- Model Modal -->
<div id="modelModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-md mx-4">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-white/10">
            <h3 id="modelModalTitle" class="text-lg font-medium text-white">{% trans "Add Model" %}</h3>
            <button onclick="closeModelModal()" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Modal Body -->
        <div class="p-4">
            <form id="modelForm" class="space-y-4">
                <div>
                    <label for="modelBrand" class="block text-sm font-medium text-white/70 mb-1">{% trans "Brand" %} *</label>
                    <select name="brand" id="modelBrand" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="">{% trans "Select Brand" %}</option>
                        {% for brand in brands %}
                            <option value="{{ brand.brand_id }}">{{ brand.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label for="modelName" class="block text-sm font-medium text-white/70 mb-1">{% trans "Model Name" %} *</label>
                    <input type="text" name="name" id="modelName" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                </div>
                <div>
                    <label for="modelStock" class="block text-sm font-medium text-white/70 mb-1">{% trans "Initial Stock" %}</label>
                    <input type="number" name="stock" id="modelStock" min="0" value="1"
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end p-4 border-t border-white/10 space-x-3">
            <button onclick="closeModelModal()" 
                class="px-3 py-1.5 text-sm font-medium text-white/70 hover:text-white bg-white/5 hover:bg-white/10 rounded-md transition-colors">
                {% trans "Cancel" %}
            </button>
            <button onclick="saveModel()" 
                class="px-3 py-1.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors">
                {% trans "Save Model" %}
            </button>
        </div>
    </div>
</div>

<script>
    let isEditingModel = false;
    let currentModelId = null;

    function openModelModal(editing = false, modelId = null) {
        try {
            isEditingModel = editing;
            currentModelId = modelId;
            document.getElementById('modelModal').classList.remove('hidden');
            
            if (editing) {
                document.getElementById('modelModalTitle').textContent = '{% trans "Edit Model" %}';
                loadModelData(modelId);
            } else {
                document.getElementById('modelModalTitle').textContent = '{% trans "Add Model" %}';
                document.getElementById('modelForm').reset();
                // Set default stock value to 1 for new models
                document.getElementById('modelStock').value = '1';
            }
        } catch (error) {
            console.error('Error in openModelModal:', error);
            alert('{% trans "An error occurred while opening the modal" %}');
        }
    }

    function closeModelModal() {
        document.getElementById('modelModal').classList.add('hidden');
        document.getElementById('modelForm').reset();
        isEditingModel = false;
        currentModelId = null;
    }

    async function loadModelData(modelId) {
        try {
            // Get the current language prefix from the URL or default to '/en'
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const url = `/${languagePrefix}/api/models/${modelId}/`;
            console.log('Loading model data from:', url);
            
            const response = await fetch(url, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                credentials: 'include'
            });
            
            if (!response.ok) {
                throw new Error('{% trans "Failed to fetch model details" %}');
            }
            
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            const data = JSON.parse(responseText);
            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to load model details" %}');
            }

            const form = document.getElementById('modelForm');
            form.querySelector('[name="brand"]').value = data.model.brand;
            form.querySelector('[name="name"]').value = data.model.name;
            form.querySelector('[name="stock"]').value = data.model.stock;
        } catch (error) {
            console.error('Error:', error);
            alert('{% trans "An error occurred while fetching model details" %}');
        }
    }

    async function saveModel() {
        try {
            const form = document.getElementById('modelForm');
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            const formData = {
                name: form.querySelector('[name="name"]').value.trim(),
                brand: form.querySelector('[name="brand"]').value,
                stock: parseInt(form.querySelector('[name="stock"]').value) || 1
            };
            
            console.log('Sending model data:', formData);
            const csrfToken = getCookie('csrftoken');
            console.log('CSRF Token:', csrfToken);

            // Get the current language prefix from the URL or default to '/en'
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            
            // Determine URL and method based on whether we're editing or creating
            const method = isEditingModel ? 'PUT' : 'POST';
            const url = isEditingModel 
                ? `/${languagePrefix}/api/models/${currentModelId}/`
                : `/${languagePrefix}/api/models/`;
            
            console.log(`Sending ${method} request to:`, url);
            
            const response = await fetch(url, {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                    'Accept': 'application/json'
                },
                body: JSON.stringify(formData),
                credentials: 'include'
            });

            console.log('Response status:', response.status);
            const responseText = await response.text();
            console.log('Raw response:', responseText);
            
            let data;
            try {
                data = JSON.parse(responseText);
            } catch (e) {
                console.error('Error parsing JSON response:', e);
                throw new Error('Invalid response from server');
            }
            
            console.log('Parsed response data:', data);

            if (!response.ok) {
                throw new Error(data.message || '{% trans "Failed to save model" %}');
            }

            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to save model" %}');
            }

            // Success - close modal and reload page
            console.log('Model saved successfully');
            closeModelModal();
            window.location.reload();
        } catch (error) {
            console.error('Error saving model:', error);
            alert(error.message || '{% trans "An error occurred while saving the model" %}');
        }
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script> 