{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Corporate Courses" %}{% endblock %}

{% block content %}
<div class="mx-auto py-5">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
            </svg>
            {% trans "My Corporate Courses" %}
        </h1>
        <div class="flex items-center space-x-4">
            <div class="flex bg-white/5 rounded-md p-1">
                <button class="group relative p-2 rounded text-white bg-white/10 view-toggle active" data-view="grid" title="{% trans 'Grid View' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2V6zM14 6a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V6zM4 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2H6a2 2 0 01-2-2v-2zM14 16a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"/>
                    </svg>
                </button>
                <button class="group relative p-2 rounded text-white/70 hover:text-white hover:bg-white/10 view-toggle" data-view="table" title="{% trans 'Table View' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M3 14h18m-9-4v8m-7 0h14a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"/>
                    </svg>
                </button>
            </div>
            <a href="{% url 'website:corporate_user_calendar_view' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                {% trans "View Calendar" %}
            </a>
        </div>
    </div>

    <!-- Course Grid View -->
    <div id="grid-view" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {% if courses %}
            {% for course in courses %}
            <div class="bg-white/5 backdrop-blur-md rounded-lg overflow-hidden border border-white/10 hover:border-primary transition-colors">
                <div class="relative">
                    <div class="w-full h-48 bg-white/5 flex items-center justify-center overflow-hidden">
                        {% if course.image %}
                            <img src="{{ course.image.url }}" alt="{{ course.name_en }}" class="w-full h-full object-cover">
                        {% elif course.icon_svg %}
                            {{ course.icon_svg|safe }}
                        {% else %}
                            <svg class="w-24 h-24 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                            </svg>
                        {% endif %}
                    </div>
                    <div class="absolute top-4 right-4 bg-primary/90 text-white text-xs font-semibold px-2 py-1 rounded">
                        {{ course.get_category_display }}
                    </div>
                </div>
                <div class="p-6">
                    <h3 class="text-lg font-semibold text-white mb-2">{{ course.name_en }}</h3>
                    <p class="text-white/70 text-sm mb-4">{{ course.description_en|truncatewords:20 }}</p>
                    
                    <div class="grid grid-cols-2 gap-3 mb-6">
                        <div>
                            <p class="text-white/50 text-xs mb-1">{% trans "Duration" %}</p>
                            <p class="text-white text-sm">{{ course.duration_hours }} {% trans "hours" %}</p>
                        </div>
                        <div>
                            <p class="text-white/50 text-xs mb-1">{% trans "Format" %}</p>
                            <p class="text-white text-sm">{{ course.get_location_display }}</p>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex justify-between items-center">
                        <a href="{% url 'website:course_detail' course.course_id %}" class="text-primary hover:text-primary/80">
                            {% trans "View Details" %}
                        </a>
                        <span class="inline-flex items-center px-3 py-1 rounded bg-green-500/20 text-green-400 text-sm">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            {% trans "Enrolled" %}
                        </span>
                    </div>
                </div>
            </div>
            {% endfor %}
        {% else %}
            <div class="col-span-full text-center py-10">
                <svg class="w-16 h-16 mx-auto text-white/20 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
                <h3 class="text-xl font-semibold text-white mb-2">{% trans "No courses enrolled yet" %}</h3>
                <p class="text-white/60 max-w-md mx-auto mb-6">{% trans "You haven't enrolled in any corporate courses yet. Please contact your corporate administrator for enrollment." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Table View (Hidden by Default) -->
    <div id="table-view" class="hidden">
        <div class="bg-white/5 backdrop-blur-md rounded-lg overflow-hidden border border-white/10">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/10">
                    <thead class="bg-white/5">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Course" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Category" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Format" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Duration" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                {% trans "Status" %}
                            </th>
                            <th scope="col" class="relative px-6 py-3">
                                <span class="sr-only">{% trans "Actions" %}</span>
                            </th>
                        </tr>
                    </thead>
                    <tbody class="divide-y divide-white/10">
                        {% for course in courses %}
                        <tr class="hover:bg-white/10">
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="flex-shrink-0 h-10 w-10 bg-white/5 rounded-lg flex items-center justify-center">
                                        {% if course.icon_svg %}
                                            {{ course.icon_svg|safe }}
                                        {% else %}
                                            <svg class="h-6 w-6 text-white/20" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                            </svg>
                                        {% endif %}
                                    </div>
                                    <div class="ml-4">
                                        <div class="text-sm font-medium text-white">{{ course.name_en }}</div>
                                        <div class="text-sm text-white/70">{{ course.description_en|truncatechars:50 }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-primary/30 text-white">
                                    {{ course.get_category_display }}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-white">{{ course.get_location_display }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-white">{{ course.duration_hours }} {% trans "hours" %}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                    {% trans "Enrolled" %}
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                <a href="{% url 'website:course_detail' course.course_id %}" class="text-primary hover:text-primary/80">{% trans "View" %}</a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="px-6 py-8 text-center text-white/60">
                                {% trans "You haven't enrolled in any corporate courses yet." %}
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle view toggling
        const viewToggles = document.querySelectorAll('.view-toggle');
        const gridView = document.getElementById('grid-view');
        const tableView = document.getElementById('table-view');
        
        viewToggles.forEach(toggle => {
            toggle.addEventListener('click', function() {
                // Update toggle buttons
                viewToggles.forEach(btn => {
                    btn.classList.remove('active', 'bg-white/10', 'text-white');
                    btn.classList.add('text-white/70');
                });
                
                this.classList.add('active', 'bg-white/10', 'text-white');
                this.classList.remove('text-white/70');
                
                // Show the selected view
                const viewType = this.getAttribute('data-view');
                if (viewType === 'grid') {
                    gridView.classList.remove('hidden');
                    tableView.classList.add('hidden');
                } else {
                    gridView.classList.add('hidden');
                    tableView.classList.remove('hidden');
                }
            });
        });
    });
</script>
{% endblock %} 