# Generated manually to fix database schema mismatch

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0001_initial'),
    ]

    operations = [
        # Drop the score column if it exists
        migrations.RunSQL(
            "ALTER TABLE website_questionnaire DROP COLUMN IF EXISTS score;",
            reverse_sql="ALTER TABLE website_questionnaire ADD COLUMN score INTEGER DEFAULT 0;"
        ),
        # Rename user_id to created_by_id if user_id exists
        migrations.RunSQL(
            """
            DO $$
            BEGIN
                IF EXISTS (SELECT 1 FROM information_schema.columns 
                          WHERE table_name='website_questionnaire' AND column_name='user_id') THEN
                    ALTER TABLE website_questionnaire RENAME COLUMN user_id TO created_by_id;
                END IF;
            END $$;
            """,
            reverse_sql="ALTER TABLE website_questionnaire RENAME COLUMN created_by_id TO user_id;"
        ),
    ] 