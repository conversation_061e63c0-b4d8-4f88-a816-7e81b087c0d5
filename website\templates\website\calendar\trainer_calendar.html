{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Calendar" %}{% endblock %}

{% block extra_head %}
<script src="https://cdn.jsdelivr.net/npm/flowbite@1.8.1/dist/flowbite.min.js"></script>
{% endblock %}

{% block content %}
<div class="px-3 py-8">
    <!-- Page Header -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            {% trans "Calendar" %}
        </h1>
    </div>

    <!-- Main Content Area -->
    <div class="grid grid-cols-1 lg:grid-cols-4 gap-1">
        <!-- Calendar (Left Column - Takes 3 columns on large screens) -->
        <div class="lg:col-span-3">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
                <!-- Calendar Header -->
                <div class="px-3 py-3 flex items-center justify-between border-b border-white/10">
                    <div class="flex items-center">
                        <button id="trainerPrevBtn" class="p-2 hover:bg-white/5 rounded-md">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <h2 id="trainerCurrentMonthYear" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                        <button id="trainerNextBtn" class="p-2 hover:bg-white/5 rounded-md">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div class="flex rounded-md bg-gray-700 p-1">
                            <button id="trainerViewMonth" class="px-3 py-1 text-sm rounded-md bg-primary text-white">Month</button>
                            <button id="trainerViewWeek" class="px-3 py-1 text-sm rounded-md text-gray-300">Week</button>
                            <button id="trainerViewDay" class="px-3 py-1 text-sm rounded-md text-gray-300">Day</button>
                        </div>
                        <button id="trainerViewToday" class="text-sm text-white hover:text-primary ml-2">{% trans "Today" %}</button>
                    </div>
                </div>
                
                <!-- Week Headers -->
                <div id="trainerWeekHeaders" class="grid grid-cols-7 border-b border-white/10">
                    <div class="text-center text-white py-2 font-medium">{% trans "Sun" %}</div>
                    <div class="text-center text-white py-2 font-medium">{% trans "Mon" %}</div>
                    <div class="text-center text-white py-2 font-medium">{% trans "Tue" %}</div>
                    <div class="text-center text-white py-2 font-medium">{% trans "Wed" %}</div>
                    <div class="text-center text-white py-2 font-medium">{% trans "Thu" %}</div>
                    <div class="text-center text-white py-2 font-medium">{% trans "Fri" %}</div>
                    <div class="text-center text-white py-2 font-medium">{% trans "Sat" %}</div>
                </div>
                
                <!-- Calendar Grid -->
                <div id="trainerCalendarGrid" class="grid grid-cols-7 text-sm text-white">
                    <!-- Calendar cells will be generated via JavaScript -->
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4">
            <!-- Filters Section -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden p-4">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Trainer Calendar Filters" %}</h3>
                <div class="space-y-3">
                    <label class="flex items-center">
                        <input type="checkbox" id="sessionsFilter" checked class="form-checkbox text-green-600 h-4 w-4 rounded">
                        <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" id="eventsFilter" class="form-checkbox text-blue-500 h-4 w-4 rounded">
                        <span class="ml-2 text-sm text-white">{% trans "Events" %}</span>
                    </label>
                </div>
            </div>

            <!-- Upcoming Events Section -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden p-4">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Trainer Events" %}</h3>
                <div id="trainerUpcomingEvents" class="space-y-4">
                    <!-- Upcoming events will be populated via JavaScript -->
                        </div>
                        </div>
                    </div>
                </div>
            </div>
{% endblock %}
<style>
.form-checkbox {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}
.form-checkbox:checked {
    border-color: transparent;
    background-color: currentColor;
}

/* Timeline styling */
.timeline-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}
.timeline-row {
    display: flex;
    align-items: center;
}
.timeline-label {
    width: 200px;
    padding-right: 15px;
    text-align: right;
    font-size: 14px;
}
.timeline-track {
    flex: 1;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.05);
    position: relative;
    border-radius: 4px;
}
.timeline-event {
    position: absolute;
    height: 100%;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
}
.event-session {
    background-color: rgba(79, 70, 229, 0.6);
}
.event-external {
    background-color: rgba(16, 185, 129, 0.6);
}
.event-conflict {
    background-color: rgba(239, 68, 68, 0.6);
}
.timeline-hours {
    display: flex;
    margin-left: 200px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 5px;
}
.timeline-hour {
    flex: 1;
    text-align: center;
    position: relative;
}
.timeline-hour:before {
    content: '';
    position: absolute;
    top: 16px;
    left: 50%;
    height: 5px;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Dropdown menu styles */
select {
    z-index: 999 !important;
    position: relative;
    appearance: auto !important;
    -webkit-appearance: auto !important;
    -moz-appearance: auto !important;
}

select option {
    background-color: #1e293b;
    color: white;
    padding: 8px;
}

/* Position the dropdown container relatively */
.room-selector, .trainer-selector, .equipment-selector {
    position: relative;
    z-index: 30;
}

/* Fix for dropdown visibility */
#roomSelect, #trainerSelect, #equipmentSelect {
    overflow: visible !important;
}

/* Ensure dropdown options are visible */
select:focus {
    outline: 2px solid #4f46e5;
}

/* Fix for Flowbite dropdown issues */
.flowbite-dropdown {
    z-index: 1000 !important;
}

/* Additional fix for dropdown menus */
select {
    position: relative !important;
    z-index: 9999 !important;
}

select option {
    position: relative !important;
    z-index: 10000 !important;
    background-color: #1e293b !important;
    color: white !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
    select {
        -moz-appearance: menulist !important;
    }
}

/* Fix for Chrome */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select {
        -webkit-appearance: menulist !important;
    }
}
</style>

{% block extra_js %}
<script>
// Initialize calendar state
window.trainerCalendarState = {
    currentDate: new Date(),
    currentView: 'month',
    allEvents: [],
    showSessions: true,
    showEvents: false
};

// DOM Elements
const trainerCurrentMonthYear = document.getElementById('trainerCurrentMonthYear');
const trainerPrevBtn = document.getElementById('trainerPrevBtn');
const trainerNextBtn = document.getElementById('trainerNextBtn');
const trainerViewMonth = document.getElementById('trainerViewMonth');
const trainerViewWeek = document.getElementById('trainerViewWeek');
const trainerViewDay = document.getElementById('trainerViewDay');
const trainerViewToday = document.getElementById('trainerViewToday');
const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
const trainerUpcomingEvents = document.getElementById('trainerUpcomingEvents');
const sessionsFilter = document.getElementById('sessionsFilter');
const eventsFilter = document.getElementById('eventsFilter');

// Helper functions
function formatDate(date) {
    return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
}

function formatTime(date) {
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' });
}

// Filter events based on type
function filterEvents(events) {
    const { showSessions, showEvents } = window.trainerCalendarState;
    return events.filter(event => {
        if (event.type === 'session' && showSessions) {
            return true;
        }
        if (event.type === 'event' && showEvents) {
            return true;
        }
        return false;
    });
}

// Render calendar
function renderTrainerCalendar(month, year, view, events) {
    // Clear the grid
    trainerCalendarGrid.innerHTML = '';
    
    const { currentView } = window.trainerCalendarState;
    
    // Set proper grid class based on view
    if (currentView === 'month') {
        // Reset the grid to the default grid layout for month view
        trainerCalendarGrid.className = 'grid grid-cols-7 text-sm text-white';
        trainerCalendarGrid.style.gridTemplateColumns = '';
        trainerCalendarGrid.style.marginTop = '0';
        trainerWeekHeaders.style.display = 'grid';
    } else if (currentView === 'week') {
        // For week view
        trainerCalendarGrid.className = 'grid text-sm text-white';
        trainerCalendarGrid.style.gridTemplateColumns = 'auto repeat(7, 1fr)';
        trainerCalendarGrid.style.marginTop = '0';
        trainerWeekHeaders.style.display = 'none';
    } else if (currentView === 'day') {
        // For day view
        trainerCalendarGrid.className = 'grid text-sm text-white';
        trainerCalendarGrid.style.gridTemplateColumns = 'auto 1fr';
        trainerCalendarGrid.style.marginTop = '0';
        trainerWeekHeaders.style.display = 'none';
    }
    
    if (currentView === 'month') {
        renderMonthView(month, year, events);
    } else if (currentView === 'week') {
        renderWeekView(events);
    } else if (currentView === 'day') {
        renderDayView(events);
    }
}

// Month view rendering
function renderMonthView(month, year, events) {
    const currentDate = new Date(year, month);
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const daysInMonth = lastDay.getDate();
    
    // Update header
    trainerCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    
    // Reset grid to default month view
    trainerCalendarGrid.className = 'grid grid-cols-7 text-sm text-white';
    trainerCalendarGrid.style.gridTemplateColumns = '';
    trainerWeekHeaders.style.display = 'grid';
    
    // Get the day of week the first day falls on (0 = Sunday, 6 = Saturday)
    let dayOfWeek = firstDay.getDay();
    
    // Show previous month days
    const daysFromPrevMonth = dayOfWeek;
    if (daysFromPrevMonth > 0) {
        const prevMonth = new Date(year, month, 0);
        const prevMonthDays = prevMonth.getDate();
        
        for (let i = prevMonthDays - daysFromPrevMonth + 1; i <= prevMonthDays; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'h-24 p-1 border border-white/10 relative text-white/30';
            
            // Add date number
            const dayNumber = document.createElement('div');
            dayNumber.className = 'text-sm';
            dayNumber.textContent = i;
            emptyCell.appendChild(dayNumber);
            
            trainerCalendarGrid.appendChild(emptyCell);
        }
    }
    
    // Fill current month days
    const today = new Date();
    for (let i = 1; i <= daysInMonth; i++) {
        const dayCell = document.createElement('div');
        dayCell.className = 'h-24 p-1 border border-white/10 relative text-white';
        
        // Add day number
        const dayNumber = document.createElement('div');
        dayNumber.className = 'text-sm';
        dayNumber.textContent = i;
        dayCell.appendChild(dayNumber);
        
        // Check if this is today
        const isToday = today.getFullYear() === year && today.getMonth() === month && today.getDate() === i;
        if (isToday) {
            dayCell.classList.add('bg-white/10');
        }
        
        // Add events for this day
        const dayDate = new Date(year, month, i);
        const dayEvents = events.filter(event => {
            const eventStart = new Date(event.start);
            return eventStart.getFullYear() === year && 
                   eventStart.getMonth() === month && 
                   eventStart.getDate() === i;
        });
        
        if (dayEvents.length > 0) {
            const eventsContainer = document.createElement('div');
            eventsContainer.className = 'mt-1 space-y-1';
        
        dayEvents.forEach(event => {
            const eventDiv = document.createElement('div');
            const eventStart = new Date(event.start);
            
            // Set correct background color based on event type
                let bgColorClass = 'bg-green-600/80';
            if (event.type === 'event') {
                    bgColorClass = 'bg-blue-600/80';
            }
            
                // Format event entry
                eventDiv.className = `px-1 py-0.5 text-xs rounded ${bgColorClass} text-white truncate`;
            eventDiv.textContent = `${formatTime(eventStart)} ${event.title}`;
                eventDiv.title = event.title;
            
                eventsContainer.appendChild(eventDiv);
        });
            
            dayCell.appendChild(eventsContainer);
        }
        
        trainerCalendarGrid.appendChild(dayCell);
        dayOfWeek = (dayOfWeek + 1) % 7;
    }
    
    // Fill trailing days from next month
    const remainingCells = 7 - dayOfWeek;
    if (remainingCells < 7) {
        for (let i = 1; i <= remainingCells; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'h-24 p-1 border border-white/10 relative text-white/30';
            
            // Add date number
            const dayNumber = document.createElement('div');
            dayNumber.className = 'text-sm';
            dayNumber.textContent = i;
            emptyCell.appendChild(dayNumber);
            
            trainerCalendarGrid.appendChild(emptyCell);
        }
    }
}

// Week view rendering
function renderWeekView(events) {
    const { currentDate } = window.trainerCalendarState;
    
    // Get the current date and find the start of the week (Sunday)
    const currentDay = currentDate.getDay(); // 0 for Sunday, 1 for Monday, etc.
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - currentDay);
    
    // Update header to show week range
    const endOfWeek = new Date(startOfWeek);
    endOfWeek.setDate(startOfWeek.getDate() + 6);
    
    const startMonth = startOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const endMonth = endOfWeek.toLocaleDateString('en-US', { month: 'short' });
    const startDay = startOfWeek.getDate();
    const endDay = endOfWeek.getDate();
    const year = endOfWeek.getFullYear();
    
    let headerText = '';
    if (startMonth === endMonth) {
        headerText = `${startMonth} ${startDay} - ${endDay}, ${year}`;
    } else {
        headerText = `${startMonth} ${startDay} - ${endMonth} ${endDay}, ${year}`;
    }
    trainerCurrentMonthYear.textContent = headerText;
    
    // Clear grid and set layout for week view
    trainerCalendarGrid.innerHTML = '';
    trainerCalendarGrid.className = 'grid text-sm text-white';
    trainerCalendarGrid.style.gridTemplateColumns = 'auto repeat(7, 1fr)';
    
    // Create header row with time column + days
    const headerRow = document.createElement('div');
    headerRow.className = 'contents';
    
    // Time column header
    const timeHeader = document.createElement('div');
    timeHeader.className = 'py-2 px-3 font-medium bg-white/5 border-b border-white/10 text-center';
    timeHeader.textContent = 'Time';
    headerRow.appendChild(timeHeader);
    
    // Day column headers
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    for (let i = 0; i < 7; i++) {
        const date = new Date(startOfWeek);
        date.setDate(startOfWeek.getDate() + i);
        
        const dayHeader = document.createElement('div');
        dayHeader.className = 'py-2 text-center font-medium bg-white/5 border-b border-white/10';
        
        // Highlight today's date
        const today = new Date();
        const isToday = date.getDate() === today.getDate() && 
                      date.getMonth() === today.getMonth() && 
                      date.getFullYear() === today.getFullYear();
        
        if (isToday) {
            dayHeader.classList.add('bg-white/10');
        }
        
        // Format: Day name (3 letters) + date
        dayHeader.innerHTML = `
            <div>${days[i]}</div>
            <div>${date.getDate()} ${date.toLocaleString('default', { month: 'short' })}</div>
        `;
        
        headerRow.appendChild(dayHeader);
    }
    
    trainerCalendarGrid.appendChild(headerRow);
    
    // Create time slots (8 AM to 8 PM)
    const hours = ['8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM', '4 PM', '5 PM', '6 PM', '7 PM', '8 PM'];
    
    // Create a row for each hour
    hours.forEach(hour => {
        const hourRow = document.createElement('div');
        hourRow.className = 'contents';
        
        // Time cell
        const timeCell = document.createElement('div');
        timeCell.className = 'py-2 px-3 font-medium border-b border-white/10';
        timeCell.textContent = hour;
        hourRow.appendChild(timeCell);
        
        // Day cells for this hour
        for (let i = 0; i < 7; i++) {
            const date = new Date(startOfWeek);
            date.setDate(startOfWeek.getDate() + i);
            
            const dayCell = document.createElement('div');
            dayCell.className = 'min-h-[60px] border-b border-l border-white/10 relative';
            dayCell.setAttribute('data-date', date.toISOString().split('T')[0]);
            dayCell.setAttribute('data-hour', hour);
            
            // Check if this is today and highlight it
            const today = new Date();
            const isToday = date.getDate() === today.getDate() && 
                          date.getMonth() === today.getMonth() && 
                          date.getFullYear() === today.getFullYear();
            
            if (isToday) {
                dayCell.classList.add('bg-white/5');
            }
            
            hourRow.appendChild(dayCell);
        }
        
        trainerCalendarGrid.appendChild(hourRow);
    });
    
    // Group events by day for efficient placement
    const eventsByDay = {};
    
    // Preprocess events to organize them by day
    events.forEach(event => {
                const eventStart = new Date(event.start);
        const eventEnd = new Date(event.end);
        
        // Check if event is in the current week view
        const eventDate = new Date(eventStart.getFullYear(), eventStart.getMonth(), eventStart.getDate());
        const firstDateOfWeek = new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate());
        const lastDateOfWeek = new Date(firstDateOfWeek);
        lastDateOfWeek.setDate(lastDateOfWeek.getDate() + 6);
        
        if (eventDate >= firstDateOfWeek && eventDate <= lastDateOfWeek) {
            // Format date for indexing
            const dateKey = eventDate.toISOString().split('T')[0];
            
            // Initialize day if not exists
            if (!eventsByDay[dateKey]) {
                eventsByDay[dateKey] = [];
            }
            
            // Add event to the day
            eventsByDay[dateKey].push({
                event: event,
                startTime: eventStart,
                endTime: eventEnd
            });
        }
    });
    
    // Place events in the calendar
    for (const dateKey in eventsByDay) {
        const dayEvents = eventsByDay[dateKey];
        
        dayEvents.forEach(({event, startTime, endTime}) => {
            // Extract title parts
            const titleParts = event.title.split(' - ');
            const courseName = titleParts[0];
            const roomInfo = titleParts.length > 1 ? titleParts.slice(1).join(' - ') : '';
            
            // Get start hour (in 24h format)
            const startHour = startTime.getHours();
            const endHour = endTime.getHours() + (endTime.getMinutes() > 0 ? 1 : 0);
            
            // Format time for display
            const formattedTime = startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            // Map 24h hour to index in our hours array
            const mapHourToIndex = h => {
                if (h < 8) return 0; // Before 8 AM
                if (h > 20) return hours.length - 1; // After 8 PM
                return h - 8; // Map 8-20 to 0-12 index
            };
            
            // Find range of hours this event spans
            const startIndex = mapHourToIndex(startHour);
            const endIndex = Math.min(mapHourToIndex(endHour), hours.length - 1);
            
            // Get all cells for this date
            const dayCells = trainerCalendarGrid.querySelectorAll(`[data-date="${dateKey}"]`);
            
            // Add event to each hour cell
            for (let i = startIndex; i <= endIndex; i++) {
                // Skip if we don't have this cell
                if (!dayCells[i]) continue;
                
                // Create event element
        const eventEl = document.createElement('div');
        
                // Set class based on event type
                const eventTypeClass = event.type === 'session' ? 'bg-green-700/75' : 'bg-blue-700/75';
                eventEl.className = `p-1 text-xs rounded m-1 border-l-2 ${eventTypeClass}`;
        
                // Set content with just the course name and time
        eventEl.innerHTML = `
                    <div class="font-semibold truncate">${courseName}</div>
                    <div class="text-xs opacity-80">${formattedTime}</div>
                `;
                
                dayCells[i].appendChild(eventEl);
            }
        });
    }
}

// Day view rendering
function renderDayView(events) {
    const { currentDate } = window.trainerCalendarState;
    
    // Format the day header
    const currentDayName = currentDate.toLocaleDateString('en-US', { weekday: 'long' });
    const formattedDate = currentDate.toLocaleDateString('en-US', { month: 'long', day: 'numeric', year: 'numeric' });
    
    // Update the header
    trainerCurrentMonthYear.textContent = `${currentDayName}, ${formattedDate.split(',')[0]}`;
    
    // Clear the grid
    trainerCalendarGrid.innerHTML = '';
    trainerCalendarGrid.className = 'grid text-sm text-white';
    trainerCalendarGrid.style.gridTemplateColumns = 'auto 1fr';
    
    // Create full-width day header
    const dayHeader = document.createElement('div');
    dayHeader.className = 'col-span-2 py-4 text-center font-medium bg-white/5 border-b border-white/10 text-xl';
    dayHeader.textContent = `${currentDayName}, ${formattedDate.split(',')[0]}`;
    trainerCalendarGrid.appendChild(dayHeader);
    
    // Create time slots (7 AM to 8 PM)
    const hours = ['7 AM', '8 AM', '9 AM', '10 AM', '11 AM', '12 PM', '1 PM', '2 PM', '3 PM', '4 PM', '5 PM', '6 PM', '7 PM', '8 PM'];
    const hourCells = {}; // Store references to time cells for later use
    
    // Create a row for each hour
    hours.forEach(hour => {
        const hourRow = document.createElement('div');
        hourRow.className = 'contents';
        
        // Time cell
        const timeCell = document.createElement('div');
        timeCell.className = 'py-2 px-3 font-medium border-b border-white/10';
        timeCell.textContent = hour;
        hourRow.appendChild(timeCell);
        
        // Day cell for this hour
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[60px] border-b border-l border-white/10 relative';
        dayCell.setAttribute('data-hour', hour);
        hourRow.appendChild(dayCell);
        
        // Store the day cell reference
        hourCells[hour] = dayCell;
        
        trainerCalendarGrid.appendChild(hourRow);
    });
    
    // Filter events for the current day
    const dayEvents = events.filter(event => {
        const eventDate = new Date(event.start);
        return eventDate.getDate() === currentDate.getDate() && 
               eventDate.getMonth() === currentDate.getMonth() && 
               eventDate.getFullYear() === currentDate.getFullYear();
    });
    
    // Process each event and create separate blocks for each hour
    dayEvents.forEach(event => {
            const eventStart = new Date(event.start);
            const eventEnd = new Date(event.end);
        
        // Get start and end hours
        const startHour = eventStart.getHours();
        const endHour = eventEnd.getHours() === 0 ? 24 : eventEnd.getHours(); // Handle midnight as 24
        
        // Extract components from title
        const titleParts = event.title.split(' - ');
        const courseName = titleParts[0];
        const trainerName = titleParts.length > 1 ? titleParts[1] : '';
        const roomName = titleParts.length > 2 ? titleParts[2] : '';
        
        // Format times for display
        const startTime = eventStart.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        const endTime = eventEnd.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        // Add event blocks for each hour from start to end
        for (let h = startHour; h < endHour; h++) {
            // Convert hour to 12-hour format for cell lookup
            const hourFormat = h === 0 ? '12 AM' : 
                             h < 12 ? `${h} AM` : 
                             h === 12 ? '12 PM' : 
                             `${h - 12} PM`;
            
            // Get the cell for this hour
            if (hours.includes(hourFormat) && hourCells[hourFormat]) {
                const cell = hourCells[hourFormat];
                
                // Create event element
            const eventEl = document.createElement('div');
            
                // Set class based on event type
                const eventTypeClass = event.type === 'session' ? 'bg-green-600/80' : 'bg-blue-600/80';
                eventEl.className = `${eventTypeClass} text-white px-2 py-2 text-sm rounded-sm m-0`;
            
                // Set content
            eventEl.innerHTML = `
                    <div class="font-medium">${courseName} - ${roomName}</div>
                    <div class="text-white/80 text-xs">${startTime} - ${endTime}</div>
                `;
                
                // Add to cell
                cell.innerHTML = ''; // Clear any existing content
                cell.appendChild(eventEl);
            }
        }
    });
}

// Show upcoming events
function showUpcomingEvents(events, container) {
    container.innerHTML = '';
    
    // Sort events by start date
    const sortedEvents = [...events].sort((a, b) => new Date(a.start) - new Date(b.start));
    
    // Get only future events and limit to 5
    const now = new Date();
    const futureEvents = sortedEvents.filter(event => new Date(event.start) > now).slice(0, 5);
    
    if (futureEvents.length === 0) {
        container.innerHTML = '<div class="text-white/70">{% trans "No upcoming events" %}</div>';
        return;
    }
    
    // Add each upcoming event to the panel
    futureEvents.forEach(event => {
        const eventDate = new Date(event.start);
        const day = eventDate.getDate();
        const month = eventDate.toLocaleString('default', { month: 'short' });
        const time = eventDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
        
        // Determine if this is a session or event
        const type = event.type || 'session';
        const typeClass = type === 'session' ? 'text-green-400' : 'text-blue-400';
        const typeLabel = type === 'session' ? 'session' : 'event';
        
        const eventElement = document.createElement('div');
        eventElement.className = 'flex items-start space-x-4 p-3 border border-white/10 rounded-md hover:bg-white/5';
        eventElement.innerHTML = `
            <div class="flex-shrink-0 w-12 text-center">
                <div class="text-sm font-semibold text-primary">${day}</div>
                <div class="text-xs text-white/70">${month}</div>
            </div>
            <div class="flex-1 min-w-0">
                <p class="text-sm font-medium text-white">${event.title}</p>
                <p class="text-xs ${typeClass}">${typeLabel} · ${time}</p>
            </div>
        `;
        container.appendChild(eventElement);
    });
}

// Update calendar when filters change
function updateCalendarView() {
    const { allEvents, currentDate, currentView } = window.trainerCalendarState;
    const filteredEvents = filterEvents(allEvents);
    renderTrainerCalendar(currentDate.getMonth(), currentDate.getFullYear(), currentView, filteredEvents);
    showUpcomingEvents(filteredEvents, trainerUpcomingEvents);
}

// Navigation functions
function updateTrainerCalendar() {
    const { currentDate, currentView, allEvents } = window.trainerCalendarState;
    
    // Update view button states
    updateTrainerViewButtons();
    
    // Build date range for fetching events based on view
    let startDate, endDate;
    
    if (currentView === 'month') {
        startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1);
        endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0);
    } else if (currentView === 'week') {
        const currentDay = currentDate.getDay();
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - currentDay);
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
    } else if (currentView === 'day') {
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0, 0);
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59, 999);
    }

    // If date range has changed significantly, fetch new events
    if (startDate && endDate) {
        const trainerId = {{ trainer.trainer_id }};
        const apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}&trainer_id=${trainerId}`;
        
        fetch(apiUrl)
            .then(response => response.json())
            .then(data => {
                window.trainerCalendarState.allEvents = data.events || [];
                const filteredEvents = filterEvents(window.trainerCalendarState.allEvents);
                renderTrainerCalendar(currentDate.getMonth(), currentDate.getFullYear(), currentView, filteredEvents);
                showUpcomingEvents(filteredEvents, trainerUpcomingEvents);
            })
            .catch(error => {
                console.error('Error loading trainer events:', error);
            });
    } else {
        renderTrainerCalendar(currentDate.getMonth(), currentDate.getFullYear(), currentView, filteredEvents);
        showUpcomingEvents(filteredEvents, trainerUpcomingEvents);
    }
}

function updateTrainerViewButtons() {
    const { currentView } = window.trainerCalendarState;
    
    // Reset all buttons to default state
    trainerViewMonth.className = 'px-3 py-1 text-sm rounded-md text-gray-300';
    trainerViewWeek.className = 'px-3 py-1 text-sm rounded-md text-gray-300';
    trainerViewDay.className = 'px-3 py-1 text-sm rounded-md text-gray-300';
    
    // Highlight the active view
    if (currentView === 'month') {
        trainerViewMonth.className = 'px-3 py-1 text-sm rounded-md bg-primary text-white';
    } else if (currentView === 'week') {
        trainerViewWeek.className = 'px-3 py-1 text-sm rounded-md bg-primary text-white';
    } else if (currentView === 'day') {
        trainerViewDay.className = 'px-3 py-1 text-sm rounded-md bg-primary text-white';
    }
}

// Jump to today
function goToToday() {
    const today = new Date();
    window.trainerCalendarState.currentDate = today;
    updateTrainerCalendar();
}

// Event listeners
trainerPrevBtn.addEventListener('click', function() {
    const { currentDate, currentView } = window.trainerCalendarState;
    
    if (currentView === 'month') {
        currentDate.setMonth(currentDate.getMonth() - 1);
    } else if (currentView === 'week') {
        currentDate.setDate(currentDate.getDate() - 7);
    } else if (currentView === 'day') {
        currentDate.setDate(currentDate.getDate() - 1);
    }
    
    updateTrainerCalendar();
});

trainerNextBtn.addEventListener('click', function() {
    const { currentDate, currentView } = window.trainerCalendarState;
    
    if (currentView === 'month') {
        currentDate.setMonth(currentDate.getMonth() + 1);
    } else if (currentView === 'week') {
        currentDate.setDate(currentDate.getDate() + 7);
    } else if (currentView === 'day') {
        currentDate.setDate(currentDate.getDate() + 1);
    }
    
    updateTrainerCalendar();
});

trainerViewMonth.addEventListener('click', function() {
    // Reset to current month when clicking Month tab
    const today = new Date();
    window.trainerCalendarState.currentDate = new Date(today.getFullYear(), today.getMonth(), 1);
    window.trainerCalendarState.currentView = 'month';
    
    // Update calendar and buttons
    updateTrainerCalendar();
    updateTrainerViewButtons();
});

trainerViewWeek.addEventListener('click', function() {
    window.trainerCalendarState.currentView = 'week';
    updateTrainerCalendar();
    updateTrainerViewButtons();
});

trainerViewDay.addEventListener('click', function() {
    window.trainerCalendarState.currentView = 'day';
    updateTrainerCalendar();
    updateTrainerViewButtons();
});

trainerViewToday.addEventListener('click', function() {
    goToToday();
    updateTrainerViewButtons();
    
    // Refresh the calendar with the current date
    const currentDate = window.trainerCalendarState.currentDate;
    renderTrainerCalendar(currentDate.getMonth(), currentDate.getFullYear(), window.trainerCalendarState.currentView, window.trainerCalendarState.allEvents);
});

// Filter toggles
sessionsFilter.addEventListener('change', function() {
    window.trainerCalendarState.showSessions = this.checked;
    updateCalendarView();
});

eventsFilter.addEventListener('change', function() {
    window.trainerCalendarState.showEvents = this.checked;
    updateCalendarView();
});

// Initialize the calendar
document.addEventListener('DOMContentLoaded', function() {
    const currentDate = window.trainerCalendarState.currentDate;
    const currentYear = currentDate.getFullYear();
    const currentMonth = currentDate.getMonth();
    
    // Build date range for fetching events
    const startDate = new Date(currentYear, currentMonth, 1);
    const endDate = new Date(currentYear, currentMonth + 1, 0); // Last day of month
    
    // Get trainer ID from the data attribute
    const trainerId = {{ trainer.trainer_id }};
    
    // Get trainer events for calendar display
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}&trainer_id=${trainerId}`;
    
    // Fetch trainer events
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // Store all events
            window.trainerCalendarState.allEvents = data.events || [];
            
            // Filter events (only show sessions by default)
            const filteredEvents = filterEvents(window.trainerCalendarState.allEvents);
            
            // Render the calendar
            renderTrainerCalendar(currentMonth, currentYear, 'month', filteredEvents);
            
            // Show upcoming events
            showUpcomingEvents(filteredEvents, trainerUpcomingEvents);
        })
        .catch(error => {
            console.error('Error loading trainer events:', error);
            trainerCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-gray-400">Error loading events. Please try again later.</div>';
        });
});
</script>
{% endblock %} 