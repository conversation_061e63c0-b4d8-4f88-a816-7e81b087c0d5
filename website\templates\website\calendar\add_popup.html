{% load i18n %}

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Holiday Management</title>
    {% load static %}
    <link rel="stylesheet" href="{% static 'website/css/tailwind.min.css' %}">
    <style>
        /* Animation for message notifications */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        @keyframes fadeOut {
            from { opacity: 1; transform: translateY(0); }
            to { opacity: 0; transform: translateY(-10px); }
        }
        
        .animate-fadeIn {
            animation: fadeIn 0.3s ease-in-out forwards;
        }
        
        .animate-fadeOut {
            animation: fadeOut 0.3s ease-in-out forwards;
        }
        
        #messageContainer {
            position: fixed;
            top: calc(var(--header-height) + 10px);
            right: 1rem;
            z-index: 9999;
            max-width: 320px;
        }
        
        /* Fixed header height */
        :root {
            --header-height: 80px;
        }
        
        /* Popup modal positioning */
        .modal-top-positioning {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 9999;
        }
        
        /* Notification styles */
        .notification-container {
            position: fixed;
            top: var(--header-height);
            right: 1rem;
            z-index: 100;
        }
        
        /* Ensure yellow warning notifications are visible */
        .bg-yellow-100,
        .border-yellow-500,
        .bg-yellow-500,
        .text-yellow-800 {
            z-index: 9999 !important;
            position: relative !important;
        }
    </style>
</head>

<!-- Calendar Add Popup Modal -->
<div id="calendarAddPopup" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full" style="max-height: 80vh; margin-top: 15vh; margin-bottom: 5vh; max-width: 600px; width: 90%;">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="addPopupTitle">
                            {% trans "Create for" %} <span id="selectedDate"></span>
                        </h3>
                        <div class="mt-4 overflow-y-auto" style="max-height: 60vh;">
                            <div class="space-y-3">
                                <button id="createSessionBtn" class="w-full flex items-center justify-center px-4 py-3 border border-white/10 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <svg class="h-5 w-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                    </svg>
                                    {% trans "Create Session" %}
                                </button>
                                <button id="createEventBtn" class="w-full flex items-center justify-center px-4 py-3 border border-white/10 rounded-md shadow-sm text-sm font-medium text-white bg-gray-800 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    <svg class="h-5 w-5 mr-2 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                                    </svg>
                                    {% trans "Create Event" %}
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 flex justify-end">
                <button type="button" id="closeAddPopupBtn" class="inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reschedule Session Modal -->
<div id="rescheduleSessionModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full" style="max-height: 80vh; margin-top: 15vh; margin-bottom: 5vh; max-width: 700px; width: 90%;">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            {% trans "Reschedule Session" %}
                        </h3>
                        <div class="mt-4 overflow-y-auto" style="max-height: 60vh;">
                            <div id="sessionDetails" class="mb-4 bg-gray-800 p-3 rounded-md text-sm text-gray-300 space-y-1">
                                <!-- Details will be populated by JS -->
                                <p><strong class="font-medium text-white">{% trans "Session" %}:</strong> <span id="session-detail-name">N/A</span></p>
                                <p><strong class="font-medium text-white">{% trans "Current Time" %}:</strong> <span id="session-detail-time">N/A</span></p>
                                <p><strong class="font-medium text-white">{% trans "Current Room" %}:</strong> <span id="session-detail-room">N/A</span></p>
                                <p><strong class="font-medium text-white">{% trans "Trainers" %}:</strong> <span id="session-detail-trainers">N/A</span></p>
                                <p><strong class="font-medium text-white">{% trans "Equipment" %}:</strong> <span id="session-detail-equipment">N/A</span></p>
                            </div>
                            <input type="hidden" id="rescheduleSessionId">
                            <input type="hidden" id="origSessionStartDate">
                            <input type="hidden" id="origSessionEndDate">
                            <input type="hidden" id="rescheduleRoomTypeId">
                            <input type="hidden" id="origSessionTrainerNames">
                            <input type="hidden" id="origSessionTrainerIds">
                            <input type="hidden" id="origSessionEquipment">
                            <div class="space-y-3">
                                <div>
                                    <label for="newSessionStartDate" class="block text-sm font-medium text-white">{% trans "New Start Date & Time" %}</label>
                                    <input type="datetime-local" id="newSessionStartDate" name="newSessionStartDate" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                </div>
                                <!-- Add new date warning container -->
                                <div id="sessionStartDateWarning" class="mt-1 p-2 bg-yellow-500/20 border border-yellow-500 rounded-md text-sm text-yellow-600 hidden"></div>
                                <div id="calculatedSessionEndTimeDisplay" class="hidden">
                                    <p class="text-sm text-gray-400">{% trans "Calculated End Time" %}: <span id="calculatedSessionEndTime"></span></p>
                                    <p class="text-xs text-gray-400 italic">{% trans "End time is automatically calculated based on original session duration" %}</p>
                                </div>
                                <div>
                                    <label for="newSessionRoom" class="block text-sm font-medium text-white">{% trans "New Room" %}</label>
                                    <select id="newSessionRoom" name="newSessionRoom" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <option value="">{% trans "Keep Current Room" %}</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="newSessionTrainer" class="block text-sm font-medium text-white">{% trans "New Trainer" %}</label>
                                    <select id="newSessionTrainer" name="newSessionTrainer" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <option value="">{% trans "Keep Current Trainer(s)" %}</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="newSessionEquipment" class="block text-sm font-medium text-white">{% trans "New Equipment" %}</label>
                                    <select id="newSessionEquipment" name="newSessionEquipment" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <option value="">{% trans "Select Equipment" %}</option>
                                    </select>
                                </div>
                                <div id="sessionDailySchedule" class="hidden">
                                    <h4 class="font-medium mb-1">{% trans "Daily Schedule" %}</h4>
                                    <div id="sessionDailySessionsList"></div>
                                    <div id="sessionDailyEventsList"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 flex justify-end space-x-2">
                <button type="button" id="closeRescheduleSessionBtn" class="inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
                <button type="button" id="saveRescheduledSessionBtn" class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
                    {% trans "Save" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reschedule Event Modal -->
<div id="rescheduleEventModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full" style="max-height: 80vh; margin-top: 15vh; margin-bottom: 5vh; max-width: 700px; width: 90%;">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white">
                            {% trans "Reschedule Event" %}
                        </h3>
                        <div class="mt-4 overflow-y-auto" style="max-height: 60vh;">
                            <div id="eventDetails" class="mb-4 bg-gray-800 p-3 rounded-md text-sm text-gray-300">
                                <!-- Details will be populated by JS -->
                            </div>
                            <input type="hidden" id="rescheduleEventId">
                            <input type="hidden" id="origEventStartDate">
                            <input type="hidden" id="origEventEndDate">
                            <input type="hidden" id="origEventEquipment">
                            <div class="space-y-3">
                                <div>
                                    <label for="newEventStartTime" class="block text-sm font-medium text-white">{% trans "New Start Time" %}</label>
                                    <input type="datetime-local" id="newEventStartTime" name="newEventStartTime" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                </div>
                                <!-- Add new date warning container -->
                                <div id="eventStartDateWarning" class="mt-1 p-2 bg-yellow-500/20 border border-yellow-500 rounded-md text-sm text-yellow-600 hidden"></div>
                                <div id="calculatedEventEndTimeDisplay" class="hidden">
                                    <p class="text-sm text-gray-400">{% trans "Calculated End Time" %}: <span id="calculatedEventEndTime"></span></p>
                                    <p class="text-xs text-gray-400 italic">{% trans "End time is automatically calculated based on original event duration" %}</p>
                                </div>
                                <div>
                                    <label for="newEventRoom" class="block text-sm font-medium text-white">{% trans "New Room" %}</label>
                                    <select id="newEventRoom" name="newEventRoom" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <option value="">{% trans "Keep Current Room" %}</option>
                                    </select>
                                </div>
                                <div>
                                    <label for="newEventEquipment" class="block text-sm font-medium text-white">{% trans "New Equipment" %}</label>
                                    <select id="newEventEquipment" name="newEventEquipment" class="mt-1 block w-full px-3 py-2 bg-gray-800 border border-gray-600 rounded-md shadow-sm text-white focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        <option value="">{% trans "Select Equipment" %}</option>
                                    </select>
                                </div>
                                <div id="eventDailySchedule" class="hidden">
                                    <h4 class="font-medium mb-1">{% trans "Daily Schedule" %}</h4>
                                    <div id="eventDailySessionsList"></div>
                                    <div id="eventDailyEventsList"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 flex justify-end space-x-2">
                <button type="button" id="closeRescheduleEventBtn" class="inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
                <button type="button" id="saveRescheduledEventBtn" class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-blue-600 text-base font-medium text-white hover:bg-blue-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:w-auto sm:text-sm">
                    {% trans "Save" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Calendar add popup functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get popup elements
    const calendarAddPopup = document.getElementById('calendarAddPopup');
    const selectedDateSpan = document.getElementById('selectedDate');
    const createSessionBtn = document.getElementById('createSessionBtn');
    const createEventBtn = document.getElementById('createEventBtn');
    const closeAddPopupBtn = document.getElementById('closeAddPopupBtn');
    
    // Storage for selected date
    let currentDate = '';
    
    // Function to open popup with date
    window.openCalendarAddPopup = function(date) {
        // Format date for display and storage
        const formattedDate = formatDateForDisplay(date);
        const apiFormatDate = formatDateForAPI(date);
        
        // Store the date for later use
        currentDate = apiFormatDate;
        
        // Update popup title with selected date
        if (selectedDateSpan) {
            selectedDateSpan.textContent = formattedDate;
        }
        
        // Show the popup
        if (calendarAddPopup) {
            calendarAddPopup.classList.remove('hidden');
        }
    };
    
    // Function to close popup
    function closeCalendarAddPopup() {
        if (calendarAddPopup) {
            calendarAddPopup.classList.add('hidden');
        }
    }
    
    // Format date for display (March 26, 2025)
    function formatDateForDisplay(date) {
        // If date is a Date object
        if (date instanceof Date) {
            return date.toLocaleDateString('en-US', { 
                month: 'long', 
                day: 'numeric', 
                year: 'numeric' 
            });
        }
        
        // If date is a string in format YYYY-MM-DD
        if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const dateParts = date.split('-');
            const parsedDate = new Date(dateParts[0], dateParts[1] - 1, dateParts[2]);
            
            return parsedDate.toLocaleDateString('en-US', { 
                month: 'long', 
                day: 'numeric', 
                year: 'numeric' 
            });
        }
        
        // Default fallback
        return date;
    }
    
    // Format date for API (YYYY-MM-DD)
    function formatDateForAPI(date) {
        if (date instanceof Date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // If already in format YYYY-MM-DD, return as is
        if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
            return date;
        }
        
        // Default fallback - today's date
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    // Add event listeners for buttons
    if (createSessionBtn) {
        createSessionBtn.addEventListener('click', function() {
            // Close the popup
            closeCalendarAddPopup();
            
            // Store flag in sessionStorage to indicate which modal to open on sessions page
            sessionStorage.setItem('openSessionModal', 'true');
            
            // Redirect to sessions page
            window.location.href = "{% url 'website:sessions' %}";
        });
    }
    
    if (createEventBtn) {
        createEventBtn.addEventListener('click', function() {
            // Close the popup
            closeCalendarAddPopup();
            
            // Store flag in sessionStorage to indicate which modal to open on sessions page
            sessionStorage.setItem('openEventModal', 'true');
            
            // Redirect to sessions page
            window.location.href = "{% url 'website:sessions' %}";
        });
    }
    
    if (closeAddPopupBtn) {
        closeAddPopupBtn.addEventListener('click', closeCalendarAddPopup);
    }
    
    // Close when clicking outside the modal content
    calendarAddPopup?.addEventListener('click', function(e) {
        if (e.target === calendarAddPopup) {
            closeCalendarAddPopup();
        }
    });
    
    // Close when pressing Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !calendarAddPopup?.classList.contains('hidden')) {
            closeCalendarAddPopup();
        }
    });
});

// Function to set minimum date for date inputs
function setMinDateForDatePickers() {
    const today = new Date();
    
    // Format for 'date' inputs: YYYY-MM-DD
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0'); // Months are 0-indexed
    const day = String(today.getDate()).padStart(2, '0');
    const todayString = `${year}-${month}-${day}`;
    
    // Format for 'datetime-local' inputs: YYYY-MM-DDThh:mm
    const hours = String(today.getHours()).padStart(2, '0');
    const minutes = String(today.getMinutes()).padStart(2, '0');
    const todayTimeString = `${todayString}T${hours}:${minutes}`;
    
    // Find all date input elements
    const dateInputs = document.querySelectorAll('input[type="date"]');
    dateInputs.forEach(input => {
        input.setAttribute('min', todayString);
    });
    
    // Find all datetime-local input elements (used in reschedule forms)
    const datetimeInputs = document.querySelectorAll('input[type="datetime-local"]');
    datetimeInputs.forEach(input => {
        input.setAttribute('min', todayTimeString);
    });
}

// Call the function when the DOM is ready
document.addEventListener('DOMContentLoaded', setMinDateForDatePickers);

// Update the openRescheduleSessionModal function to also set min dates
const originalOpenRescheduleSessionModal = window.openRescheduleSessionModal;
window.openRescheduleSessionModal = function(...args) {
    // Call the original function first
    if (originalOpenRescheduleSessionModal) {
        originalOpenRescheduleSessionModal.apply(this, args);
    }
    
    // Then set min dates on any date inputs in the modal
    setTimeout(setMinDateForDatePickers, 100); // Small delay to ensure modal is open
};

// Update the openRescheduleEventModal function to also set min dates
const originalOpenRescheduleEventModal = window.openRescheduleEventModal;
window.openRescheduleEventModal = function(...args) {
    // Call the original function first
    if (originalOpenRescheduleEventModal) {
        originalOpenRescheduleEventModal.apply(this, args);
    }
    
    // Then set min dates on any date inputs in the modal
    setTimeout(setMinDateForDatePickers, 100); // Small delay to ensure modal is open
};

</script>

<!-- Holiday Add Popup Modal -->
<div id="holidayAddPopup" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:w-full" style="max-height: 80vh; margin-top: 15vh; margin-bottom: 5vh; max-width: 650px; width: 90%;">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="holidayPopupTitle">
                            {% trans "Add Holiday" %}
                        </h3>
                        <div class="mt-4 overflow-y-auto" style="max-height: 60vh;">
                            <form id="holidayForm" class="space-y-4">
                                <div>
                                    <label for="holidayName" class="block text-sm font-medium text-white">{% trans "Holiday Name" %} *</label>
                                    <input type="text" name="holidayName" id="holidayName" class="mt-1 block w-full py-2 px-3 border border-white/10 bg-gray-800 rounded-md shadow-sm text-white focus:outline-none focus:ring-primary focus:border-primary" required>
                                </div>
                                
                                <div>
                                    <label for="holidayDate" class="block text-sm font-medium text-white">{% trans "Date" %} *</label>
                                    <input type="date" name="holidayDate" id="holidayDate" class="mt-1 block w-full py-2 px-3 border border-white/10 bg-gray-800 rounded-md shadow-sm text-white focus:outline-none focus:ring-primary focus:border-primary" required>
                                </div>
                                
                                <div>
                                    <label for="holidayDescription" class="block text-sm font-medium text-white">{% trans "Description" %}</label>
                                    <textarea name="holidayDescription" id="holidayDescription" rows="3" class="mt-1 block w-full py-2 px-3 border border-white/10 bg-gray-800 rounded-md shadow-sm text-white focus:outline-none focus:ring-primary focus:border-primary"></textarea>
                                </div>
                                
                                <div class="text-xs text-white/50">{% trans "* Required fields" %}</div>
                            </form>
                            
                            <!-- Conflicts Section - Will be shown dynamically when conflicts exist -->
                            <div id="holiday-conflicts" class="hidden bg-blue-950 text-white border border-red-500 rounded-lg p-4 mt-4">
                                <h3 class="text-lg font-bold text-white mb-2">⚠️ Scheduling Conflicts</h3>
                                <p class="mb-4">This holiday conflicts with scheduled sessions and events. You need to reschedule them before adding this holiday.</p>
                                
                                <div id="conflicting-sessions-container" class="mb-4 hidden">
                                    <h4 class="text-md font-bold text-white mb-2">Affected Sessions (<span id="sessions-count">0</span>)</h4>
                                    <div class="overflow-x-auto">
                                        <table class="w-full text-sm text-left text-gray-400 mb-4">
                                            <thead class="text-xs text-gray-400 uppercase bg-blue-900">
                                                <tr>
                                                    <th scope="col" class="px-4 py-2">Course</th>
                                                    <th scope="col" class="px-4 py-2">Time</th>
                                                    <th scope="col" class="px-4 py-2">Room</th>
                                                    <th scope="col" class="px-4 py-2">Trainers</th>
                                                    <th scope="col" class="px-4 py-2">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="conflicting-sessions">
                                                <!-- Sessions will be populated dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <div id="conflicting-events-container" class="mb-4 hidden">
                                    <h4 class="text-md font-bold text-white mb-2">Affected Events (<span id="events-count">0</span>)</h4>
                                    <div class="overflow-x-auto">
                                        <table class="w-full text-sm text-left text-gray-400">
                                            <thead class="text-xs text-gray-400 uppercase bg-blue-900">
                                                <tr>
                                                    <th scope="col" class="px-4 py-2">Event Name</th>
                                                    <th scope="col" class="px-4 py-2">Time</th>
                                                    <th scope="col" class="px-4 py-2">Room</th>
                                                    <th scope="col" class="px-4 py-2">Actions</th>
                                                </tr>
                                            </thead>
                                            <tbody id="conflicting-events">
                                                <!-- Events will be populated dynamically -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                
                                <p class="mt-4 text-sm text-yellow-300">Please reschedule all conflicting items before adding this holiday. After rescheduling, you can try submitting again.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 flex justify-between">
                <button type="button" id="closeHolidayPopupBtn" class="inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:text-sm">
                    {% trans "Cancel" %}
                </button>
                <button type="button" id="saveHolidayBtn" class="inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:text-sm">
                    {% trans "Save Holiday" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Holiday add popup functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get popup elements
    const holidayAddPopup = document.getElementById('holidayAddPopup');
    const holidayForm = document.getElementById('holidayForm');
    const holidayNameInput = document.getElementById('holidayName');
    const holidayDateInput = document.getElementById('holidayDate');
    const holidayDescriptionInput = document.getElementById('holidayDescription');
    const saveHolidayBtn = document.getElementById('saveHolidayBtn');
    const closeHolidayPopupBtn = document.getElementById('closeHolidayPopupBtn');
    
    // Function to open popup
    window.openHolidayAddPopup = function(date) {
        // Clear form
        holidayForm.reset();
        
        // Hide any previously shown conflict sections
        const conflictsSection = document.getElementById('holiday-conflicts');
        if (conflictsSection) {
            conflictsSection.classList.add('hidden');
        }
        
        // If date is provided, set it in the form
        if (date) {
            const formattedDate = formatDateForAPI(date);
            holidayDateInput.value = formattedDate;
        } else {
            // Set today's date as default
            const today = new Date();
            const year = today.getFullYear();
            const month = String(today.getMonth() + 1).padStart(2, '0');
            const day = String(today.getDate()).padStart(2, '0');
            holidayDateInput.value = `${year}-${month}-${day}`;
        }
        
        // Focus on name field
        setTimeout(() => {
            holidayNameInput.focus();
        }, 100);
        
        // Show the popup
        if (holidayAddPopup) {
            holidayAddPopup.classList.remove('hidden');
        }
    };
    
    // Function to close popup
    function closeHolidayAddPopup() {
        if (holidayAddPopup) {
            holidayAddPopup.classList.add('hidden');
        }
    }
    
    // Make the function available globally
    window.closeHolidayAddPopup = closeHolidayAddPopup;
    
    // Save holiday
    function saveHoliday() {
        // Get values from form
        const holidayName = document.getElementById('holidayName').value;
        const holidayDate = document.getElementById('holidayDate').value;
        const holidayDescription = document.getElementById('holidayDescription').value;
        
        // Validate required fields
        if (!holidayName || !holidayDate) {
            showMessage("{% trans 'Please fill in all required fields.' %}", 'error');
            return;
        }
        
        // Check if date is a weekend (Friday or Saturday)
        const selectedDate = new Date(holidayDate);
        const dayOfWeek = selectedDate.getDay();
        // In JavaScript, 5 is Friday and 6 is Saturday (0 is Sunday)
        if (dayOfWeek === 5 || dayOfWeek === 6) {
            showMessage("{% trans 'Holidays cannot be added on weekends (Friday or Saturday).' %}", 'error');
            return;
        }
        
        // Check if date already has a holiday
        // We'll make an API call to check existing holidays
        fetch('{% url "website:get_holidays_api" %}', {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success && data.holidays) {
                // Check if any existing holiday has the same date
                const existingHoliday = data.holidays.find(holiday => 
                    holiday.date === holidayDate
                );
                
                if (existingHoliday) {
                    showMessage(`{% trans 'A holiday already exists on this date: ' %}${existingHoliday.name}`, 'error');
                    return;
                }
                
                // If we get here, date is valid, continue with saving
                proceedWithSavingHoliday(holidayName, holidayDate, holidayDescription);
            } else {
                // If we can't fetch holidays, proceed with save and let server validation handle it
                proceedWithSavingHoliday(holidayName, holidayDate, holidayDescription);
            }
        })
        .catch(error => {
            console.error('Error checking existing holidays:', error);
            // If we can't fetch holidays, proceed with save and let server validation handle it
            proceedWithSavingHoliday(holidayName, holidayDate, holidayDescription);
        });
    }
    
    // Function to proceed with saving holiday after validation
    function proceedWithSavingHoliday(holidayName, holidayDate, holidayDescription) {
        // Show loading state
        const saveBtn = document.getElementById('saveHolidayBtn');
        saveBtn.disabled = true;
        saveBtn.innerHTML = `<svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg> {% trans 'Saving...' %}`;
        
        // Prepare data
        const data = {
            name: holidayName,
            date: holidayDate,
            description: holidayDescription
        };
        
        // Send to server
        fetch('{% url "website:add_holiday" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(data)
        })
        .then(response => {
            console.log('Response status:', response.status);
            // If we get a 409 Conflict, parse the JSON and handle it specially
            if (response.status === 409) {
                return response.json().then(data => {
                    // Throw a special error that includes the conflict data
                    const error = new Error('Conflict detected');
                    error.conflictData = data;
                    error.status = 409;
                    throw error;
                });
            }
            return response.json();
        })
        .then(result => {
            // Reset form state
            saveBtn.disabled = false;
            saveBtn.textContent = "{% trans 'Save Holiday' %}";
            
            if (result.success) {
                // Show success message
                showMessage(result.message || "{% trans 'Holiday created successfully!' %}", 'success');
                
                // Close modal
                closeHolidayAddPopup(); 
                
                // Refresh calendar
                if (typeof window.refreshCalendarEvents === 'function') {
                    console.log('Refreshing calendar events via window.refreshCalendarEvents()');
                    window.refreshCalendarEvents();
                } else if (typeof window.reloadCalendar === 'function') {
                    console.log('Reloading calendar via window.reloadCalendar()');
                    window.reloadCalendar();
                } else if (typeof window.fetchHolidays === 'function') {
                    console.log('Fetching holidays via window.fetchHolidays()');
                    window.fetchHolidays();
                } else {
                    console.log('No calendar refresh function found, reloading page');
                    window.location.reload();
                }
            } else {
                // Show error
                showMessage(result.message || "{% trans 'Error creating holiday.' %}", 'error');
            }
        })
        .catch(error => {
            console.error('Error creating holiday:', error);
            
            // Reset button state
            saveBtn.disabled = false;
            saveBtn.textContent = "{% trans 'Save Holiday' %}";
            
            // Handle conflict errors specially
            if (error.status === 409 && error.conflictData) {
                // Show conflict message
                showMessage("{% trans 'This holiday conflicts with existing sessions or events.' %}", 'warning');
                
                // Show the conflicts section
                const conflictsSection = document.getElementById('holiday-conflicts');
                if (conflictsSection) {
                    conflictsSection.classList.remove('hidden');
                }
                
                // Display conflict details in the UI
                if (error.conflictData.conflicts) {
                    handleHolidayConflicts(error.conflictData.conflicts);
                }
            } else {
                // Show generic error
                showMessage("{% trans 'An error occurred while saving the holiday.' %}", 'error');
            }
        });
    }
    
    // Format date for API (YYYY-MM-DD)
    function formatDateForAPI(date) {
        if (date instanceof Date) {
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        
        // If already in format YYYY-MM-DD, return as is
        if (typeof date === 'string' && date.match(/^\d{4}-\d{2}-\d{2}$/)) {
            return date;
        }
        
        // Default fallback - today's date
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
    
    // Get CSRF token from cookies
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
    
    // Add event listeners
    if (saveHolidayBtn) {
        saveHolidayBtn.addEventListener('click', saveHoliday);
    }
    
    if (closeHolidayPopupBtn) {
        closeHolidayPopupBtn.addEventListener('click', closeHolidayAddPopup);
    }
    
    // Handle form submit to prevent default
    if (holidayForm) {
        holidayForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveHoliday();
        });
    }
    
    // Close when clicking outside the modal content
    holidayAddPopup?.addEventListener('click', function(e) {
        if (e.target === holidayAddPopup) {
            closeHolidayAddPopup();
        }
    });
    
    // Close when pressing Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !holidayAddPopup?.classList.contains('hidden')) {
            closeHolidayAddPopup();
        }
    });
});

// Update handleHolidayConflicts function to pass start/end times AND room type id
function handleHolidayConflicts(conflicts) {
    try {
        const conflictsSection = document.getElementById('holiday-conflicts');
        if (!conflictsSection) {
            console.error('holiday-conflicts element not found');
            return;
        }
        
        // Get the current holiday date being entered
        const holidayDateInput = document.getElementById('holidayDate');
        const currentHolidayDate = holidayDateInput ? holidayDateInput.value : '';
        console.log("Current holiday date for conflict context:", currentHolidayDate);

        const sessionsContainer = document.getElementById('conflicting-sessions-container');
        const eventsContainer = document.getElementById('conflicting-events-container');
        const sessionsCount = document.getElementById('sessions-count');
        const eventsCount = document.getElementById('events-count');
        const sessionsTableBody = document.getElementById('conflicting-sessions');
        const eventsTableBody = document.getElementById('conflicting-events');
        
        // Reset tables if they exist
        if (sessionsTableBody) sessionsTableBody.innerHTML = '';
        if (eventsTableBody) eventsTableBody.innerHTML = '';
        
        // Check if we have any conflicts
        if (conflicts.total > 0) {
            // Show conflicts section
            conflictsSection.classList.remove('hidden');
            
            // Update count displays
            if (sessionsCount) sessionsCount.textContent = conflicts.sessions_count || 0;
            if (eventsCount) eventsCount.textContent = conflicts.events_count || 0;
            
            // Handle sessions
            if (conflicts.sessions_count > 0 && sessionsContainer && sessionsTableBody) {
                sessionsContainer.classList.remove('hidden');
                
                conflicts.sessions.forEach(session => {
                    try {
                        const row = document.createElement('tr');
                        row.className = 'border-b border-gray-700';
                        
                        // Get room name and type ID
                        let currentRoomName = session.room || ''; 
                        let currentRoomTypeId = session.room_type_id || ''; // Get room type ID
                        let trainerNamesStr = session.trainers || '';
                        let equipmentNamesStr = session.equipment || '';
                        let trainerIds = session.trainer_ids || []; // Get trainer IDs, default to empty array
                        let trainerIdsString = ''; // Initialize string version
                        
                        // Convert trainerIds to comma-separated string if it's an array
                        if (Array.isArray(trainerIds)) {
                            trainerIdsString = trainerIds.join(',');
                        } else {
                            // If not an array, try converting whatever it is to a string
                            trainerIdsString = String(trainerIds);
                        }
                        
                        // Format date/time
                        const startTime = new Date(session.start_time);
                        const endTime = new Date(session.end_time);
                        const timeStr = `${startTime.toLocaleDateString()} ${startTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${endTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                        
                        // **DEBUG LOG:** Check the value of currentRoomTypeId
                        console.log(`Generating button for session ${session.id}, room_type_id: ${session.room_type_id}, JS var currentRoomTypeId: ${currentRoomTypeId}`);

                        // Create a button to open the reschedule modal, passing necessary details AND the currentHolidayDate
                        row.innerHTML = `
                            <td class="px-4 py-2">${session.course_name || 'Unknown'}</td>
                            <td class="px-4 py-2">${timeStr}</td>
                            <td class="px-4 py-2">${currentRoomName || 'None'}</td>
                            <td class="px-4 py-2">${trainerNamesStr || 'None'}</td>
                            <td class="px-4 py-2">
                                <button 
                                    class="text-blue-400 hover:underline reschedule-session-btn" 
                                    onclick="openRescheduleSessionModal(
                                        '${session.id || ''}', 
                                        '${(session.course_name || 'Session').replace(/'/g, "\\'")}', 
                                        '${session.start_time || ''}', 
                                        '${session.end_time || ''}', 
                                        '${currentRoomName.replace(/'/g, "\\'")}', 
                                        '${currentRoomTypeId}', 
                                        '${trainerNamesStr.replace(/'/g, "\\'")}', 
                                        '${equipmentNamesStr.replace(/'/g, "\\'")}',
                                        '${trainerIdsString.replace(/'/g, "\\'")}', 
                                        '${currentHolidayDate}' // Pass the holiday date
                                    )"
                                >
                                    Reschedule
                                </button>
                            </td>
                        `;
                        
                        sessionsTableBody.appendChild(row);
                    } catch (err) {
                        console.error('Error processing session conflict:', err);
                    }
                });
            } else if (sessionsContainer) {
                sessionsContainer.classList.add('hidden');
            }
            
            // Handle events
            if (conflicts.events_count > 0 && eventsContainer && eventsTableBody) {
                eventsContainer.classList.remove('hidden');
                
                conflicts.events.forEach(event => {
                    try {
                        const row = document.createElement('tr');
                        row.className = 'border-b border-gray-700';
                        
                        // Format date/time
                        let timeStr = 'Not scheduled';
                        let currentRoomName = event.room || ''; // Get room name
                        if (event.start_time && event.end_time) {
                            const startTime = new Date(event.start_time);
                            const endTime = new Date(event.end_time);
                            timeStr = `${startTime.toLocaleDateString()} ${startTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})} - ${endTime.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}`;
                        }
                        
                        // Create a button to open the reschedule modal, passing necessary details AND the currentHolidayDate
                        row.innerHTML = `
                            <td class="px-4 py-2">${event.name || 'Unknown'}</td>
                            <td class="px-4 py-2">${timeStr}</td>
                            <td class="px-4 py-2">${currentRoomName || 'None'}</td>
                            <td class="px-4 py-2">
                                <button 
                                    class="text-blue-400 hover:underline reschedule-event-btn" 
                                    onclick="openRescheduleEventModal(
                                        '${event.id || ''}', 
                                        '${(event.name || 'Event').replace(/'/g, "\\'")}', 
                                        '${event.start_time || ''}', 
                                        '${event.end_time || ''}', 
                                        '${currentRoomName.replace(/'/g, "\\'")}',
                                        '${currentHolidayDate}' // Pass the holiday date
                                    )"
                                >
                                    Reschedule
                                </button>
                            </td>
                        `;
                        
                        eventsTableBody.appendChild(row);
                    } catch (err) {
                        console.error('Error processing event conflict:', err);
                    }
                });
            } else if (eventsContainer) {
                eventsContainer.classList.add('hidden');
            }
        } else {
            // No conflicts, hide the section
            conflictsSection.classList.add('hidden');
        }
    } catch (err) {
        console.error('Error handling conflicts:', err);
        showMessage('Error processing conflicts. Please try again.', 'error');
    }
}

// Add a hidden input to store the holiday date context
document.addEventListener('DOMContentLoaded', () => {
    const hiddenInput = document.createElement('input');
    hiddenInput.type = 'hidden';
    hiddenInput.id = 'holidayContextDate';
    document.body.appendChild(hiddenInput);
});

// Update openRescheduleSessionModal to accept and store the holiday date
function openRescheduleSessionModal(sessionId, sessionCode, startDate, endDate, currentRoom, roomTypeId, trainerNames, equipmentNames, trainerIds, holidayDate = '') {
    console.log("Opening reschedule modal for Session:", sessionId, sessionCode, startDate, endDate, currentRoom, roomTypeId, trainerNames, equipmentNames, trainerIds, "Holiday Date Context:", holidayDate);
    
    // Store the holiday date context
    const holidayContextInput = document.getElementById('holidayContextDate');
    if (holidayContextInput) {
        holidayContextInput.value = holidayDate;
    }

    // Close the holiday modal first
    const holidayPopup = document.getElementById('holidayAddPopup');
    if(holidayPopup) holidayPopup.classList.add('hidden');

    // Get modal elements
    const sessionDetailsDiv = document.getElementById('sessionDetails');
    const sessionIdInput = document.getElementById('rescheduleSessionId');
    const origStartDateInput = document.getElementById('origSessionStartDate');
    const origEndDateInput = document.getElementById('origSessionEndDate');
    const roomTypeIdInput = document.getElementById('rescheduleRoomTypeId');
    const newStartDateInput = document.getElementById('newSessionStartDate');
    const roomSelect = document.getElementById('newSessionRoom');
    const trainerSelect = document.getElementById('newSessionTrainer');
    const origTrainerNamesInput = document.getElementById('origSessionTrainerNames');
    const origTrainerIdsInput = document.getElementById('origSessionTrainerIds');
    const origEquipmentInput = document.getElementById('origSessionEquipment');
    const equipmentSelect = document.getElementById('newSessionEquipment');

    // Populate session details
    if (sessionDetailsDiv) { 
        const currentRoomName = currentRoom || 'N/A'; 
        document.getElementById('session-detail-name').textContent = sessionCode || 'N/A';
        document.getElementById('session-detail-time').textContent = `${formatDateTime(startDate)} - ${formatDateTime(endDate)}`;
        document.getElementById('session-detail-room').textContent = currentRoomName;
        document.getElementById('session-detail-trainers').textContent = trainerNames || 'N/A';
        document.getElementById('session-detail-equipment').textContent = equipmentNames || 'N/A';
    } else {
        console.error("Element with ID 'sessionDetails' not found!");
        return; // Stop if essential elements are missing
    }

    // Store original data including room type ID, trainer names, and trainer IDs
    if (sessionIdInput) sessionIdInput.value = sessionId;
    if (origStartDateInput) origStartDateInput.value = startDate; 
    if (origEndDateInput) origEndDateInput.value = endDate;     
    if (roomTypeIdInput) roomTypeIdInput.value = roomTypeId || ''; // Store room type id
    if (origTrainerNamesInput) origTrainerNamesInput.value = trainerNames || ''; // Store original trainer names
    if (origTrainerIdsInput) origTrainerIdsInput.value = trainerIds || ''; // Store original trainer IDs
    if (origEquipmentInput) origEquipmentInput.value = equipmentNames || ''; // Store original equipment

    // Set default value for new start date input & calculate initial end time
    if (newStartDateInput) {
        newStartDateInput.value = formatDateForInput(startDate);
        calculateAndDisplayEndTime('session'); 
    }

    // Clear room dropdown initially
    if (roomSelect) {
        roomSelect.innerHTML = '<option value="">{% trans "Loading rooms..." %}</option>';
        roomSelect.disabled = true;
    }
    
    // Clear trainer dropdown initially
    if (trainerSelect) {
        trainerSelect.innerHTML = '<option value="">{% trans "Loading trainers..." %}</option>';
        trainerSelect.disabled = true;
    }
    
    // Clear equipment dropdown initially
    if (equipmentSelect) {
        equipmentSelect.innerHTML = '<option value="">{% trans "Loading equipment..." %}</option>';
        equipmentSelect.disabled = true;
    }
    
    // Fetch and populate available rooms, trainers, and equipment based on the *initial* date/time
    const initialStartTime = formatDateForInput(startDate);
    const initialEndTime = calculateEndTime('session'); // Use the calculated end time based on initial start

    if (initialStartTime && initialEndTime) {
        // Fetch rooms
        fetchAvailableRooms('session', initialStartTime, initialEndTime, roomTypeId)
        .then(rooms => {
                populateRoomDropdown('session', rooms, currentRoom || 'N/A');
                 if (roomSelect) roomSelect.disabled = false;
        })
        .catch(error => {
                console.error('Failed to fetch initial rooms:', error);
                if (roomSelect) {
                     roomSelect.innerHTML = '<option value="">{% trans "Error loading rooms" %}</option>';
                     roomSelect.disabled = true;
                }
            });

        // Fetch trainers
        fetchAvailableTrainers(initialStartTime, initialEndTime)
        .then(trainers => {
            populateTrainerDropdown(trainers, trainerIds || '', trainerNames || '');
            if (trainerSelect) trainerSelect.disabled = false;
        })
        .catch(error => {
            console.error('Failed to fetch initial trainers:', error);
            if (trainerSelect) {
                trainerSelect.innerHTML = '<option value="">{% trans "Error loading trainers" %}</option>';
                trainerSelect.disabled = true;
            }
        });
        
        // Fetch equipment
        fetchAvailableEquipment(initialStartTime, initialEndTime)
        .then(equipment => {
            populateEquipmentDropdown(equipment, equipmentNames || '');
            if (equipmentSelect) equipmentSelect.disabled = false;
        })
        .catch(error => {
            console.error('Failed to fetch initial equipment:', error);
            if (equipmentSelect) {
                equipmentSelect.innerHTML = '<option value="">{% trans "Error loading equipment" %}</option>';
                equipmentSelect.disabled = true;
            }
        });
    } else {
         console.error("Could not format initial start/end times for resource fetch.");
         if (roomSelect) {
            roomSelect.innerHTML = '<option value="">{% trans "Error getting times" %}</option>';
            roomSelect.disabled = true;
         }
         if (trainerSelect) {
            trainerSelect.innerHTML = '<option value="">{% trans "Error getting times" %}</option>';
            trainerSelect.disabled = true;
         }
         if (equipmentSelect) {
            equipmentSelect.innerHTML = '<option value="">{% trans "Error getting times" %}</option>';
            equipmentSelect.disabled = true;
         }
    }
    
    // Show the modal
    const modal = document.getElementById('rescheduleSessionModal');
    if (modal) {
        modal.classList.remove('hidden');
    } else {
         console.error("Element with ID 'rescheduleSessionModal' not found!");
    }
}

// Helper function to determine initial date for rescheduling
function determineRescheduleDate(originalDateStr, holidayDateStr) {
    // Default to the original date if no holiday date is provided
    if (!holidayDateStr) {
        return new Date(originalDateStr);
    }
    
    // Parse dates
    const originalDate = new Date(originalDateStr);
    const holidayDate = new Date(holidayDateStr);
    
    // If the original date is on the holiday, suggest the next day
    if (originalDate.toDateString() === holidayDate.toDateString()) {
        const nextDay = new Date(holidayDate);
        nextDay.setDate(nextDay.getDate() + 1);
        // Keep the same time, just change the date
        nextDay.setHours(originalDate.getHours());
        nextDay.setMinutes(originalDate.getMinutes());
        return nextDay;
    }
    
    // Otherwise keep the original date
    return originalDate;
}

// Update openRescheduleEventModal to accept and store the holiday date
function openRescheduleEventModal(eventId, eventTitle, startDate, endDate, currentRoom, holidayDate = '') {
    console.log("Opening reschedule modal for Event:", eventId, eventTitle, startDate, endDate, currentRoom, "Holiday Date Context:", holidayDate);

    // Store the holiday date context
    const holidayContextInput = document.getElementById('holidayContextDate');
    if (holidayContextInput) {
        holidayContextInput.value = holidayDate;
    }

    // Close the holiday modal first
    const holidayPopup = document.getElementById('holidayAddPopup');
    if(holidayPopup) holidayPopup.classList.add('hidden');

    // Get modal elements
    const eventDetailsDiv = document.getElementById('eventDetails');
    const eventIdInput = document.getElementById('rescheduleEventId');
    const origStartDateInput = document.getElementById('origEventStartDate');
    const origEndDateInput = document.getElementById('origEventEndDate');
    const origEquipmentInput = document.getElementById('origEventEquipment');
    const newStartTimeInput = document.getElementById('newEventStartTime');
    const roomSelect = document.getElementById('newEventRoom');
    const equipmentSelect = document.getElementById('newEventEquipment');

    // Fetch event equipment from API
    fetch(`{% url "website:calendar_events_api" %}?event_id=${eventId}`)
        .then(response => response.ok ? response.json() : Promise.reject('Failed to fetch event details'))
        .then(data => {
            // Extract equipment information
            let currentEquipmentNames = 'None';
            if (data.events && data.events.length > 0) {
                const event = data.events[0];
                if (event.equipment && event.equipment.length > 0) {
                    currentEquipmentNames = event.equipment.map(eq => eq.name).join(', ');
                }
            }
            
            // Populate event details
            if (eventDetailsDiv) {
                const currentRoomName = currentRoom || 'N/A';
                eventDetailsDiv.innerHTML = `
                    <p><strong>{% trans "Event" %}:</strong> ${eventTitle || 'N/A'}</p>
                    <p><strong>{% trans "Current Time" %}:</strong> ${formatDateTime(startDate)} - ${formatDateTime(endDate)}</p>
                    <p><strong>{% trans "Current Room" %}:</strong> ${currentRoomName}</p>
                    <p><strong>{% trans "Equipment" %}:</strong> ${currentEquipmentNames}</p>
                `;
            } else {
                console.error("Element with ID 'eventDetails' not found!");
                return;
            }
            
            // Store original data
            if (eventIdInput) eventIdInput.value = eventId;
            if (origStartDateInput) origStartDateInput.value = startDate; // Store ISO string
            if (origEndDateInput) origEndDateInput.value = endDate;     // Store ISO string
            if (origEquipmentInput) origEquipmentInput.value = currentEquipmentNames; // Store original equipment
            
            // Set default value for new start date input & calculate initial end time
            if (newStartTimeInput) {
                const initialDate = determineRescheduleDate(startDate, holidayDate);
                newStartTimeInput.value = formatDateForInput(initialDate);
                calculateAndDisplayEndTime('event');
            }
            
            // Show the modal
            const modal = document.getElementById('rescheduleEventModal');
            if (modal) {
                modal.classList.remove('hidden');
            } else {
                console.error("Element with ID 'rescheduleEventModal' not found!");
            }
            
            // Update available rooms and equipment based on the selected date
            updateAvailableRooms('event');
            updateAvailableEventEquipment();
        })
        .catch(error => {
            console.error('Error fetching event details:', error);
            
            // Populate with basic info even if API fails
            if (eventDetailsDiv) {
                const currentRoomName = currentRoom || 'N/A';
                eventDetailsDiv.innerHTML = `
                    <p><strong>{% trans "Event" %}:</strong> ${eventTitle || 'N/A'}</p>
                    <p><strong>{% trans "Current Time" %}:</strong> ${formatDateTime(startDate)} - ${formatDateTime(endDate)}</p>
                    <p><strong>{% trans "Current Room" %}:</strong> ${currentRoomName}</p>
                    <p><strong>{% trans "Equipment" %}:</strong> Unable to load</p>
                `;
            }
            
            // Store original data
            if (eventIdInput) eventIdInput.value = eventId;
            if (origStartDateInput) origStartDateInput.value = startDate;
            if (origEndDateInput) origEndDateInput.value = endDate;
            if (origEquipmentInput) origEquipmentInput.value = 'None';
            
            // Set default value for new start date input & calculate initial end time
            if (newStartTimeInput) {
                const initialDate = determineRescheduleDate(startDate, holidayDate);
                newStartTimeInput.value = formatDateForInput(initialDate);
                calculateAndDisplayEndTime('event');
            }
            
            // Show the modal
            const modal = document.getElementById('rescheduleEventModal');
            if (modal) {
                modal.classList.remove('hidden');
            }
            
            // Update available rooms and equipment
            updateAvailableRooms('event');
            updateAvailableEventEquipment();
        });
}

// Fetch available rooms - added roomTypeId parameter
function fetchAvailableRooms(type, startTime, endTime, roomTypeId = null) {
    // Use the correct API endpoint
    let url = '{% url "website:available_rooms_api" %}?';
    const params = new URLSearchParams();
    
    // Log parameters for debugging
    console.log(`fetchAvailableRooms called with: type=${type}, startTime=${startTime}, endTime=${endTime}, roomTypeId=${roomTypeId}`);
    
    // Ensure start and end times are valid YYYY-MM-DDTHH:mm strings
    if (!startTime || !endTime || !startTime.includes('T') || !endTime.includes('T')) {
        console.error("Start or end time is invalid for fetching rooms. Expected YYYY-MM-DDTHH:mm format.", {startTime, endTime});
        return Promise.reject("Invalid date/time format for room fetch");
    }

    // Directly append the local time strings
    params.append('start_datetime', startTime); 
    params.append('end_datetime', endTime);    

    // Add required_room_type_id if provided (usually for sessions)
    if (roomTypeId) {
        console.log(`Adding room type filter: ${roomTypeId}`);
        params.append('required_room_type_id', roomTypeId);
    }
    
    url += params.toString();
    console.log("Fetching available rooms URL:", url);
    
    return fetch(url, {
        method: 'GET',
            headers: {
            'Accept': 'application/json',
            'X-CSRFToken': getCsrfToken() // Include CSRF token if needed
        }
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
        console.log("Available rooms received:", data);
        if (!data.available_rooms || data.available_rooms.length === 0) {
            console.log("No available rooms returned matching criteria");
        }
        return data.available_rooms || [];
    });
}

// Calculate end time based on start time and original duration
function calculateEndTime(type) {
    const startInput = document.getElementById(type === 'session' ? 'newSessionStartDate' : 'newEventStartTime');
    const origStartInput = document.getElementById(type === 'session' ? 'origSessionStartDate' : 'origEventStartDate');
    const origEndInput = document.getElementById(type === 'session' ? 'origSessionEndDate' : 'origEventEndDate');

    if (!startInput?.value || !origStartInput?.value || !origEndInput?.value) {
        console.warn("Missing dates for end time calculation:", 
            { start: startInput?.value, origStart: origStartInput?.value, origEnd: origEndInput?.value });
        return null; // Not enough info
    }

    try {
        const origStart = new Date(origStartInput.value);
        const origEnd = new Date(origEndInput.value);
        const durationMs = origEnd.getTime() - origStart.getTime();

        if (isNaN(durationMs) || durationMs < 0) {
            console.error("Invalid original duration calculated:", { origStart, origEnd, durationMs });
            return null;
        }

        const newStart = new Date(startInput.value);
        if (isNaN(newStart.getTime())) {
             console.error("Invalid new start date:", startInput.value);
             return null;
        }

        const newEnd = new Date(newStart.getTime() + durationMs);
        const newEndFormatted = formatDateForInput(newEnd); // Format as YYYY-MM-DDTHH:mm
        
        console.log("End time calculation:", 
            { origStart: origStartInput.value, origEnd: origEndInput.value, durationMs, newStart: startInput.value, newEndCalc: newEnd, newEndFormatted });

        // Return the calculated end time in YYYY-MM-DDTHH:mm format
        return newEndFormatted;

    } catch (e) {
        console.error("Error calculating end time:", e);
        return null;
    }
}

// Update the display for calculated end time
function calculateAndDisplayEndTime(type) {
    const calculatedEnd = calculateEndTime(type);
    const displayContainer = document.getElementById(type === 'session' ? 'calculatedSessionEndTimeDisplay' : 'calculatedEventEndTimeDisplay');
    const displaySpan = document.getElementById(type === 'session' ? 'calculatedSessionEndTime' : 'calculatedEventEndTime');

    if (calculatedEnd && displayContainer && displaySpan) {
        displaySpan.textContent = formatDateTime(calculatedEnd); // Use a user-friendly format
        displayContainer.classList.remove('hidden');
    } else if (displayContainer) {
        displaySpan.textContent = '{% trans "Error calculating" %}'; // Show error in display
        displayContainer.classList.remove('hidden'); // Ensure it's visible
    }
}

// Helper function to format date/time for input fields (YYYY-MM-DDThh:mm)
    function formatDateForInput(dateStr) {
        if (!dateStr) return '';
    try {
        const date = new Date(dateStr);
        // Check if the date is valid
        if (isNaN(date.getTime())) {
            console.error("Invalid date string for input formatting:", dateStr);
            return '';
        }
        // Directly format as YYYY-MM-DDTHH:mm in local time
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        const hours = String(date.getHours()).padStart(2, '0');
        const minutes = String(date.getMinutes()).padStart(2, '0');
        
        return `${year}-${month}-${day}T${hours}:${minutes}`;

    } catch (e) {
        console.error("Error formatting date for input:", e);
        return '';
    }
}

// Helper function to format date/time for display
function formatDateTime(isoString) {
    if (!isoString) return 'N/A';
    try {
        const date = new Date(isoString);
        if (isNaN(date.getTime())) return 'Invalid Date';
        // Example format: "Apr 24, 2025, 9:00 AM"
        return date.toLocaleString('en-US', { 
            year: 'numeric', 
            month: 'short', 
            day: 'numeric', 
            hour: 'numeric', 
            minute: '2-digit',
            hour12: true 
        });
    } catch (e) {
        console.error("Error formatting date:", e);
        return 'Formatting Error';
    }
}

// Show message function for success/error feedback
function showMessage(message, type = 'success') {
    const messageContainer = document.getElementById('messageContainer');
    const messageElement = document.createElement('div');
    
    messageElement.classList.add('p-4', 'rounded', 'shadow-md', 'mb-2', 'animate-fadeIn');
    if (type === 'success') {
        messageElement.classList.add('bg-green-100', 'text-green-800', 'border-l-4', 'border-green-500');
    } else if (type === 'error') {
        messageElement.classList.add('bg-red-100', 'text-red-800', 'border-l-4', 'border-red-500');
    } else if (type === 'warning') {
        messageElement.classList.add('bg-yellow-100', 'text-yellow-800', 'border-l-4', 'border-yellow-500');
    }
    
    messageElement.textContent = message;
    messageContainer.appendChild(messageElement);
    
    // Remove message after 5 seconds
    setTimeout(() => {
        messageElement.classList.add('animate-fadeOut');
        setTimeout(() => {
            messageContainer.removeChild(messageElement);
        }, 500);
    }, 5000);
}

function closeModal() {
    // Get the holiday modal
    const holidayModal = document.getElementById('holidayAddPopup');
    if (holidayModal) {
        holidayModal.classList.add('hidden');
    }
}

function refreshCalendar() {
    // Logic to refresh calendar - this could be a page reload or an API call
    if (window.refreshCalendarEvents) {
        window.refreshCalendarEvents();
    } else if (window.fetchHolidays) {
        // If refreshCalendarEvents doesn't exist, try fetchHolidays
        window.fetchHolidays();
    } else {
        // Last resort: reload the page
        window.location.reload();
    }
}

// Helper function to get CSRF token
function getCsrfToken() {
    return document.querySelector('[name=csrfmiddlewaretoken]').value;
}

// Helper function to populate the room dropdown
function populateRoomDropdown(type, rooms, currentRoomName) {
    const roomSelect = document.getElementById(type === 'session' ? 'newSessionRoom' : 'newEventRoom');
    if (!roomSelect) return;

    // Store current selection if exists
    const currentSelection = roomSelect.value;
    
    console.log(`Populating room dropdown for ${type} with ${rooms.length} rooms, current room: ${currentRoomName}`);
    
    // Get current room type ID (for filtering)
    const currentRoomTypeId = (type === 'session') ? document.getElementById('rescheduleRoomTypeId')?.value : null;
    console.log(`Current room type ID: ${currentRoomTypeId}`);
    
    // Filter rooms by type if we have a current room type ID
    let filteredRooms = rooms;
    if (currentRoomTypeId && type === 'session') {
        filteredRooms = rooms.filter(room => {
            const roomTypeMatches = room.room_type_id && 
                                    room.room_type_id.toString() === currentRoomTypeId.toString();
            return roomTypeMatches;
        });
        console.log(`Filtered from ${rooms.length} rooms to ${filteredRooms.length} rooms of type ${currentRoomTypeId}`);
    }
    
    // Debug each room
    filteredRooms.forEach(room => {
        console.log(`Room option: ${room.name} (id=${room.id}, type=${room.room_type_id || 'unknown'})`);
    });

    // Clear existing options except the placeholder if needed
    roomSelect.innerHTML = ''; 

    // Add available rooms
    if (filteredRooms.length > 0) {
        filteredRooms.forEach(room => {
            const option = document.createElement('option');
            option.value = room.id;
            // Include room type in display for clarity
            option.textContent = `${room.name} (${room.capacity || '?'}) [Type: ${room.room_type_id || '?'}]`;
            roomSelect.appendChild(option);
        });
    } else {
        // Add a disabled option informing user no rooms are available
        const noRoomsOption = document.createElement('option');
        noRoomsOption.disabled = true;
        noRoomsOption.textContent = "{% trans 'No rooms available for this time slot' %}";
        roomSelect.appendChild(noRoomsOption);
    }

    // Restore previous selection if it's still valid
    if (Array.from(roomSelect.options).some(opt => opt.value === currentSelection)) {
        roomSelect.value = currentSelection;
    } else {
        // If previous selection is invalid, select the first available option if any
        if (roomSelect.options.length > 0 && !roomSelect.options[0].disabled) {
            roomSelect.value = roomSelect.options[0].value;
        } else {
            roomSelect.value = ""; // Leave blank if no options available
        }
    }
}

// Function to fetch and update available rooms based on selected time
function updateAvailableRooms(type) {
    console.log(`Updating available rooms for ${type}...`);
    const newStartTime = document.getElementById(type === 'session' ? 'newSessionStartDate' : 'newEventStartTime')?.value;
    const calculatedEndTime = calculateEndTime(type); // Get the *newly* calculated end time
    const currentRoomName = document.getElementById(type === 'session' ? 'sessionDetails' : 'eventDetails')?.querySelector('p:last-child')?.textContent.split(': ')[1] || '';
    // Get the stored room type ID (only relevant for sessions)
    const roomTypeId = (type === 'session') ? document.getElementById('rescheduleRoomTypeId')?.value : null;

    console.log(`UpdateAvailableRooms params: newStartTime=${newStartTime}, calculatedEndTime=${calculatedEndTime}, roomTypeId=${roomTypeId}`);
    
    if (!newStartTime || !calculatedEndTime) {
        console.warn("Cannot update rooms, missing new start or calculated end time.");
        // Optionally clear or disable the dropdown
        const roomSelect = document.getElementById(type === 'session' ? 'newSessionRoom' : 'newEventRoom');
        if (roomSelect) {
             roomSelect.innerHTML = '<option value="">{% trans "Select time first" %}</option>';
             roomSelect.disabled = true;
        }
        return;
    }

    const roomSelect = document.getElementById(type === 'session' ? 'newSessionRoom' : 'newEventRoom');
    if (roomSelect) roomSelect.disabled = true; // Disable while loading

    // Pass roomTypeId to fetchAvailableRooms if it exists
    fetchAvailableRooms(type, newStartTime, calculatedEndTime, roomTypeId)
        .then(rooms => {
            console.log(`Received ${rooms.length} available rooms from API`);
            populateRoomDropdown(type, rooms, currentRoomName);
             if (roomSelect) roomSelect.disabled = false; // Re-enable after loading
        })
        .catch(error => {
            console.error(`Failed to fetch available rooms for ${type}:`, error);
            showMessage(`{% trans "Error loading available rooms for the selected time." %}`, 'error');
            if (roomSelect) {
                roomSelect.innerHTML = '<option value="">{% trans "Error loading rooms" %}</option>';
                roomSelect.disabled = true; // Keep disabled on error
            }
        });
}

// Fetch available trainers function
function fetchAvailableTrainers(startTime, endTime) {
    let url = '{% url "website:available_trainers_api" %}?';
    const params = new URLSearchParams();

    console.log(`fetchAvailableTrainers called with: startTime=${startTime}, endTime=${endTime}`);

    if (!startTime || !endTime || !startTime.includes('T') || !endTime.includes('T')) {
        console.error("Start or end time is invalid for fetching trainers.", {startTime, endTime});
        return Promise.reject("Invalid date/time format for trainer fetch");
    }

    params.append('start_datetime', startTime);
    params.append('end_datetime', endTime);

    url += params.toString();
    console.log("Fetching available trainers URL:", url);

    return fetch(url, {
        method: 'GET',
            headers: {
            'Accept': 'application/json',
                'X-CSRFToken': getCsrfToken()
        }
        })
        .then(response => {
            if (!response.ok) {
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
        console.log("Available trainers received:", data);
        if (!data.available_trainers || data.available_trainers.length === 0) {
            console.log("No available trainers returned matching criteria");
        }
        return data.available_trainers || [];
    });
}

// Helper function to populate the trainer dropdown according to the new logic
function populateTrainerDropdown(availableTrainers, originalTrainerIdsStr, originalTrainerNamesStr) {
    const trainerSelect = document.getElementById('newSessionTrainer');
    if (!trainerSelect) return;

    console.log(`Populating trainer dropdown. Available: ${availableTrainers.length}, Original IDs: ${originalTrainerIdsStr}, Original Names: ${originalTrainerNamesStr}`);

    trainerSelect.innerHTML = ''; // Clear existing options

    // Parse original trainer IDs (assuming comma-separated string)
    const originalTrainerIds = originalTrainerIdsStr ? originalTrainerIdsStr.split(',').map(id => parseInt(id.trim(), 10)).filter(id => !isNaN(id)) : [];
    
    // Extract IDs from the available trainers list
    const availableTrainerIds = availableTrainers.map(trainer => trainer.id);
    
    // Check if ALL original trainers are in the available list
    let allOriginalTrainersAvailable = false;
    if (originalTrainerIds.length > 0) {
        allOriginalTrainersAvailable = originalTrainerIds.every(id => availableTrainerIds.includes(id));
    }
    
    console.log(`Original Trainer IDs: [${originalTrainerIds.join(', ')}]`);
    console.log(`Available Trainer IDs: [${availableTrainerIds.join(', ')}]`);
    console.log(`Are all original trainers available? ${allOriginalTrainersAvailable}`);

    if (allOriginalTrainersAvailable) {
        // Add the original trainer(s) name as the only option
        const option = document.createElement('option');
        option.value = originalTrainerIdsStr; // Keep the original ID string as value
        option.textContent = originalTrainerNamesStr || "Original Trainer(s)"; 
        // option.disabled = true; // Optionally disable it if they MUST keep the original
        trainerSelect.appendChild(option);
        trainerSelect.value = originalTrainerIdsStr;
    } else {
        // Add a disabled option informing user no trainers (or original not available)
        const noTrainersOption = document.createElement('option');
        noTrainersOption.disabled = true;
        noTrainersOption.textContent = "{% trans 'Original trainer(s) not available for this time slot' %}";
        trainerSelect.appendChild(noTrainersOption);
        trainerSelect.value = ""; // Reset value
    }
}

// Function to fetch and update available trainers based on selected time
function updateAvailableTrainers() {
    console.log(`Updating available trainers...`);
    const newStartTime = document.getElementById('newSessionStartDate')?.value;
    const calculatedEndTime = calculateEndTime('session');
    const originalTrainerIdsStr = document.getElementById('origSessionTrainerIds')?.value || ''; // Get stored IDs
    const originalTrainerNamesStr = document.getElementById('origSessionTrainerNames')?.value || ''; // Get stored names

    console.log(`UpdateAvailableTrainers params: newStartTime=${newStartTime}, calculatedEndTime=${calculatedEndTime}`);

    if (!newStartTime || !calculatedEndTime) {
        console.warn("Cannot update trainers, missing new start or calculated end time.");
        const trainerSelect = document.getElementById('newSessionTrainer');
        if (trainerSelect) {
            // Display appropriate message when time is invalid
            trainerSelect.innerHTML = '';
            const option = document.createElement('option');
            option.disabled = true;
            option.textContent = "{% trans 'Select valid time first' %}";
            trainerSelect.appendChild(option);
            trainerSelect.disabled = true;
        }
        return;
    }

    const trainerSelect = document.getElementById('newSessionTrainer');
    if (trainerSelect) {
        trainerSelect.disabled = true; // Disable while loading
        
        // Show loading state
        trainerSelect.innerHTML = '';
        const loadingOption = document.createElement('option');
        loadingOption.value = "";
        loadingOption.textContent = "{% trans 'Checking trainer availability...' %}";
        trainerSelect.appendChild(loadingOption);
    }

    fetchAvailableTrainers(newStartTime, calculatedEndTime)
        .then(availableTrainers => {
            console.log(`Received ${availableTrainers.length} available trainers from API for check`);
            // Pass available trainers AND original IDs/Names to the updated populate function
            populateTrainerDropdown(availableTrainers, originalTrainerIdsStr, originalTrainerNamesStr); 
            if (trainerSelect) trainerSelect.disabled = false; // Re-enable after loading
        })
        .catch(error => {
            console.error(`Failed to fetch available trainers:`, error);
            showMessage(`{% trans "Error checking trainer availability." %}`, 'error');
            if (trainerSelect) {
                trainerSelect.innerHTML = '<option value="">{% trans "Error checking availability" %}</option>';
                trainerSelect.disabled = true; // Keep disabled on error
            }
        });
}

// Add event listeners to recalculate end time AND update rooms AND trainers when start time changes
// Use 'input' event for more immediate feedback on datetime-local changes
const newSessionStartDateInput = document.getElementById('newSessionStartDate');
const newEventStartTimeInput = document.getElementById('newEventStartTime');
// Need hidden input to store original trainer names for the "Keep Current" option display
const hiddenTrainerInput = document.createElement('input');
hiddenTrainerInput.type = 'hidden';
hiddenTrainerInput.id = 'origSessionTrainerNames';
document.body.appendChild(hiddenTrainerInput); // Append somewhere in the body

if (newSessionStartDateInput) {
    newSessionStartDateInput.addEventListener('input', () => {
        console.log("Session Start Date input event fired");
        calculateAndDisplayEndTime('session');
        updateAvailableRooms('session');
        updateAvailableTrainers();
        updateAvailableEquipment(); // Added call to update equipment
        checkSessionDateWarnings(); // Add call to check for weekend/holiday
    });
}

if (newEventStartTimeInput) {
    newEventStartTimeInput.addEventListener('input', () => {
        console.log("Event Start Time input event fired");
        calculateAndDisplayEndTime('event');
        updateAvailableRooms('event');
        updateAvailableEventEquipment(); // Add this line
        checkEventDateWarnings(); // Add call to check for weekend/holiday
    });
}

// Close modal event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Get the reschedule event modal close button
    const closeRescheduleEventBtn = document.getElementById('closeRescheduleEventBtn');
    const rescheduleEventModal = document.getElementById('rescheduleEventModal');
    
    // Get the reschedule session modal close button
    const closeRescheduleSessionBtn = document.getElementById('closeRescheduleSessionBtn');
    const rescheduleSessionModal = document.getElementById('rescheduleSessionModal');
    
    // Add event listener for the reschedule event close button
    if (closeRescheduleEventBtn && rescheduleEventModal) {
        closeRescheduleEventBtn.addEventListener('click', function() {
            rescheduleEventModal.classList.add('hidden');
        });
        
        // Close when clicking outside the modal content
        rescheduleEventModal.addEventListener('click', function(e) {
        if (e.target === rescheduleEventModal) {
                rescheduleEventModal.classList.add('hidden');
            }
        });
    }
    
    // Add event listener for the reschedule session close button
    if (closeRescheduleSessionBtn && rescheduleSessionModal) {
        closeRescheduleSessionBtn.addEventListener('click', function() {
            rescheduleSessionModal.classList.add('hidden');
        });
        
        // Close when clicking outside the modal content
        rescheduleSessionModal.addEventListener('click', function(e) {
            if (e.target === rescheduleSessionModal) {
                rescheduleSessionModal.classList.add('hidden');
            }
        });
    }
    
    // Close reschedule modals when pressing Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            if (rescheduleEventModal && !rescheduleEventModal.classList.contains('hidden')) {
                rescheduleEventModal.classList.add('hidden');
            }
            if (rescheduleSessionModal && !rescheduleSessionModal.classList.contains('hidden')) {
                rescheduleSessionModal.classList.add('hidden');
            }
        }
    });
    
    // Add event listener for the reschedule session save button
    const saveRescheduledSessionBtn = document.getElementById('saveRescheduledSessionBtn');
    if (saveRescheduledSessionBtn) {
        // This event handler is now provided by the standalone saveRescheduleSession function
        // Removing this duplicate event handler to avoid conflicts
        saveRescheduledSessionBtn.addEventListener('click', saveRescheduleSession);
    }
    
    // Add event listener for the reschedule event save button
    const saveRescheduledEventBtn = document.getElementById('saveRescheduledEventBtn');
    if (saveRescheduledEventBtn) {
        saveRescheduledEventBtn.addEventListener('click', saveRescheduleEvent);
    }
});

// Function to save rescheduled session
function saveRescheduleSession() {
    // Get values from the form
    const sessionId = document.getElementById('rescheduleSessionId').value;
    const newStartDate = document.getElementById('newSessionStartDate').value;
    const newRoomSelect = document.getElementById('newSessionRoom');
    const newRoomId = newRoomSelect ? newRoomSelect.value : '';
    const newEquipmentSelect = document.getElementById('newSessionEquipment');
    const newEquipmentId = newEquipmentSelect ? newEquipmentSelect.value : '';
    
    // Calculate end time
    const endTimeData = calculateEndTime('session');
    if (!endTimeData) {
        showMessage('{% trans "Error calculating end time. Please try again." %}', 'error');
        return;
    }
    
    // Create ISO format dates
    const startDateIso = new Date(newStartDate).toISOString();
    const endDateIso = new Date(endTimeData).toISOString();
    
    console.log(`Saving rescheduled session: ${sessionId} from ${startDateIso} to ${endDateIso}, room: ${newRoomId}, equipment: ${newEquipmentId}`);
    
    // Show loading state
    const saveButton = document.getElementById('saveRescheduledSessionBtn');
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> {% trans "Saving..." %}';
    }
    
    // Prepare request data
    const data = {
        session_id: sessionId,
        new_start_date: startDateIso,
        new_end_date: endDateIso
    };
    
    // Add optional data if available
    if (newRoomId) {
        data.new_room_id = newRoomId;
    }
    
    if (newEquipmentId) {
        data.new_equipment_id = newEquipmentId;
    }
    
    // Send request to API
    fetch('{% url "website:reschedule_session_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errData => {
                throw new Error(errData.message || `Server error: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            console.log('Session rescheduled successfully:', data);
            
            // Show success message
            showMessage(data.message || '{% trans "Session rescheduled successfully" %}', 'success');
            
            // Close modal
            const modal = document.getElementById('rescheduleSessionModal');
            if (modal) {
                modal.classList.add('hidden');
            }
            
            // Re-open the holiday modal and refresh conflicts
            const holidayModal = document.getElementById('holidayAddPopup');
            if (holidayModal) {
                holidayModal.classList.remove('hidden');
                refreshHolidayConflicts(); // Refresh the conflict list
            }
            
            // --- REMOVED PAGE RELOAD ---
            // setTimeout(() => {
            //     window.location.reload();
            // }, 1000);
        } else {
            // Handle API success but operation failed
            throw new Error(data.message || '{% trans "Unknown error during rescheduling" %}');
        }
    })
    .catch(error => {
        console.error('Error rescheduling session:', error);
        
        // Show error message
        showMessage(error.message || '{% trans "Error rescheduling session" %}', 'error');
        
        // Reset save button
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.innerHTML = '{% trans "Save" %}';
        }
    });
}

// Function to fetch available equipment based on selected date/time
function fetchAvailableEquipment(startTime, endTime, id = '', type = 'session') {
    let url = '{% url "website:available_equipment_api" %}?';
    const params = new URLSearchParams();

    console.log(`fetchAvailableEquipment called with: startTime=${startTime}, endTime=${endTime}, id=${id}, type=${type}`);

    if (!startTime || !endTime || !startTime.includes('T') || !endTime.includes('T')) {
        console.error("Start or end time is invalid for fetching equipment.", {startTime, endTime});
        return Promise.reject("Invalid date/time format for equipment fetch");
    }

    params.append('start_datetime', startTime);
    params.append('end_datetime', endTime);
    
    // Add ID to exclude current session/event from availability check
    if (id) {
        if (type === 'session') {
            params.append('exclude_session_id', id);
        } else if (type === 'event') {
            params.append('exclude_event_id', id);
        }
    }

    url += params.toString();
    console.log("Fetching available equipment URL:", url);

    return fetch(url, {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log("Available equipment received:", data);
        if (!data.available_equipment || data.available_equipment.length === 0) {
            console.log("No available equipment returned matching criteria");
        }
        return data.available_equipment || [];
    });
}

// Helper function to populate the equipment dropdown for events
function populateEventEquipmentDropdown(availableEquipment, originalEquipmentStr) {
    const equipmentSelect = document.getElementById('newEventEquipment');
    if (!equipmentSelect) return;

    console.log(`Populating event equipment dropdown. Available: ${availableEquipment.length}, Original Equipment: ${originalEquipmentStr}`);

    equipmentSelect.innerHTML = ''; // Clear existing options

    // Parse original equipment names
    const originalEquipment = originalEquipmentStr ? originalEquipmentStr.split(',').map(item => item.trim()) : [];
    
    // Add a default "No Equipment" option
    const noEquipmentOption = document.createElement('option');
    noEquipmentOption.value = "none";
    noEquipmentOption.textContent = "{% trans 'No Equipment' %}";
    equipmentSelect.appendChild(noEquipmentOption);

    // Only add available equipment if there are any
    if (availableEquipment && availableEquipment.length > 0) {
        // Add available equipment
        availableEquipment.forEach(equipment => {
            const option = document.createElement('option');
            option.value = equipment.id; // Use equipment ID as value
            option.textContent = `${equipment.name} (${equipment.code})`;
            
            // Check if this equipment was in the original event
            const equipNameCode = `${equipment.name} (${equipment.code})`;
            if (originalEquipment.some(item => item.includes(equipment.code))) {
                option.selected = true; // Pre-select this option
                option.textContent += ' - {% trans "Currently Assigned" %}';
            }
            
            equipmentSelect.appendChild(option);
        });
    } else {
        // Add a disabled option informing user no equipment is available
        const noEquipmentAvailableOption = document.createElement('option');
        noEquipmentAvailableOption.disabled = true;
        noEquipmentAvailableOption.textContent = "{% trans 'No additional equipment available for this time slot' %}";
        equipmentSelect.appendChild(noEquipmentAvailableOption);
    }

    // Set default value to "No Equipment" if original had none
    if (!originalEquipment.length || originalEquipment[0].toLowerCase() === 'none') {
        equipmentSelect.value = "none";
    }
}

// Helper function to populate the equipment dropdown for sessions
function populateEquipmentDropdown(availableEquipment, originalEquipmentStr) {
    const equipmentSelect = document.getElementById('newSessionEquipment');
    if (!equipmentSelect) return;

    console.log(`Populating session equipment dropdown. Available: ${availableEquipment.length}, Original Equipment: ${originalEquipmentStr}`);

    equipmentSelect.innerHTML = ''; // Clear existing options

    // Parse original equipment names
    const originalEquipment = originalEquipmentStr ? originalEquipmentStr.split(',').map(item => item.trim()) : [];
    
    // Add a default "No Equipment" option
    const noEquipmentOption = document.createElement('option');
    noEquipmentOption.value = "none";
    noEquipmentOption.textContent = "{% trans 'No Equipment' %}";
    equipmentSelect.appendChild(noEquipmentOption);

    // Only add available equipment if there are any
    if (availableEquipment && availableEquipment.length > 0) {
        // Add available equipment
        availableEquipment.forEach(equipment => {
            const option = document.createElement('option');
            option.value = equipment.id; // Use equipment ID as value
            option.textContent = `${equipment.name} (${equipment.code})`;
            
            // Check if this equipment was in the original session
            const equipNameCode = `${equipment.name} (${equipment.code})`;
            if (originalEquipment.some(item => item.includes(equipment.code))) {
                option.selected = true; // Pre-select this option
                option.textContent += ' - {% trans "Currently Assigned" %}';
            }
            
            equipmentSelect.appendChild(option);
        });
    } else {
        // Add a disabled option informing user no equipment is available
        const noEquipmentAvailableOption = document.createElement('option');
        noEquipmentAvailableOption.disabled = true;
        noEquipmentAvailableOption.textContent = "{% trans 'No additional equipment available for this time slot' %}";
        equipmentSelect.appendChild(noEquipmentAvailableOption);
    }

    // Set default value to "No Equipment" if original had none
    if (!originalEquipment.length || originalEquipment[0].toLowerCase() === 'none') {
        equipmentSelect.value = "none";
    }
}

// Function to update available equipment for sessions
function updateAvailableEquipment() {
    console.log(`Updating available equipment for session...`);
    const newStartTime = document.getElementById('newSessionStartDate')?.value;
    const calculatedEndTime = calculateEndTime('session');
    const originalEquipment = document.getElementById('origSessionEquipment')?.value || '';
    const sessionId = document.getElementById('rescheduleSessionId')?.value || '';

    console.log(`updateAvailableEquipment params: newStartTime=${newStartTime}, calculatedEndTime=${calculatedEndTime}`);

    if (!newStartTime || !calculatedEndTime) {
        console.warn("Cannot update equipment, missing new start or calculated end time.");
        const equipmentSelect = document.getElementById('newSessionEquipment');
        if (equipmentSelect) {
            equipmentSelect.innerHTML = '<option value="">{% trans "Select valid time first" %}</option>';
            equipmentSelect.disabled = true;
        }
        return;
    }

    const equipmentSelect = document.getElementById('newSessionEquipment');
    if (equipmentSelect) {
        equipmentSelect.disabled = true; // Disable while loading
        equipmentSelect.innerHTML = '<option value="">{% trans "Loading..." %}</option>';
    }

    // Convert to ISO strings
    const startDateTime = new Date(newStartTime).toISOString();
    const endDateTime = new Date(calculatedEndTime).toISOString();
    
    // Use the shared fetchAvailableEquipment function
    fetchAvailableEquipment(startDateTime, endDateTime, sessionId, 'session')
        .then(equipment => {
            // Reset dropdown with "No Equipment" option
            if (equipmentSelect) {
                equipmentSelect.innerHTML = '<option value="none">{% trans "No Equipment" %}</option>';
                
                // Add equipment options
                equipment.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.name}${originalEquipment.includes(item.name) ? ' - {% trans "Currently Assigned" %}' : ''}`;
                    equipmentSelect.appendChild(option);
                });
                
                equipmentSelect.disabled = false;
            }
        })
        .catch(error => {
            console.error('Error fetching available equipment for session:', error);
            if (equipmentSelect) {
                equipmentSelect.innerHTML = '<option value="none">{% trans "Error loading equipment" %}</option>';
                equipmentSelect.disabled = false;
            }
        });
}

// Function to update available equipment for events
function updateAvailableEventEquipment() {
    console.log(`Updating available equipment for event...`);
    const startTime = document.getElementById('newEventStartTime')?.value;
    const eventId = document.getElementById('rescheduleEventId')?.value;
    const originalEquipment = document.getElementById('origEventEquipment')?.value || '';
    
    if (!startTime) {
        console.warn("Cannot update equipment, missing start time.");
        return;
    }
    
    // Calculate the end time
    const endTime = calculateEndTime('event');
    
    if (!endTime) {
        console.warn("Cannot update equipment, end time calculation failed.");
        return;
    }
    
    // Get the equipment dropdown
    const equipmentDropdown = document.getElementById('newEventEquipment');
    if (!equipmentDropdown) {
        console.error("Equipment dropdown not found");
        return;
    }
    
    // Show loading state
    equipmentDropdown.disabled = true;
    equipmentDropdown.innerHTML = '<option value="">{% trans "Loading..." %}</option>';
    
    // Convert to ISO strings
    const startDateTime = new Date(startTime).toISOString();
    const endDateTime = new Date(endTime).toISOString();
    
    // Use the shared fetchAvailableEquipment function
    fetchAvailableEquipment(startDateTime, endDateTime, eventId, 'event')
        .then(equipment => {
            // Reset dropdown
            equipmentDropdown.innerHTML = '<option value="none">{% trans "No Equipment" %}</option>';
            
            // Add equipment options
            equipment.forEach(item => {
                const option = document.createElement('option');
                option.value = item.id;
                option.textContent = `${item.name}${originalEquipment.includes(item.name) ? ' - {% trans "Currently Assigned" %}' : ''}`;
                equipmentDropdown.appendChild(option);
            });
            
            // Enable dropdown
            equipmentDropdown.disabled = false;
        })
        .catch(error => {
            console.error('Error fetching available equipment for event:', error);
            equipmentDropdown.innerHTML = '<option value="none">{% trans "Error loading equipment" %}</option>';
            equipmentDropdown.disabled = false;
        });
}

// Shared function to fetch available equipment
function fetchAvailableEquipment(startTime, endTime, itemId, type) {
    const endpoint = '{% url "website:available_equipment_api" %}';
    
    // Create URL with query parameters
    const url = new URL(endpoint, window.location.origin);
    url.searchParams.append('start_datetime', startTime);
    url.searchParams.append('end_datetime', endTime);
    
    // Add the appropriate exclusion ID if available
    if (itemId) {
        if (type === 'session') {
            url.searchParams.append('exclude_session_id', itemId);
        } else if (type === 'event') {
            url.searchParams.append('exclude_event_id', itemId);
        }
    }
    
    console.log(`Fetching available equipment from: ${url.toString()}`);
    
    return fetch(url.toString(), {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'X-CSRFToken': getCsrfToken()
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`Server returned ${response.status}: ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log("Available equipment received:", data);
        if (!data.available_equipment || data.available_equipment.length === 0) {
            console.log("No available equipment returned matching criteria");
        }
        return data.available_equipment || [];
    });
}

// Helper function to format date for input fields
function formatDateForInput(date) {
    if (!date || (date instanceof Date && isNaN(date.getTime()))) {
        return '';
    }
    
    // If date is a string, convert to Date object
    if (typeof date === 'string') {
        date = new Date(date);
    }
    
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    
    return `${year}-${month}-${day}T${hours}:${minutes}`;
}

// Function to save rescheduled event
function saveRescheduleEvent() {
    // Get values from the form
    const eventId = document.getElementById('rescheduleEventId').value;
    const newStartTime = document.getElementById('newEventStartTime').value;
    const newRoomSelect = document.getElementById('newEventRoom');
    const newRoomId = newRoomSelect ? newRoomSelect.value : '';
    const newEquipmentSelect = document.getElementById('newEventEquipment');
    const newEquipmentId = newEquipmentSelect ? newEquipmentSelect.value : '';
    
    // Calculate end time
    const endTimeData = calculateEndTime('event');
    if (!endTimeData) {
        showMessage('{% trans "Error calculating end time. Please try again." %}', 'error');
        return;
    }
    
    // Create ISO format dates
    const startTimeIso = new Date(newStartTime).toISOString();
    const endTimeIso = new Date(endTimeData).toISOString();
    
    console.log(`Saving rescheduled event: ${eventId} from ${startTimeIso} to ${endTimeIso}, room: ${newRoomId}, equipment: ${newEquipmentId}`);
    
    // Show loading state
    const saveButton = document.getElementById('saveRescheduledEventBtn');
    if (saveButton) {
        saveButton.disabled = true;
        saveButton.innerHTML = '<svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle><path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path></svg> {% trans "Saving..." %}';
    }
    
    // Prepare request data
    const data = {
        event_id: eventId,
        new_start_time: startTimeIso,
        new_end_time: endTimeIso
    };
    
    // Add optional data if available
    if (newRoomId) {
        data.new_room_id = newRoomId;
    }
    
    if (newEquipmentId) {
        data.new_equipment_id = newEquipmentId;
    }
    
    // Send request to API
    fetch('{% url "website:reschedule_event_api" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(data)
    })
    .then(response => {
        if (!response.ok) {
            return response.json().then(errData => {
                throw new Error(errData.message || `Server error: ${response.status}`);
            });
        }
        return response.json();
    })
    .then(data => {
        if (data.success) {
            console.log('Event rescheduled successfully:', data);
            
            // Show success message
            showMessage(data.message || '{% trans "Event rescheduled successfully" %}', 'success');
            
            // Close modal
            const modal = document.getElementById('rescheduleEventModal');
            if (modal) {
                modal.classList.add('hidden');
            }
            
            // Re-open the holiday modal and refresh conflicts
            const holidayModal = document.getElementById('holidayAddPopup');
            if (holidayModal) {
                holidayModal.classList.remove('hidden');
                refreshHolidayConflicts(); // Refresh the conflict list
            }

            // --- REMOVED PAGE RELOAD ---
            // setTimeout(() => {
            //     window.location.reload();
            // }, 1000);
        } else {
            // Handle API success but operation failed
            throw new Error(data.message || '{% trans "Unknown error during rescheduling" %}');
        }
    })
    .catch(error => {
        console.error('Error rescheduling event:', error);
        
        // Show error message
        showMessage(error.message || '{% trans "Error rescheduling event" %}', 'error');
        
        // Reset save button
        if (saveButton) {
            saveButton.disabled = false;
            saveButton.innerHTML = '{% trans "Save" %}';
        }
    });
}

// Function to re-check holiday conflicts and update the modal
function refreshHolidayConflicts() {
    const holidayContextInput = document.getElementById('holidayContextDate');
    const holidayDate = holidayContextInput ? holidayContextInput.value : '';
    const holidayNameInput = document.getElementById('holidayName'); // Needed for context, even if not sent
    const holidayName = holidayNameInput ? holidayNameInput.value : 'Holiday'; // Default name if needed

    if (holidayDate) {
        console.log("Refreshing holiday conflicts for date:", holidayDate);
        
        // Simulate the check that happens during saveHoliday
        // Show a temporary loading state in the conflicts section
        const conflictsSection = document.getElementById('holiday-conflicts');
        const sessionsTableBody = document.getElementById('conflicting-sessions');
        const eventsTableBody = document.getElementById('conflicting-events');
        if (conflictsSection && sessionsTableBody && eventsTableBody) {
            conflictsSection.classList.remove('hidden');
            sessionsTableBody.innerHTML = `<tr><td colspan="5" class="text-center p-4">{% trans "Checking for remaining conflicts..." %}</td></tr>`;
            eventsTableBody.innerHTML = `<tr><td colspan="4" class="text-center p-4">{% trans "Checking for remaining conflicts..." %}</td></tr>`;
        }

        // Call the check_holiday_conflicts API endpoint
        fetch('{% url "website:check_holiday_conflicts" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify({ date: holidayDate })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.has_conflicts) {
                    console.log("Conflicts still exist:", data.conflicts);
                    // Update the conflict display
                    handleHolidayConflicts(data.conflicts);
                } else {
                    console.log("No more conflicts found for this holiday date.");
                    // Hide the conflict section if no conflicts remain
                    if (conflictsSection) {
                        conflictsSection.classList.add('hidden');
                    }
                    // Maybe enable the save holiday button?
                    const saveHolidayBtn = document.getElementById('saveHolidayBtn');
                    if (saveHolidayBtn) saveHolidayBtn.disabled = false;
                    showMessage("{% trans 'Previously conflicting item rescheduled. You can now save the holiday.' %}", "info");
                }
            } else {
                console.error("Error checking holiday conflicts:", data.message);
                showMessage(data.message || '{% trans "Error refreshing conflict list." %}', 'error');
                 // Keep conflict section visible but show error
                 if (conflictsSection && sessionsTableBody && eventsTableBody) {
                    sessionsTableBody.innerHTML = `<tr><td colspan="5" class="text-center p-4 text-red-500">{% trans "Error refreshing conflicts." %}</td></tr>`;
                    eventsTableBody.innerHTML = `<tr><td colspan="4" class="text-center p-4 text-red-500">{% trans "Error refreshing conflicts." %}</td></tr>`;
                 }
            }
        })
        .catch(error => {
            console.error('Fetch error during conflict refresh:', error);
            showMessage('{% trans "Could not refresh conflict list." %}', 'error');
            // Keep conflict section visible but show error
            if (conflictsSection && sessionsTableBody && eventsTableBody) {
                sessionsTableBody.innerHTML = `<tr><td colspan="5" class="text-center p-4 text-red-500">{% trans "Error refreshing conflicts." %}</td></tr>`;
                eventsTableBody.innerHTML = `<tr><td colspan="4" class="text-center p-4 text-red-500">{% trans "Error refreshing conflicts." %}</td></tr>`;
            }
        });

        // Clear the context date after use
        // holidayContextInput.value = ''; // Let's keep it for now in case of multiple conflicts
    } else {
        console.warn("No holiday date context found for refreshing conflicts.");
    }
}

// Function to check for weekend/holiday when rescheduling a session
function checkSessionDateWarnings() {
    const dateInput = document.getElementById('newSessionStartDate');
    const warningContainer = document.getElementById('sessionStartDateWarning');
    
    if (!dateInput || !warningContainer) return;
    
    const dateValue = dateInput.value;
    if (!dateValue) {
        warningContainer.classList.add('hidden');
        return;
    }
    
    const dateObj = new Date(dateValue);
    
    // Check if date is a weekend
    if (isWeekend(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a weekend day (Friday or Saturday)" %}';
        warningContainer.classList.remove('hidden');
        return;
    }
    
    // Check if date is a holiday
    if (isHoliday(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a holiday" %}';
        warningContainer.classList.remove('hidden');
        return;
    }
    
    // No warnings, hide the container
    warningContainer.classList.add('hidden');
}

// Function to check if a date is a weekend
function isWeekend(dateObj) {
    const day = dateObj.getDay();
    // Friday is 5, Saturday is 6 in JavaScript
    return day === 5 || day === 6;
}

// Function to check if a date is a holiday
function isHoliday(dateObj) {
    if (!window.holidaysLoaded) return false;
    
    const dateStr = dateObj.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    return window.holidaysData.includes(dateStr);
}

// Function to load holidays from the server
function loadHolidays() {
    if (window.holidaysLoaded) return Promise.resolve(window.holidaysData);
    
    return fetch('/api/holidays/get/')
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            if (data.success) {
                window.holidaysData = data.holidays.map(h => h.date.split('T')[0]); // Extract just the date part
                window.holidaysLoaded = true;
                return window.holidaysData;
            } else {
                console.error('Error loading holidays:', data.message);
                return [];
            }
        })
        .catch(error => {
            console.error('Error fetching holidays:', error);
            return [];
        });
}

// Load holidays when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Initialize global holiday variables if they don't exist
    if (typeof window.holidaysData === 'undefined') {
        window.holidaysData = [];
    }
    if (typeof window.holidaysLoaded === 'undefined') {
        window.holidaysLoaded = false;
    }
    
    loadHolidays();
    
    // Add event listener for the reschedule session modal open
    const originalOpenRescheduleSessionModal = window.openRescheduleSessionModal;
    window.openRescheduleSessionModal = function(...args) {
        // Call the original function
        if (originalOpenRescheduleSessionModal) {
            originalOpenRescheduleSessionModal.apply(this, args);
        }
        
        // Then check for date warnings after a slight delay to ensure modal is populated
        setTimeout(checkSessionDateWarnings, 200);
    };
    
    // Add event listener for the reschedule event modal open
    const originalOpenRescheduleEventModal = window.openRescheduleEventModal;
    window.openRescheduleEventModal = function(...args) {
        // Call the original function
        if (originalOpenRescheduleEventModal) {
            originalOpenRescheduleEventModal.apply(this, args);
        }
        
        // Then check for date warnings after a slight delay to ensure modal is populated
        setTimeout(checkEventDateWarnings, 200);
    };
});

// Function to check for weekend/holiday when rescheduling an event
function checkEventDateWarnings() {
    const dateInput = document.getElementById('newEventStartTime');
    const warningContainer = document.getElementById('eventStartDateWarning');
    
    if (!dateInput || !warningContainer) return;
    
    const dateValue = dateInput.value;
    if (!dateValue) {
        warningContainer.classList.add('hidden');
        return;
    }
    
    const dateObj = new Date(dateValue);
    
    // Check if date is a weekend
    if (isWeekend(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a weekend day (Friday or Saturday)" %}';
        warningContainer.classList.remove('hidden');
        return;
    }
    
    // Check if date is a holiday
    if (isHoliday(dateObj)) {
        warningContainer.textContent = '{% trans "This date is a holiday" %}';
        warningContainer.classList.remove('hidden');
        return;
    }
    
    // No warnings, hide the container
    warningContainer.classList.add('hidden');
}
</script>

<!-- Add message container for notifications -->
<div id="messageContainer" class="fixed top-4 right-4 z-50"></div>

</body>
</html> 