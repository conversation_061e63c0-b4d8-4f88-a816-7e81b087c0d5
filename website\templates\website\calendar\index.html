{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Calendar" %}{% endblock %}

{% block extra_head %}
<script src="https://cdn.jsdelivr.net/npm/flowbite@1.8.1/dist/flowbite.min.js"></script>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    {% include 'website/calendar/add_popup.html' %}

    <!-- Page Header -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <div class="flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            <h1 class="text-3xl font-bold text-white">{% trans "Calendar" %}</h1>
        </div>
        
        <div class="flex space-x-4">
            {% if user.is_staff %}
            <a href="{% url 'website:sessions' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                </svg>
                {% trans "Add to calendar" %}
            </a>
            <button id="addHolidayBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                {% trans "Add Holiday" %}
            </button>
            {% endif %}
        </div>
    </div>

    <!-- Tabs -->
    <div class="mb-6 border-b border-white/10">
        <ul class="flex flex-wrap -mb-px text-sm font-medium" id="calendarTabs" role="tablist">
            <li class="mr-2" role="presentation">
                <button class="inline-block px-4 py-2 border-b-2 border-primary text-primary hover:text-white" id="main-tab" data-tabs-target="#main" data-tabs-toggle="#calendarTabContent" type="button" role="tab" aria-controls="main" aria-selected="true">{% trans "Main Calendar" %}</button>
            </li>
            <li class="mr-2" role="presentation">
                <button class="inline-block px-4 py-2 border-b-2 border-transparent text-white/70 hover:text-white hover:border-white/30" id="rooms-tab" data-tabs-target="#rooms" data-tabs-toggle="#calendarTabContent" type="button" role="tab" aria-controls="rooms" aria-selected="false">{% trans "Rooms" %}</button>
            </li>
            <li class="mr-2" role="presentation">
                <button class="inline-block px-4 py-2 border-b-2 border-transparent text-white/70 hover:text-white hover:border-white/30" id="trainers-tab" data-tabs-target="#trainers" data-tabs-toggle="#calendarTabContent" type="button" role="tab" aria-controls="trainers" aria-selected="false">{% trans "Trainers" %}</button>
            </li>
            <li role="presentation">
                <button class="inline-block px-4 py-2 border-b-2 border-transparent text-white/70 hover:text-white hover:border-white/30" id="equipment-tab" data-tabs-target="#equipment" data-tabs-toggle="#calendarTabContent" type="button" role="tab" aria-controls="equipment" aria-selected="false">{% trans "Equipment" %}</button>
            </li>
        </ul>
    </div>
        
    <!-- Tab Content -->
    <div id="calendarTabContent">
        <!-- Main Calendar Tab -->
        <div class="block" id="main" role="tabpanel" aria-labelledby="main-tab">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Calendar -->
                <div class="lg:col-span-3">
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible">
                        <!-- Calendar Header -->
                        <div class="px-6 py-4 flex items-center justify-between border-b border-white/10">
                            <div class="flex items-center">
                                <button id="prevMonth" class="p-2 hover:bg-white/5 rounded-md">
                                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                </button>
                                <h2 id="current-month-year" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                                <button id="nextMonth" class="p-2 hover:bg-white/5 rounded-md">
                                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex rounded-md bg-gray-700 p-1">
                                    <button id="viewMonth" class="px-3 py-1 text-sm rounded-md bg-primary text-white">Month</button>
                                    <button id="viewWeek" class="px-3 py-1 text-sm rounded-md text-gray-300">Week</button>
                                    <button id="viewDay" class="px-3 py-1 text-sm rounded-md text-gray-300">Day</button>
                                </div>
                            </div>
                        </div>
                        
                        <div id="calendarGrid" class="grid grid-cols-7 text-sm text-white">
                            <!-- Calendar cells will be generated via JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Calendar Filters -->
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{% trans "Calendar Filters" %}</h3>
                        <div class="space-y-3 calendar-filters">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-green-600 h-4 w-4 rounded" checked data-filter="session">
                                <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-blue-500 h-4 w-4 rounded" checked data-filter="event">
                                <span class="ml-2 text-sm text-white">{% trans "Events" %}</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-red-500 h-4 w-4 rounded" checked data-filter="holiday">
                                <span class="ml-2 text-sm text-white">{% trans "Holidays" %}</span>
                            </label>
                        </div>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Events" %}</h3>
                        <div id="upcomingEvents" class="space-y-4">
                            <!-- Events will be populated via JavaScript -->
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-12 text-center">
                                    <div class="text-sm font-semibold text-primary">{% now "d" %}</div>
                                    <div class="text-xs text-white/70">{% now "M" %}</div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-white">Loading events...</p>
                                    <p class="text-xs text-white/70">Please wait</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rooms Tab -->
        <div class="hidden" id="rooms" role="tabpanel" aria-labelledby="rooms-tab">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Calendar -->
                <div class="lg:col-span-3">
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible">
                        <!-- Calendar Header -->
                        <div class="px-6 py-4 flex items-center justify-between border-b border-white/10">
                            <div class="flex items-center space-x-4">
                                <div class="room-selector">
                                    <label class="text-white mr-2">{% trans "Select Room:" %}</label>
                                    <select id="roomSelect" class="bg-white/10 border border-white/20 text-white rounded-md px-3 py-2" style="z-index: 9999; position: relative;">
                                        <option value="">{% trans "All Rooms" %}</option>
                                        {% for room in rooms %}
                                        <option value="{{ room.room_id }}">{{ room.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <button id="roomPrevMonth" class="p-2 hover:bg-white/5 rounded-md">
                                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                </button>
                                <h2 id="room-current-month-year" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                                <button id="roomNextMonth" class="p-2 hover:bg-white/5 rounded-md">
                                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex rounded-md bg-gray-700 p-1">
                                    <button id="roomViewMonth" class="px-3 py-1 text-sm rounded-md bg-primary text-white">Month</button>
                                    <button id="roomViewWeek" class="px-3 py-1 text-sm rounded-md text-gray-300">Week</button>
                                    <button id="roomViewDay" class="px-3 py-1 text-sm rounded-md text-gray-300">Day</button>
                                </div>
                                <button id="roomTodayBtn" class="text-sm text-white hover:text-primary ml-2">{% trans "Today" %}</button>
                            </div>
                        </div>
                        
                        <div id="roomCalendarGrid" class="grid grid-cols-7 text-sm text-white">
                            <!-- Calendar cells will be generated via JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Calendar Filters -->
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{% trans "Room Calendar Filters" %}</h3>
                        <div class="space-y-3 room-calendar-filters">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-green-600 h-4 w-4 rounded" checked data-filter="session">
                                <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                            </label>
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-blue-500 h-4 w-4 rounded" checked data-filter="event">
                                <span class="ml-2 text-sm text-white">{% trans "Events" %}</span>
                            </label>
                        </div>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Room Events" %}</h3>
                        <div id="roomUpcomingEvents" class="space-y-4">
                            <!-- Events will be populated via JavaScript -->
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0 w-12 text-center">
                                    <div class="text-sm font-semibold text-primary">{% now "d" %}</div>
                                    <div class="text-xs text-white/70">{% now "M" %}</div>
                                </div>
                                <div class="flex-1 min-w-0">
                                    <p class="text-sm font-medium text-white">Loading events...</p>
                                    <p class="text-xs text-white/70">Please wait</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Trainers Tab -->
        <div class="hidden" id="trainers" role="tabpanel" aria-labelledby="trainers-tab">
            <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                <!-- Calendar -->
                <div class="lg:col-span-3">
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible">
                        <!-- Calendar Header -->
                        <div class="px-6 py-4 flex items-center justify-between border-b border-white/10">
                            <div class="flex items-center space-x-4">
                                <div class="trainer-selector">
                                    <label class="text-white mr-2">{% trans "Select Trainer:" %}</label>
                                    <select id="trainerSelect" class="bg-white/10 border border-white/20 text-white rounded-md px-3 py-2" style="z-index: 9999; position: relative;">
                                        <option value="">{% trans "All Trainers" %}</option>
                                        {% for trainer in trainers %}
                                        <option value="{{ trainer.trainer_id }}">{{ trainer.user.get_full_name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                            <div class="flex items-center">
                                <button id="trainerPrevMonth" class="p-2 hover:bg-white/5 rounded-md">
                                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                    </svg>
                                </button>
                                <h2 id="trainer-current-month-year" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                                <button id="trainerNextMonth" class="p-2 hover:bg-white/5 rounded-md">
                                    <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                    </svg>
                                </button>
                            </div>
                            <div class="flex items-center space-x-2">
                                <div class="flex rounded-md bg-gray-700 p-1">
                                    <button id="trainerViewMonth" class="px-3 py-1 text-sm rounded-md bg-primary text-white">Month</button>
                                    <button id="trainerViewWeek" class="px-3 py-1 text-sm rounded-md text-gray-300">Week</button>
                                    <button id="trainerViewDay" class="px-3 py-1 text-sm rounded-md text-gray-300">Day</button>
                                </div>
                                <button id="trainerTodayBtn" class="text-sm text-white hover:text-primary ml-2">{% trans "Today" %}</button>
                            </div>
                        </div>
                        
                        <div id="trainerCalendarGrid" class="grid grid-cols-7 text-sm text-white">
                            <!-- Calendar cells will be generated via JavaScript -->
                        </div>
                    </div>
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Calendar Filters -->
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{% trans "Trainer Calendar Filters" %}</h3>
                        <div class="space-y-3 trainer-calendar-filters">
                            <label class="flex items-center">
                                <input type="checkbox" class="form-checkbox text-green-600 h-4 w-4 rounded" checked data-filter="session">
                                <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                            </label>
                        </div>
                    </div>

                    <!-- Upcoming Events -->
                    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                        <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Trainer Events" %}</h3>
                        <div id="trainerUpcomingEvents" class="space-y-4">
                            <!-- Events will be populated via JavaScript -->
                            <div class="p-4 text-center text-white/70">
                                <span>Select a trainer to see events</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

            <!-- Equipment Tab -->
            <div class="hidden" id="equipment" role="tabpanel" aria-labelledby="equipment-tab">
                <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
                    <!-- Calendar -->
                    <div class="lg:col-span-3">
                        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible">
                            <!-- Calendar Header -->
                            <div class="px-6 py-4 flex items-center justify-between border-b border-white/10">
                                <div class="flex items-center space-x-4">
                                    <div class="equipment-selector">
                                        <label class="text-white mr-2">{% trans "Select Equipment:" %}</label>
                                        <select id="equipmentSelect" class="bg-white/10 border border-white/20 text-white rounded-md px-3 py-2" style="z-index: 9999; position: relative;">
                                            <option value="">{% trans "All Equipment" %}</option>
                                            {% for item in equipment %}
                                            <option value="{{ item.equipment_id }}">{{ item.code }} - {{ item.name }}</option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>
                                <div class="flex items-center">
                                    <button id="equipmentPrevMonth" class="p-2 hover:bg-white/5 rounded-md">
                                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                                        </svg>
                                    </button>
                                    <h2 id="equipment-current-month-year" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                                    <button id="equipmentNextMonth" class="p-2 hover:bg-white/5 rounded-md">
                                        <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                                        </svg>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="flex rounded-md bg-gray-700 p-1">
                                        <button id="equipmentViewMonth" class="px-3 py-1 text-sm rounded-md bg-primary text-white">Month</button>
                                        <button id="equipmentViewWeek" class="px-3 py-1 text-sm rounded-md text-gray-300">Week</button>
                                        <button id="equipmentViewDay" class="px-3 py-1 text-sm rounded-md text-gray-300">Day</button>
                                    </div>
                                    <button id="equipmentTodayBtn" class="text-sm text-white hover:text-primary ml-2">{% trans "Today" %}</button>
                                </div>
                            </div>
                            
                            <div id="equipmentCalendarGrid" class="grid grid-cols-7 text-sm text-white">
                                <!-- Calendar cells will be generated via JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="space-y-6">
                        <!-- Calendar Filters -->
                        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">{% trans "Equipment Calendar Filters" %}</h3>
                            <div class="space-y-3 equipment-calendar-filters">
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox text-green-600 h-4 w-4 rounded" checked data-filter="session">
                                    <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="checkbox" class="form-checkbox text-blue-500 h-4 w-4 rounded" checked data-filter="event">
                                    <span class="ml-2 text-sm text-white">{% trans "Events" %}</span>
                                </label>
                            </div>
                        </div>

                        <!-- Upcoming Events -->
                        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                            <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Equipment Events" %}</h3>
                            <div id="equipmentUpcomingEvents" class="space-y-4">
                                <!-- Events will be populated via JavaScript -->
                                <div class="p-4 text-center text-white/70">
                                    <span>Select equipment to see events</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.form-checkbox {
    background-color: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
}
.form-checkbox:checked {
    border-color: transparent;
    background-color: currentColor;
}

/* Timeline styling */
.timeline-container {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-top: 20px;
}
.timeline-row {
    display: flex;
    align-items: center;
}
.timeline-label {
    width: 200px;
    padding-right: 15px;
    text-align: right;
    font-size: 14px;
}
.timeline-track {
    flex: 1;
    height: 40px;
    background-color: rgba(255, 255, 255, 0.05);
    position: relative;
    border-radius: 4px;
}
.timeline-event {
    position: absolute;
    height: 100%;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    color: white;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    padding: 0 8px;
}
.event-session {
    background-color: rgba(79, 70, 229, 0.6);
}
.event-external {
    background-color: rgba(16, 185, 129, 0.6);
}
.event-conflict {
    background-color: rgba(239, 68, 68, 0.6);
}
.timeline-hours {
    display: flex;
    margin-left: 200px;
    font-size: 10px;
    color: rgba(255, 255, 255, 0.5);
    margin-bottom: 5px;
}
.timeline-hour {
    flex: 1;
    text-align: center;
    position: relative;
}
.timeline-hour:before {
    content: '';
    position: absolute;
    top: 16px;
    left: 50%;
    height: 5px;
    width: 1px;
    background-color: rgba(255, 255, 255, 0.2);
}

/* Dropdown menu styles */
select {
    z-index: 999 !important;
    position: relative;
    appearance: auto !important;
    -webkit-appearance: auto !important;
    -moz-appearance: auto !important;
}

select option {
    background-color: #1e293b;
    color: white;
    padding: 8px;
}

/* Position the dropdown container relatively */
.room-selector, .trainer-selector, .equipment-selector {
    position: relative;
    z-index: 30;
}

/* Fix for dropdown visibility */
#roomSelect, #trainerSelect, #equipmentSelect {
    overflow: visible !important;
}

/* Ensure dropdown options are visible */
select:focus {
    outline: 2px solid #4f46e5;
}

/* Fix for Flowbite dropdown issues */
.flowbite-dropdown {
    z-index: 1000 !important;
}

/* Additional fix for dropdown menus */
select {
    position: relative !important;
    z-index: 9999 !important;
}

select option {
    position: relative !important;
    z-index: 10000 !important;
    background-color: #1e293b !important;
    color: white !important;
}

/* Fix for Firefox */
@-moz-document url-prefix() {
    select {
        -moz-appearance: menulist !important;
    }
}

/* Fix for Chrome */
@media screen and (-webkit-min-device-pixel-ratio:0) {
    select {
        -webkit-appearance: menulist !important;
    }
}

/* Additional CSS for holiday indicators */
.holiday-indicator {
    position: absolute;
    bottom: 2px;
    right: 2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    z-index: 10;
    transition: transform 0.2s ease-in-out;
}

.holiday-indicator:hover {
    transform: scale(1.5);
}

.calendar-cell {
    position: relative;
}
</style>

<script>
// Global variables
let calendarGrid;
let currentMonthYearElement;
let viewCurrentDate;
let currentView;
let allEvents = [];

// Helper function for logging debug information
function logDebug(message) {
    console.log(`[Calendar] ${new Date().toLocaleTimeString()} - ${message}`);
}

// Document ready event
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded - Calendar initialization starting');
    
    // Set global flag to prevent duplicate initialization
    if (window.calendarInitialized) {
        console.warn('Calendar already initialized - skipping duplicate initialization');
        return;
    }
    
    // Mark as initialized
    window.calendarInitialized = true;
    
    // Get elements
    calendarGrid = document.getElementById('calendarGrid');
    currentMonthYearElement = document.getElementById('current-month-year');
    
    console.log('Initializing main calendar');
    
    try {
        // Initialize tabs
        const tabsElement = document.getElementById('calendar-tabs');
        console.log('Found tabs element:', tabsElement);
        
        if (window.Flowbite && window.Flowbite.initTabs) {
            console.log('Using Flowbite API to initialize tabs');
            window.Flowbite.initTabs();
        } else {
            console.log('Using custom tabs initialization');
            // Add click handlers to all tab elements
            const tabs = document.querySelectorAll('[role="tab"]');
            tabs.forEach(tab => {
                tab.addEventListener('click', function(e) {
                    e.preventDefault();
                    
                    // Get the target content ID from aria-controls
                    const targetId = this.getAttribute('aria-controls');
                    
                    // Hide all tab content
                    document.querySelectorAll('[role="tabpanel"]').forEach(panel => {
                        panel.classList.add('hidden');
                    });
                    
                    // Show the target content
                    if (targetId) {
                        const targetPanel = document.getElementById(targetId);
                        if (targetPanel) {
                            targetPanel.classList.remove('hidden');
                        }
                    }
                    
                    // Update active state on tabs
                    tabs.forEach(t => {
                        t.setAttribute('aria-selected', 'false');
                        t.classList.remove('text-primary', 'border-primary');
                        t.classList.add('text-gray-400', 'border-transparent');
                    });
                    
                    // Set this tab as active
                    this.setAttribute('aria-selected', 'true');
                    this.classList.remove('text-gray-400', 'border-transparent');
                    this.classList.add('text-primary', 'border-primary');
                });
            });
        }
        
        // Initialize main calendar
        initMainCalendar();
        
        // Set up main navigation button handlers ONCE
        console.log('Setting up main calendar navigation buttons');
        const prevBtn = document.getElementById('prevMonth');
        if (prevBtn) {
            console.log('Found prevMonth button, adding event listener');
            // First remove any existing listeners to avoid duplicates
            prevBtn.replaceWith(prevBtn.cloneNode(true));
            const newPrevBtn = document.getElementById('prevMonth');
            newPrevBtn.addEventListener('click', function() {
                console.log('Previous month button clicked');
                navigateCalendar('prev');
            });
        }
        
        const nextBtn = document.getElementById('nextMonth');
        if (nextBtn) {
            console.log('Found nextMonth button, adding event listener');
            // First remove any existing listeners to avoid duplicates
            nextBtn.replaceWith(nextBtn.cloneNode(true));
            const newNextBtn = document.getElementById('nextMonth');
            newNextBtn.addEventListener('click', function() {
                console.log('Next month button clicked');
                navigateCalendar('next');
            });
        }
        
        const todayBtn = document.getElementById('todayBtn');
        if (todayBtn) {
            console.log('Found today button, adding event listener');
            // First remove any existing listeners to avoid duplicates
            todayBtn.replaceWith(todayBtn.cloneNode(true));
            const newTodayBtn = document.getElementById('todayBtn');
            newTodayBtn.addEventListener('click', function() {
                console.log('Today button clicked');
                navigateCalendar('today');
            });
        }
        
        console.log('Calendar initialization complete');
        
    } catch (error) {
        console.error('Error initializing calendar:', error);
    }
});

// Function to navigate the calendar
function navigateCalendar(direction) {
    // Clear debug info
    console.clear();
    console.log(`CALENDAR NAVIGATION: '${direction}' at ${new Date().toISOString()}`);
    
    if (!window.calendarState) {
        console.error('Calendar state not initialized');
        return;
    }
    
    // Store current date before modification
    const prevDate = new Date(window.calendarState.currentDate);
    console.log(`Previous date: ${prevDate.toISOString()}`);
    
    // Create a NEW date object instead of modifying the existing one
    let newDate = new Date(window.calendarState.currentDate);
    
    if (direction === 'prev') {
        if (window.calendarState.currentView === 'month') {
            // Go to previous month
            newDate.setMonth(newDate.getMonth() - 1);
        } else if (window.calendarState.currentView === 'week') {
            // Go to previous week
            newDate.setDate(newDate.getDate() - 7);
        } else if (window.calendarState.currentView === 'day') {
            // Go to previous day
            newDate.setDate(newDate.getDate() - 1);
        }
    } else if (direction === 'next') {
        if (window.calendarState.currentView === 'month') {
            // Go to next month
            newDate.setMonth(newDate.getMonth() + 1);
        } else if (window.calendarState.currentView === 'week') {
            // Go to next week
            newDate.setDate(newDate.getDate() + 7);
        } else if (window.calendarState.currentView === 'day') {
            // Go to next day
            newDate.setDate(newDate.getDate() + 1);
        }
    } else if (direction === 'today') {
        // Go to today
        newDate = new Date();
    }
    
    // Set the new date
    window.calendarState.currentDate = newDate;
    
    // Log the new date for debugging
    console.log(`New date: ${newDate.toISOString()}`);
    
    // Update the calendar with the new date
    updateCalendar();
}

// Function to update the active state of view buttons
function updateActiveButton(activeButtonId) {
    const viewButtons = ['viewMonth', 'viewWeek', 'viewDay'];
    viewButtons.forEach(id => {
        const button = document.getElementById(id);
        if (button) {
            if (id === activeButtonId) {
                button.classList.add('bg-primary', 'text-white');
                button.classList.remove('bg-gray-700', 'text-gray-300');
            } else {
                button.classList.remove('bg-primary', 'text-white');
                button.classList.add('bg-gray-700', 'text-gray-300');
            }
        }
    });
}

// Main calendar initialization
function initMainCalendar() {
    // Initialize calendar state
    window.calendarState = {
        currentDate: new Date(),
        currentView: 'month', // 'month', 'week', or 'day'
        allEvents: [], // Store all events for filtering
    };
    
    const calendarGrid = document.getElementById('calendarGrid');
    const currentMonthYear = document.getElementById('current-month-year');
    const upcomingEventsContainer = document.getElementById('upcomingEvents');
    
    if (!calendarGrid) {
        console.error('Calendar grid element not found');
        return;
    }
    
    // Set initial month/year display
    if (currentMonthYear) {
        currentMonthYear.textContent = window.calendarState.currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }
    
    // Show loading indicator
    calendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading events...</div>';
    
    // Set up view buttons
    const viewMonth = document.getElementById('viewMonth');
    const viewWeek = document.getElementById('viewWeek');
    const viewDay = document.getElementById('viewDay');
    
    if (viewMonth) {
        viewMonth.addEventListener('click', function() {
            window.calendarState.currentView = 'month';
            updateCalendar();
            updateViewButtons('viewMonth', ['viewMonth', 'viewWeek', 'viewDay']);
        });
    }
    
    if (viewWeek) {
        viewWeek.addEventListener('click', function() {
            window.calendarState.currentView = 'week';
            updateCalendar();
            updateViewButtons('viewWeek', ['viewMonth', 'viewWeek', 'viewDay']);
        });
    }
    
    if (viewDay) {
        viewDay.addEventListener('click', function() {
            window.calendarState.currentView = 'day';
            updateCalendar();
            updateViewButtons('viewDay', ['viewMonth', 'viewWeek', 'viewDay']);
        });
    }
    
    // Set up navigation buttons
    const prevBtn = document.getElementById('prevMonth');
    const nextBtn = document.getElementById('nextMonth');
    const todayBtn = document.getElementById('todayBtn');
    
    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            navigateCalendar('prev');
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            navigateCalendar('next');
        });
    }
    
    if (todayBtn) {
        todayBtn.addEventListener('click', function() {
            navigateCalendar('today');
        });
    }
    
    // Set up event filter checkboxes
    const sessionCheckbox = document.querySelector('.calendar-filters input[data-filter="session"]');
    const eventCheckbox = document.querySelector('.calendar-filters input[data-filter="event"]');
    const holidayCheckbox = document.querySelector('.calendar-filters input[data-filter="holiday"]');
    
    if (sessionCheckbox) {
        console.log('Found session filter checkbox, adding event listener');
        sessionCheckbox.addEventListener('change', function() {
            console.log('Sessions filter changed:', this.checked);
            refreshCalendarView();
        });
    } else {
        console.warn('Session filter checkbox not found');
    }
    
    if (eventCheckbox) {
        console.log('Found event filter checkbox, adding event listener');
        eventCheckbox.addEventListener('change', function() {
            console.log('Events filter changed:', this.checked);
            refreshCalendarView();
        });
    } else {
        console.warn('Event filter checkbox not found');
    }
    
    if (holidayCheckbox) {
        console.log('Found holiday filter checkbox, adding event listener');
        holidayCheckbox.addEventListener('change', function() {
            console.log('Holidays filter changed:', this.checked);
            refreshCalendarView();
        });
    } else {
        console.warn('Holiday filter checkbox not found');
    }
    
    // Add event listener for room selector
    const roomSelect = document.getElementById('roomSelect');
    if (roomSelect) {
        roomSelect.addEventListener('change', function() {
            updateCalendar(); // Refresh calendar when room selection changes
        });
    }
    
    // Initial calendar load
    const month = window.calendarState.currentDate.getMonth();
    const year = window.calendarState.currentDate.getFullYear();
    
    // Get calendar events from API
    const startDate = new Date(year, month, 1);
    const endDate = new Date(year, month + 1, 0);
    
    getCalendarEvents(startDate, endDate)
        .then(events => {
            // Store all events for filtering
            window.calendarState.allEvents = events;
            
            // Filter events based on checkboxes
            const filteredEvents = events.filter(shouldShowEvent);
            
            // Render calendar
            renderCalendar(month, year, window.calendarState.currentView, filteredEvents, calendarGrid);
            
            // Show upcoming events
            if (upcomingEventsContainer) {
                showUpcomingEvents(filteredEvents, upcomingEventsContainer, 'main');
            }
        })
        .catch(error => {
            console.error('Error loading calendar events:', error);
            calendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events. Please try again later.</div>';
        });
}

// Refresh calendar view based on current filters
function refreshCalendarView() {
    console.log('Refreshing calendar view based on filters');
    
    const calendarGrid = document.getElementById('calendarGrid');
    const upcomingEventsContainer = document.getElementById('upcomingEvents');
    
    if (!window.calendarState || !window.calendarState.allEvents) {
        console.warn('No calendar state or events found');
        return;
    }
    
    const events = window.calendarState.allEvents || [];
    console.log('Total events before filtering:', events.length);
    
    // Apply filters
    const filteredEvents = events.filter(shouldShowEvent);
    console.log('Events after filtering:', filteredEvents.length);
    
    // Get current month/year
    const month = window.calendarState.currentDate.getMonth();
    const year = window.calendarState.currentDate.getFullYear();
    
    // Re-render calendar with filtered events
    renderCalendar(month, year, window.calendarState.currentView, filteredEvents, calendarGrid);
    
    // Update upcoming events
    if (upcomingEventsContainer) {
        showUpcomingEvents(filteredEvents, upcomingEventsContainer, 'main');
    }
}

// Check if an event should be shown based on filter checkboxes
function shouldShowEvent(event) {
    console.log('Filtering event:', event.title, 'Type:', event.type);
    
    // Get filter checkboxes
    const sessionCheckbox = document.querySelector('.calendar-filters input[data-filter="session"]');
    const eventCheckbox = document.querySelector('.calendar-filters input[data-filter="event"]');
    const holidayCheckbox = document.querySelector('.calendar-filters input[data-filter="holiday"]');
    
    // Default to showing all types if checkboxes don't exist
    const showSessions = sessionCheckbox ? sessionCheckbox.checked : true;
    const showEvents = eventCheckbox ? eventCheckbox.checked : true;
    const showHolidays = holidayCheckbox ? holidayCheckbox.checked : true;
    
    console.log('Filter settings - Sessions:', showSessions, 'Events:', showEvents, 'Holidays:', showHolidays);
    
    // Ensure type is lowercase for case-insensitive comparison
    const eventType = event.type ? event.type.toLowerCase() : '';
    
    // Explicitly check event type
    if (eventType === 'session' || event.is_session === true) {
        return showSessions;
    } else if (eventType === 'event' || eventType === '' || !eventType) {
        return showEvents;
    } else if (eventType === 'holiday') {
        return showHolidays;
    } else {
        // For any other type, show it by default
        console.log('Event with unknown type:', event);
        return true;
    }
}

// Get calendar events from the API
function getCalendarEvents(startDate, endDate) {
    // Format dates for API request
    const formatDate = (date) => {
        return date.toISOString().split('T')[0]; // YYYY-MM-DD format
    };
    
    // Get the selected room ID if any
    const roomSelect = document.getElementById('roomSelect');
    let roomId = null;
    
    if (roomSelect && roomSelect.value && roomSelect.value !== 'All Rooms') {
        const roomText = roomSelect.value;
        console.log('Selected room value:', roomText);
        
        // Check if the room value is already a numeric ID
        if (/^\d+$/.test(roomText)) {
            roomId = roomText;
        } else {
            // Try to extract room ID from strings like "room 1 (12 capacity)"
            const roomMatch = roomText.match(/room\s+(\d+)/i);
            if (roomMatch && roomMatch[1]) {
                roomId = roomMatch[1];
            } else {
                // Last attempt - extract any number
                const numberMatch = roomText.match(/(\d+)/);
                if (numberMatch && numberMatch[1]) {
                    roomId = numberMatch[1];
                }
            }
        }
        
        // Log selected room information
        console.log('Room selection detected:', roomText, '→ Extracted ID:', roomId);
        
        // Log dropdown options for debugging
        if (roomSelect.options) {
            console.log('Available room options:');
            for (let i = 0; i < roomSelect.options.length; i++) {
                console.log(`- Option ${i}: value='${roomSelect.options[i].value}', text='${roomSelect.options[i].text}'`);
            }
        }
    }
    
    // Build the API URL with date parameters and room filter
    let url = `{% url 'website:calendar_events_api' %}?start=${formatDate(startDate)}&end=${formatDate(endDate)}`;
    
    // Add room ID parameter if a room is selected
    if (roomId) {
        url += `&room_id=${roomId}`;
    }
    
    console.log('Fetching calendar events from API:', url);
    
    // Return a Promise that resolves with the events
    return new Promise((resolve, reject) => {
        fetch(url)
            .then(response => {
                console.log('API response status:', response.status);
                if (!response.ok) {
                    throw new Error('Network response was not ok: ' + response.status);
                }
                return response.json();
            })
            .then(response => {
                console.log('API response received:', response);
                
                // Check if events were returned
                if (response.events && Array.isArray(response.events)) {
                    let events = response.events;
                    console.log('Found events:', events.length);
                    
                    // Log details of the first few events to understand their structure
                    if (events.length > 0) {
                        console.log('First event details:', events[0]);
                        if (events[0].room) {
                            console.log('Room info in first event:', typeof events[0].room, events[0].room);
                        }
                        
                        // Log the first 3 events (or fewer if less available)
                        const sampleCount = Math.min(3, events.length);
                        for (let i = 0; i < sampleCount; i++) {
                            const event = events[i];
                            console.log(`Event ${i+1}/${sampleCount}:`, {
                                id: event.id,
                                title: event.title,
                                type: event.type,
                                start: event.start,
                                room_id: event.room_id,
                                room: event.room
                            });
                        }
                    }
                    
                    // Convert date strings to actual date objects for each event
                    events = events.map(event => {
                        try {
                            // Ensure 'date' field is available for filtering
                            event.date = event.start;
                            
                            // Ensure each event has a type property
                            if (!event.type) {
                                // Default to 'event' if session is not explicitly specified
                                event.type = event.is_session ? 'session' : 'event';
                                console.log(`Set missing type for event "${event.title}" to "${event.type}"`);
                            }
                            
                            // Normalize type to lowercase for consistent filtering
                            if (typeof event.type === 'string') {
                                event.type = event.type.toLowerCase();
                            }
                            
                            return event;
                        } catch (e) {
                            console.error('Error processing event:', e, event);
                            // Ensure event has a type even if there's an error
                            if (!event.type) event.type = 'event';
                            return event;
                        }
                    });
                    
                    resolve(events);
                } else {
                    console.log('No events found or invalid response format');
                    console.log('Response details:', response);
                    resolve([]);
                }
            })
            .catch(error => {
                console.error('Error fetching calendar events:', error);
                reject(error);
            });
    });
}

// Create event element for display in calendar
function createEventElement(event) {
    // Create the event element
    const eventElement = document.createElement('div');
    eventElement.className = 'mb-1 p-1 rounded text-xs cursor-pointer hover:opacity-80 transition-opacity';
    
    // Set different colors for session vs event
    if (event.type === 'session') {
        eventElement.classList.add('bg-green-600');
    } else {
        eventElement.classList.add('bg-blue-600');
    }
    
    // Override with explicit color if provided
    if (event.color && event.color.startsWith('#')) {
        eventElement.style.backgroundColor = event.color;
    } else if (event.color && !event.color.startsWith('#')) {
        eventElement.classList.add(event.color);
    }
    
    // Set text color to white
    eventElement.classList.add('text-white');
    
    // Create time display for tooltip
    let timeText = '';
    try {
        const eventDate = new Date(event.date);
        const hours = eventDate.getHours();
        const minutes = eventDate.getMinutes();
        const ampm = hours >= 12 ? 'PM' : 'AM';
        const displayHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
        timeText = `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
    } catch (e) {
        timeText = 'Time N/A';
    }
    
    // Create simple content with just the title
    eventElement.innerHTML = `<div class="font-medium truncate">${event.title || 'Untitled Event'}</div>`;
    
    // Add tooltip attributes for hover
    eventElement.setAttribute('title', `${event.title || 'Untitled Event'}\nType: ${event.type || 'Event'}\nTime: ${timeText}`);
    
    // Add click event to show details
    eventElement.addEventListener('click', () => {
        alert(`${event.title || 'Event Details'}\nType: ${event.type || 'Not specified'}\nTime: ${timeText}\nID: ${event.id || 'N/A'}`);
    });
    
    return eventElement;
}

// Reset grid layout classes
function resetGridLayout() {
    calendarGrid.className = 'grid';
}

// Render month view
function renderMonthView(month, year, events, container) {
    // Set grid classes
    container.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Add day headers (Sun - Sat)
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    days.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        container.appendChild(dayHeader);
    });
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayIndex = firstDay.getDay();
    
    // Calculate days from previous month to display
    for (let i = 0; i < firstDayIndex; i++) {
        const previousMonthDay = new Date(year, month, -firstDayIndex + i + 1);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = previousMonthDay.getDate();
        dayCell.appendChild(dateDisplay);
        
        container.appendChild(dayCell);
    }
    
    // Add days for current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 hover:bg-white/10 overflow-y-auto cursor-pointer';
        
        // Check if this is today
        const today = new Date();
        if (currentDate.getDate() === today.getDate() && 
            currentDate.getMonth() === today.getMonth() && 
            currentDate.getFullYear() === today.getFullYear()) {
            dayCell.classList.add('border', 'border-primary', 'bg-primary/10');
        }
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/70';
        dateDisplay.textContent = i;
        dayCell.appendChild(dateDisplay);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.date);
                return eventDate.getDate() === i && 
                       eventDate.getMonth() === month && 
                       eventDate.getFullYear() === year;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = createEventElement(event);
            dayCell.appendChild(eventElement);
        });
        
        // Add click event to open the calendar add popup for this date
        dayCell.addEventListener('click', function(e) {
            // Only trigger if clicking on the cell itself, not on an event
            if (e.target === dayCell || e.target === dateDisplay) {
                if (typeof openCalendarAddPopup === 'function') {
                    openCalendarAddPopup(currentDate);
                }
            }
        });
        
        container.appendChild(dayCell);
    }
    
    // Fill remaining cells with next month's days
    const totalCells = 42; // 6 rows x 7 days
    const cellsToAdd = totalCells - (firstDayIndex + lastDay.getDate());
    
    for (let i = 1; i <= cellsToAdd; i++) {
        const nextMonthDay = new Date(year, month + 1, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = i;
        dayCell.appendChild(dateDisplay);
        
        container.appendChild(dayCell);
    }
}

// Render week view
function renderWeekView(startDate, endDate, events) {
    calendarGrid.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Create and append day headers
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    weekdays.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        calendarGrid.appendChild(dayHeader);
    });
    
    // Get the first day of the week (Sunday)
    const weekStart = new Date(startDate);
    
    // Create day cells for the week
    for (let i = 0; i < 7; i++) {
        const currentDate = new Date(weekStart);
        currentDate.setDate(weekStart.getDate() + i);
        
        const dayCell = document.createElement('div');
        dayCell.className = 'border-t border-white/10 h-32 overflow-y-auto p-1';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/70';
        dateDisplay.textContent = currentDate.getDate();
        dayCell.appendChild(dateDisplay);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.date);
                return eventDate.getDate() === currentDate.getDate() && 
                       eventDate.getMonth() === currentDate.getMonth() && 
                       eventDate.getFullYear() === currentDate.getFullYear();
            } catch (e) {
                console.error('Error filtering event for week view:', e);
                return false;
            }
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = createEventElement(event);
            dayCell.appendChild(eventElement);
        });
        
        calendarGrid.appendChild(dayCell);
    }
}

// Render day view
function renderDayView(selectedDate, events) {
    calendarGrid.className = 'grid grid-cols-1 gap-1 p-2';
    
    // Filter events for this day
    const dayDate = selectedDate;
    const dayEvents = events.filter(event => {
        try {
            const eventDate = new Date(event.date);
            return eventDate.getDate() === dayDate.getDate() && 
                   eventDate.getMonth() === dayDate.getMonth() && 
                   eventDate.getFullYear() === dayDate.getFullYear();
        } catch (e) {
            console.error('Error filtering event for day view:', e);
            return false;
        }
    });
    
    // Create hour cells for the day (9am to 9pm)
    for (let hour = 9; hour <= 21; hour++) {
        const hourCell = document.createElement('div');
        hourCell.className = 'border-t border-white/10 min-h-16 p-1 relative';
        
        // Add time label
        const timeLabel = document.createElement('div');
        timeLabel.className = 'text-xs text-white/70 absolute top-1 left-1';
        timeLabel.textContent = `${hour}:00`;
        hourCell.appendChild(timeLabel);
        
        // Add a content div with padding to not overlap the time label
        const contentDiv = document.createElement('div');
        contentDiv.className = 'pt-5 pl-14';
        
        // Filter events for this hour
        const hourEvents = dayEvents.filter(event => {
            try {
                const eventTime = new Date(event.date);
                return eventTime.getHours() === hour;
            } catch (e) {
                console.error('Error filtering event by hour:', e);
                return false;
            }
        });
        
        // Add events to the content div
        if (hourEvents.length > 0) {
        hourEvents.forEach(event => {
                const eventElement = createEventElement(event);
                contentDiv.appendChild(eventElement);
            });
        }
        
        hourCell.appendChild(contentDiv);
        calendarGrid.appendChild(hourCell);
    }
}

// Room calendar functionality
function initRoomCalendar() {
    console.log('initRoomCalendar called');
    
    // Set global flag to prevent duplicate initialization
    if (window.roomCalendarInitialized) {
        console.warn('Room calendar already initialized - skipping duplicate initialization');
        
        // Still update based on the current date
        updateRoomCalendar();
        return;
    }
    
    // Mark as initialized
    window.roomCalendarInitialized = true;

    // Get elements with proper error checking
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    const roomCurrentMonthYear = document.getElementById('room-current-month-year');
    const roomUpcomingEvents = document.getElementById('roomUpcomingEvents');
    
    if (!roomCalendarGrid) {
        console.error('Room calendar grid element not found');
        return;
    }
    
    // Initialize room calendar state
    window.roomCalendarState = {
        currentDate: new Date(),
        currentView: 'month',
        allEvents: []
    };
    
    const currentDate = window.roomCalendarState.currentDate;
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Bind room selection change event
    if (roomSelect) {
        roomSelect.addEventListener('change', function() {
            console.log('Room selection changed, updating calendar');
            updateRoomCalendar();
        });
    }
    
    // Show loading indicator
    roomCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading room events...</div>';
    
    if (roomCurrentMonthYear) {
        roomCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }
    
    // Set up view buttons
    const roomViewMonth = document.getElementById('roomViewMonth');
    const roomViewWeek = document.getElementById('roomViewWeek');
    const roomViewDay = document.getElementById('roomViewDay');
    
    if (roomViewMonth) {
        roomViewMonth.addEventListener('click', function() {
            window.roomCalendarState.currentView = 'month';
            updateRoomCalendar();
            updateRoomViewButtons('roomViewMonth');
        });
    }
    
    if (roomViewWeek) {
        roomViewWeek.addEventListener('click', function() {
            window.roomCalendarState.currentView = 'week';
            updateRoomCalendar();
            updateRoomViewButtons('roomViewWeek');
        });
    }
    
    if (roomViewDay) {
        roomViewDay.addEventListener('click', function() {
            window.roomCalendarState.currentView = 'day';
            updateRoomCalendar();
            updateRoomViewButtons('roomViewDay');
        });
    }
    
    // Initialize view buttons state
    updateRoomViewButtons('roomViewMonth');
    
    // Build date range for fetching events
    const startDate = new Date(currentYear, currentMonth, 1);
    const endDate = new Date(currentYear, currentMonth + 1, 0); // Last day of month
    
    // Fetch events for all rooms and the date range
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}`;
    
    // Add room filter if specific room is selected
    if (roomSelect && roomSelect.value && roomSelect.value !== '') {
        apiUrl += `&room_id=${roomSelect.value}`;
    }
    
    console.log('Fetching room events from API:', apiUrl);
    
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // Store events and render calendar
            window.roomCalendarState.allEvents = data.events || [];
            const filteredEvents = window.roomCalendarState.allEvents;
            renderRoomCalendar(currentMonth, currentYear, 'month', filteredEvents);
            
            // Show upcoming events
            if (roomUpcomingEvents) {
                showUpcomingEvents(filteredEvents, roomUpcomingEvents, 'room');
            }
        })
        .catch(error => {
            console.error('Error loading room events:', error);
            roomCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events. Please try again.</div>';
        });
    
    // Set up event listeners for room calendar filter checkboxes
    const roomSessionCheckbox = document.querySelector('.room-calendar-filters input[data-filter="session"]');
    const roomEventCheckbox = document.querySelector('.room-calendar-filters input[data-filter="event"]');
    
    if (roomSessionCheckbox) {
        roomSessionCheckbox.addEventListener('change', refreshRoomCalendarView);
    }
    
    if (roomEventCheckbox) {
        roomEventCheckbox.addEventListener('change', refreshRoomCalendarView);
    }
    
    // Set up navigation buttons
    const roomPrevMonth = document.getElementById('roomPrevMonth');
    const roomNextMonth = document.getElementById('roomNextMonth');
    const roomTodayBtn = document.getElementById('roomTodayBtn');
    
    console.log('Setting up room calendar navigation buttons in initRoomCalendar');
    if (roomPrevMonth) {
        console.log('Found roomPrevMonth button in initRoomCalendar');
        // First remove any existing listeners to avoid duplicates
        roomPrevMonth.replaceWith(roomPrevMonth.cloneNode(true));
        const newRoomPrevBtn = document.getElementById('roomPrevMonth');
        newRoomPrevBtn.addEventListener('click', function() {
            console.log('Previous month button clicked in initRoomCalendar');
            navigateRoomCalendar('prev');
        });
    }
    
    if (roomNextMonth) {
        console.log('Found roomNextMonth button in initRoomCalendar');
        // First remove any existing listeners to avoid duplicates
        roomNextMonth.replaceWith(roomNextMonth.cloneNode(true));
        const newRoomNextBtn = document.getElementById('roomNextMonth');
        newRoomNextBtn.addEventListener('click', function() {
            console.log('Next month button clicked in initRoomCalendar');
            navigateRoomCalendar('next');
        });
    }
    
    if (roomTodayBtn) {
        console.log('Found roomTodayBtn button in initRoomCalendar');
        // First remove any existing listeners to avoid duplicates
        roomTodayBtn.replaceWith(roomTodayBtn.cloneNode(true));
        const newRoomTodayBtn = document.getElementById('roomTodayBtn');
        newRoomTodayBtn.addEventListener('click', function() {
            console.log('Today button clicked in initRoomCalendar');
            navigateRoomCalendar('today');
        });
    }
}

// Function to refresh the room calendar view based on filters
function refreshRoomCalendarView() {
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    const roomUpcomingEvents = document.getElementById('roomUpcomingEvents');
    
    if (!window.roomCalendarState) {
        window.roomCalendarState = {
            currentDate: new Date(),
            currentView: 'month',
            allEvents: []
        };
        return;
    }
    
    // Get events and apply filters
    const events = window.roomCalendarState.allEvents || [];
    const filteredEvents = events.filter(shouldShowRoomEvent);
    
    // Get current date from state
    const currentDate = window.roomCalendarState.currentDate;
    const month = currentDate.getMonth();
    const year = currentDate.getFullYear();
    
    // Re-render calendar with filtered events
    renderRoomCalendar(month, year, 'month', filteredEvents);
    
    // Update upcoming events
    if (roomUpcomingEvents) {
        showUpcomingEvents(filteredEvents, roomUpcomingEvents, 'room');
    }
}

// Function to check if an event should be shown based on filters
function shouldShowRoomEvent(event) {
    const sessionCheckbox = document.querySelector('.room-calendar-filters input[data-filter="session"]');
    const eventCheckbox = document.querySelector('.room-calendar-filters input[data-filter="event"]');
    
    const showSessions = sessionCheckbox ? sessionCheckbox.checked : true;
    const showEvents = eventCheckbox ? eventCheckbox.checked : true;
    
    // Ensure type is lowercase for case-insensitive comparison
    const eventType = event.type ? event.type.toLowerCase() : '';
    
    if (eventType === 'session') {
        return showSessions;
    } else if (eventType === 'holiday') {
        // Always show holidays
        return true;
    } else {
        return showEvents;
    }
}

// Render room calendar
function renderRoomCalendar(month, year, view, events) {
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    if (!roomCalendarGrid) {
        console.error('Room calendar grid element not found');
        return;
    }
    
    // Clear the grid and remove loading indicator
    roomCalendarGrid.innerHTML = '';
    
    // Use similar layout as the main calendar
    roomCalendarGrid.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Add day headers (Sun - Sat)
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    days.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        roomCalendarGrid.appendChild(dayHeader);
    });
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayIndex = firstDay.getDay();
    
    // Calculate days from previous month to display
    for (let i = 0; i < firstDayIndex; i++) {
        const previousMonthDay = new Date(year, month, -firstDayIndex + i + 1);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = previousMonthDay.getDate();
        dayCell.appendChild(dateDisplay);
        
        roomCalendarGrid.appendChild(dayCell);
    }
    
    // Add days for current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 hover:bg-white/10 overflow-y-auto relative cursor-pointer';
        
        // Check if this is today
        const today = new Date();
        if (currentDate.getDate() === today.getDate() && 
            currentDate.getMonth() === today.getMonth() && 
            currentDate.getFullYear() === today.getFullYear()) {
            dayCell.classList.add('border', 'border-primary', 'bg-primary/10');
        }
        
        // Create header container for date and add button
        const headerContainer = document.createElement('div');
        headerContainer.className = 'flex justify-between items-center mb-1';
        
        // Add date to the header
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-sm text-white/70';
        dateDisplay.textContent = i;
        headerContainer.appendChild(dateDisplay);
        
        // Add the "+" button
        const addButton = document.createElement('button');
        addButton.className = 'text-primary hover:text-white bg-primary/20 hover:bg-primary/40 rounded-full w-5 h-5 flex items-center justify-center transition-colors';
        addButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>';
        addButton.title = 'Add Event';
        
        // Format date for popup
        addButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            if (typeof openCalendarAddPopup === 'function') {
                openCalendarAddPopup(currentDate);
            }
        });
        
        headerContainer.appendChild(addButton);
        dayCell.appendChild(headerContainer);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.start);
                return eventDate.getDate() === i && 
                       eventDate.getMonth() === month && 
                       eventDate.getFullYear() === year;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = document.createElement('div');
            
            // Set classes based on event type
            if (event.type === 'session') {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-green-600/70 truncate';
            } else if (event.type === 'holiday') {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-red-600/70 truncate';
                // Add holiday emoji prefix
                if (!event.title.startsWith('🎉')) {
                    event.title = `🎉 ${event.title}`;
                }
            } else {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-blue-500/70 truncate';
            }
            
            // Set event title
            eventElement.textContent = event.title || 'Untitled Event';
            
            // Add the event to the cell
            dayCell.appendChild(eventElement);
        });
        
        // Add click event to open the calendar add popup for this date
        dayCell.addEventListener('click', function(e) {
            // Only trigger if clicking on the cell background, not on events or buttons
            if (e.target === dayCell) {
                if (typeof openCalendarAddPopup === 'function') {
                    openCalendarAddPopup(currentDate);
                }
            }
        });
        
        roomCalendarGrid.appendChild(dayCell);
    }
    
    // Fill remaining cells with next month's days
    const totalCells = 42; // 6 rows x 7 days
    const cellsToAdd = totalCells - (firstDayIndex + lastDay.getDate());
    
    for (let i = 1; i <= cellsToAdd; i++) {
        const nextMonthDay = new Date(year, month + 1, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = i;
        dayCell.appendChild(dateDisplay);
        
        roomCalendarGrid.appendChild(dayCell);
    }
}

// Set up room calendar controls
function setupRoomCalendarControls() {
    const roomPrevMonth = document.getElementById('roomPrevMonth');
    const roomNextMonth = document.getElementById('roomNextMonth');
    const roomTodayBtn = document.getElementById('roomTodayBtn');
    const roomViewMonth = document.getElementById('roomViewMonth');
    const roomViewWeek = document.getElementById('roomViewWeek');
    const roomViewDay = document.getElementById('roomViewDay');
    
    // Initialize room view state
    window.roomCalendarState = {
        currentDate: new Date(),
        currentView: 'month'
    };
    
    // Add event listeners for navigation buttons
    console.log('Setting up room calendar navigation buttons');
    if (roomPrevMonth) {
        console.log('Found roomPrevMonth button, adding event listener');
        // First remove any existing listeners to avoid duplicates
        roomPrevMonth.replaceWith(roomPrevMonth.cloneNode(true));
        const newRoomPrevBtn = document.getElementById('roomPrevMonth');
        newRoomPrevBtn.addEventListener('click', function() {
            console.log('Previous month button clicked for room calendar');
            navigateRoomCalendar('prev');
        });
    }
    
    if (roomNextMonth) {
        console.log('Found roomNextMonth button, adding event listener');
        // First remove any existing listeners to avoid duplicates
        roomNextMonth.replaceWith(roomNextMonth.cloneNode(true));
        const newRoomNextBtn = document.getElementById('roomNextMonth');
        newRoomNextBtn.addEventListener('click', function() {
            console.log('Next month button clicked for room calendar');
            navigateRoomCalendar('next');
        });
    }
    
    if (roomTodayBtn) {
        console.log('Found roomTodayBtn button, adding event listener');
        // First remove any existing listeners to avoid duplicates
        roomTodayBtn.replaceWith(roomTodayBtn.cloneNode(true));
        const newRoomTodayBtn = document.getElementById('roomTodayBtn');
        newRoomTodayBtn.addEventListener('click', function() {
            console.log('Today button clicked for room calendar');
            navigateRoomCalendar('today');
        });
    }
    
    // Add event listeners for view buttons
    if (roomViewMonth) {
        roomViewMonth.addEventListener('click', function() {
            window.roomCalendarState.currentView = 'month';
            updateRoomCalendar();
            updateRoomViewButtons('roomViewMonth');
        });
    }
    
    if (roomViewWeek) {
        roomViewWeek.addEventListener('click', function() {
            window.roomCalendarState.currentView = 'week';
            updateRoomCalendar();
            updateRoomViewButtons('roomViewWeek');
        });
    }
    
    if (roomViewDay) {
        roomViewDay.addEventListener('click', function() {
            window.roomCalendarState.currentView = 'day';
            updateRoomCalendar();
            updateRoomViewButtons('roomViewDay');
        });
    }
}

// Navigate room calendar
function navigateRoomCalendar(direction) {
    // Clear debug info and log navigation
    console.clear();
    console.log(`ROOM CALENDAR NAVIGATION: '${direction}' at ${new Date().toISOString()}`);
    
    const state = window.roomCalendarState;
    if (!state) {
        console.error('Room calendar state not initialized');
        return;
    }
    
    // Add debounce to prevent multiple rapid calls
    if (window.roomNavigationInProgress) {
        console.log('Room navigation already in progress, ignoring duplicate call');
        return;
    }
    
    // Set a flag to indicate navigation is in progress
    window.roomNavigationInProgress = true;
    
    // Store current date before modification
    const prevDate = new Date(state.currentDate);
    console.log(`Previous date: ${prevDate.toISOString()}`);
    
    // Create a NEW date object instead of modifying the existing one
    let newDate = new Date(state.currentDate);
    
    if (direction === 'prev') {
        if (state.currentView === 'month') {
            // Go to previous month
            newDate.setMonth(newDate.getMonth() - 1);
        } else if (state.currentView === 'week') {
            // Go to previous week
            newDate.setDate(newDate.getDate() - 7);
        } else if (state.currentView === 'day') {
            // Go to previous day
            newDate.setDate(newDate.getDate() - 1);
        }
    } else if (direction === 'next') {
        if (state.currentView === 'month') {
            // Go to next month
            newDate.setMonth(newDate.getMonth() + 1);
        } else if (state.currentView === 'week') {
            // Go to next week
            newDate.setDate(newDate.getDate() + 7);
        } else if (state.currentView === 'day') {
            // Go to next day
            newDate.setDate(newDate.getDate() + 1);
        }
    } else if (direction === 'today') {
        // Go to today
        newDate = new Date();
    }
    
    // Set the new date
    state.currentDate = newDate;
    
    // Log the new date for debugging
    console.log(`New date: ${newDate.toISOString()}`);
    
    // Update the calendar with the new date
    updateRoomCalendar();
    
    // Reset the navigation flag after a short delay
    setTimeout(() => {
        window.roomNavigationInProgress = false;
    }, 500);
}

// Update room calendar display
function updateRoomCalendar() {
    console.log('Updating room calendar');
    
    // Get necessary elements
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    const roomSelect = document.getElementById('roomSelect');
    const roomCurrentMonthYear = document.getElementById('room-current-month-year');
    const roomUpcomingEvents = document.getElementById('roomUpcomingEvents');
    
    if (!roomCalendarGrid) {
        console.error('Room calendar grid element not found');
        return;
    }
    
    // Initialize room calendar state if it doesn't exist
    if (!window.roomCalendarState) {
        window.roomCalendarState = {
            currentDate: new Date(),
            currentView: 'month',
            allEvents: []
        };
    }
    
    // Get current date and view from state
    const { currentDate, currentView } = window.roomCalendarState;
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Update title based on current view
    if (roomCurrentMonthYear) {
        if (currentView === 'month') {
            roomCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        } else if (currentView === 'week') {
            // Calculate week start and end
            const dayOfWeek = currentDate.getDay();
            const weekStart = new Date(currentDate);
            weekStart.setDate(currentDate.getDate() - dayOfWeek);
            
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            
            roomCurrentMonthYear.textContent = `${weekStart.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            })} - ${weekEnd.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: 'numeric' 
            })}`;
        } else if (currentView === 'day') {
            roomCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { 
                weekday: 'long',
                month: 'long', 
                day: 'numeric',
                year: 'numeric' 
            });
        }
    }
    
    // Show loading indicator
    roomCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading room events...</div>';
    
    // Build date range for fetching events based on current view
    let startDate, endDate;
    
    if (currentView === 'month') {
        startDate = new Date(currentYear, currentMonth, 1);
        endDate = new Date(currentYear, currentMonth + 1, 0); // Last day of month
    } else if (currentView === 'week') {
        const dayOfWeek = currentDate.getDay();
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - dayOfWeek);
        
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59);
    } else if (currentView === 'day') {
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0);
        
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59);
    }
    
    // Fetch events for all rooms and the date range
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}`;
    
    // Add room filter if specific room is selected
    if (roomSelect && roomSelect.value && roomSelect.value !== '') {
        apiUrl += `&room_id=${roomSelect.value}`;
    }
    
    console.log('Fetching room events from API:', apiUrl);
    
    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error(`Network response error: ${response.status} ${response.statusText}`);
            }
            return response.json();
        })
        .then(data => {
            console.log(`Received ${data.events ? data.events.length : 0} events for room calendar`);
            
            // Store all events for filtering
            window.roomCalendarState.allEvents = data.events || [];
            
            // Filter events based on checkbox filters
            const filteredEvents = window.roomCalendarState.allEvents.filter(shouldShowRoomEvent);
            console.log(`Rendering ${filteredEvents.length} events for all rooms`);
            
            // Render calendar with filtered events based on current view
            if (currentView === 'month') {
                renderRoomCalendar(currentMonth, currentYear, currentView, filteredEvents);
            } else if (currentView === 'week' || currentView === 'day') {
                renderDetailedRoomCalendar(currentDate, currentView, filteredEvents);
            }
            
            // Show upcoming events
            if (roomUpcomingEvents) {
                showUpcomingEvents(filteredEvents, roomUpcomingEvents, 'room');
            }
        })
        .catch(error => {
            console.error('Error loading room events:', error);
            roomCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events: ' + error.message + '</div>';
        });
}

// Update room view buttons
function updateRoomViewButtons(activeButtonId) {
    const viewButtons = ['roomViewMonth', 'roomViewWeek', 'roomViewDay'];
    viewButtons.forEach(id => {
        const button = document.getElementById(id);
        if (button) {
            if (id === activeButtonId) {
                button.classList.add('bg-primary', 'text-white');
                button.classList.remove('bg-gray-700', 'text-gray-300');
            } else {
                button.classList.remove('bg-primary', 'text-white');
                button.classList.add('bg-gray-700', 'text-gray-300');
            }
        }
    });
}

// Show upcoming room events
function showRoomUpcomingEvents(events) {
    const roomUpcomingEvents = document.getElementById('roomUpcomingEvents');
    if (!roomUpcomingEvents) return;
    
    // Sort events by date (ascending)
    const sortedEvents = events.slice();
    try {
        sortedEvents.sort((a, b) => {
            const dateA = new Date(a.date || a.start);
            const dateB = new Date(b.date || b.start);
            return dateA - dateB;
        });
    } catch (e) {
        console.error('Error sorting room events:', e);
    }
    
    // Get the next 5 events (starting from today)
    const today = new Date();
    const upcomingEvents = sortedEvents.filter(event => {
        try {
            const eventDate = new Date(event.date || event.start);
            return eventDate >= today;
        } catch (e) {
            return false;
        }
    }).slice(0, 5);
    
    // Clear the container
    roomUpcomingEvents.innerHTML = '';
    
    // Display upcoming events
    if (upcomingEvents.length === 0) {
        roomUpcomingEvents.innerHTML = '<div class="p-4 text-center text-white/70">No upcoming events</div>';
        return;
    }
    
    upcomingEvents.forEach(event => {
        try {
            const eventDate = new Date(event.date || event.start);
            const day = eventDate.getDate();
            const month = eventDate.toLocaleString('en-US', { month: 'short' });
            const time = eventDate.toLocaleString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
            
            // Create event element
            const eventElement = document.createElement('div');
            eventElement.className = 'flex items-start space-x-4 p-2 rounded hover:bg-white/5';
            
            // Date box
            const dateBox = document.createElement('div');
            dateBox.className = 'flex-shrink-0 w-12 text-center';
            dateBox.innerHTML = `
                <div class="text-sm font-semibold text-primary">${day}</div>
                <div class="text-xs text-white/70">${month}</div>
            `;
            
            // Event details
            const eventDetails = document.createElement('div');
            eventDetails.className = 'flex-1 min-w-0';
            
            // Set text color based on event type
            let typeClass;
            if (event.type === 'session') {
                typeClass = 'text-green-400';
            } else if (event.type === 'holiday') {
                typeClass = 'text-red-400';
            } else {
                typeClass = 'text-blue-400';
            }
            
            // Add holiday emoji for holidays
            const holidayPrefix = event.type === 'holiday' ? '🎉 ' : '';
            
            // For holidays, show "All day" instead of time
            const timeDisplay = event.type === 'holiday' ? 'All day' : time;
            
            // Just show title and minimal info
            eventDetails.innerHTML = `
                <p class="text-sm font-medium text-white">${holidayPrefix}${event.title || 'Untitled Event'}</p>
                <p class="text-xs ${typeClass}">${event.type || 'Event'} · ${timeDisplay}</p>
            `;
            
            // Add tooltip for hover
            eventElement.setAttribute('title', `${event.title || 'Untitled Event'}\nType: ${event.type || 'Event'}\nTime: ${timeDisplay}\nID: ${event.id || 'N/A'}`);
            
            // Add elements to container
            eventElement.appendChild(dateBox);
            eventElement.appendChild(eventDetails);
            roomUpcomingEvents.appendChild(eventElement);
        } catch (e) {
            console.error(`Error creating upcoming ${type} event item:`, e);
        }
    });
}

// Trainer calendar functionality
function initTrainerCalendar() {
    // Get elements with proper error checking
    const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
    const trainerSelect = document.getElementById('trainerSelect');
    const trainerCurrentMonthYear = document.getElementById('trainer-current-month-year');
    const trainerUpcomingEvents = document.getElementById('trainerUpcomingEvents');
    
    if (!trainerCalendarGrid) {
        console.error('Trainer calendar grid element not found');
        return;
    }
    
    if (!trainerSelect) {
        console.error('Trainer select element not found');
        return;
    }
    
    // Extract trainer ID from select
    const selectedTrainerValue = trainerSelect.value;
    let trainerId = selectedTrainerValue && selectedTrainerValue !== '' ? selectedTrainerValue : null;
    
    // Initialize trainer calendar state
    window.trainerCalendarState = {
        currentDate: new Date(),
        currentView: 'month',
        allEvents: []
    };
    
    const currentDate = window.trainerCalendarState.currentDate;
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Show loading indicator
    trainerCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading trainer events...</div>';
    
    if (trainerCurrentMonthYear) {
        trainerCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }
    
    // Set up view buttons
    const trainerViewMonth = document.getElementById('trainerViewMonth');
    const trainerViewWeek = document.getElementById('trainerViewWeek');
    const trainerViewDay = document.getElementById('trainerViewDay');
    
    if (trainerViewMonth) {
        trainerViewMonth.addEventListener('click', function() {
            window.trainerCalendarState.currentView = 'month';
            updateTrainerCalendar();
            updateTrainerViewButtons();
        });
    }
    
    if (trainerViewWeek) {
        trainerViewWeek.addEventListener('click', function() {
            window.trainerCalendarState.currentView = 'week';
            updateTrainerCalendar();
            updateTrainerViewButtons();
        });
    }
    
    if (trainerViewDay) {
        trainerViewDay.addEventListener('click', function() {
            window.trainerCalendarState.currentView = 'day';
            updateTrainerCalendar();
            updateTrainerViewButtons();
        });
    }
    
    // Initialize the view buttons state
    updateTrainerViewButtons();
    
    // Build date range for fetching events
    const startDate = new Date(currentYear, currentMonth, 1);
    const endDate = new Date(currentYear, currentMonth + 1, 0); // Last day of month
    
    // Get trainer events for calendar display
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}`;
    
    // Add trainer filter if specific trainer is selected
    if (trainerId) {
        apiUrl += `&trainer_id=${trainerId}`;
    }
    
    // Fetch trainer events
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // Store events and render calendar
            window.trainerCalendarState.allEvents = data.events || [];
            
            // Set event checkbox to unchecked by default (only show sessions)
            const trainerEventCheckbox = document.querySelector('.trainer-calendar-filters input[data-filter="event"]');
            if (trainerEventCheckbox) {
                trainerEventCheckbox.checked = false;
            }
            
            // Filter out events (only show sessions)
            const showSessions = true;
            const showEvents = false;
            const filteredEvents = window.trainerCalendarState.allEvents.filter(
                event => shouldShowTrainerEvent(event, showSessions, showEvents)
            );
            
            renderTrainerCalendar(currentMonth, currentYear, 'month', filteredEvents);
            
            // Show upcoming events
            if (trainerUpcomingEvents) {
                showUpcomingEvents(filteredEvents, trainerUpcomingEvents, 'trainer');
            }
        })
        .catch(error => {
            console.error('Error loading trainer events:', error);
            trainerCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events. Please try again.</div>';
        });
    
    // Trainer selection change listener
    trainerSelect.addEventListener('change', function() {
        // Re-initialize with new trainer selection
        initTrainerCalendar();
    });
    
    // Set up event listeners for trainer calendar filter checkboxes
    const trainerSessionCheckbox = document.querySelector('.trainer-calendar-filters input[data-filter="session"]');
    const trainerEventCheckbox = document.querySelector('.trainer-calendar-filters input[data-filter="event"]');
    
    if (trainerSessionCheckbox) {
        trainerSessionCheckbox.addEventListener('change', refreshTrainerCalendarView);
    }
    
    if (trainerEventCheckbox) {
        trainerEventCheckbox.addEventListener('change', refreshTrainerCalendarView);
    }
    
    // Set up navigation buttons
    const trainerPrevMonth = document.getElementById('trainerPrevMonth');
    const trainerNextMonth = document.getElementById('trainerNextMonth');
    const trainerTodayBtn = document.getElementById('trainerTodayBtn');
    
    if (trainerPrevMonth) {
        trainerPrevMonth.addEventListener('click', function() {
            navigateTrainerCalendar('prev');
        });
    }
    
    if (trainerNextMonth) {
        trainerNextMonth.addEventListener('click', function() {
            navigateTrainerCalendar('next');
        });
    }
    
    if (trainerTodayBtn) {
        trainerTodayBtn.addEventListener('click', function() {
            navigateTrainerCalendar('today');
        });
    }
}

// Render trainer calendar
function renderTrainerCalendar(month, year, view, events) {
    const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
    if (!trainerCalendarGrid) {
        console.error('Trainer calendar grid element not found');
        return;
    }
    
    // Clear the grid and remove loading indicator
    trainerCalendarGrid.innerHTML = '';
    
    // Use similar layout as the main calendar
    trainerCalendarGrid.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Add day headers (Sun - Sat)
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    days.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        trainerCalendarGrid.appendChild(dayHeader);
    });
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayIndex = firstDay.getDay();
    
    // Calculate days from previous month to display
    for (let i = 0; i < firstDayIndex; i++) {
        const previousMonthDay = new Date(year, month, -firstDayIndex + i + 1);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = previousMonthDay.getDate();
        dayCell.appendChild(dateDisplay);
        
        trainerCalendarGrid.appendChild(dayCell);
    }
    
    // Add days for current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 hover:bg-white/10 overflow-y-auto relative';
        
        // Check if this is today
        const today = new Date();
        if (currentDate.getDate() === today.getDate() && 
            currentDate.getMonth() === today.getMonth() && 
            currentDate.getFullYear() === today.getFullYear()) {
            dayCell.classList.add('border', 'border-primary', 'bg-primary/10');
        }
        
        // Create header container for date and add button
        const headerContainer = document.createElement('div');
        headerContainer.className = 'flex justify-between items-center mb-1';
        
        // Add date to the header
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-sm text-white/70';
        dateDisplay.textContent = i;
        headerContainer.appendChild(dateDisplay);
        
        // Add the "+" button
        const addButton = document.createElement('button');
        addButton.className = 'text-primary hover:text-white bg-primary/20 hover:bg-primary/40 rounded-full w-5 h-5 flex items-center justify-center transition-colors';
        addButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>';
        addButton.title = 'Add Event';
        
        // Format date for URL
        const formattedDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        
        // Add click event to redirect to create event/session page with date
        addButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            
            // Use the openCalendarAddPopup function instead of redirecting
            if (typeof openCalendarAddPopup === 'function') {
                openCalendarAddPopup(formattedDate);
            }
        });
        
        headerContainer.appendChild(addButton);
        dayCell.appendChild(headerContainer);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.start);
                return eventDate.getDate() === i && 
                       eventDate.getMonth() === month && 
                       eventDate.getFullYear() === year;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = document.createElement('div');
            
            // Set classes based on event type
            if (event.type === 'session') {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-green-600/70 truncate';
            } else if (event.type === 'holiday') {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-red-600/70 truncate';
                // Add holiday emoji prefix
                if (!event.title.startsWith('🎉')) {
                    event.title = `🎉 ${event.title}`;
                }
            } else {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-blue-500/70 truncate';
            }
            
            // Set event title
            eventElement.textContent = event.title || 'Untitled Event';
            
            // Add the event to the cell
            dayCell.appendChild(eventElement);
        });
        
        trainerCalendarGrid.appendChild(dayCell);
    }
    
    // Fill remaining cells with next month's days
    const totalCells = 42; // 6 rows x 7 days
    const cellsToAdd = totalCells - (firstDayIndex + lastDay.getDate());
    
    for (let i = 1; i <= cellsToAdd; i++) {
        const nextMonthDay = new Date(year, month + 1, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = i;
        dayCell.appendChild(dateDisplay);
        
        trainerCalendarGrid.appendChild(dayCell);
    }
}

// Function to refresh the trainer calendar view based on filters
function refreshTrainerCalendarView() {
    if (!window.trainerCalendarState) return;
    
    const date = window.trainerCalendarState.currentDate;
    const events = window.trainerCalendarState.allEvents || [];
    
    // Get filter states (session checkbox defaults to checked, event checkbox to unchecked)
    const showSessions = document.querySelector('.trainer-calendar-filters input[data-filter="session"]')?.checked ?? true;
    const showEvents = document.querySelector('.trainer-calendar-filters input[data-filter="event"]')?.checked ?? false;
    
    // Filter events based on checkboxes
    const filteredEvents = events.filter(event => shouldShowTrainerEvent(event, showSessions, showEvents));
    
    // Re-render calendar with filtered events
    renderTrainerCalendar(
        date.getMonth(),
        date.getFullYear(),
        window.trainerCalendarState.currentView,
        filteredEvents
    );
    
    // Update upcoming events
    const trainerUpcomingEvents = document.getElementById('trainerUpcomingEvents');
    if (trainerUpcomingEvents) {
        showUpcomingEvents(filteredEvents, trainerUpcomingEvents, 'trainer');
    }
}

// Function to check if a trainer event should be shown based on filters
function shouldShowTrainerEvent(event, showSessions, showEvents) {
    // Always show holidays
    if (event.type === 'holiday') return true;
    if (event.type === 'session' && !showSessions) return false;
    if (event.type !== 'session' && event.type !== 'holiday' && !showEvents) return false;
    return true;
}

// Equipment calendar functionality
function initEquipmentCalendar() {
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    const equipmentPrevMonth = document.getElementById('equipmentPrevMonth');
    const equipmentNextMonth = document.getElementById('equipmentNextMonth');
    const equipmentTodayBtn = document.getElementById('equipmentTodayBtn');
    const equipmentViewMonthButton = document.getElementById('equipmentViewMonth');
    const equipmentViewWeekButton = document.getElementById('equipmentViewWeek');
    const equipmentViewDayButton = document.getElementById('equipmentViewDay');
    
    if (!equipmentCalendarGrid) {
        console.error('Equipment calendar grid element not found');
        return;
    }
    
    // Initialize calendar state
    window.equipmentCalendarState = {
        currentDate: new Date(),
        currentView: 'month',
        allEvents: [],
        isNavigating: false
    };
    
    // Set up navigation buttons
    if (equipmentPrevMonth) {
        equipmentPrevMonth.addEventListener('click', () => navigateEquipmentCalendar('prev'));
    }
    
    if (equipmentNextMonth) {
        equipmentNextMonth.addEventListener('click', () => navigateEquipmentCalendar('next'));
    }
    
    if (equipmentTodayBtn) {
        equipmentTodayBtn.addEventListener('click', () => navigateEquipmentCalendar('today'));
    }
    
    // Set up view buttons
    if (equipmentViewMonthButton) {
        equipmentViewMonthButton.addEventListener('click', () => {
            window.equipmentCalendarState.currentView = 'month';
            updateEquipmentCalendar();
            updateEquipmentViewButtons();
        });
    }
    
    if (equipmentViewWeekButton) {
        equipmentViewWeekButton.addEventListener('click', () => {
            window.equipmentCalendarState.currentView = 'week';
            updateEquipmentCalendar();
            updateEquipmentViewButtons();
        });
    }
    
    if (equipmentViewDayButton) {
        equipmentViewDayButton.addEventListener('click', () => {
            window.equipmentCalendarState.currentView = 'day';
            updateEquipmentCalendar();
            updateEquipmentViewButtons();
        });
    }
    
    // Set up filter checkboxes
    const equipmentFilters = document.querySelectorAll('.equipment-calendar-filters input[type="checkbox"]');
    equipmentFilters.forEach(filter => {
        filter.addEventListener('change', refreshEquipmentCalendarView);
    });
    
    // Initial calendar update
    updateEquipmentCalendar();
    updateEquipmentViewButtons();
}

// Navigate equipment calendar based on action (prev, next, today)
function navigateEquipmentCalendar(direction) {
    // If navigation is in progress, return to prevent multiple rapid calls
    if (window.equipmentCalendarState?.isNavigating) return;
    
    // Set the navigation flag
    if (window.equipmentCalendarState) {
        window.equipmentCalendarState.isNavigating = true;
    }
    
    // Get current date from state
    const currentDate = window.equipmentCalendarState?.currentDate || new Date();
    const currentView = window.equipmentCalendarState?.currentView || 'month';
    
    let newDate = new Date(currentDate);
    
    // Handle different navigation directions
    if (direction === 'prev') {
        if (currentView === 'month') {
            // Previous month
            newDate.setMonth(currentDate.getMonth() - 1);
        } else if (currentView === 'week') {
            // Previous week
            newDate.setDate(currentDate.getDate() - 7);
        } else if (currentView === 'day') {
            // Previous day
            newDate.setDate(currentDate.getDate() - 1);
        }
    } else if (direction === 'next') {
        if (currentView === 'month') {
            // Next month
            newDate.setMonth(currentDate.getMonth() + 1);
        } else if (currentView === 'week') {
            // Next week
            newDate.setDate(currentDate.getDate() + 7);
        } else if (currentView === 'day') {
            // Next day
            newDate.setDate(currentDate.getDate() + 1);
        }
    } else if (direction === 'today') {
        // Reset to today
        newDate = new Date();
    }
    
    // Update the state
    if (window.equipmentCalendarState) {
        window.equipmentCalendarState.currentDate = newDate;
    }
    
    // Update the calendar
    updateEquipmentCalendar();
    
    // Reset navigation flag after a short delay
    setTimeout(() => {
        if (window.equipmentCalendarState) {
            window.equipmentCalendarState.isNavigating = false;
        }
    }, 200);
}

// Update equipment calendar display
function updateEquipmentCalendar() {
    console.log('Updating equipment calendar');
    
    // Get necessary elements
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    const equipmentSelect = document.getElementById('equipmentSelect');
    const equipmentCurrentMonthYear = document.getElementById('equipment-current-month-year');
    const equipmentUpcomingEvents = document.getElementById('equipmentUpcomingEvents');
    
    if (!equipmentCalendarGrid) {
        console.error('Equipment calendar grid element not found');
        return;
    }
    
    if (!equipmentSelect) {
        console.error('Equipment select element not found');
        return;
    }
    
    // Get current date and view from state
    const { currentDate, currentView } = window.equipmentCalendarState;
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    
    // Extract equipment ID if a specific equipment is selected
    const selectedEquipmentValue = equipmentSelect.value;
    console.log('Selected equipment value:', selectedEquipmentValue);
    
    let equipmentId = null;
    if (selectedEquipmentValue && selectedEquipmentValue !== 'All Equipment' && selectedEquipmentValue !== '') {
        // Check if the value is already a numeric ID
        if (/^\d+$/.test(selectedEquipmentValue)) {
            equipmentId = selectedEquipmentValue;
        } else {
            // Try to extract any number from the equipment value
            const numberMatch = selectedEquipmentValue.match(/(\d+)/);
            if (numberMatch && numberMatch[1]) {
                equipmentId = numberMatch[1];
            }
        }
        console.log('Equipment selection detected:', selectedEquipmentValue, '→ Extracted ID:', equipmentId);
    } else {
        console.log('No specific equipment selected - showing all equipment events');
    }
    
    // Show loading indicator
    equipmentCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading equipment events...</div>';
    
    if (equipmentCurrentMonthYear) {
        equipmentCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    }
    
    // Build date range for fetching events
    const startDate = getViewStartDate(currentDate, currentView);
    const endDate = getViewEndDate(currentDate, currentView);
    
    // Fetch events for the selected equipment and date range
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}`;
    
    // Add equipment filter if specific equipment is selected
    if (equipmentId) {
        apiUrl += `&equipment_id=${equipmentId}`;
        console.log('Fetching events for specific equipment ID:', equipmentId);
    } else {
        // If "All Equipment" is selected, only show events with equipment
        apiUrl += `&with_equipment=true`;
        console.log('Fetching all events with any equipment assigned');
    }
    
    console.log('Fetching equipment events from API:', apiUrl);
    
    fetch(apiUrl)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then(data => {
            console.log('API response received:', data);
            console.log(`Received ${data.events ? data.events.length : 0} events for equipment calendar`);
            
            // Parse events for the calendar
            let events = data.events || [];
            
            // Log event details to help troubleshoot
            if (events.length > 0) {
                console.log('First event details:', events[0]);
                // Log a few sample events
                const sampleCount = Math.min(3, events.length);
                for (let i = 0; i < sampleCount; i++) {
                    const event = events[i];
                    console.log(`Event ${i+1}/${sampleCount}:`, {
                        id: event.id,
                        title: event.title,
                        type: event.type,
                        start: event.start,
                        equipment_id: event.equipment_id,
                        equipment: event.equipment
                    });
                }
            } else {
                console.log('No events found for the selected criteria');
            }
            
            // Render calendar with events
            renderCalendar(currentMonth, currentYear, currentView, events, equipmentCalendarGrid);
            
            // Show upcoming events if container exists
            if (equipmentUpcomingEvents) {
                showUpcomingEvents(events, equipmentUpcomingEvents, 'equipment');
            }
        })
        .catch(error => {
            console.error('Error fetching equipment events:', error);
            equipmentCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading equipment events. Please try again later.</div>';
        });
}

// Helper to format hours and minutes for timeline
function formatHoursMinutes(date) {
    return date.getHours().toString().padStart(2, '0') + ':' + 
           date.getMinutes().toString().padStart(2, '0');
}

// Generate HTML for timeline displays
function generateTimelineHTML(data) {
    // Create hours markers (8 AM to 6 PM)
    let hoursHTML = '<div class="timeline-hours">';
    for (let hour = 8; hour <= 18; hour++) {
        const displayHour = hour > 12 ? hour - 12 : hour;
        const ampm = hour >= 12 ? 'PM' : 'AM';
        hoursHTML += `<div class="timeline-hour">${displayHour} ${ampm}</div>`;
    }
    hoursHTML += '</div>';
    
    // Create timeline rows
    let rowsHTML = '<div class="timeline-container">';
    
    data.forEach(item => {
        rowsHTML += `
            <div class="timeline-row">
                <div class="timeline-label">${item.name}</div>
                <div class="timeline-track">
                    ${item.events.map(event => {
                        const startPercent = calculateTimePercent(event.start);
                        const endPercent = calculateTimePercent(event.end);
                        const width = endPercent - startPercent;
                        
                        return `
                            <div class="timeline-event ${event.type}" 
                                 style="left: ${startPercent}%; width: ${width}%;" 
                                 title="${event.title} (${formatTime(event.start)} - ${formatTime(event.end)})">
                                ${event.title}
                            </div>
                        `;
                    }).join('')}
                </div>
            </div>
        `;
    });
    
    rowsHTML += '</div>';
    
    return hoursHTML + rowsHTML;
}

// Calculate position on timeline (8 AM to 6 PM = 0% to 100%)
function calculateTimePercent(time) {
    const [hours, minutes] = time.split(':').map(Number);
    const totalMinutes = hours * 60 + minutes;
    const startMinutes = 8 * 60; // 8 AM
    const endMinutes = 18 * 60;  // 6 PM
    const range = endMinutes - startMinutes;
    
    return Math.max(0, Math.min(100, ((totalMinutes - startMinutes) / range) * 100));
}

// Format time for display
function formatTime(time) {
    const [hours, minutes] = time.split(':').map(Number);
    const displayHours = hours > 12 ? hours - 12 : hours;
    const ampm = hours >= 12 ? 'PM' : 'AM';
    return `${displayHours}:${minutes.toString().padStart(2, '0')} ${ampm}`;
}

// Debug helper function
const isDev = false; // Always set to false to disable debug mode

// Show debug panel in development mode - disabled
/*
if (isDev) {
    document.getElementById('calendar-debug').classList.remove('hidden');
}
*/

function logDebug(message) {
    // Only log to console in development builds
    if (isDev) {
        console.log(`[Calendar] ${message}`);
    }
    
    // Disabled UI logging
    /*
    if (isDev) {
        const timestamp = new Date().toLocaleTimeString();
        const debugLog = document.getElementById('debug-log');
        const logEntry = document.createElement('p');
        logEntry.className = 'text-xs mb-1';
        logEntry.innerHTML = `<span class="text-gray-500">${timestamp}</span> ${message}`;
        debugLog.appendChild(logEntry);
        debugLog.scrollTop = debugLog.scrollHeight;
    }
    */
}

// Helper function to check if an event should be displayed based on filter settings
function shouldShowEvent(event) {
    // Use the sidebar filters with the calendar-filters class
    const sidebarSessionsFilter = document.querySelector('.calendar-filters input[data-filter="session"]');
    const sidebarEventsFilter = document.querySelector('.calendar-filters input[data-filter="event"]');
    const sidebarHolidaysFilter = document.querySelector('.calendar-filters input[data-filter="holiday"]');
    
    let showSessions = true;
    let showEvents = true;
    let showHolidays = true;
    
    // Use sidebar filters if they exist
    if (sidebarSessionsFilter) {
        showSessions = sidebarSessionsFilter.checked;
    }
    
    if (sidebarEventsFilter) {
        showEvents = sidebarEventsFilter.checked;
    }
    
    if (sidebarHolidaysFilter) {
        showHolidays = sidebarHolidaysFilter.checked;
    }
    
    // Check event type and return appropriate value
    if (event.type === 'session' && !showSessions) {
        return false;
    }
    
    if (event.type === 'event' && !showEvents) {
        return false;
    }
    
    if (event.type === 'holiday' && !showHolidays) {
        return false;
    }
    
    return true;
}

// Helper function to reset grid classes
function resetGridClasses(container) {
    // Remove existing grid classes
    container.classList.remove(
        'grid-cols-7', 'grid-cols-1',
        'calendar-month-view', 'calendar-week-view', 'calendar-day-view'
    );
}

// Generate calendar grid
function generateCalendar(month, year, day, view) {
    // Show loading indicator
    calendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading events...</div>';
    
    // Update display based on view type
    let displayDate;
    let startDate;
    let endDate;
    
    if (view === 'month') {
        // Month view - show full month
        displayDate = new Date(year, month, 1);
        startDate = new Date(year, month, 1);
        endDate = new Date(year, month + 1, 0);
        
        // Update month/year display
        currentMonthYearElement.textContent = displayDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
    } else if (view === 'week') {
        // Week view - show the week containing the selected day
        const selectedDate = new Date(year, month, day);
        const dayOfWeek = selectedDate.getDay(); // 0 = Sunday, 1 = Monday, etc.
        
        // Calculate start of week (Sunday)
        startDate = new Date(year, month, day - dayOfWeek);
        // Calculate end of week (Saturday)
        endDate = new Date(year, month, day + (6 - dayOfWeek));
        
        // Update display text
        const startFormatted = startDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
        const endFormatted = endDate.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' });
        currentMonthYearElement.textContent = `${startFormatted} - ${endFormatted}`;
    } else if (view === 'day') {
        // Day view - show just the selected day
        startDate = new Date(year, month, day);
        endDate = new Date(year, month, day, 23, 59, 59);
        
        // Update display text
        currentMonthYearElement.textContent = startDate.toLocaleDateString('en-US', { 
            weekday: 'long', 
            month: 'long', 
            day: 'numeric',
            year: 'numeric' 
        });
    }
    
    // Get events for the selected time range
    logDebug(`Fetching events for ${startDate.toLocaleDateString()} to ${endDate.toLocaleDateString()}`);
    
    // Store all events for filtering
    allEvents = getCalendarEvents(startDate, endDate);
    logDebug(`Found ${allEvents.length} events, rendering ${view} view`);
    
    if (allEvents.length > 0) {
        logDebug(`First event: ${allEvents[0].title} at ${allEvents[0].date}`);
    }
    
    // Filter events based on current checkbox settings
    const filteredEvents = allEvents.filter(event => shouldShowEvent(event));
    logDebug(`After filtering: ${filteredEvents.length} events to display`);
    
    // Clear grid
    calendarGrid.innerHTML = '';
    
    // Render appropriate view
    if (view === 'month') {
        renderMonthView(month, year, filteredEvents);
    } else if (view === 'week') {
        renderWeekView(startDate, endDate, filteredEvents);
    } else if (view === 'day') {
        renderDayView(startDate, filteredEvents);
    }
    
    // Show upcoming events
    showUpcomingEvents(filteredEvents);
}

// Show upcoming events (universal function)
function showUpcomingEvents(events, container, type = 'main') {
    if (!container) {
        console.error(`Container for ${type} upcoming events not found`);
        return;
    }
    
    // Sort events by date (ascending)
    const sortedEvents = events.slice();
    try {
        sortedEvents.sort((a, b) => {
            const dateA = new Date(a.date || a.start);
            const dateB = new Date(b.date || b.start);
            return dateA - dateB;
        });
    } catch (e) {
        console.error(`Error sorting ${type} events:`, e);
    }
    
    // Get the next 5 events (starting from today)
    const today = new Date();
    const upcomingEvents = sortedEvents.filter(event => {
        try {
            const eventDate = new Date(event.date || event.start);
            return eventDate >= today;
        } catch (e) {
            return false;
        }
    }).slice(0, 5);
    
    // Clear the container
    container.innerHTML = '';
    
    // Display upcoming events
    if (upcomingEvents.length === 0) {
        container.innerHTML = `<div class="p-4 text-center text-white/70">No upcoming ${type} events</div>`;
        return;
    }
    
    upcomingEvents.forEach(event => {
        try {
            const eventDate = new Date(event.date || event.start);
            const day = eventDate.getDate();
            const month = eventDate.toLocaleString('en-US', { month: 'short' });
            const time = eventDate.toLocaleString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
            
            // Create event element
            const eventElement = document.createElement('div');
            eventElement.className = 'flex items-start space-x-4 p-2 rounded hover:bg-white/5';
            
            // Date box
            const dateBox = document.createElement('div');
            dateBox.className = 'flex-shrink-0 w-12 text-center';
            dateBox.innerHTML = `
                <div class="text-sm font-semibold text-primary">${day}</div>
                <div class="text-xs text-white/70">${month}</div>
            `;
            
            // Event details
            const eventDetails = document.createElement('div');
            eventDetails.className = 'flex-1 min-w-0';
            
            // Set text color based on event type
            let typeClass;
            if (event.type === 'session') {
                typeClass = 'text-green-400';
            } else if (event.type === 'holiday') {
                typeClass = 'text-red-400';
            } else {
                typeClass = 'text-blue-400';
            }
            
            // Add holiday emoji for holidays
            const holidayPrefix = event.type === 'holiday' ? '🎉 ' : '';
            
            // For holidays, show "All day" instead of time
            const timeDisplay = event.type === 'holiday' ? 'All day' : time;
            
            // Just show title and minimal info
            eventDetails.innerHTML = `
                <p class="text-sm font-medium text-white">${holidayPrefix}${event.title || 'Untitled Event'}</p>
                <p class="text-xs ${typeClass}">${event.type || 'Event'} · ${timeDisplay}</p>
            `;
            
            // Add tooltip for hover
            eventElement.setAttribute('title', `${event.title || 'Untitled Event'}\nType: ${event.type || 'Event'}\nTime: ${timeDisplay}\nID: ${event.id || 'N/A'}`);
            
            // Add elements to container
            eventElement.appendChild(dateBox);
            eventElement.appendChild(eventDetails);
            container.appendChild(eventElement);
        } catch (e) {
            console.error(`Error creating upcoming ${type} event item:`, e);
        }
    });
}

// Render calendar for any type (universal function)
function renderCalendar(month, year, view, events, container) {
    if (!container) {
        console.error('Calendar container not found');
        return;
    }
    
    // Clear the grid
    container.innerHTML = '';
    
    if (view === 'month') {
        renderMonthView(month, year, events, container);
    } else if (view === 'week') {
        renderWeekView(month, year, events, container);
    } else if (view === 'day') {
        renderDayView(month, year, events, container);
    }
}

// Update view buttons (universal function)
function updateViewButtons(activeButtonId, buttonIds) {
    buttonIds.forEach(id => {
        const button = document.getElementById(id);
        if (button) {
            if (id === activeButtonId) {
                button.classList.add('bg-primary', 'text-white');
                button.classList.remove('bg-gray-700', 'text-gray-300');
            } else {
                button.classList.remove('bg-primary', 'text-white');
                button.classList.add('bg-gray-700', 'text-gray-300');
            }
        }
    });
}

// Render month view
function renderMonthView(month, year, events, container) {
    // Set grid classes
    container.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Add day headers (Sun - Sat)
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    days.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        container.appendChild(dayHeader);
    });
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayIndex = firstDay.getDay();
    
    // Calculate days from previous month to display
    for (let i = 0; i < firstDayIndex; i++) {
        const previousMonthDay = new Date(year, month, -firstDayIndex + i + 1);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = previousMonthDay.getDate();
        dayCell.appendChild(dateDisplay);
        
        container.appendChild(dayCell);
    }
    
    // Add days for current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 hover:bg-white/10 overflow-y-auto relative';
        
        // Check if this is today
        const today = new Date();
        if (currentDate.getDate() === today.getDate() && 
            currentDate.getMonth() === today.getMonth() && 
            currentDate.getFullYear() === today.getFullYear()) {
            dayCell.classList.add('border', 'border-primary', 'bg-primary/10');
        }
        
        // Create header container for date and add button
        const headerContainer = document.createElement('div');
        headerContainer.className = 'flex justify-between items-center mb-1';
        
        // Add date to the header
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-sm text-white/70';
        dateDisplay.textContent = i;
        headerContainer.appendChild(dateDisplay);
        
        // Add the "+" button
        const addButton = document.createElement('button');
        addButton.className = 'text-primary hover:text-white bg-primary/20 hover:bg-primary/40 rounded-full w-5 h-5 flex items-center justify-center transition-colors';
        addButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>';
        addButton.title = 'Add Event';
        
        // Format date for URL
        const formattedDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        
        // Add click event to open the calendar add popup instead of redirecting
        addButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            
            // Call the openCalendarAddPopup function with the formatted date
            if (typeof openCalendarAddPopup === 'function') {
                openCalendarAddPopup(formattedDate);
            }
        });
        
        headerContainer.appendChild(addButton);
        dayCell.appendChild(headerContainer);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.date || event.start);
                return eventDate.getDate() === i && 
                       eventDate.getMonth() === month && 
                       eventDate.getFullYear() === year;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = createEventElement(event);
            dayCell.appendChild(eventElement);
        });
        
        container.appendChild(dayCell);
    }
    
    // Fill remaining cells with next month's days
    const totalCells = 42; // 6 rows x 7 days
    const cellsToAdd = totalCells - (firstDayIndex + lastDay.getDate());
    
    for (let i = 1; i <= cellsToAdd; i++) {
        const nextMonthDay = new Date(year, month + 1, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = i;
        dayCell.appendChild(dateDisplay);
        
        container.appendChild(dayCell);
    }
}

// Render week view
function renderWeekView(month, year, events, container) {
    // Set base classes for the container
    container.className = 'grid grid-cols-8 gap-1 p-2';
    
    // Get the current week's start date based on month and year
    const currentDate = new Date(year, month, 1);
    
    // If we're looking at a specific day within this month
    if (window.calendarState && window.calendarState.currentDate) {
        if (window.calendarState.currentDate.getMonth() === month && 
            window.calendarState.currentDate.getFullYear() === year) {
            currentDate.setDate(window.calendarState.currentDate.getDate());
        }
    }
    
    // Find the first day of the week (Sunday)
    const dayOfWeek = currentDate.getDay();
    const startOfWeek = new Date(currentDate);
    startOfWeek.setDate(currentDate.getDate() - dayOfWeek);
    
    // Create the time header
    const timeHeader = document.createElement('div');
    timeHeader.className = 'text-center font-semibold py-2 text-white/70';
    timeHeader.textContent = 'Time';
    container.appendChild(timeHeader);
    
    // Create day headers
    for (let i = 0; i < 7; i++) {
        const day = new Date(startOfWeek);
        day.setDate(startOfWeek.getDate() + i);
        
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-2 text-sky-100';
        
        // Format the day header to show day of week and date
        const dayName = day.toLocaleDateString('en-US', { weekday: 'short' });
        const dayNumber = day.getDate();
        const monthName = day.toLocaleDateString('en-US', { month: 'short' });
        dayHeader.innerHTML = `${dayName}<br>${dayNumber} ${monthName}`;
        
        // Highlight today
        const today = new Date();
        if (day.getDate() === today.getDate() && 
            day.getMonth() === today.getMonth() && 
            day.getFullYear() === today.getFullYear()) {
            dayHeader.classList.add('bg-primary/30', 'rounded');
        }
        
        container.appendChild(dayHeader);
    }
    
    // Create time slots for each hour from 8am to 8pm
    for (let hour = 8; hour <= 20; hour++) {
        // Time cell
        const timeCell = document.createElement('div');
        timeCell.className = 'text-center text-sm py-2 text-white/70 border-t border-white/10';
        timeCell.textContent = `${hour % 12 || 12} ${hour < 12 ? 'AM' : 'PM'}`;
        container.appendChild(timeCell);
        
        // Day cells for this hour
        for (let day = 0; day < 7; day++) {
            const currentDay = new Date(startOfWeek);
            currentDay.setDate(startOfWeek.getDate() + day);
            currentDay.setHours(hour, 0, 0, 0);
            
            const hourCell = document.createElement('div');
            hourCell.className = 'relative min-h-[60px] bg-white/5 hover:bg-white/10 border-t border-white/10';
            
            // Find events during this hour on this day
            const hourEvents = events.filter(event => {
                try {
                    const eventStart = new Date(event.date || event.start);
                    const eventEnd = new Date(event.end || eventStart);
                    eventEnd.setHours(eventEnd.getHours() + 1); // Default 1 hour if no end time
                    
                    return eventStart.getDate() === currentDay.getDate() && 
                           eventStart.getMonth() === currentDay.getMonth() && 
                           eventStart.getFullYear() === currentDay.getFullYear() &&
                           eventStart.getHours() <= hour && 
                           eventEnd.getHours() > hour;
                } catch (e) {
                    return false;
                }
            });
            
            // Add events to the hour cell
            hourEvents.forEach(event => {
                const eventElement = document.createElement('div');
                const typeClass = event.type === 'session' ? 'bg-green-700/50 border-green-400' : 'bg-blue-700/50 border-blue-400';
                eventElement.className = `p-1 text-xs rounded m-1 border-l-2 ${typeClass}`;
                
                const eventStart = new Date(event.date || event.start);
                const startTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                
                eventElement.innerHTML = `
                    <div class="font-semibold truncate">${event.title}</div>
                    <div class="text-xs opacity-70">${startTime}</div>
                `;
                
                hourCell.appendChild(eventElement);
            });
            
            container.appendChild(hourCell);
        }
    }
}

// Render day view
function renderDayView(month, year, events, container) {
    // Set base classes for the container
    container.className = 'grid grid-cols-1 gap-1 p-2';
    
    // Get the current date
    let currentDate = new Date(year, month, 1);
    
    // If we're looking at a specific day within this month
    if (window.calendarState && window.calendarState.currentDate) {
        if (window.calendarState.currentDate.getMonth() === month && 
            window.calendarState.currentDate.getFullYear() === year) {
            currentDate = new Date(window.calendarState.currentDate);
        }
    }
    
    // Create date header
    const dateHeader = document.createElement('div');
    dateHeader.className = 'text-center font-semibold py-4 text-white text-xl';
    dateHeader.textContent = currentDate.toLocaleDateString('en-US', { 
        weekday: 'long', 
        month: 'long', 
        day: 'numeric'
    });
    container.appendChild(dateHeader);
    
    // Create a container for the timeline
    const timelineContainer = document.createElement('div');
    timelineContainer.className = 'bg-white/5 rounded-lg p-4 relative';
    
    // Create time slots for each hour from 7am to 10pm
    for (let hour = 7; hour <= 22; hour++) {
        const hourRow = document.createElement('div');
        hourRow.className = 'grid grid-cols-12 border-t border-white/10 min-h-[80px]';
        
        // Time cell
        const timeCell = document.createElement('div');
        timeCell.className = 'col-span-1 text-right pr-2 text-sm py-2 text-white/70';
        timeCell.textContent = `${hour % 12 || 12} ${hour < 12 ? 'AM' : 'PM'}`;
        hourRow.appendChild(timeCell);
        
        // Events cell
        const eventsCell = document.createElement('div');
        eventsCell.className = 'col-span-11 relative';
        
        // Find events during this hour
        const hourEvents = events.filter(event => {
            try {
                const eventStart = new Date(event.date || event.start);
                const eventEnd = new Date(event.end || eventStart);
                eventEnd.setHours(eventEnd.getHours() + 1); // Default 1 hour if no end time
                
                return eventStart.getDate() === currentDate.getDate() && 
                       eventStart.getMonth() === currentDate.getMonth() && 
                       eventStart.getFullYear() === currentDate.getFullYear() &&
                       eventStart.getHours() <= hour && 
                       eventEnd.getHours() > hour;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the hour cell
        hourEvents.forEach(event => {
            const eventElement = document.createElement('div');
            const typeClass = event.type === 'session' ? 'bg-green-700 border-green-400' : 'bg-blue-700 border-blue-400';
            eventElement.className = `p-2 text-sm rounded my-1 border-l-2 ${typeClass}`;
            
            const eventStart = new Date(event.date || event.start);
            const eventEnd = new Date(event.end || eventStart);
            eventEnd.setHours(eventEnd.getHours() + 1); // Default 1 hour if no end time
            
            const startTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
            const endTime = eventEnd.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
            
            eventElement.innerHTML = `
                <div class="font-semibold">${event.title}</div>
                <div class="text-xs opacity-70">${startTime} - ${endTime}</div>
                <div class="text-xs opacity-70">${event.location || ''}</div>
            `;
            
            eventsCell.appendChild(eventElement);
        });
        
        hourRow.appendChild(eventsCell);
        timelineContainer.appendChild(hourRow);
    }
    
    container.appendChild(timelineContainer);
    
    // If no events, show message
    if (events.length === 0) {
        const noEventsMessage = document.createElement('div');
        noEventsMessage.className = 'text-center py-8 text-white/70';
        noEventsMessage.textContent = 'No events scheduled for this day';
        timelineContainer.appendChild(noEventsMessage);
    }
}

// Create event element for month view
function createEventElement(event) {
    const eventElement = document.createElement('div');
    let typeClass;
    
    // Style based on event type
    if (event.type === 'session') {
        typeClass = 'bg-green-700/50 border-green-400';
    } else if (event.type === 'holiday') {
        typeClass = 'bg-red-700/50 border-red-400';
    } else {
        typeClass = 'bg-blue-700/50 border-blue-400';
    }
    
    eventElement.className = `p-1 text-xs mt-1 rounded border-l-2 ${typeClass}`;
    
    // Get start time
    let startTime = '';
    try {
        const eventDate = new Date(event.date || event.start);
        startTime = eventDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
    } catch (e) {
        console.error('Error parsing event time:', e);
    }
    
    // For holidays, we don't need to show the time
    const timeDisplay = event.type === 'holiday' ? '' : `${startTime} `;
    
    // Add holiday emoji for holidays
    const holidayPrefix = event.type === 'holiday' ? '🎉 ' : '';
    
    // Event title with time
    eventElement.innerHTML = `
        <div class="truncate">${holidayPrefix}${timeDisplay}${event.title || 'Untitled Event'}</div>
    `;
    
    // Add tooltip for hover
    eventElement.title = `${event.title || 'Untitled Event'}\nType: ${event.type || 'Event'}\nID: ${event.id || 'N/A'}`;
    
    return eventElement;
}

// Navigate main calendar
function navigateCalendar(direction) {
    if (!window.calendarState) return;
    
    const currentDate = new Date(window.calendarState.currentDate);
    const currentView = window.calendarState.currentView;
    
    // Debounce navigation to prevent multiple rapid calls
    if (window.calendarState.navigating) return;
    window.calendarState.navigating = true;
    
    // Update the date based on the navigation direction
    if (direction === 'prev') {
        if (currentView === 'month') {
            currentDate.setMonth(currentDate.getMonth() - 1);
        } else if (currentView === 'week') {
            currentDate.setDate(currentDate.getDate() - 7);
        } else if (currentView === 'day') {
            currentDate.setDate(currentDate.getDate() - 1);
        }
    } else if (direction === 'next') {
        if (currentView === 'month') {
            currentDate.setMonth(currentDate.getMonth() + 1);
        } else if (currentView === 'week') {
            currentDate.setDate(currentDate.getDate() + 7);
        } else if (currentView === 'day') {
            currentDate.setDate(currentDate.getDate() + 1);
        }
    } else if (direction === 'today') {
        currentDate = new Date();
    }
    
    // Update the calendar state
    window.calendarState.currentDate = currentDate;
    
    // Update the calendar display
    updateCalendar();
    
    // Reset the navigation flag after a short delay
    setTimeout(() => {
        window.calendarState.navigating = false;
    }, 500);
}

// Update main calendar display
function updateCalendar() {
    if (!window.calendarState) return;
    
    const currentMonthYear = document.getElementById('current-month-year');
    const calendarGrid = document.getElementById('calendarGrid');
    const upcomingEventsContainer = document.getElementById('upcomingEvents');
    
    if (!calendarGrid) {
        console.error('Calendar grid element not found');
        return;
    }
    
    const month = window.calendarState.currentDate.getMonth();
    const year = window.calendarState.currentDate.getFullYear();
    
    // Update month/year display
    if (currentMonthYear) {
        if (window.calendarState.currentView === 'month') {
            currentMonthYear.textContent = window.calendarState.currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        } else if (window.calendarState.currentView === 'week') {
            const weekStart = new Date(window.calendarState.currentDate);
            const dayOfWeek = weekStart.getDay();
            weekStart.setDate(weekStart.getDate() - dayOfWeek);
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            currentMonthYear.textContent = `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
        } else if (window.calendarState.currentView === 'day') {
            currentMonthYear.textContent = window.calendarState.currentDate.toLocaleDateString('en-US', { weekday: 'long', month: 'long', day: 'numeric', year: 'numeric' });
        }
    }
    
    // Show loading indicator
        calendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading events...</div>';
    
    // Build date range for fetching events
    let startDate, endDate;
    
    if (window.calendarState.currentView === 'month') {
        startDate = new Date(year, month, 1);
        endDate = new Date(year, month + 1, 0);
    } else if (window.calendarState.currentView === 'week') {
        const dayOfWeek = window.calendarState.currentDate.getDay();
        startDate = new Date(window.calendarState.currentDate);
        startDate.setDate(startDate.getDate() - dayOfWeek);
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
    } else if (window.calendarState.currentView === 'day') {
        startDate = new Date(window.calendarState.currentDate);
        endDate = new Date(window.calendarState.currentDate);
        endDate.setHours(23, 59, 59);
    }
    
    // Fetch events for the selected date range
    getCalendarEvents(startDate, endDate)
        .then(events => {
            // Store all events for filtering
            window.calendarState.allEvents = events;
            
            // Filter events based on checkboxes
            const filteredEvents = events.filter(shouldShowEvent);
            
            // Render calendar with filtered events
            renderCalendar(month, year, window.calendarState.currentView, filteredEvents, calendarGrid);
            
            // Show upcoming events
            if (upcomingEventsContainer) {
                showUpcomingEvents(filteredEvents, upcomingEventsContainer, 'main');
            }
        })
        .catch(error => {
            console.error('Error fetching calendar events:', error);
            calendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events. Please try again later.</div>';
        });
}

// Helper functions for date ranges
function getViewStartDate(date, view) {
    if (view === 'month') {
        return new Date(date.getFullYear(), date.getMonth(), 1);
    } else if (view === 'week') {
        const dayOfWeek = date.getDay();
        const startDate = new Date(date);
        startDate.setDate(date.getDate() - dayOfWeek);
        return startDate;
    } else if (view === 'day') {
        return new Date(date.getFullYear(), date.getMonth(), date.getDate());
    }
    // Default to month view
    return new Date(date.getFullYear(), date.getMonth(), 1);
}

function getViewEndDate(date, view) {
    if (view === 'month') {
        return new Date(date.getFullYear(), date.getMonth() + 1, 0); // Last day of month
    } else if (view === 'week') {
        const dayOfWeek = date.getDay();
        const endDate = new Date(date);
        endDate.setDate(date.getDate() + (6 - dayOfWeek));
        return endDate;
    } else if (view === 'day') {
        const endDate = new Date(date.getFullYear(), date.getMonth(), date.getDate());
        endDate.setHours(23, 59, 59);
        return endDate;
    }
    // Default to month view
    return new Date(date.getFullYear(), date.getMonth() + 1, 0);
}

// Helper function to update view button states (month, week, day)
function updateViewButtons(activeButtonId, allButtonIds) {
    // Update the active state for view buttons
    allButtonIds.forEach(buttonId => {
        const button = document.getElementById(buttonId);
        if (button) {
            if (buttonId === activeButtonId) {
                button.classList.add('bg-primary', 'text-white');
                button.classList.remove('text-gray-300');
            } else {
                button.classList.remove('bg-primary', 'text-white');
                button.classList.add('text-gray-300');
            }
        }
    });
}

// Add a placeholder detailed room calendar renderer that falls back to the month view for now
function renderDetailedRoomCalendar(date, view, events) {
    console.log(`Rendering detailed room calendar view (${view}) with ${events.length} events`);
    
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    if (!roomCalendarGrid) {
        console.error('Room calendar grid element not found');
        return;
    }
    
    // Clear the grid
    roomCalendarGrid.innerHTML = '';
    
    if (view === 'week') {
        renderRoomWeekCalendar(date, events);
    } else if (view === 'day') {
        renderRoomDayCalendar(date, events);
    }
}

// Render room week calendar view
function renderRoomWeekCalendar(date, events) {
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    if (!roomCalendarGrid) return;
    
    // Set grid classes for week view
    roomCalendarGrid.className = 'grid grid-cols-8 text-sm text-white';
    
    // Calculate first day of the week (Sunday)
    const dayOfWeek = date.getDay();
    const weekStart = new Date(date);
    weekStart.setDate(date.getDate() - dayOfWeek);
    
    // Create header row with days of the week
    const headerRow = document.createElement('div');
    headerRow.className = 'col-span-1 bg-white/5 border-b border-white/10 p-2';
    headerRow.textContent = 'Time';
    roomCalendarGrid.appendChild(headerRow);
    
    // Create day headers (Sunday to Saturday)
    for (let i = 0; i < 7; i++) {
        const dayDate = new Date(weekStart);
        dayDate.setDate(weekStart.getDate() + i);
        
        const dayHeader = document.createElement('div');
        dayHeader.className = 'col-span-1 bg-white/5 border-b border-white/10 p-2 text-center font-semibold';
        
        // Highlight today
        if (dayDate.toDateString() === new Date().toDateString()) {
            dayHeader.classList.add('text-primary');
        }
        
        dayHeader.innerHTML = `${dayDate.toLocaleDateString('en-US', { weekday: 'short' })}<br>${dayDate.getDate()}`;
        roomCalendarGrid.appendChild(dayHeader);
    }
    
    // Create time slots (8am to 8pm)
    for (let hour = 8; hour <= 20; hour++) {
        // Time label
        const timeLabel = document.createElement('div');
        timeLabel.className = 'col-span-1 border-b border-white/10 p-2 text-right';
        timeLabel.textContent = `${hour > 12 ? hour - 12 : hour}${hour >= 12 ? 'pm' : 'am'}`;
        roomCalendarGrid.appendChild(timeLabel);
        
        // Create cells for each day
        for (let day = 0; day < 7; day++) {
            const cellDate = new Date(weekStart);
            cellDate.setDate(weekStart.getDate() + day);
            cellDate.setHours(hour, 0, 0, 0);
            
            const cell = document.createElement('div');
            cell.className = 'col-span-1 border-b border-l border-white/10 p-1 min-h-[60px] relative';
            
            // Highlight current hour
            const now = new Date();
            if (cellDate.toDateString() === now.toDateString() && hour === now.getHours()) {
                cell.classList.add('bg-primary/10');
            }
            
            // Find events for this time slot
            const hourEvents = events.filter(event => {
                try {
                    const eventStart = new Date(event.start || event.date);
                    
                    return cellDate.getDate() === eventStart.getDate() && 
                           cellDate.getMonth() === eventStart.getMonth() && 
                           cellDate.getFullYear() === eventStart.getFullYear() &&
                           hour === eventStart.getHours();
                } catch (e) {
                    return false;
                }
            });
            
            // Add events to cell
            hourEvents.forEach(event => {
                const eventElement = document.createElement('div');
                const typeClass = event.type === 'session' ? 'bg-green-800/70 border-green-500' : 'bg-blue-800/70 border-blue-500';
                eventElement.className = `p-1 text-xs rounded mb-1 border-l-2 ${typeClass}`;
                
                try {
                    const eventStart = new Date(event.start || event.date);
                    const eventTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                    eventElement.textContent = `${eventTime} - ${event.title}`;
                } catch (e) {
                    eventElement.textContent = event.title || 'Untitled event';
                }
                
                cell.appendChild(eventElement);
            });
            
            roomCalendarGrid.appendChild(cell);
        }
    }
}

// Render room day calendar view
function renderRoomDayCalendar(selectedDate, events) {
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    if (!roomCalendarGrid) return;
    
    // Set grid classes for day view
    roomCalendarGrid.className = 'grid grid-cols-1 gap-1 p-2';
    
    // Filter events for this day
    const dayEvents = events.filter(event => {
        try {
            const eventDate = new Date(event.start || event.date);
            return eventDate.getDate() === selectedDate.getDate() && 
                   eventDate.getMonth() === selectedDate.getMonth() && 
                   eventDate.getFullYear() === selectedDate.getFullYear();
        } catch (e) {
            return false;
        }
    });
    
    // Sort events by time
    dayEvents.sort((a, b) => {
        const timeA = new Date(a.start || a.date);
        const timeB = new Date(b.start || b.date);
        return timeA - timeB;
    });
    
    // Create hour cells for the day (7am to 10pm)
    const timeContainer = document.createElement('div');
    timeContainer.className = 'space-y-2';
    
    if (dayEvents.length === 0) {
        const noEvents = document.createElement('div');
        noEvents.className = 'p-4 text-center text-white/70';
        noEvents.textContent = 'No events scheduled for this room today';
        timeContainer.appendChild(noEvents);
    } else {
        // Create hour cells
        for (let hour = 7; hour <= 22; hour++) {
            // Find events for this hour
            const hourEvents = dayEvents.filter(event => {
                try {
                    const eventStart = new Date(event.start || event.date);
                    const eventEnd = new Date(event.end || new Date(eventStart).setHours(eventStart.getHours() + 1));
                    
                    return eventStart.getHours() <= hour && eventEnd.getHours() > hour;
                } catch (e) {
                    return false;
                }
            });
            
            // Only show hours with events
            if (hourEvents.length === 0) continue;
            
            // Create hour row
            const hourRow = document.createElement('div');
            hourRow.className = 'grid grid-cols-12 bg-white/5 rounded overflow-hidden';
            
            // Time cell
            const timeCell = document.createElement('div');
            timeCell.className = 'col-span-1 p-3 bg-white/5 text-center font-semibold';
            timeCell.textContent = `${hour > 12 ? hour - 12 : hour}${hour >= 12 ? 'pm' : 'am'}`;
            hourRow.appendChild(timeCell);
            
            // Events cell
            const eventsCell = document.createElement('div');
            eventsCell.className = 'col-span-11 p-2';
            
            // Create a flex container for events to display them side by side
            const eventsContainer = document.createElement('div');
            eventsContainer.className = 'flex flex-row gap-2';
            eventsCell.appendChild(eventsContainer);
            
            // Add events
            hourEvents.forEach(event => {
                const eventElement = document.createElement('div');
                const typeClass = event.type === 'session' ? 'bg-green-800/70 border-green-500' : 'bg-blue-800/70 border-blue-500';
                eventElement.className = `p-2 text-sm rounded flex-1 ${typeClass}`;
                
                const eventStart = new Date(event.start || event.date);
                const eventEnd = new Date(event.end || new Date(eventStart).setHours(eventStart.getHours() + 1));
                
                const startTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                const endTime = eventEnd.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                
                eventElement.innerHTML = `
                    <div class="font-semibold">${event.title}</div>
                    <div class="text-xs opacity-70">${startTime} - ${endTime}</div>
                `;
                
                eventsContainer.appendChild(eventElement);
            });
            
            hourRow.appendChild(eventsCell);
            timeContainer.appendChild(hourRow);
        }
    }
    
    roomCalendarGrid.appendChild(timeContainer);
}

// Update room view buttons
function updateRoomViewButtons() {
    // Get all room view buttons
    const roomViewButtons = document.querySelectorAll('[id^="roomView"]');
    
    // Remove active class from all buttons
    roomViewButtons.forEach(button => {
        button.classList.remove('bg-primary', 'text-white');
        button.classList.add('bg-white/10', 'text-gray-300');
    });
    
    // Add active class to the current view button
    const currentView = window.roomCalendarState ? window.roomCalendarState.currentView : 'month';
    const activeButtonId = `roomView${currentView.charAt(0).toUpperCase() + currentView.slice(1)}`;
    const activeButton = document.getElementById(activeButtonId);
    
    if (activeButton) {
        activeButton.classList.remove('bg-white/10', 'text-gray-300');
        activeButton.classList.add('bg-primary', 'text-white');
    }
}

// Render detailed room calendar for week and day views
function renderDetailedRoomCalendar(date, view, events) {
    console.log(`Rendering detailed room calendar view (${view}) with ${events.length} events`);
    
    const roomCalendarGrid = document.getElementById('roomCalendarGrid');
    if (!roomCalendarGrid) {
        console.error('Room calendar grid element not found');
        return;
    }
    
    // Clear the grid
    roomCalendarGrid.innerHTML = '';
    
    if (view === 'week') {
        renderRoomWeekCalendar(date, events);
    } else if (view === 'day') {
        renderRoomDayCalendar(date, events);
    }
}

// Update trainer calendar display
function updateTrainerCalendar() {
    if (!window.trainerCalendarState) return;
    
    const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
    const trainerCurrentMonthYear = document.getElementById('trainer-current-month-year');
    const trainerUpcomingEvents = document.getElementById('trainerUpcomingEvents');
    
    if (!trainerCalendarGrid) {
        console.error('Trainer calendar grid element not found');
        return;
    }
    
    const currentDate = window.trainerCalendarState.currentDate;
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const currentView = window.trainerCalendarState.currentView;
    
    // Update month/year display
    if (trainerCurrentMonthYear) {
        if (currentView === 'month') {
            trainerCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        } else if (currentView === 'week') {
            // Calculate week start and end dates
            const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 6 = Saturday
            const weekStart = new Date(currentDate);
            weekStart.setDate(currentDate.getDate() - dayOfWeek);
            
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            
            trainerCurrentMonthYear.textContent = `${weekStart.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            })} - ${weekEnd.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: 'numeric' 
            })}`;
        } else if (currentView === 'day') {
            trainerCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { 
                weekday: 'long',
                month: 'long', 
                day: 'numeric',
                year: 'numeric' 
            });
        }
    }
    
    // Show loading indicator
    trainerCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading trainer events...</div>';
    
    // Get trainer ID if set
    const trainerSelect = document.getElementById('trainerSelect');
    let trainerId = null;
    if (trainerSelect && trainerSelect.value) {
        trainerId = trainerSelect.value;
    }
    
    // Build date range for fetching events based on the view
    let startDate, endDate;
    
    if (currentView === 'month') {
        startDate = new Date(currentYear, currentMonth, 1);
        endDate = new Date(currentYear, currentMonth + 1, 0); // Last day of month
    } else if (currentView === 'week') {
        const dayOfWeek = currentDate.getDay();
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - dayOfWeek);
        
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59);
    } else if (currentView === 'day') {
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0);
        
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59);
    }
    
    // Get trainer events for calendar display
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}`;
    
    // Add trainer filter if specific trainer is selected
    if (trainerId) {
        apiUrl += `&trainer_id=${trainerId}`;
    }
    
    // Fetch trainer events
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // Store events and render calendar
            window.trainerCalendarState.allEvents = data.events || [];
            
            // Filter events based on checkboxes
            const showSessions = document.querySelector('.trainer-calendar-filters input[data-filter="session"]')?.checked ?? true;
            const showEvents = document.querySelector('.trainer-calendar-filters input[data-filter="event"]')?.checked ?? false;
            
            const filteredEvents = window.trainerCalendarState.allEvents.filter(
                event => shouldShowTrainerEvent(event, showSessions, showEvents)
            );
            
            // Render calendar based on current view
            if (currentView === 'month') {
                renderTrainerCalendar(currentMonth, currentYear, currentView, filteredEvents);
            } else if (currentView === 'week' || currentView === 'day') {
                renderTrainerDetailedCalendar(currentDate, currentView, filteredEvents);
            }
            
            // Show upcoming events
            if (trainerUpcomingEvents) {
                showUpcomingEvents(filteredEvents, trainerUpcomingEvents, 'trainer');
            }
        })
        .catch(error => {
            console.error('Error loading trainer events:', error);
            trainerCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events. Please try again.</div>';
        });
}

// Update equipment calendar display
function updateEquipmentCalendar() {
    if (!window.equipmentCalendarState) return;
    
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    const equipmentCurrentMonthYear = document.getElementById('equipment-current-month-year');
    const equipmentUpcomingEvents = document.getElementById('equipmentUpcomingEvents');
    
    if (!equipmentCalendarGrid) {
        console.error('Equipment calendar grid element not found');
        return;
    }
    
    const currentDate = window.equipmentCalendarState.currentDate;
    const currentMonth = currentDate.getMonth();
    const currentYear = currentDate.getFullYear();
    const currentView = window.equipmentCalendarState.currentView;
    
    // Update month/year display
    if (equipmentCurrentMonthYear) {
        if (currentView === 'month') {
            equipmentCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
        } else if (currentView === 'week') {
            // Calculate week start and end dates
            const dayOfWeek = currentDate.getDay(); // 0 = Sunday, 6 = Saturday
            const weekStart = new Date(currentDate);
            weekStart.setDate(currentDate.getDate() - dayOfWeek);
            
            const weekEnd = new Date(weekStart);
            weekEnd.setDate(weekStart.getDate() + 6);
            
            equipmentCurrentMonthYear.textContent = `${weekStart.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric' 
            })} - ${weekEnd.toLocaleDateString('en-US', { 
                month: 'short', 
                day: 'numeric',
                year: 'numeric' 
            })}`;
        } else if (currentView === 'day') {
            equipmentCurrentMonthYear.textContent = currentDate.toLocaleDateString('en-US', { 
                weekday: 'long',
                month: 'long', 
                day: 'numeric',
                year: 'numeric' 
            });
        }
    }
    
    // Show loading indicator
    equipmentCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70"><div class="inline-block animate-spin mr-2 h-4 w-4 border-2 border-current border-r-transparent rounded-full"></div> Loading equipment events...</div>';
    
    // Get equipment ID if set
    const equipmentSelect = document.getElementById('equipmentSelect');
    let equipmentId = null;
    if (equipmentSelect && equipmentSelect.value) {
        equipmentId = equipmentSelect.value;
    }
    
    // Build date range for fetching events based on the view
    let startDate, endDate;
    
    if (currentView === 'month') {
        startDate = new Date(currentYear, currentMonth, 1);
        endDate = new Date(currentYear, currentMonth + 1, 0); // Last day of month
    } else if (currentView === 'week') {
        const dayOfWeek = currentDate.getDay();
        startDate = new Date(currentDate);
        startDate.setDate(currentDate.getDate() - dayOfWeek);
        
        endDate = new Date(startDate);
        endDate.setDate(startDate.getDate() + 6);
        endDate.setHours(23, 59, 59);
    } else if (currentView === 'day') {
        startDate = new Date(currentDate);
        startDate.setHours(0, 0, 0);
        
        endDate = new Date(currentDate);
        endDate.setHours(23, 59, 59);
    }
    
    // Get equipment events for calendar display
    let apiUrl = `{% url "website:calendar_events_api" %}?start=${startDate.toISOString().split('T')[0]}&end=${endDate.toISOString().split('T')[0]}`;
    
    // Add equipment filter if specific equipment is selected
    if (equipmentId) {
        apiUrl += `&equipment_id=${equipmentId}`;
    }
    
    // Fetch equipment events
    fetch(apiUrl)
        .then(response => response.json())
        .then(data => {
            // Store events and render calendar
            window.equipmentCalendarState.allEvents = data.events || [];
            
            // Filter events based on checkboxes
    const showSessions = document.querySelector('.equipment-calendar-filters input[data-filter="session"]')?.checked ?? true;
    const showEvents = document.querySelector('.equipment-calendar-filters input[data-filter="event"]')?.checked ?? true;
    
            const filteredEvents = window.equipmentCalendarState.allEvents.filter(
                event => shouldShowEquipmentEvent(event, showSessions, showEvents)
            );
            
            // Render calendar based on current view
            if (currentView === 'month') {
                renderEquipmentCalendar(currentMonth, currentYear, currentView, filteredEvents);
            } else if (currentView === 'week' || currentView === 'day') {
                renderDetailedEquipmentCalendar(currentDate, currentView, filteredEvents);
            }
            
            // Show upcoming events
            if (equipmentUpcomingEvents) {
                showUpcomingEvents(filteredEvents, equipmentUpcomingEvents, 'equipment');
            }
        })
        .catch(error => {
            console.error('Error loading equipment events:', error);
            equipmentCalendarGrid.innerHTML = '<div class="col-span-7 p-4 text-center text-white/70">Error loading events. Please try again.</div>';
        });
}

// Function to check if an equipment event should be shown based on filters
function shouldShowEquipmentEvent(event, showSessions, showEvents) {
    // Always show holidays
    if (event.type === 'holiday') return true;
    if (event.type === 'session' && !showSessions) return false;
    if (event.type !== 'session' && event.type !== 'holiday' && !showEvents) return false;
    return true;
}

// Helper function to initialize filter checkboxes for any calendar view
function initFilterCheckboxes(containerSelector, refreshFunction) {
    const container = document.querySelector(containerSelector);
    if (!container) {
        console.warn(`Filter container ${containerSelector} not found`);
        return;
    }
    
    const sessionCheckbox = container.querySelector('input[data-filter="session"]');
    const eventCheckbox = container.querySelector('input[data-filter="event"]');
    const holidayCheckbox = container.querySelector('input[data-filter="holiday"]');
    
    if (sessionCheckbox) {
        console.log(`Found session filter checkbox in ${containerSelector}, adding event listener`);
        sessionCheckbox.addEventListener('change', function() {
            console.log(`Sessions filter changed in ${containerSelector}:`, this.checked);
            refreshFunction();
        });
    } else {
        console.warn(`Session filter checkbox not found in ${containerSelector}`);
    }
    
    if (eventCheckbox) {
        console.log(`Found event filter checkbox in ${containerSelector}, adding event listener`);
        eventCheckbox.addEventListener('change', function() {
            console.log(`Events filter changed in ${containerSelector}:`, this.checked);
            refreshFunction();
        });
    } else {
        console.warn(`Event filter checkbox not found in ${containerSelector}`);
    }
    
    if (holidayCheckbox) {
        console.log(`Found holiday filter checkbox in ${containerSelector}, adding event listener`);
        holidayCheckbox.addEventListener('change', function() {
            console.log(`Holidays filter changed in ${containerSelector}:`, this.checked);
            refreshFunction();
        });
    } else {
        console.warn(`Holiday filter checkbox not found in ${containerSelector}`);
    }
}

// Initialize everything when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM content loaded - initializing calendar');
    
    // Initialize tabs
    try {
        // Use Flowbite's standard initialization instead
        console.log('Using standard Flowbite initialization');
        initTabs(); // Always use our custom tabs
    } catch (e) {
        console.error('Error initializing tabs:', e);
        // Fallback to custom initialization
        initTabs();
    }
    
    // Initialize main calendar by default
    // REMOVED: initMainCalendar(); - Duplicate call causing multiple initialization
    
    // Initialize filter checkboxes for all calendar views
    initFilterCheckboxes('.calendar-filters', refreshCalendarView);
    initFilterCheckboxes('.room-calendar-filters', refreshRoomCalendarView);
    initFilterCheckboxes('.trainer-calendar-filters', refreshTrainerCalendarView);
    initFilterCheckboxes('.equipment-calendar-filters', refreshEquipmentCalendarView);
    
    // Initialize room, trainer, and equipment calendars
    const roomTabButton = document.getElementById('rooms-tab');
    if (roomTabButton) {
        roomTabButton.addEventListener('click', function() {
            initRoomCalendar();
        });
    }
    
    const trainerTabButton = document.getElementById('trainers-tab');
    if (trainerTabButton) {
        trainerTabButton.addEventListener('click', function() {
            initTrainerCalendar();
        });
    }
    
    const equipmentTabButton = document.getElementById('equipment-tab');
    if (equipmentTabButton) {
        equipmentTabButton.addEventListener('click', function() {
            initEquipmentCalendar();
        });
    }
});

// Update trainer view buttons active state
function updateTrainerViewButtons() {
    // Get all trainer view buttons
    const trainerViewButtons = document.querySelectorAll('[id^="trainerView"]');
    
    // Remove active class from all buttons
    trainerViewButtons.forEach(button => {
        button.classList.remove('bg-primary', 'text-white');
        button.classList.add('bg-white/10', 'text-gray-300');
    });
    
    // Add active class to the current view button
    const currentView = window.trainerCalendarState ? window.trainerCalendarState.currentView : 'month';
    const activeButtonId = `trainerView${currentView.charAt(0).toUpperCase() + currentView.slice(1)}`;
    const activeButton = document.getElementById(activeButtonId);
    
    if (activeButton) {
        activeButton.classList.remove('bg-white/10', 'text-gray-300');
        activeButton.classList.add('bg-primary', 'text-white');
    }
}

// Navigate trainer calendar
function navigateTrainerCalendar(direction) {
    const state = window.trainerCalendarState;
    if (!state) return;
    
    // Add debounce to prevent multiple rapid calls
    if (window.trainerNavigationInProgress) {
        console.log('Trainer navigation already in progress, ignoring duplicate call');
        return;
    }
    
    // Set a flag to indicate navigation is in progress
    window.trainerNavigationInProgress = true;
    
    if (direction === 'prev') {
        if (state.currentView === 'month') {
            // Go to previous month
            state.currentDate.setMonth(state.currentDate.getMonth() - 1);
        } else if (state.currentView === 'week') {
            // Go to previous week
            state.currentDate.setDate(state.currentDate.getDate() - 7);
        } else if (state.currentView === 'day') {
            // Go to previous day
            state.currentDate.setDate(state.currentDate.getDate() - 1);
        }
    } else if (direction === 'next') {
        if (state.currentView === 'month') {
            // Go to next month
            state.currentDate.setMonth(state.currentDate.getMonth() + 1);
        } else if (state.currentView === 'week') {
            // Go to next week
            state.currentDate.setDate(state.currentDate.getDate() + 7);
        } else if (state.currentView === 'day') {
            // Go to next day
            state.currentDate.setDate(state.currentDate.getDate() + 1);
        }
    } else if (direction === 'today') {
        // Go to today
        state.currentDate = new Date();
    }
    
    // Update the calendar
    updateTrainerCalendar();
    
    // Reset the navigation flag after a short delay
    setTimeout(() => {
        window.trainerNavigationInProgress = false;
    }, 500);
}

// Render detailed trainer calendar for week and day views
function renderTrainerDetailedCalendar(date, view, events) {
    const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
    if (!trainerCalendarGrid) {
        console.error('Trainer calendar grid element not found');
        return;
    }
    
    // Clear the grid
    trainerCalendarGrid.innerHTML = '';
    
    if (view === 'week') {
        renderTrainerWeekCalendar(date, events);
    } else if (view === 'day') {
        renderTrainerDayCalendar(date, events);
    }
}

// Render trainer week calendar view
function renderTrainerWeekCalendar(date, events) {
    const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
    if (!trainerCalendarGrid) return;
    
    // Set grid classes for week view
    trainerCalendarGrid.className = 'grid grid-cols-8 text-sm text-white';
    
    // Calculate first day of the week (Sunday)
    const dayOfWeek = date.getDay();
    const weekStart = new Date(date);
    weekStart.setDate(date.getDate() - dayOfWeek);
    
    // Create header row with days of the week
    const headerRow = document.createElement('div');
    headerRow.className = 'col-span-1 bg-white/5 border-b border-white/10 p-2';
    headerRow.textContent = 'Time';
    trainerCalendarGrid.appendChild(headerRow);
    
    // Create day headers (Sunday to Saturday)
    for (let i = 0; i < 7; i++) {
        const dayDate = new Date(weekStart);
        dayDate.setDate(weekStart.getDate() + i);
        
        const dayHeader = document.createElement('div');
        dayHeader.className = 'col-span-1 bg-white/5 border-b border-white/10 p-2 text-center';
        dayHeader.textContent = dayDate.toLocaleDateString('en-US', { weekday: 'short', day: 'numeric' });
        trainerCalendarGrid.appendChild(dayHeader);
    }
    
    // Create hourly cells (7am to 10pm)
    for (let hour = 7; hour <= 22; hour++) {
        // Create time label
        const timeLabel = document.createElement('div');
        timeLabel.className = 'col-span-1 bg-white/5 border-b border-white/10 p-2 text-center';
        timeLabel.textContent = `${hour > 12 ? hour - 12 : hour}${hour >= 12 ? 'pm' : 'am'}`;
        trainerCalendarGrid.appendChild(timeLabel);
        
        // Create cells for each day of the week
        for (let i = 0; i < 7; i++) {
            const cellDate = new Date(weekStart);
            cellDate.setDate(weekStart.getDate() + i);
            cellDate.setHours(hour, 0, 0, 0);
            
            const cell = document.createElement('div');
            cell.className = 'col-span-1 bg-white/5 border-b border-white/10 border-l border-white/10 p-1 relative min-h-[60px]';
            
            // Filter events for this day and hour
            const hourEvents = events.filter(event => {
                try {
                    const eventStart = new Date(event.start || event.date);
                    
                    return cellDate.getDate() === eventStart.getDate() && 
                          cellDate.getMonth() === eventStart.getMonth() && 
                          cellDate.getFullYear() === eventStart.getFullYear() &&
                          hour === eventStart.getHours();
                } catch (e) {
                    return false;
                }
            });
            
            // Add events to cell
            hourEvents.forEach(event => {
                const eventElement = document.createElement('div');
                let typeClass;
                
                // Style based on event type
                if (event.type === 'session') {
                    typeClass = 'bg-green-800/70 border-green-500';
                } else if (event.type === 'holiday') {
                    typeClass = 'bg-red-800/70 border-red-500';
                    // Add holiday emoji prefix
                    if (!event.title.startsWith('🎉')) {
                        event.title = `🎉 ${event.title}`;
                    }
                } else {
                    typeClass = 'bg-blue-800/70 border-blue-500';
                }
                
                eventElement.className = `p-1 text-xs rounded mb-1 border-l-2 ${typeClass}`;
                
                try {
                    const eventStart = new Date(event.start || event.date);
                    const eventTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                    eventElement.textContent = `${eventTime} - ${event.title}`;
                } catch (e) {
                    eventElement.textContent = event.title || 'Untitled event';
                }
                
                cell.appendChild(eventElement);
            });
            
            trainerCalendarGrid.appendChild(cell);
        }
    }
}

// Render trainer day calendar view
function renderTrainerDayCalendar(selectedDate, events) {
    const trainerCalendarGrid = document.getElementById('trainerCalendarGrid');
    if (!trainerCalendarGrid) return;
    
    // Set grid classes for day view
    trainerCalendarGrid.className = 'grid grid-cols-1 gap-1 p-2';
    
    // Filter events for this day
    const dayEvents = events.filter(event => {
        try {
            const eventDate = new Date(event.start || event.date);
            return eventDate.getDate() === selectedDate.getDate() && 
                  eventDate.getMonth() === selectedDate.getMonth() && 
                  eventDate.getFullYear() === selectedDate.getFullYear();
        } catch (e) {
            return false;
        }
    });
    
    // Sort events by time
    dayEvents.sort((a, b) => {
        const timeA = new Date(a.start || a.date);
        const timeB = new Date(b.start || b.date);
        return timeA - timeB;
    });
    
    // Create hour cells for the day (7am to 10pm)
    const timeContainer = document.createElement('div');
    timeContainer.className = 'space-y-2';
    
    if (dayEvents.length === 0) {
        const noEvents = document.createElement('div');
        noEvents.className = 'p-4 text-center text-white/70';
        noEvents.textContent = 'No events scheduled for this trainer today';
        timeContainer.appendChild(noEvents);
    } else {
        // Create hour cells
        for (let hour = 7; hour <= 22; hour++) {
            // Find events for this hour
            const hourEvents = dayEvents.filter(event => {
                try {
                    const eventStart = new Date(event.start || event.date);
                    const eventEnd = new Date(event.end || new Date(eventStart).setHours(eventStart.getHours() + 1));
                    
                    return eventStart.getHours() <= hour && eventEnd.getHours() > hour;
                } catch (e) {
                    return false;
                }
            });
            
            // Only show hours with events
            if (hourEvents.length === 0) continue;
            
            // Create hour row
            const hourRow = document.createElement('div');
            hourRow.className = 'grid grid-cols-12 bg-white/5 rounded overflow-hidden';
            
            // Time cell
            const timeCell = document.createElement('div');
            timeCell.className = 'col-span-1 p-3 bg-white/5 text-center font-semibold';
            timeCell.textContent = `${hour > 12 ? hour - 12 : hour}${hour >= 12 ? 'pm' : 'am'}`;
            hourRow.appendChild(timeCell);
            
            // Events cell
            const eventsCell = document.createElement('div');
            eventsCell.className = 'col-span-11 p-2';
            
            // Add events
            hourEvents.forEach(event => {
                const eventElement = document.createElement('div');
                const typeClass = event.type === 'session' ? 'bg-green-800/70 border-green-500' : 'bg-blue-800/70 border-blue-500';
                eventElement.className = `p-2 text-sm rounded my-1 border-l-2 ${typeClass}`;
                
                const eventStart = new Date(event.start || event.date);
                const eventEnd = new Date(event.end || new Date(eventStart).setHours(eventStart.getHours() + 1));
                
                const startTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                const endTime = eventEnd.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                
                eventElement.innerHTML = `
                    <div class="font-semibold">${event.title}</div>
                    <div class="text-xs opacity-70">${startTime} - ${endTime}</div>
                `;
                
                eventsCell.appendChild(eventElement);
            });
            
            hourRow.appendChild(eventsCell);
            timeContainer.appendChild(hourRow);
        }
    }
    
    trainerCalendarGrid.appendChild(timeContainer);
}

// Update equipment view buttons active state
function updateEquipmentViewButtons() {
    // Get all equipment view buttons
    const equipmentViewButtons = document.querySelectorAll('[id^="equipmentView"]');
    
    // Remove active class from all buttons
    equipmentViewButtons.forEach(button => {
        button.classList.remove('bg-primary', 'text-white');
        button.classList.add('bg-white/10', 'text-gray-300');
    });
    
    // Add active class to the current view button
    const currentView = window.equipmentCalendarState ? window.equipmentCalendarState.currentView : 'month';
    const activeButtonId = `equipmentView${currentView.charAt(0).toUpperCase() + currentView.slice(1)}`;
    const activeButton = document.getElementById(activeButtonId);
    
    if (activeButton) {
        activeButton.classList.remove('bg-white/10', 'text-gray-300');
        activeButton.classList.add('bg-primary', 'text-white');
    }
}

// Render detailed equipment calendar for week and day views
function renderDetailedEquipmentCalendar(date, view, events) {
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    if (!equipmentCalendarGrid) {
        console.error('Equipment calendar grid element not found');
        return;
    }
    
    // Clear the grid
    equipmentCalendarGrid.innerHTML = '';
    
    if (view === 'week') {
        renderEquipmentWeekView(date, events);
    } else if (view === 'day') {
        renderEquipmentDayView(date, events);
    }
}

// Render equipment week view similar to main calendar
function renderEquipmentWeekView(date, events) {
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    if (!equipmentCalendarGrid) return;
    
    // Set grid classes for week view
    equipmentCalendarGrid.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Create day headers (Sunday to Saturday)
    const weekdays = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    
    weekdays.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        equipmentCalendarGrid.appendChild(dayHeader);
    });
    
    // Get the first day of the week (Sunday)
    const dayOfWeek = date.getDay();
    const weekStart = new Date(date);
    weekStart.setDate(date.getDate() - dayOfWeek);
    
    // Create day cells for each day of the week
    for (let i = 0; i < 7; i++) {
        const cellDate = new Date(weekStart);
        cellDate.setDate(weekStart.getDate() + i);
        
        const dayCell = document.createElement('div');
        dayCell.className = 'border-t border-white/10 h-32 overflow-y-auto p-1';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/70';
        dateDisplay.textContent = cellDate.getDate();
        dayCell.appendChild(dateDisplay);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.start || event.date);
                return eventDate.getDate() === cellDate.getDate() && 
                       eventDate.getMonth() === cellDate.getMonth() && 
                       eventDate.getFullYear() === cellDate.getFullYear();
            } catch (e) {
                return false;
            }
        });
        
        // Sort events by time
        dayEvents.sort((a, b) => {
            const timeA = new Date(a.start || a.date);
            const timeB = new Date(b.start || b.date);
            return timeA - timeB;
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = document.createElement('div');
            const typeClass = event.type === 'session' ? 'bg-green-800/70 border-l-2 border-green-500' : 'bg-blue-800/70 border-l-2 border-blue-500';
            eventElement.className = `p-1 text-xs rounded mb-1 ${typeClass}`;
            
            try {
                const eventStart = new Date(event.start || event.date);
                const eventTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                eventElement.textContent = `${eventTime} - ${event.title || 'Untitled'}`;
            } catch (e) {
                eventElement.textContent = event.title || 'Untitled';
            }
            
            dayCell.appendChild(eventElement);
        });
        
        equipmentCalendarGrid.appendChild(dayCell);
    }
}

// Render equipment day view similar to main calendar
function renderEquipmentDayView(selectedDate, events) {
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    if (!equipmentCalendarGrid) return;
    
    // Set grid classes for day view
    equipmentCalendarGrid.className = 'grid grid-cols-1 gap-1 p-2';
    
    // Filter events for this day
    const dayEvents = events.filter(event => {
        try {
            const eventDate = new Date(event.start || event.date);
            return eventDate.getDate() === selectedDate.getDate() && 
                   eventDate.getMonth() === selectedDate.getMonth() && 
                   eventDate.getFullYear() === selectedDate.getFullYear();
        } catch (e) {
            return false;
        }
    });
    
    // Sort events by time
    dayEvents.sort((a, b) => {
        const timeA = new Date(a.start || a.date);
        const timeB = new Date(b.start || b.date);
        return timeA - timeB;
    });
    
    // Create hour cells for the day (5am to 11pm)
    for (let hour = 5; hour <= 23; hour++) {
        const hourCell = document.createElement('div');
        hourCell.className = 'border-t border-white/10 min-h-16 p-1 relative';
        
        // Add time label
        const timeLabel = document.createElement('div');
        timeLabel.className = 'text-xs text-white/70 absolute top-1 left-1';
        timeLabel.textContent = `${hour}:00`;
        hourCell.appendChild(timeLabel);
        
        // Add a content div with padding to not overlap the time label
        const contentDiv = document.createElement('div');
        contentDiv.className = 'pt-5 pl-14';
        
        // Filter events for this hour
        const hourEvents = dayEvents.filter(event => {
            try {
                const eventTime = new Date(event.start || event.date);
                return eventTime.getHours() === hour;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the content div
        hourEvents.forEach(event => {
            const eventElement = document.createElement('div');
            const typeClass = event.type === 'session' ? 'bg-green-800/70 border-l-2 border-green-500' : 'bg-blue-800/70 border-l-2 border-blue-500';
            eventElement.className = `p-2 text-sm rounded my-1 ${typeClass}`;
            
            const eventStart = new Date(event.start || event.date);
            const eventTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
            
            eventElement.innerHTML = `
                <div class="font-semibold">${event.title || 'Untitled'}</div>
                <div class="text-xs opacity-70">${eventTime}</div>
            `;
            
            contentDiv.appendChild(eventElement);
        });
        
        hourCell.appendChild(contentDiv);
        equipmentCalendarGrid.appendChild(hourCell);
    }
}

// Initialize tabs
function initTabs() {
    console.log('Custom tab initialization function called');
    const tabs = document.querySelectorAll('[role="tab"]');
    
    // Set up a function to directly initialize the correct calendar based on tab ID
    const initializeTabCalendar = function(tabId) {
        console.log('Initializing calendar for tab:', tabId);
        
        if (tabId === 'rooms-tab' && typeof initRoomCalendar === 'function') {
            console.log('Initializing room calendar');
            initRoomCalendar();
        } else if (tabId === 'trainers-tab' && typeof initTrainerCalendar === 'function') {
            console.log('Initializing trainer calendar');
            initTrainerCalendar();
        } else if (tabId === 'equipment-tab' && typeof initEquipmentCalendar === 'function') {
            console.log('Initializing equipment calendar');
            initEquipmentCalendar();
        } else if (tabId === 'main-tab' && typeof initMainCalendar === 'function') {
            console.log('Initializing main calendar');
            initMainCalendar();
        }
    };
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Get the target content ID from data-tabs-target
            const targetId = this.getAttribute('data-tabs-target').replace('#', '');
            const tabId = this.id;
            
            console.log('Tab clicked:', tabId, 'targeting:', targetId);
            
            // Hide all tab content
            document.querySelectorAll('[role="tabpanel"]').forEach(panel => {
                panel.classList.add('hidden');
            });
            
            // Show the target content
            if (targetId) {
                const targetPanel = document.getElementById(targetId);
                if (targetPanel) {
                    targetPanel.classList.remove('hidden');
                }
            }
            
            // Update active state on tabs
            tabs.forEach(t => {
                t.setAttribute('aria-selected', 'false');
                t.classList.remove('border-primary', 'text-primary');
                t.classList.add('border-transparent', 'text-white/70');
            });
            
            // Set this tab as active
            this.setAttribute('aria-selected', 'true');
            this.classList.remove('border-transparent', 'text-white/70');
            this.classList.add('border-primary', 'text-primary');
            
            // Initialize calendar based on active tab - do this immediately
            initializeTabCalendar(tabId);
        });
        
        // Add immediate initialization for rooms, trainers, and equipment tabs
        // This ensures the first click works properly
        if (tab.id === 'rooms-tab' || tab.id === 'trainers-tab' || tab.id === 'equipment-tab') {
            // Store the original click handler
            const originalClickHandler = tab.onclick;
            
            // Replace with immediate initialization handler for first click
            tab.addEventListener('firstclick', function(e) {
                console.log('First click on tab:', tab.id);
                // The tab handler will call the appropriate initialization
            });
        }
    });
    
    // Return the initialization function so it can be used elsewhere
    return initializeTabCalendar;
}

// Find and update the Add Event button click handler
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener to the Add Event button in the top right
    const addEventBtn = document.querySelector('a.inline-flex.items-center[href*="sessions"]');
    if (addEventBtn) {
        // Create a new button to replace the link
        const newButton = document.createElement('button');
        // Copy all classes from the original link
        newButton.className = addEventBtn.className;
        // Copy inner HTML
        newButton.innerHTML = addEventBtn.innerHTML;
        // Set type to button
        newButton.type = 'button';
        // Add click event listener
        newButton.addEventListener('click', function(e) {
            e.preventDefault();
            // Open the calendar add popup with the current date
            if (typeof openCalendarAddPopup === 'function') {
                openCalendarAddPopup(new Date());
            }
        });
        
        // Replace the original link with the new button
        addEventBtn.parentNode.replaceChild(newButton, addEventBtn);
    }
});

// Add Holiday button click event
const addHolidayBtn = document.getElementById('addHolidayBtn');
if (addHolidayBtn) {
    addHolidayBtn.addEventListener('click', function() {
        // Open holiday popup
        if (typeof openHolidayAddPopup === 'function') {
            openHolidayAddPopup();
        }
    });
}

// Function to reload calendar after holiday changes
window.reloadCalendar = function() {
    // Trigger calendar reload if calendar object exists
    if (window.calendar && typeof window.calendar.refetchEvents === 'function') {
        window.calendar.refetchEvents();
    } else {
        console.log("Calendar object not found, reloading page instead");
        window.location.reload();
    }
};

// Function to refresh the equipment calendar view based on filters
function refreshEquipmentCalendarView() {
    if (!window.equipmentCalendarState) return;
    
    const date = window.equipmentCalendarState.currentDate;
    const events = window.equipmentCalendarState.allEvents || [];
    
    // Get filter states (session checkbox defaults to checked, event checkbox to unchecked)
    const showSessions = document.querySelector('.equipment-calendar-filters input[data-filter="session"]')?.checked ?? true;
    const showEvents = document.querySelector('.equipment-calendar-filters input[data-filter="event"]')?.checked ?? false;
    
    // Filter events based on checkboxes
    const filteredEvents = events.filter(event => shouldShowEquipmentEvent(event, showSessions, showEvents));
    
    // Re-render calendar with filtered events
    renderEquipmentCalendar(
        date.getMonth(),
        date.getFullYear(),
        window.equipmentCalendarState.currentView,
        filteredEvents
    );
    
    // Update upcoming events
    const equipmentUpcomingEvents = document.getElementById('equipmentUpcomingEvents');
    if (equipmentUpcomingEvents) {
        showUpcomingEvents(filteredEvents, equipmentUpcomingEvents, 'equipment');
    }
}

// Function to check if an event should be shown based on equipment filters
function shouldShowEquipmentEvent(event, showSessions, showEvents) {
    // Always show holidays
    if (event.type === 'holiday') return true;
    if (event.type === 'session' && !showSessions) return false;
    if (event.type !== 'session' && event.type !== 'holiday' && !showEvents) return false;
    return true;
}

// Navigate equipment calendar based on action (prev, next, today)
function navigateEquipmentCalendar(direction) {
    // If navigation is in progress, return to prevent multiple rapid calls
    if (window.equipmentCalendarState?.isNavigating) return;
    
    // Set the navigation flag
    if (window.equipmentCalendarState) {
        window.equipmentCalendarState.isNavigating = true;
    }
    
    // Get current date from state
    const currentDate = window.equipmentCalendarState?.currentDate || new Date();
    const currentView = window.equipmentCalendarState?.currentView || 'month';
    
    let newDate = new Date(currentDate);
    
    // Handle different navigation directions
    if (direction === 'prev') {
        if (currentView === 'month') {
            // Previous month
            newDate.setMonth(currentDate.getMonth() - 1);
        } else if (currentView === 'week') {
            // Previous week
            newDate.setDate(currentDate.getDate() - 7);
        } else if (currentView === 'day') {
            // Previous day
            newDate.setDate(currentDate.getDate() - 1);
        }
    } else if (direction === 'next') {
        if (currentView === 'month') {
            // Next month
            newDate.setMonth(currentDate.getMonth() + 1);
        } else if (currentView === 'week') {
            // Next week
            newDate.setDate(currentDate.getDate() + 7);
        } else if (currentView === 'day') {
            // Next day
            newDate.setDate(currentDate.getDate() + 1);
        }
    } else if (direction === 'today') {
        // Reset to today
        newDate = new Date();
    }
    
    // Update the state
    if (window.equipmentCalendarState) {
        window.equipmentCalendarState.currentDate = newDate;
    }
    
    // Update the calendar
    updateEquipmentCalendar();
    
    // Reset navigation flag after a short delay
    setTimeout(() => {
        if (window.equipmentCalendarState) {
            window.equipmentCalendarState.isNavigating = false;
        }
    }, 200);
}

// Render equipment calendar
function renderEquipmentCalendar(month, year, view, events) {
    const equipmentCalendarGrid = document.getElementById('equipmentCalendarGrid');
    if (!equipmentCalendarGrid) {
        console.error('Equipment calendar grid element not found');
        return;
    }
    
    // Clear the grid and remove loading indicator
    equipmentCalendarGrid.innerHTML = '';
    
    // Use similar layout as the trainer calendar
    equipmentCalendarGrid.className = 'grid grid-cols-7 gap-1 p-2';
    
    // Add day headers (Sun - Sat)
    const days = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
    days.forEach(day => {
        const dayHeader = document.createElement('div');
        dayHeader.className = 'text-center font-semibold py-1 text-sky-100';
        dayHeader.textContent = day;
        equipmentCalendarGrid.appendChild(dayHeader);
    });
    
    // Get the first day of the month
    const firstDay = new Date(year, month, 1);
    // Get the last day of the month
    const lastDay = new Date(year, month + 1, 0);
    
    // Get the day of the week for the first day (0 = Sunday, 6 = Saturday)
    const firstDayIndex = firstDay.getDay();
    
    // Calculate days from previous month to display
    for (let i = 0; i < firstDayIndex; i++) {
        const previousMonthDay = new Date(year, month, -firstDayIndex + i + 1);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = previousMonthDay.getDate();
        dayCell.appendChild(dateDisplay);
        
        equipmentCalendarGrid.appendChild(dayCell);
    }
    
    // Add days for current month
    for (let i = 1; i <= lastDay.getDate(); i++) {
        const currentDate = new Date(year, month, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 hover:bg-white/10 overflow-y-auto relative';
        
        // Check if this is today
        const today = new Date();
        if (currentDate.getDate() === today.getDate() && 
            currentDate.getMonth() === today.getMonth() && 
            currentDate.getFullYear() === today.getFullYear()) {
            dayCell.classList.add('border', 'border-primary', 'bg-primary/10');
        }
        
        // Create header container for date and add button
        const headerContainer = document.createElement('div');
        headerContainer.className = 'flex justify-between items-center mb-1';
        
        // Add date to the header
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-sm text-white/70';
        dateDisplay.textContent = i;
        headerContainer.appendChild(dateDisplay);
        
        // Add the "+" button
        const addButton = document.createElement('button');
        addButton.className = 'text-primary hover:text-white bg-primary/20 hover:bg-primary/40 rounded-full w-5 h-5 flex items-center justify-center transition-colors';
        addButton.innerHTML = '<svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path></svg>';
        addButton.title = 'Add Event';
        
        // Format date for popup
        const formattedDate = `${year}-${String(month + 1).padStart(2, '0')}-${String(i).padStart(2, '0')}`;
        
        // Add click event to redirect to create event/session page with date
        addButton.addEventListener('click', (e) => {
            e.stopPropagation(); // Prevent event bubbling
            
            // Use the openCalendarAddPopup function instead of redirecting
            if (typeof openCalendarAddPopup === 'function') {
                openCalendarAddPopup(formattedDate);
            }
        });
        
        headerContainer.appendChild(addButton);
        dayCell.appendChild(headerContainer);
        
        // Filter events for this day
        const dayEvents = events.filter(event => {
            try {
                const eventDate = new Date(event.start || event.date);
                return eventDate.getDate() === i && 
                       eventDate.getMonth() === month && 
                       eventDate.getFullYear() === year;
            } catch (e) {
                return false;
            }
        });
        
        // Add events to the cell
        dayEvents.forEach(event => {
            const eventElement = document.createElement('div');
            
            // Set classes based on event type
            if (event.type === 'session') {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-green-600/70 truncate';
            } else if (event.type === 'holiday') {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-red-600/70 truncate';
                // Add holiday emoji prefix
                if (!event.title.startsWith('🎉')) {
                    event.title = `🎉 ${event.title}`;
                }
            } else {
                eventElement.className = 'mt-1 p-1 rounded-md text-xs bg-blue-500/70 truncate';
            }
            
            // Set event title with time
            try {
                const eventStart = new Date(event.start || event.date);
                const eventTime = eventStart.toLocaleTimeString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                eventElement.textContent = `${eventTime} - ${event.title || 'Untitled Event'}`;
            } catch (e) {
                eventElement.textContent = event.title || 'Untitled Event';
            }
            
            // Add the event to the cell
            dayCell.appendChild(eventElement);
        });
        
        equipmentCalendarGrid.appendChild(dayCell);
    }
    
    // Fill remaining cells with next month's days
    const totalCells = 42; // 6 rows x 7 days
    const cellsToAdd = totalCells - (firstDayIndex + lastDay.getDate());
    
    for (let i = 1; i <= cellsToAdd; i++) {
        const nextMonthDay = new Date(year, month + 1, i);
        const dayCell = document.createElement('div');
        dayCell.className = 'min-h-[100px] p-1 bg-white/5 opacity-50';
        
        // Add date to the top of the cell
        const dateDisplay = document.createElement('div');
        dateDisplay.className = 'text-right text-sm text-white/50';
        dateDisplay.textContent = i;
        dayCell.appendChild(dateDisplay);
        
        equipmentCalendarGrid.appendChild(dayCell);
    }
}

// Function to refresh the equipment calendar view based on filters
function refreshEquipmentCalendarView() {
    if (!window.equipmentCalendarState) return;
    
    const date = window.equipmentCalendarState.currentDate;
    const events = window.equipmentCalendarState.allEvents || [];
    
    // Get filter states (session checkbox defaults to checked, event checkbox to unchecked)
    const showSessions = document.querySelector('.equipment-calendar-filters input[data-filter="session"]')?.checked ?? true;
    const showEvents = document.querySelector('.equipment-calendar-filters input[data-filter="event"]')?.checked ?? false;
    
    // Filter events based on checkboxes
    const filteredEvents = events.filter(event => shouldShowEquipmentEvent(event, showSessions, showEvents));
    
    // Re-render calendar with filtered events
    renderEquipmentCalendar(
        date.getMonth(),
        date.getFullYear(),
        window.equipmentCalendarState.currentView,
        filteredEvents
    );
    
    // Update upcoming events
    const equipmentUpcomingEvents = document.getElementById('equipmentUpcomingEvents');
    if (equipmentUpcomingEvents) {
        showUpcomingEvents(filteredEvents, equipmentUpcomingEvents, 'equipment');
    }
}

// Function to fetch and display holidays
function fetchHolidays() {
    fetch('/api/holidays/get/')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.holidays) {
            // Store holidays globally for reference
            window.calendarHolidays = data.holidays;
            
            // Apply holiday markers to calendar cells
            applyHolidayMarkersToCalendar();
        }
    })
    .catch(error => {
        console.error('Error fetching holidays:', error);
    });
}

// Function to apply holiday markers to calendar cells
function applyHolidayMarkersToCalendar() {
    // Skip if no holidays data
    if (!window.calendarHolidays) return;
    
    // Get all date cells
    const dateCells = document.querySelectorAll('[data-date]');
    
    // Loop through cells and check against holidays
    dateCells.forEach(cell => {
        const cellDate = cell.getAttribute('data-date');
        
        // Check if this date is a holiday
        const holiday = window.calendarHolidays.find(h => h.date === cellDate);
        
        if (holiday) {
            // Create or get the holiday indicator
            let holidayIndicator = cell.querySelector('.holiday-indicator');
            
            if (!holidayIndicator) {
                holidayIndicator = document.createElement('div');
                holidayIndicator.className = 'holiday-indicator absolute bottom-1 right-1 w-4 h-4 rounded-full';
                cell.appendChild(holidayIndicator);
            }
            
            // Update indicator color based on conflicts
            if (holiday.has_conflicts) {
                holidayIndicator.classList.add('bg-red-500');
                holidayIndicator.title = `${holiday.name} - Has schedule conflicts that need resolution`;
            } else {
                holidayIndicator.classList.add('bg-yellow-500');
                holidayIndicator.title = holiday.name;
            }
            
            // Add holiday info to the cell tooltip
            cell.setAttribute('title', holiday.name);
        }
    });
}

// Call fetchHolidays when page loads
document.addEventListener('DOMContentLoaded', function() {
    // Existing initialization code...
    
    // Fetch holidays
    fetchHolidays();
});

// Make fetchHolidays available globally
window.fetchHolidays = fetchHolidays;
</script>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/1.8.1/flowbite.min.js"></script>
    <script>
        // Initialize everything when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM content loaded - main footer initialization');
            
            // Initialize tabs - now handled by our custom script only once
            // No need to initialize multiple times which could cause event duplication
            
            // Add event listeners for selectors only - the tab buttons are handled by initTabs()
            const roomSelect = document.getElementById('roomSelect');
            if (roomSelect) {
                roomSelect.addEventListener('change', function() {
                    console.log('Room select changed');
                    if (typeof initRoomCalendar === 'function') {
                    initRoomCalendar();
                    }
                });
            }
            
            const trainerSelect = document.getElementById('trainerSelect');
            if (trainerSelect) {
                trainerSelect.addEventListener('change', function() {
                    console.log('Trainer select changed');
                    if (typeof initTrainerCalendar === 'function') {
                    initTrainerCalendar();
                    }
                });
            }
            
            const equipmentSelect = document.getElementById('equipmentSelect');
            if (equipmentSelect) {
                equipmentSelect.addEventListener('change', function() {
                    console.log('Equipment select changed');
                    if (typeof initEquipmentCalendar === 'function') {
                    initEquipmentCalendar();
                    }
                });
            }
            
            // Add Holiday button click event
            const addHolidayBtn = document.getElementById('addHolidayBtn');
            if (addHolidayBtn) {
                addHolidayBtn.addEventListener('click', function() {
                    console.log('Add holiday button clicked');
                    // Open holiday popup
                    if (typeof openHolidayAddPopup === 'function') {
                        openHolidayAddPopup();
                    }
                });
            }
        });
    </script>
{% endblock %} 

<script>
// Add Holiday button click event handler
document.addEventListener('DOMContentLoaded', function() {
    const addHolidayBtn = document.getElementById('addHolidayBtn');
    if (addHolidayBtn) {
        addHolidayBtn.addEventListener('click', function() {
            // Open holiday popup
            if (typeof openHolidayAddPopup === 'function') {
                openHolidayAddPopup();
            }
        });
    }
    
    // Function to reload calendar after holiday changes
    window.reloadCalendar = function() {
        // Trigger calendar reload if calendar object exists
        if (window.calendar && typeof window.calendar.refetchEvents === 'function') {
            window.calendar.refetchEvents();
        } else {
            console.log("Calendar object not found, reloading page instead");
            window.location.reload();
        }
    };
});
</script>

<script>
// Fix for tab navigation
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOMContentLoaded: Initializing tabs');
    
    // Call initTabs and get the initialization function
    const initializeTabCalendar = initTabs();
    
    // Initialize main calendar by default
    if (typeof initMainCalendar === 'function') {
        console.log('DOMContentLoaded: Initializing main calendar by default');
        initMainCalendar();
    }
    
    // Main tab should be active by default
    const main_tab = document.getElementById('main-tab');
    if (main_tab && main_tab.getAttribute('aria-selected') !== 'true') {
        console.log('Setting main tab as active');
        main_tab.click();
    }
    
    // Set up direct event handlers for room, trainer, and equipment tabs
    // This ensures the first click always works properly
    const rooms_tab = document.getElementById('rooms-tab');
    const trainers_tab = document.getElementById('trainers-tab');
    const equipment_tab = document.getElementById('equipment-tab');
    
    // Set up direct initialization on first click
    if (rooms_tab) {
        rooms_tab.addEventListener('click', function onFirstClick() {
            console.log('First click on rooms tab - direct handler');
            if (typeof initRoomCalendar === 'function') {
                initRoomCalendar();
            }
            // Only need this for the first click
            rooms_tab.removeEventListener('click', onFirstClick);
        }, { once: true });
    }
    
    if (trainers_tab) {
        trainers_tab.addEventListener('click', function onFirstClick() {
            console.log('First click on trainers tab - direct handler');
            if (typeof initTrainerCalendar === 'function') {
                initTrainerCalendar();
            }
            // Only need this for the first click
            trainers_tab.removeEventListener('click', onFirstClick);
        }, { once: true });
    }
    
    if (equipment_tab) {
        equipment_tab.addEventListener('click', function onFirstClick() {
            console.log('First click on equipment tab - direct handler');
            if (typeof initEquipmentCalendar === 'function') {
                initEquipmentCalendar();
            }
            // Only need this for the first click
            equipment_tab.removeEventListener('click', onFirstClick);
        }, { once: true });
    }
    
    // Function to reload calendar after holiday changes
    window.reloadCalendar = function() {
        // Trigger calendar reload if calendar object exists
        if (window.calendar && typeof window.calendar.refetchEvents === 'function') {
            window.calendar.refetchEvents();
        } else {
            console.log("Calendar object not found, reloading page instead");
            window.location.reload();
        }
    };
});
</script>
</body>
</html>