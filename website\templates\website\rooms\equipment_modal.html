{% load i18n %}

<!-- Equipment Modal -->
<div id="equipmentModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 hidden">
    <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] flex flex-col">
        <!-- Modal Header -->
        <div class="flex items-center justify-between p-4 border-b border-white/10 flex-shrink-0">
            <h3 id="equipmentModalTitle" class="text-lg font-medium text-white">{% trans "Add Equipment" %}</h3>
            <button onclick="closeEquipmentModal()" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>

        <!-- Scrollable Form Section -->
        <div class="overflow-y-auto flex-grow p-4">
            <form id="equipmentForm" class="space-y-3">
                <!-- Code -->
                <div>
                    <label for="code" class="block text-sm font-medium text-white/70 mb-1">{% trans "Equipment Code" %} *</label>
                    <div class="relative">
                        <input type="text" name="code" id="code" required pattern="[A-Za-z0-9\-]+" maxlength="50"
                            class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"
                            onchange="checkEquipmentCode(this.value)">
                        <div id="codeValidation" class="hidden absolute right-0 top-0 h-full flex items-center pr-3">
                            <svg class="h-5 w-5 text-red-500" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                    </div>
                    <p id="codeError" class="mt-1 text-sm text-red-500 hidden">{% trans "This code is already in use" %}</p>
                </div>

                <!-- Name -->
                <div>
                    <label for="name" class="block text-sm font-medium text-white/70 mb-1">{% trans "Name" %} *</label>
                    <input type="text" name="name" id="name" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                </div>

                <!-- Category -->
                <div>
                    <label for="category" class="block text-sm font-medium text-white/70 mb-1">{% trans "Category" %} *</label>
                    <select name="category" id="category" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="">{% trans "Select Category" %}</option>
                        {% for category in categories %}
                            <option value="{{ category.category_id }}">{{ category.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Brand -->
                <div>
                    <label for="brand" class="block text-sm font-medium text-white/70 mb-1">{% trans "Brand" %} *</label>
                    <select name="brand" id="brand" required onchange="loadModels(this.value)"
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="">{% trans "Select Brand" %}</option>
                        {% for brand in brands %}
                            <option value="{{ brand.brand_id }}">{{ brand.name }}</option>
                        {% endfor %}
                    </select>
                </div>

                <!-- Model -->
                <div>
                    <label for="model" class="block text-sm font-medium text-white/70 mb-1">{% trans "Model" %} *</label>
                    <select name="model" id="model" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="">{% trans "Select Model" %}</option>
                    </select>
                </div>

                <!-- Stock Quantity -->
                <div>
                    <label for="stock" class="block text-sm font-medium text-white/70 mb-1">{% trans "Stock Quantity" %}</label>
                    <p class="text-sm text-white/70" id="modelStock">{% trans "Available stock will be shown when a model is selected" %}</p>
                </div>

                <!-- Status -->
                <div>
                    <label for="status" class="block text-sm font-medium text-white/70 mb-1">{% trans "Status" %} *</label>
                    <select name="status" id="status" required
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm">
                        <option value="READY" selected>{% trans "Ready" %}</option>
                        <option value="AVAILABLE">{% trans "Available" %}</option>
                        <option value="BUSY">{% trans "Busy" %}</option>
                        <option value="BOOKED">{% trans "Booked" %}</option>
                        <option value="FIXED">{% trans "Fixed in Room" %}</option>
                        <option value="MAINTENANCE">{% trans "Maintenance" %}</option>
                        <option value="SCRAP">{% trans "Scrap" %}</option>
                    </select>
                </div>

                <!-- Description -->
                <div>
                    <label for="description" class="block text-sm font-medium text-white/70 mb-1">{% trans "Description" %}</label>
                    <textarea name="description" id="description" rows="2"
                        class="w-full bg-white/5 border border-white/10 rounded-md py-1.5 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent text-sm"></textarea>
                </div>
            </form>
        </div>

        <!-- Modal Footer -->
        <div class="flex items-center justify-end p-4 border-t border-white/10 space-x-3 flex-shrink-0">
            <button onclick="closeEquipmentModal()" 
                class="px-3 py-1.5 text-sm font-medium text-white/70 hover:text-white bg-white/5 hover:bg-white/10 rounded-md transition-colors">
                {% trans "Cancel" %}
            </button>
            <button onclick="saveEquipment()" 
                class="px-3 py-1.5 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors">
                {% trans "Save Equipment" %}
            </button>
        </div>
    </div>
</div>

<script>
    let isEditingEquipment = false;
    let currentEquipmentId = null;
    let isCodeValid = true;

    function openEquipmentModal(editing = false, equipmentId = null) {
        isEditingEquipment = editing;
        currentEquipmentId = equipmentId;
        document.getElementById('equipmentModal').classList.remove('hidden');
        
        if (editing) {
            document.getElementById('equipmentModalTitle').textContent = '{% trans "Edit Equipment" %}';
            // Load equipment data
            loadEquipmentData(equipmentId);
        } else {
            document.getElementById('equipmentModalTitle').textContent = '{% trans "Add Equipment" %}';
            document.getElementById('equipmentForm').reset();
        }
    }

    async function loadEquipmentData(equipmentId) {
        try {
            const response = await fetch(`{% url 'website:equipment_detail' '0' %}`.replace('0', equipmentId));
            const data = await response.json();
            
            if (data.status !== 'success') {
                throw new Error(data.message || '{% trans "Failed to load equipment details" %}');
            }

            const form = document.getElementById('equipmentForm');
            form.querySelector('[name="code"]').value = data.equipment.code;
            form.querySelector('[name="name"]').value = data.equipment.name;
            form.querySelector('[name="category"]').value = data.equipment.category;
            form.querySelector('[name="brand"]').value = data.equipment.brand;
            form.querySelector('[name="status"]').value = data.equipment.status;
            
            // Load models for the selected brand and then set the model
            await loadModels(data.equipment.brand);
            form.querySelector('[name="model"]').value = data.equipment.model;
            
            form.querySelector('[name="description"]').value = data.equipment.description || '';

            // Store original code for comparison
            window.originalEquipmentCode = data.equipment.code;
        } catch (error) {
            console.error('Error:', error);
            alert('{% trans "An error occurred while fetching equipment details" %}');
        }
    }

    function closeEquipmentModal() {
        document.getElementById('equipmentModal').classList.add('hidden');
        document.getElementById('equipmentForm').reset();
        isEditingEquipment = false;
        currentEquipmentId = null;
        isCodeValid = true;
        hideCodeValidation();
    }

    async function checkEquipmentCode(code) {
        if (!code) {
            hideCodeValidation();
            return;
        }

        // If editing and code hasn't changed, consider it valid
        if (isEditingEquipment && code === window.originalEquipmentCode) {
            isCodeValid = true;
            hideCodeValidation();
            return;
        }

        try {
            const response = await fetch(`{% url 'website:check_equipment_code' %}?code=${encodeURIComponent(code)}`, {
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();
            
            if (data.status === 'success') {
                isCodeValid = !data.exists;
                if (data.exists) {
                    showCodeValidation(false);
                } else {
                    hideCodeValidation();
                }
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error:', error);
            isCodeValid = false;
            showCodeValidation(false);
        }
    }

    function showCodeValidation(isValid) {
        const validationIcon = document.getElementById('codeValidation');
        const errorMessage = document.getElementById('codeError');
        
        validationIcon.classList.remove('hidden');
        if (!isValid) {
            errorMessage.classList.remove('hidden');
        } else {
            errorMessage.classList.add('hidden');
        }
    }

    function hideCodeValidation() {
        document.getElementById('codeValidation').classList.add('hidden');
        document.getElementById('codeError').classList.add('hidden');
    }

    async function saveEquipment() {
        const form = document.getElementById('equipmentForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const newCode = form.querySelector('[name="code"]').value;

        // Check code uniqueness only if it's a new equipment or if code has changed
        if ((!isEditingEquipment || newCode !== window.originalEquipmentCode) && !isCodeValid) {
            alert('{% trans "Please enter a unique equipment code" %}');
            return;
        }

        const formData = {
            name: form.querySelector('[name="name"]').value,
            category: form.querySelector('[name="category"]').value,
            brand: form.querySelector('[name="brand"]').value,
            model: form.querySelector('[name="model"]').value,
            status: form.querySelector('[name="status"]').value,
            description: form.querySelector('[name="description"]').value,
            code: newCode
        };

        try {
            const url = isEditingEquipment 
                ? `{% url 'website:equipment_detail' '0' %}`.replace('0', currentEquipmentId)
                : '{% url "website:create_equipment" %}';
            
            const response = await fetch(url, {
                method: isEditingEquipment ? 'PUT' : 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(formData)
            });

            const data = await response.json();
            if (response.ok && data.status === 'success') {
                window.location.reload();
            } else {
                throw new Error(data.message || '{% trans "Failed to save equipment" %}');
            }
        } catch (error) {
            console.error('Error:', error);
            alert(error.message || '{% trans "An error occurred while saving the equipment" %}');
        }
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    async function loadModels(brandId) {
        if (!brandId) {
            resetModelDropdown();
            return;
        }

        try {
            const languagePrefix = window.location.pathname.split('/')[1] || 'en';
            const response = await fetch(`/${languagePrefix}/api/models/?brand=${brandId}`, {
                headers: {
                    'Accept': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                credentials: 'include'
            });

            const data = await response.json();
            if (data.status === 'success') {
                updateModelDropdown(data.models);
            } else {
                throw new Error(data.message || '{% trans "Failed to load models" %}');
            }
        } catch (error) {
            console.error('Error loading models:', error);
            alert('{% trans "An error occurred while loading models" %}');
        }
    }

    function updateModelDropdown(models) {
        const modelSelect = document.getElementById('model');
        const modelStockElement = document.getElementById('modelStock');
        modelSelect.innerHTML = `<option value="">{% trans "Select Model" %}</option>`;
        
        models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.id;
            option.textContent = `${model.name} (Stock: ${model.stock})`;
            option.dataset.stock = model.stock;
            modelSelect.appendChild(option);
        });

        // Update stock display when model changes
        modelSelect.onchange = function() {
            const selectedOption = modelSelect.options[modelSelect.selectedIndex];
            if (selectedOption.value) {
                const stock = selectedOption.dataset.stock;
                modelStockElement.textContent = `{% trans "Available Stock" %}: ${stock}`;
            } else {
                modelStockElement.textContent = '{% trans "Available stock will be shown when a model is selected" %}';
            }
        };
    }

    function resetModelDropdown() {
        const modelSelect = document.getElementById('model');
        const modelStockElement = document.getElementById('modelStock');
        modelSelect.innerHTML = `<option value="">{% trans "Select Model" %}</option>`;
        modelStockElement.textContent = '{% trans "Available stock will be shown when a model is selected" %}';
    }
</script> 