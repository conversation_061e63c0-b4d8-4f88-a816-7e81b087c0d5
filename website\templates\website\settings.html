{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-900 to-gray-900">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10">
            <div class="p-8">
                <h1 class="text-2xl font-bold text-white mb-8">{% trans "Settings" %}</h1>

                {% if messages %}
                <div id="messages-container" class="fixed top-6 right-4 z-50 w-auto max-w-lg">
                    {% for message in messages %}
                    <div class="message-popup rounded-md border mb-4 flex items-center justify-between shadow-lg p-3
                        {% if 'error' in message.tags %}
                            bg-red-500/10 border-red-500/30 text-red-400
                        {% elif 'warning' in message.tags %}
                            bg-amber-500/10 border-amber-500/30 text-amber-400
                        {% elif 'success' in message.tags %}
                            bg-green-500/10 border-green-500/30 text-green-400
                        {% else %}
                            bg-blue-500/10 border-blue-500/30 text-blue-400
                        {% endif %}">
                        <span>{{ message }}</span>
                        <button class="text-inherit ml-4 text-xl leading-none" onclick="this.parentElement.remove()">&times;</button>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Settings Navigation -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <div class="md:col-span-1">
                        <nav class="space-y-1">
                            <button class="w-full flex items-center px-4 py-2 text-sm font-medium text-white bg-white/10 rounded-md" data-tab="profile">
                                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                </svg>
                                {% trans "Profile Settings" %}
                            </button>
                            <button class="w-full flex items-center px-4 py-2 text-sm font-medium text-white/70 hover:text-white hover:bg-white/10 rounded-md" data-tab="account">
                                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                                </svg>
                                {% trans "Account Security" %}
                            </button>
                            {% comment %} <button class="w-full flex items-center px-4 py-2 text-sm font-medium text-white/70 hover:text-white hover:bg-white/10 rounded-md" data-tab="notifications">
                                <svg class="mr-3 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"/>
                                </svg>
                                {% trans "Notifications" %}
                            </button> {% endcomment %}
                        </nav>
                    </div>

                    <div class="md:col-span-3">
                        <!-- Profile Settings Tab -->
                        <div class="tab-content" id="profile">
                            <form method="post" class="space-y-6">
                                {% csrf_token %}
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Full Name" %}</label>
                                    <input type="text" name="full_name" value="{{ request.user.get_full_name }}" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Email" %}</label>
                                    <input type="email" name="email" value="{{ request.user.email }}" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Language" %}</label>
                                    <select name="language" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                        {% for lang in LANGUAGES %}
                                            <option value="{{ lang.0 }}" {% if LANGUAGE_CODE == lang.0 %}selected{% endif %}>{{ lang.1 }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div>
                                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        {% trans "Save Changes" %}
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Account Security Tab (hidden by default) -->
                        <div class="tab-content hidden" id="account">
                            <form method="post" class="space-y-6">
                                {% csrf_token %}
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Current Password" %}</label>
                                    <input type="password" name="current_password" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "New Password" %}</label>
                                    <input type="password" name="new_password" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-white/70">{% trans "Confirm New Password" %}</label>
                                    <input type="password" name="confirm_password" class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        {% trans "Update Password" %}
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- Notifications Tab (hidden by default) -->
                        <div class="tab-content hidden" id="notifications">
                            <form method="post" class="space-y-6">
                                {% csrf_token %}
                                <div class="space-y-4">
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-white">{% trans "Email Notifications" %}</label>
                                            <p class="text-sm text-white/70">{% trans "Receive email notifications about your courses and updates" %}</p>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" name="email_notifications" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                    <div class="flex items-center justify-between">
                                        <div>
                                            <label class="text-sm font-medium text-white">{% trans "Course Updates" %}</label>
                                            <p class="text-sm text-white/70">{% trans "Get notified about new course content and updates" %}</p>
                                        </div>
                                        <label class="switch">
                                            <input type="checkbox" name="course_updates" checked>
                                            <span class="slider round"></span>
                                        </label>
                                    </div>
                                </div>
                                <div>
                                    <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                        {% trans "Save Preferences" %}
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* Toggle Switch Styles */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.1);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--color-primary);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('[data-tab]');
    const contents = document.querySelectorAll('.tab-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', () => {
            // Remove active classes
            tabs.forEach(t => t.classList.remove('bg-white/10', 'text-white'));
            tabs.forEach(t => t.classList.add('text-white/70'));
            contents.forEach(c => c.classList.add('hidden'));

            // Add active classes
            tab.classList.remove('text-white/70');
            tab.classList.add('bg-white/10', 'text-white');
            document.getElementById(tab.dataset.tab).classList.remove('hidden');
        });
    });

    // Auto-hide messages with fade-out effect
    const messages = document.querySelectorAll('.message-popup');
    messages.forEach(message => {
        let timeout = 5000; // Default timeout
        if (message.classList.contains('text-green-400')) { // success
            timeout = 5000;
        } else if (message.classList.contains('text-amber-400')) { // warning
            timeout = 7000;
        } else if (message.classList.contains('text-red-400')) { // error
            timeout = 10000;
        }
        
        setTimeout(() => {
            message.style.transition = 'opacity 0.5s ease';
            message.style.opacity = '0';
            setTimeout(() => {
                if (message.parentNode) {
                    message.remove();
                }
            }, 500); // Wait for transition to finish
        }, timeout);
    });
});
</script>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 