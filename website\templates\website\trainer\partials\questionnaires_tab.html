{% load i18n %}

<div class="mb-4 flex justify-between items-center">
    <h4 class="font-semibold text-lg text-white flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        {% trans "Questionnaires" %}
    </h4>
    
    <a href="{% url 'website:add_questionnaire' course_id=session.course.course_id %}" class="inline-flex items-center px-3 py-1.5 border border-blue-700 rounded-md bg-blue-800 hover:bg-blue-700 text-white transition-colors duration-150">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
        </svg>
        {% trans "Add Questionnaire" %}
    </a>
</div>

{% if pending_responses %}
<div class="mb-6">
    <h5 class="font-semibold text-md text-white mb-3 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        {% trans "Questionnaire Responses Pending Review" %}
    </h5>
    
    <div class="overflow-x-auto rounded-lg border border-yellow-700/50 bg-yellow-900/20">
        <table class="min-w-full divide-y divide-yellow-700/40">
            <thead class="bg-yellow-900/40">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-yellow-300 uppercase tracking-wider">
                        {% trans "User" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-yellow-300 uppercase tracking-wider">
                        {% trans "Questionnaire" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-yellow-300 uppercase tracking-wider">
                        {% trans "Submitted" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-yellow-300 uppercase tracking-wider">
                        {% trans "Score" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-yellow-300 uppercase tracking-wider">
                        {% trans "Actions" %}
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-yellow-700/30 bg-yellow-900/10">
                {% for response in pending_responses %}
                    <tr class="hover:bg-yellow-900/30 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                            {{ response.user.get_full_name }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{ response.questionnaire.title }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{ response.created_at|date:"d M Y, H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{ response.total_score }} / {{ response.max_total_score }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-right">
                            <a href="{% url 'website:review_questionnaire_response' response_id=response.response_id %}" class="text-yellow-400 hover:text-yellow-300 inline-flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {% trans "Review" %}
                            </a>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
{% endif %}

{% if questionnaires %}
    <div class="overflow-x-auto rounded-lg border border-gray-700">
        <table class="min-w-full divide-y divide-gray-700">
            <thead class="bg-gray-700/50">
                <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        {% trans "Title" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        {% trans "Session" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        {% trans "Questions" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                        {% trans "Total Score" %}
                    </th>
                    <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider">
                        {% trans "Actions" %}
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-700 bg-gray-800">
                {% for questionnaire in questionnaires %}
                    <tr class="hover:bg-gray-700 transition-colors duration-150">
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                            {{ questionnaire.title }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {% trans "Session" %} {{ questionnaire.session_number }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{ questionnaire.questions|length }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                            {{ questionnaire.total_score }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-right">
                            <div class="flex justify-end space-x-2">
                                <a href="{% url 'website:view_questionnaire' course_id=session.course.course_id questionnaire_id=questionnaire.questionnaire_id %}" class="text-blue-400 hover:text-blue-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                    </svg>
                                </a>
                                <a href="{% url 'website:edit_questionnaire' course_id=session.course.course_id questionnaire_id=questionnaire.questionnaire_id %}" class="text-yellow-400 hover:text-yellow-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </a>
                            </div>
                        </td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
{% else %}
    <div class="text-center py-10 bg-gray-800/50 rounded-lg border border-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
        <h3 class="text-xl font-medium text-white mb-2">{% trans "No questionnaires found" %}</h3>
        <p class="text-gray-400 max-w-md mx-auto mb-6">{% trans "There are no questionnaires created for this course yet." %}</p>
        
        <a href="{% url 'website:add_questionnaire' course_id=session.course.course_id %}" class="inline-flex items-center px-4 py-2 border border-blue-700 rounded-md bg-blue-800 hover:bg-blue-700 text-white transition-colors duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            {% trans "Create Questionnaire" %}
        </a>
    </div>
{% endif %} 