{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Cancel Course" %} - {{ course.name_en }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- <PERSON> Header -->
        <div class="flex items-center space-x-3 mb-8">
            <a href="{% url 'website:course_detail' course.course_id %}" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-white">{% trans "Cancel Course" %} - {{ course.name_en }}</h1>
        </div>

        <!-- Cancel Form -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
            <form method="post" class="space-y-6">
                {% csrf_token %}
                
                <div>
                    <label for="reason" class="block text-sm font-medium text-white">
                        {% trans "Cancellation Reason" %}
                    </label>
                    <div class="mt-1">
                        <textarea
                            id="reason"
                            name="reason"
                            rows="4"
                            class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                            required
                        ></textarea>
                    </div>
                </div>

                <div class="flex items-center justify-end space-x-4">
                    <a href="{% url 'website:course_detail' course.course_id %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-white/10 hover:bg-white/20">
                        {% trans "Cancel" %}
                    </a>
                    <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                        {% trans "Confirm Cancellation" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 