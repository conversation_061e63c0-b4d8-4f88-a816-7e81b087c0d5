version: "3.3"

networks:
  frontend:
    ipam:
      config:
        - subnet: *********/24
  backend:
    ipam:
      config:
        - subnet: *********/24
volumes:
  postgres_data:
  static:
  media:

services:
  app:
    restart: always
    build: .
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - static:/static
      - media:/media
    container_name: Academy-LMS-App
    env_file: .env
    depends_on:
      - db
    ports:
      - "8111:8000"
    networks:
      - backend
      - frontend
  db:
    restart: always
    image: postgres:14
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - postgres_data:/var/lib/postgresql/data/
    container_name: Academy-LMS-DB
    env_file: .env
    ports:
      - "8110:5432"
    networks:
      - backend
  nginx:
    restart: always
    image: ramy7elmygb/pcapi:nginx
    volumes:
      - /etc/timezone:/etc/timezone:ro
      - /etc/localtime:/etc/localtime:ro
      - static:/static
      - media:/media
    container_name: Academy-LMS-Nginx
    env_file: .env
    ports:
      - "8109:443"
    depends_on:
      - app
    networks:
      - frontend
