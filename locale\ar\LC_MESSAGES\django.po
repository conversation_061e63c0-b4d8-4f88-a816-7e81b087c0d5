# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-02-23 10:30+0200\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: ar\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
#: .\project\settings.py:139
msgid "English"
msgstr ""

#: .\project\settings.py:140
msgid "Arabic"
msgstr ""

#: .\website\admin.py:15
msgid "Basic Information"
msgstr "المعلومات الأساسية"

#: .\website\admin.py:18 .\website\admin.py:42
#: .\website\templates\website\classes.html:168
#: .\website\templates\website\trainees.html:52
#: .\website\templates\website\trainees.html:82
msgid "Status"
msgstr "الحالة"

#: .\website\admin.py:31
msgid "Personal Information"
msgstr "المعلومات الشخصية"

#: .\website\admin.py:36
msgid "Corporate Information"
msgstr "معلومات الشركة"

#: .\website\admin.py:41
msgid "Internal Information"
msgstr "المعلومات الداخلية"

#: .\website\admin.py:43
msgid "Permissions"
msgstr "الصلاحيات"

#: .\website\admin.py:44
msgid "Important Dates"
msgstr "تواريخ مهمة"

#: .\website\models.py:8
msgid "Code"
msgstr "الرمز"

#: .\website\models.py:9
msgid "Name"
msgstr "الاسم"

#: .\website\models.py:10
msgid "Description"
msgstr "الوصف"

#: .\website\models.py:11
msgid "Is Active"
msgstr "نشط"

#: .\website\models.py:12
msgid "Created At"
msgstr "تاريخ الإنشاء"

#: .\website\models.py:13
msgid "Updated At"
msgstr "تاريخ التحديث"

#: .\website\models.py:19 .\website\models.py:46
msgid "User Type"
msgstr "نوع المستخدم"

#: .\website\models.py:20
msgid "User Types"
msgstr "أنواع المستخدمين"

#: .\website\models.py:25
msgid "The Email field must be set"
msgstr "يجب تعيين حقل البريد الإلكتروني"

#: .\website\models.py:27
msgid "The Username field must be set"
msgstr "يجب تعيين حقل اسم المستخدم"

#: .\website\models.py:42 .\website\templates\website\profile.html:35
#: .\website\templates\website\settings.html:49
msgid "Email"
msgstr "البريد الإلكتروني"

#: .\website\models.py:43 .\website\templates\website\profile.html:31
msgid "Username"
msgstr "اسم المستخدم"

#: .\website\models.py:52
msgid "Phone Number"
msgstr "رقم الهاتف"

#: .\website\models.py:53
msgid "Nationality"
msgstr "الجنسية"

#: .\website\models.py:54
msgid "Passport Number"
msgstr "رقم جواز السفر"

#: .\website\models.py:55
msgid "National ID"
msgstr "الرقم القومي"

#: .\website\models.py:56
msgid "Address"
msgstr "العنوان"

#: .\website\models.py:57
msgid "Date of Birth"
msgstr "تاريخ الميلاد"

#: .\website\models.py:60
msgid "Company Name"
msgstr "اسم الشركة"

#: .\website\models.py:61
msgid "Commercial Registration Number"
msgstr "رقم السجل التجاري"

#: .\website\models.py:62
msgid "Tax Registration Number"
msgstr "رقم التسجيل الضريبي"

#: .\website\models.py:63
msgid "Key Person Name"
msgstr "اسم الشخص المسؤول"

#: .\website\models.py:64
msgid "Key Person Phone"
msgstr "هاتف الشخص المسؤول"

#: .\website\models.py:65
msgid "Key Person Email"
msgstr "البريد الإلكتروني للشخص المسؤول"

#: .\website\models.py:68
msgid "Employee ID"
msgstr "رقم الموظف"

#: .\website\models.py:71
msgid "Is Profile Complete"
msgstr "اكتمال الملف الشخصي"

#: .\website\models.py:73
msgid "Account Status"
msgstr "حالة الحساب"

#: .\website\models.py:76 .\website\templates\website\classes.html:126
#: .\website\templates\website\classes.html:205
#: .\website\templates\website\trainees.html:55
#: .\website\templates\website\trainees.html:119
msgid "Active"
msgstr "نشط"

#: .\website\models.py:77 .\website\templates\website\trainees.html:56
msgid "Inactive"
msgstr "غير نشط"

#: .\website\models.py:78
msgid "Pending Approval"
msgstr "في انتظار الموافقة"

#: .\website\models.py:87
msgid "Groups"
msgstr "المجموعات"

#: .\website\models.py:88
msgid ""
"The groups this user belongs to. A user will get all permissions granted to "
"each of their groups."
msgstr "المجموعات التي ينتمي إليها هذا المستخدم. سيحصل المستخدم على جميع الصلاحيات الممنوحة لكل مجموعة."

#: .\website\models.py:94
msgid "User Permissions"
msgstr "صلاحيات المستخدم"

#: .\website\models.py:95
msgid "Specific permissions for this user."
msgstr "صلاحيات محددة لهذا المستخدم."

#: .\website\models.py:110
msgid "User"
msgstr "مستخدم"

#: .\website\models.py:111
msgid "Users"
msgstr "المستخدمون"

#: .\website\templates\website\calendar.html:5
#: .\website\templates\website\calendar.html:18
#: .\website\templates\website\header.html:33
#: .\website\templates\website\header.html:174
#: .\website\templates\website\main.html:73
msgid "Calendar"
msgstr "التقويم"

#: .\website\templates\website\calendar.html:25
msgid "Month"
msgstr ""

#: .\website\templates\website\calendar.html:31
msgid "Add Event"
msgstr ""

#: .\website\templates\website\calendar.html:55
msgid "Today"
msgstr ""

#: .\website\templates\website\calendar.html:60
msgid "Sun"
msgstr ""

#: .\website\templates\website\calendar.html:61
msgid "Mon"
msgstr ""

#: .\website\templates\website\calendar.html:62
msgid "Tue"
msgstr ""

#: .\website\templates\website\calendar.html:63
msgid "Wed"
msgstr ""

#: .\website\templates\website\calendar.html:64
msgid "Thu"
msgstr ""

#: .\website\templates\website\calendar.html:65
msgid "Fri"
msgstr ""

#: .\website\templates\website\calendar.html:66
msgid "Sat"
msgstr ""

#: .\website\templates\website\calendar.html:99
msgid "Upcoming Events"
msgstr ""

#: .\website\templates\website\calendar.html:125
msgid "My Calendars"
msgstr ""

#: .\website\templates\website\calendar.html:129
#: .\website\templates\website\courses.html:5
#: .\website\templates\website\courses.html:18
#: .\website\templates\website\header.html:27
#: .\website\templates\website\header.html:165
msgid "Courses"
msgstr "الدورات"

#: .\website\templates\website\calendar.html:133
msgid "Meetings"
msgstr ""

#: .\website\templates\website\calendar.html:137
msgid "Personal"
msgstr ""

#: .\website\templates\website\classes.html:5
#: .\website\templates\website\classes.html:18
msgid "Classes Management"
msgstr ""

#: .\website\templates\website\classes.html:24
msgid "Create Class"
msgstr ""

#: .\website\templates\website\classes.html:34
msgid "Search classes..."
msgstr ""

#: .\website\templates\website\classes.html:49
msgid "Course:"
msgstr ""

#: .\website\templates\website\classes.html:52
msgid "All Courses"
msgstr ""

#: .\website\templates\website\classes.html:81
#: .\website\templates\website\courses.html:97
msgid "Type:"
msgstr ""

#: .\website\templates\website\classes.html:84
#: .\website\templates\website\courses.html:100
msgid "All Types"
msgstr ""

#: .\website\templates\website\classes.html:89
#: .\website\templates\website\courses.html:105
msgid "Workshop"
msgstr ""

#: .\website\templates\website\classes.html:94
#: .\website\templates\website\courses.html:110
msgid "Room"
msgstr ""

#: .\website\templates\website\classes.html:99
#: .\website\templates\website\courses.html:115
msgid "Lab"
msgstr ""

#: .\website\templates\website\classes.html:104
#: .\website\templates\website\courses.html:120
msgid "Online"
msgstr ""

#: .\website\templates\website\classes.html:118
msgid "Status:"
msgstr ""

#: .\website\templates\website\classes.html:121
#: .\website\templates\website\trainees.html:54
msgid "All Status"
msgstr ""

#: .\website\templates\website\classes.html:131
msgid "Upcoming"
msgstr ""

#: .\website\templates\website\classes.html:136
msgid "Completed"
msgstr ""

#: .\website\templates\website\classes.html:153
msgid "Class Name"
msgstr ""

#: .\website\templates\website\classes.html:156
#: .\website\templates\website\courses.html:216
msgid "Course"
msgstr ""

#: .\website\templates\website\classes.html:159
#: .\website\templates\website\profile.html:44
msgid "Trainer"
msgstr ""

#: .\website\templates\website\classes.html:162
msgid "Schedule"
msgstr ""

#: .\website\templates\website\classes.html:165
msgid "Students"
msgstr ""

#: .\website\templates\website\classes.html:171
#: .\website\templates\website\courses.html:231
#: .\website\templates\website\trainees.html:85
msgid "Actions"
msgstr ""

#: .\website\templates\website\classes.html:238
#: .\website\templates\website\trainees.html:152
msgid "Showing"
msgstr ""

#: .\website\templates\website\classes.html:238
#: .\website\templates\website\trainees.html:152
msgid "to"
msgstr ""

#: .\website\templates\website\classes.html:238
#: .\website\templates\website\trainees.html:152
msgid "of"
msgstr ""

#: .\website\templates\website\classes.html:238
#: .\website\templates\website\trainees.html:152
msgid "results"
msgstr ""

#: .\website\templates\website\courses.html:23
msgid "Grid View"
msgstr ""

#: .\website\templates\website\courses.html:28
msgid "Table View"
msgstr ""

#: .\website\templates\website\courses.html:38
msgid "Create Course"
msgstr ""

#: .\website\templates\website\courses.html:50
msgid "Search courses..."
msgstr ""

#: .\website\templates\website\courses.html:65
msgid "Category:"
msgstr ""

#: .\website\templates\website\courses.html:68
msgid "All Categories"
msgstr ""

#: .\website\templates\website\courses.html:73
msgid "Programming"
msgstr ""

#: .\website\templates\website\courses.html:78
msgid "Design"
msgstr ""

#: .\website\templates\website\courses.html:83
msgid "Business"
msgstr ""

#: .\website\templates\website\courses.html:134
msgid "Level:"
msgstr ""

#: .\website\templates\website\courses.html:137
msgid "All Levels"
msgstr ""

#: .\website\templates\website\courses.html:142
msgid "Beginner"
msgstr ""

#: .\website\templates\website\courses.html:147
#: .\website\templates\website\courses.html:174
#: .\website\templates\website\courses.html:261
msgid "Intermediate"
msgstr ""

#: .\website\templates\website\courses.html:152
msgid "Advanced"
msgstr ""

#: .\website\templates\website\courses.html:178
#: .\website\templates\website\courses.html:246
msgid "Sample Course Title"
msgstr ""

#: .\website\templates\website\courses.html:179
msgid ""
"Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod "
"tempor incididunt ut labore."
msgstr ""

#: .\website\templates\website\courses.html:200
#: .\website\templates\website\courses.html:268
msgid "lessons"
msgstr ""

#: .\website\templates\website\courses.html:202
msgid "View Course"
msgstr ""

#: .\website\templates\website\courses.html:219
msgid "Instructor"
msgstr ""

#: .\website\templates\website\courses.html:222
msgid "Level"
msgstr ""

#: .\website\templates\website\courses.html:225
msgid "Duration"
msgstr ""

#: .\website\templates\website\courses.html:228
msgid "Lessons"
msgstr ""

#: .\website\templates\website\header.html:21
#: .\website\templates\website\header.html:156
msgid "Dashboard"
msgstr "لوحة التحكم"

#: .\website\templates\website\header.html:40
#: .\website\templates\website\header.html:184
#: .\website\templates\website\trainees.html:76
msgid "Classes"
msgstr "الفصول"

#: .\website\templates\website\header.html:46
#: .\website\templates\website\header.html:193
msgid "Trainers"
msgstr "المدربين"

#: .\website\templates\website\header.html:120
#: .\website\templates\website\header.html:226
msgid "Profile"
msgstr "الملف الشخصي"

#: .\website\templates\website\header.html:129
#: .\website\templates\website\header.html:233
#: .\website\templates\website\settings.html:12
msgid "Settings"
msgstr "الإعدادات"

#: .\website\templates\website\header.html:138
#: .\website\templates\website\header.html:239
msgid "Logout"
msgstr "تسجيل الخروج"

#: .\website\templates\website\main.html:12
msgid "Welcome"
msgstr "مرحباً بعودتك"

#: .\website\templates\website\main.html:15
msgid "System Administrator Dashboard"
msgstr ""

#: .\website\templates\website\main.html:17
msgid "Trainer Dashboard"
msgstr ""

#: .\website\templates\website\main.html:19
msgid "Corporate User Dashboard"
msgstr ""

#: .\website\templates\website\main.html:21
msgid "Student Dashboard"
msgstr ""

#: .\website\templates\website\main.html:29
msgid "Enrolled Courses"
msgstr ""

#: .\website\templates\website\main.html:33
msgid "Upcoming Sessions"
msgstr ""

#: .\website\templates\website\main.html:37
msgid "Certificates"
msgstr ""

#: .\website\templates\website\main.html:48
msgid "Upcoming Courses"
msgstr ""

#: .\website\templates\website\main.html:52
msgid "No upcoming courses"
msgstr ""

#: .\website\templates\website\main.html:59
msgid "Recent Activities"
msgstr ""

#: .\website\templates\website\main.html:63
msgid "No recent activities"
msgstr ""

#: .\website\templates\website\main.html:79
#: .\website\templates\website\settings.html:34
msgid "Notifications"
msgstr ""

#: .\website\templates\website\main.html:83
msgid "No new notifications"
msgstr ""

#: .\website\templates\website\profile.html:28
msgid "Profile Information"
msgstr ""

#: .\website\templates\website\profile.html:39
msgid "Role"
msgstr ""

#: .\website\templates\website\profile.html:42
msgid "Administrator"
msgstr ""

#: .\website\templates\website\profile.html:46
msgid "Corporate User"
msgstr ""

#: .\website\templates\website\profile.html:48
msgid "Student"
msgstr ""

#: .\website\templates\website\profile.html:56
msgid "Activity"
msgstr ""

#: .\website\templates\website\profile.html:59
msgid "No recent activity"
msgstr ""

#: .\website\templates\website\profile.html:67
msgid "Quick Actions"
msgstr ""

#: .\website\templates\website\profile.html:70
msgid "Edit Profile"
msgstr ""

#: .\website\templates\website\profile.html:73
msgid "View Certificates"
msgstr ""

#: .\website\templates\website\profile.html:76
msgid "My Courses"
msgstr ""

#: .\website\templates\website\settings.html:22
msgid "Profile Settings"
msgstr ""

#: .\website\templates\website\settings.html:28
msgid "Account Security"
msgstr ""

#: .\website\templates\website\settings.html:45
msgid "Full Name"
msgstr ""

#: .\website\templates\website\settings.html:53
msgid "Language"
msgstr ""

#: .\website\templates\website\settings.html:62
msgid "Save Changes"
msgstr ""

#: .\website\templates\website\settings.html:73
msgid "Current Password"
msgstr ""

#: .\website\templates\website\settings.html:77
msgid "New Password"
msgstr ""

#: .\website\templates\website\settings.html:81
msgid "Confirm New Password"
msgstr ""

#: .\website\templates\website\settings.html:86
msgid "Update Password"
msgstr ""

#: .\website\templates\website\settings.html:99
msgid "Email Notifications"
msgstr ""

#: .\website\templates\website\settings.html:100
msgid "Receive email notifications about your courses and updates"
msgstr ""

#: .\website\templates\website\settings.html:109
msgid "Course Updates"
msgstr ""

#: .\website\templates\website\settings.html:110
msgid "Get notified about new course content and updates"
msgstr ""

#: .\website\templates\website\settings.html:120
msgid "Save Preferences"
msgstr ""

#: .\website\templates\website\trainees.html:5
#: .\website\templates\website\trainees.html:18
msgid "Trainers Management"
msgstr "إدارة المدربين"

#: .\website\templates\website\trainees.html:24
msgid "Add Trainer"
msgstr "إضافة مدرب"

#: .\website\templates\website\trainees.html:32
msgid "Search"
msgstr "البحث"

#: .\website\templates\website\trainees.html:34
msgid "Search trainers..."
msgstr "البحث عن المدربين..."

#: .\website\templates\website\trainees.html:43
msgid "Class"
msgstr "الفصل"

#: .\website\templates\website\trainees.html:45
msgid "All Classes"
msgstr "جميع الفصول"

#: .\website\templates\website\trainees.html:57
msgid "Graduated"
msgstr "متخرج"

#: .\website\templates\website\trainees.html:70
msgid "Trainer"
msgstr "مدرب"

#: .\website\templates\website\trainees.html:73
msgid "Contact"
msgstr "تواصل"

#: .\website\templates\website\trainees.html:79
msgid "Progress"
msgstr "التقدم"

#: .\website\views.py:255
msgid "Profile updated successfully"
msgstr ""

#: .\website\views.py:264
msgid "Current password is incorrect"
msgstr ""

#: .\website\views.py:266
msgid "New passwords do not match"
msgstr ""

#: .\website\views.py:270
msgid "Password updated successfully"
msgstr ""

#: .\website\views.py:279
msgid "Notification preferences updated"
msgstr ""

msgid "Welcome Back"
msgstr "مرحباً بعودتك"

msgid "Sign in to your GB Academy account"
msgstr "تسجيل الدخول إلى حسابك في أكاديمية GB"

msgid "Email or Username"
msgstr "البريد الإلكتروني أو اسم المستخدم"

msgid "Password"
msgstr "كلمة المرور"

msgid "Remember me"
msgstr "تذكرني"

msgid "Forgot password?"
msgstr "نسيت كلمة المرور؟"

msgid "Sign in"
msgstr "تسجيل الدخول"

msgid "Don't have an account?"
msgstr "ليس لديك حساب؟"

msgid "Register here"
msgstr "سجل هنا"

msgid "BUSINESS"
msgstr "الأعمال"

msgid "AUTOMOTIVE"
msgstr "السيارات"

msgid "Contact Information"
msgstr "معلومات الاتصال"

msgid "Rooms"
msgstr "الغرف"

msgid "Rooms Management"
msgstr "إدارة الغرف"

msgid "Search rooms..."
msgstr "البحث عن الغرف..."

msgid "Available"
msgstr "متاح"

msgid "Booked"
msgstr "محجوز"

msgid "Maintenance"
msgstr "صيانة"

msgid "Create Room"
msgstr "إنشاء غرفة"

msgid "Room Name"
msgstr "اسم الغرفة"

msgid "Current Course"
msgstr "الدورة الحالية"

msgid "Specialization"
msgstr "التخصص"

msgid "All Specializations"
msgstr "جميع التخصصات"

msgid "On Leave"
msgstr "في إجازة"

msgid "Specializations"
msgstr "التخصصات"

msgid "Current Courses"
msgstr "الدورات الحالية"

msgid "Existing User"
msgstr "مستخدم موجود"

msgid "New User"
msgstr "مستخدم جديد"

msgid "Search Users"
msgstr "البحث عن المستخدمين"

msgid "Search by name or email..."
msgstr "البحث بالاسم أو البريد الإلكتروني..."

msgid "Make Trainer"
msgstr "تعيين كمدرب"

msgid "First Name"
msgstr "الاسم الأول"

msgid "Last Name"
msgstr "اسم العائلة"

msgid "Cancel"
msgstr "إلغاء"

msgid "Save"
msgstr "حفظ"

msgid "User successfully promoted to trainer."
msgstr "تمت ترقية المستخدم إلى مدرب بنجاح."

msgid "User not found."
msgstr "المستخدم غير موجود."

msgid "Trainer user type not found."
msgstr "نوع المستخدم (مدرب) غير موجود."

msgid "No phone number"
msgstr "لا يوجد رقم هاتف"

msgid "No specializations"
msgstr "لا توجد تخصصات"

msgid "No active courses"
msgstr "لا توجد دورات نشطة"

msgid "No trainers found"
msgstr "لم يتم العثور على مدربين"

msgid "Inactive"
msgstr "غير نشط"

msgid "Trainer updated successfully!"
msgstr "تم تحديث المدرب بنجاح!"

msgid "Trainer created successfully!"
msgstr "تم إنشاء المدرب بنجاح!"

msgid "Failed to create trainer"
msgstr "فشل في إنشاء المدرب"

msgid "An error occurred while creating the trainer"
msgstr "حدث خطأ أثناء إنشاء المدرب"

msgid "Please fill in all required fields"
msgstr "يرجى ملء جميع الحقول المطلوبة"

msgid "Room created successfully!"
msgstr "تم إنشاء الغرفة بنجاح!"

msgid "Failed to create room"
msgstr "فشل في إنشاء الغرفة"

msgid "An error occurred while creating the room"
msgstr "حدث خطأ أثناء إنشاء الغرفة"

msgid "Capacity"
msgstr "السعة"

msgid "Description"
msgstr "الوصف"

msgid "No active course"
msgstr "لا توجد دورة نشطة"

msgid "No rooms found"
msgstr "لم يتم العثور على غرف"

msgid "Room updated successfully!"
msgstr "تم تحديث الغرفة بنجاح!"

msgid "Room deactivated successfully!"
msgstr "تم إلغاء تنشيط الغرفة بنجاح!"

msgid "Failed to load room details"
msgstr "فشل في تحميل تفاصيل الغرفة"

msgid "An error occurred while loading the room"
msgstr "حدث خطأ أثناء تحميل الغرفة"

msgid "Failed to update room"
msgstr "فشل في تحديث الغرفة"

msgid "An error occurred while updating the room"
msgstr "حدث خطأ أثناء تحديث الغرفة"

msgid "Are you sure you want to deactivate this room?"
msgstr "هل أنت متأكد أنك تريد إلغاء تنشيط هذه الغرفة؟"

msgid "Failed to deactivate room"
msgstr "فشل في إلغاء تنشيط الغرفة"

msgid "An error occurred while deactivating the room"
msgstr "حدث خطأ أثناء إلغاء تنشيط الغرفة"

msgid "Edit Room"
msgstr "تعديل الغرفة"

msgid "Room deleted successfully!"
msgstr "تم حذف الغرفة بنجاح!"

msgid "Are you sure you want to delete this room?"
msgstr "هل أنت متأكد أنك تريد حذف هذه الغرفة؟"

msgid "Failed to delete room"
msgstr "فشل في حذف الغرفة"

msgid "An error occurred while deleting the room"
msgstr "حدث خطأ أثناء حذف الغرفة"
