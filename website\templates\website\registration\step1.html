{% extends "website/login_base.html" %}
{% load static %}

{% block content %}
<!-- Background Slideshow -->
<div class="fixed inset-0 z-0">
    <div class="slideshow-container">
        <div class="slide">
            <img src="{% static 'images/slides/slide1.png' %}" class="slide-image" alt="Background 1">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide2.png' %}" class="slide-image" alt="Background 2">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide3.png' %}" class="slide-image" alt="Background 3">
            <div class="overlay"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="min-h-screen flex items-center justify-center relative z-10">
    <div class="container px-4 py-4">
        <div class="max-w-5xl mx-auto">
            <div class="w-full space-y-3 bg-black/20 backdrop-blur-md p-6 rounded-lg shadow-xl border border-white/10">
                <div class="text-center">
                    <div class="bg-white inline-block p-4 rounded-lg mb-4">
                        <img class="mx-auto h-20 w-auto" src="{% static 'images/logo.png' %}" alt="GB Academy Logo">
                    </div>
                    <h2 class="text-3xl font-bold tracking-tight text-white mb-1">
                        Create Account
                    </h2>
                    <p class="text-base text-white/80">
                        Step 1 of <span id="total-steps">3</span>: Registration Type
                    </p>
                </div>

                <div id="timeline-container" class="transition-all duration-500 opacity-100">
                    {% include "website/registration/timeline.html" with current_step=1 show_corporate=initial_corporate|default:False %}
                </div>

                <form method="post" class="mt-6 space-y-4" id="registration-form">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="bg-red-500/20 backdrop-blur-md border border-red-500/30 text-white/90 px-4 py-3 rounded-md" role="alert">
                        <strong class="font-bold">Please fix the following errors:</strong>
                        <ul class="mt-2">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {% for radio in form.user_type %}
                        <label class="relative block cursor-pointer registration-type" for="{{ radio.id_for_label }}" data-type="{{ radio.data.value }}">
                            <input type="radio" name="{{ form.user_type.name }}" value="{{ radio.data.value }}"
                                   id="{{ radio.id_for_label }}"
                                   class="sr-only peer registration-radio"
                                   data-type="{{ radio.data.value }}"
                                   {% if radio.data.value == initial_type %}checked{% elif not initial_type and forloop.first %}checked{% endif %}>
                            <div class="flex flex-col items-center p-4 bg-white/5 backdrop-blur-md rounded-lg border border-white/10 
                                        hover:bg-white/10 transition-all duration-300
                                        peer-checked:bg-blue-500/20 peer-checked:border-blue-300/50 peer-checked:scale-[1.02]">
                                <div class="w-14 h-14 mb-3 rounded-full bg-white/10 flex items-center justify-center">
                                    {% if forloop.first %}
                                    <!-- Individual Icon -->
                                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                    {% else %}
                                    <!-- Corporate Icon -->
                                    <svg class="w-7 h-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" 
                                              d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    {% endif %}
                                </div>
                                <h3 class="text-lg font-semibold text-white mb-1">{{ radio.choice_label }}</h3>
                                <p class="text-sm text-white/70 text-center">
                                    {% if forloop.first %}
                                    Register your company to manage corporate training and employee development.
                                    {% else %}
                                    Register as an individual to access our courses and training programs.
                                    {% endif %}
                                </p>
                                <div class="mt-3 text-xs text-white/60">
                                    {% if forloop.first %}
                                    4 steps registration process
                                    {% else %}
                                    3 steps registration process
                                    {% endif %}
                                </div>
                            </div>
                        </label>
                        {% endfor %}
                    </div>
                    
                    <div class="pt-4 text-center max-w-2xl mx-auto">
                        <button type="submit"
                                class="w-full bg-white/20 hover:bg-white/30 text-white text-base rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-300 transform hover:scale-[1.02]">
                            Continue
                        </button>
                    </div>
                </form>

                <div class="mt-4 text-center">
                    <a href="{% url 'website:home' %}" class="text-sm text-white/80 hover:text-white transition-colors duration-300">
                        ← Back to Login
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Registration Type Selection Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const timelineContainer = document.getElementById('timeline-container');
    const totalStepsSpan = document.getElementById('total-steps');
    const radioButtons = document.querySelectorAll('.registration-radio');
    const registrationForm = document.getElementById('registration-form');
    const registrationTypes = document.querySelectorAll('.registration-type');

    function updateRegistrationView(isIndividual) {
        // Update total steps
        totalStepsSpan.textContent = isIndividual ? '3' : '4';

        // Store the selection in localStorage
        localStorage.setItem('registrationType', isIndividual ? 'individual' : 'corporate');

        // Add animation classes to registration type cards
        registrationTypes.forEach(type => {
            const isSelected = type.dataset.type === (isIndividual ? 'individual' : 'corporate');
            const card = type.querySelector('div');
            card.classList.toggle('scale-[1.02]', isSelected);
            card.classList.toggle('bg-blue-500/20', isSelected);
            card.classList.toggle('border-blue-300/50', isSelected);
        });

        // Start fade out animation
        timelineContainer.style.opacity = '0';
        timelineContainer.style.transform = 'translateY(-10px)';
        
        // Update timeline via AJAX
        fetch(`{% url 'website:registration_timeline' %}?show_corporate=${!isIndividual}&current_step=1`)
            .then(response => response.text())
            .then(html => {
                // Create a temporary container to hold the new content
                const tempContainer = document.createElement('div');
                tempContainer.innerHTML = html;
                
                // Wait for fade out to complete
                setTimeout(() => {
                    // Update the content
                    timelineContainer.innerHTML = tempContainer.firstElementChild.innerHTML;
                    
                    // Force a reflow
                    void timelineContainer.offsetHeight;
                    
                    // Start fade in animation
                    timelineContainer.style.opacity = '1';
                    timelineContainer.style.transform = 'translateY(0)';
                }, 300);
            })
            .catch(error => {
                console.error('Error updating timeline:', error);
                // Restore visibility on error
                timelineContainer.style.opacity = '1';
                timelineContainer.style.transform = 'translateY(0)';
            });
    }

    // Set up initial styles
    timelineContainer.style.transition = 'all 0.3s ease-in-out';
    timelineContainer.style.opacity = '1';
    timelineContainer.style.transform = 'translateY(0)';

    // Initialize the view based on the selected radio button
    function initializeView() {
        const selectedRadio = document.querySelector('.registration-radio:checked');
        if (selectedRadio) {
            const isIndividual = selectedRadio.dataset.type === 'individual';
            updateRegistrationView(isIndividual);
        } else {
            // Default to individual if no selection
            const firstRadio = document.querySelector('.registration-radio');
            if (firstRadio) {
                firstRadio.checked = true;
                updateRegistrationView(true);
            }
        }
    }

    // Initialize on page load with a slight delay to ensure proper rendering
    setTimeout(initializeView, 100);

    // Add change event listeners to radio buttons
    radioButtons.forEach(radio => {
        radio.addEventListener('change', function() {
            const isIndividual = this.dataset.type === 'individual';
            updateRegistrationView(isIndividual);
        });
    });

    // Clear localStorage when form is submitted
    registrationForm.addEventListener('submit', function() {
        localStorage.removeItem('registrationType');
    });
});
</script>
{% endblock %} 