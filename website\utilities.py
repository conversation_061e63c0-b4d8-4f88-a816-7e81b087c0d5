from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver

def update_category_stock(category):
    """Update a category's stock count based on equipment count."""
    from website.models import Equipment
    
    # Count all equipment in this category
    count = Equipment.objects.filter(category=category).count()
    
    # Update the category's stock count
    if category.stock != count:
        category.stock = count
        category.save(update_fields=['stock'])
    
    return count

def update_all_category_stocks():
    """Update stock counts for all categories."""
    from website.models import Category
    
    updated = 0
    for category in Category.objects.all():
        update_category_stock(category)
        updated += 1
    
    return updated

@receiver(pre_save, sender='website.Equipment')
def handle_equipment_category_change(sender, instance, **kwargs):
    """When equipment's category changes, update both old and new category stock counts."""
    if instance.pk:  # Only for existing equipment (not new ones)
        try:
            # Get the original equipment instance from the database
            original = sender.objects.get(pk=instance.pk)
            
            # If category changed, update the old category's stock count
            if original.category != instance.category:
                # Decrement old category's stock
                original.category.stock -= 1
                original.category.save(update_fields=['stock'])
                
                # Increment new category's stock
                instance.category.stock += 1
                instance.category.save(update_fields=['stock'])
        except sender.DoesNotExist:
            # This shouldn't happen, but if it does, we'll handle it in post_save
            pass

# Add signal handlers to automatically update category stock
@receiver(post_save, sender='website.Equipment')
def handle_equipment_save(sender, instance, created, **kwargs):
    """When equipment is saved, update its category's stock count."""
    if created:  # Only increment for new equipment
        category = instance.category
        category.stock += 1
        category.save(update_fields=['stock'])

@receiver(post_delete, sender='website.Equipment')
def handle_equipment_delete(sender, instance, **kwargs):
    """When equipment is deleted, update its category's stock count."""
    category = instance.category
    category.stock -= 1
    category.save(update_fields=['stock']) 