"""pcapiproj URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib.sitemaps.views import sitemap
from django.urls import path, include, re_path
from rest_framework import routers
from django.contrib.auth.views import LogoutView, LoginView
from django.utils.translation import gettext_lazy as _
from django.views.decorators.http import require_http_methods
from django.db import transaction
import logging

from website.sitemaps import *
from . import views, views_trainer, corporate_views, individual_views, dashboard_views, otp_views, GB_views

router = routers.DefaultRouter()

sitemaps = {
    "static": StaticViewSitemap,
    # "cars": CarSitemap
}

app_name = 'website'

urlpatterns = [
    path('i18n/', include('django.conf.urls.i18n')),
    # SiteMap
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),

    path('', views.home, name='home'),  # This will handle the root URL
    # path('login/', LoginView.as_view(template_name='website/login.html', redirect_field_name='next'), name='login'),
    path('dashboard-redirect/', corporate_views.redirect_to_dashboard, name='dashboard_redirect'),
    path('main/', views.main_view, name='main'),  # Main page after login
    path('logout/', LogoutView.as_view(next_page='website:home'), name='logout'),
    path('register/', views.register_step1, name='register_step1'),
    path('register/step2/', views.register_step2, name='register_step2'),
    path('register/step3/', views.register_step3, name='register_step3'),
    path('register/step4/', views.register_step4, name='register_step4'),
    path('register/timeline/', views.registration_timeline, name='registration_timeline'),
    path('profile/', views.profile_view, name='profile'),
    path('settings/', views.settings_view, name='settings'),
    path('force-password-reset/', corporate_views.force_password_reset, name='force_password_reset'),
    path('courses/', views.courses_view, name='courses'),
    path('calendar/', views.calendar_view, name='calendar'),
    path('trainer-calendar/', views.trainer_calendar_view, name='trainer_calendar'),
    path('my-calendar/', views.user_calendar_view, name='user_calendar'),
    path('calendar/events/', views.calendar_events_api, name='calendar_events_api'),
    path('calendar/resources/', views.calendar_resources_api, name='calendar_resources_api'),
    path('my-calendar/events/', views.user_calendar_events_api, name='user_calendar_events_api'),
    path('corporate-calendar/', views.corporate_admin_calendar, name='corporate_admin_calendar'),
    path('corporate-calendar/events/', views.corporate_calendar_events_api, name='corporate_calendar_events_api'),
    path('my-requests/', views.my_corporate_requests_view, name='my_corporate_requests'),
    path('api/my-requests/', views.my_corporate_requests_api, name='my_corporate_requests_api'),
    path('my-users/', views.my_corporate_users_view, name='my_corporate_users'),
    path('api/my-users/', views.corporate_users_api, name='corporate_users_api'),
    path('api/my-users/add/', views.add_corporate_user, name='add_corporate_user'),
    path('api/my-users/<int:user_id>/delete/', views.delete_corporate_user, name='delete_corporate_user'),
    path('my-reservations/', GB_views.my_reservations_redirect, name='my_reservations'),
    path('individual-reservations/', individual_views.user_reservations_view, name='user_reservations'),
    path('api/user-attendance/', views.user_attendance_api, name='user_attendance_api'),
    path('api/attendance/', views.attendance_api, name='attendance_api'),
    path('my-corporate-reservations/', corporate_views.my_corporate_reservations_view, name='my_corporate_reservations'),
    path('api/reservations/<str:reservation_id>/details/', corporate_views.reservation_details_api, name='reservation_details_api'),
    path('api/reservations/<str:reservation_id>/cancel/', corporate_views.cancel_reservation_api, name='cancel_reservation_api'),
    path('reservations/<int:reservation_id>/check-payment/', views.check_before_payment, name='check_before_payment'),
    path('reservations/<int:reservation_id>/confirm-payment/', views.confirm_payment, name='confirm_payment'),
    path('api/cancellation-deadline/', views.get_cancellation_deadline, name='get_cancellation_deadline'),
    path('emergency-cancellation-request/', views.emergency_cancellation_request, name='emergency_cancellation_request'),
    path('admin-reservations/', views.admin_reservations_view, name='admin_reservations'),
    path('admin-update-attendance/', views.admin_update_attendance, name='admin_update_attendance'),
    path('admin-cancellation-requests/', views.admin_cancellation_requests, name='admin_cancellation_requests'),
    path('api/emergency-cancellations/', views.emergency_cancellation_api, name='emergency_cancellation_api'),
    path('reservations/<int:reservation_id>/update/', views.update_reservation, name='update_reservation'),
    path('api/course-instances/<str:instance_id>/details/', views.course_instance_details, name='course_instance_details'),
    path('rooms/', views.rooms_view, name='rooms'),
    path('equipment/', views.equipment_view, name='equipment'),
    path('equipment/<int:equipment_id>/', views.equipment_detail, name='equipment_detail'),
    path('equipment/<int:equipment_id>/delete/', views.delete_equipment, name='delete_equipment'),
    path('equipment/create/', views.create_equipment, name='create_equipment'),
    path('trainers/', views.trainers_view, name='trainers'),
    path('trainers/<int:trainer_id>/', views.trainer_detail, name='trainer_detail'),
    path('trainers/<int:trainer_id>/edit/', views.edit_trainer, name='edit_trainer'),
    path('courses/assign/', views.assign_course_to_trainers, name='assign_course_to_trainers'),
    path('courses/create/', views.create_course, name='create_course'),
    path('courses/<str:course_id>/', views.course_detail, name='course_detail'),
    path('courses/<str:course_id>/edit/', views.edit_course, name='edit_course'),
    path('courses/<str:course_id>/cancel/', views.cancel_course, name='cancel_course'),
    path('courses/<str:course_id>/reactivate/', views.reactivate_course, name='reactivate_course'),
    path('courses/<str:course_id>/delete/', views.delete_course, name='delete_course'),
    path('courses/<str:course_id>/duplicate/', views.duplicate_course, name='duplicate_course'),
    path('courses/<str:course_id>/debug/', views.debug_course_data, name='debug_course_data'),
    # Course content URLs
    path('courses/<str:course_id>/contents/', views.course_content_list, name='course_content_list'),
    path('courses/<str:course_id>/contents/add/', views.add_course_content, name='add_course_content'),
    path('courses/<str:course_id>/contents/<int:content_id>/edit/', views.edit_course_content, name='edit_course_content'),
    path('courses/<str:course_id>/contents/<int:content_id>/delete/', views.delete_course_content, name='delete_course_content'),
    path('courses/<str:course_id>/contents/<int:content_id>/download/', views.download_course_content, name='download_course_content'),
    path('courses/<str:course_id>/questionnaire/add/', views_trainer.add_questionnaire, name='add_questionnaire'),
    # Questionnaire API endpoints
    path('api/courses/<str:course_id>/questionnaires/', views_trainer.get_course_questionnaires, name='get_course_questionnaires'),
    path('api/courses/<str:course_id>/questionnaires/<int:questionnaire_id>/view/', views_trainer.get_questionnaire_details, name='view_questionnaire_api'),
    path('courses/<str:course_id>/questionnaires/<int:questionnaire_id>/view/', views_trainer.view_questionnaire, name='view_questionnaire'),
    path('courses/<str:course_id>/questionnaires/<int:questionnaire_id>/edit/', views_trainer.edit_questionnaire, name='edit_questionnaire'),
    path('courses/<str:course_id>/questionnaires/<int:questionnaire_id>/delete/', views_trainer.delete_questionnaire, name='delete_questionnaire'),
    path('api/courses/<str:course_id>/questionnaires/<int:questionnaire_id>/delete/', views_trainer.delete_questionnaire_api, name='delete_questionnaire_api'),
    path('enroll/<int:instance_id>/', views.enroll_in_instance, name='enroll_in_instance'),
    path('api/users/', views.get_users, name='get_users'),
    path('api/promote-to-trainer/', views.promote_to_trainer, name='promote_to_trainer'),
    path('api/create-trainer/', views.create_trainer, name='create_trainer'),
    path('api/create-room/', views.create_room, name='create_room'),
    path('rooms/<int:room_id>/', views.room_detail, name='room_detail'),
    path('rooms/<int:room_id>/edit/', views.edit_room, name='edit_room'),
    path('rooms/<int:room_id>/update/', views.update_room, name='update_room'),
    path('rooms/<int:room_id>/deactivate/', views.deactivate_room, name='deactivate_room'),
    path('rooms/<int:room_id>/delete/', views.delete_room, name='delete_room'),
    path('api/rooms/<int:room_id>/available-equipment/', views.room_available_equipment, name='room_available_equipment'),
    path('sessions/', views.sessions_view, name='sessions'),
    path('api/create-session/', views.create_session, name='create_session'),
    path('api/create-event/', views.create_event, name='create_event'),
    path('sessions/check-availability/', views.check_availability, name='check_availability'),
    path('api/check-room-availability/', views.check_room_availability, name='check_room_availability'),
    path('api/check-equipment-availability/', views.check_equipment_availability_view, name='check_equipment_availability'),
    path('api/delete-trainer-availability/', views.delete_trainer_availability, name='delete_trainer_availability'),
    path('equipment/check-code/', views.check_equipment_code, name='check_equipment_code'),
    # API endpoints for categories, brands, and models
    path('api/categories/', views.category_api, name='category_list'),
    path('api/categories/<int:category_id>/', views.category_api, name='category_detail'),
    path('api/brands/', views.brand_api, name='brand_list'),
    path('api/brands/<int:brand_id>/', views.brand_api, name='brand_detail'),
    path('api/models/', views.model_api, name='model_list'),
    path('api/models/<int:model_id>/', views.model_api, name='model_detail'),
    # Event management URLs
    path('api/events/', views.create_event, name='create_event'),
    path('api/events/<uuid:event_id>/', views.create_event, name='event_detail'),
    # Holiday management URLs
    path('api/holidays/', views.add_holiday, name='add_holiday'),
    path('api/holidays/<int:holiday_id>/', views.add_holiday, name='update_holiday'),
    path('api/holidays/get/', views.get_holidays_api, name='get_holidays_api'),
    path('api/holidays/check-conflicts/', views.check_holiday_conflicts, name='check_holiday_conflicts'),
    
    # Rescheduling APIs for holiday conflicts
    path('api/reschedule-session/', views.reschedule_session_api, name='reschedule_session_api'),
    path('api/reschedule-event/', views.reschedule_event_api, name='reschedule_event_api'),
    
    # New API endpoints for rescheduling
    path('api/available-rooms/', views.available_rooms_api, name='available_rooms_api'),
    path('api/suggested-dates/', views.suggested_dates_api, name='suggested_dates_api'),
    path('api/available-trainers/', views.available_trainers_api, name='available_trainers_api'),
    path('api/available-equipment/', views.available_equipment_api, name='available_equipment_api'),
    path('api/get-corporates/', views.get_corporates_api, name='get_corporates_api'),
    # Corporate admin management URLs
    path('corporate-admins/', corporate_views.corporate_admins_view, name='corporate_admins_view'),
    path('corporate-dashboard/', dashboard_views.corporate_admin_dashboard, name='corporate_admin_dashboard'),
    path('corporate-reservations/', corporate_views.corporate_reservations_view, name='corporate_reservations'),
    path('corporate-reservations/<int:corporate_id>/', corporate_views.corporate_reservations_view, name='corporate_specific_reservations'),
    path('process-corporate-form/', corporate_views.process_corporate_form, name='process_corporate_form'),
    path('update-corporate-form/', corporate_views.update_corporate_form, name='update_corporate_form'),
    path('api/corporates/', corporate_views.list_corporates, name='list_corporates'),
    path('api/corporates/<int:corporate_id>/', corporate_views.corporate_detail, name='corporate_detail'),
    path('api/corporates/create/', corporate_views.create_corporate, name='create_corporate'),
    path('api/corporates/<int:corporate_id>/update/', corporate_views.update_corporate, name='update_corporate'),
    path('api/corporates/<int:corporate_id>/delete/', corporate_views.delete_corporate, name='delete_corporate'),
    path('api/corporates/<int:corporate_id>/users/', corporate_views.get_corporate_users, name='get_corporate_users'),
    # Add new corporate course instances API
    path('api/corporate-course-instances/', corporate_views.get_corporate_course_instances, name='get_corporate_course_instances'),
    # Corporate course request URLs
    path('course-request/', corporate_views.course_request_form, name='course_request_form'),
    path('course-request/<str:course_id>/', corporate_views.course_request_form, name='course_request_form'),
    path('course-requests/', corporate_views.course_requests_list, name='course_requests_list'),
    path('course-request/<int:request_id>/update-status/', corporate_views.update_course_request_status, name='update_course_request_status'),
    path('new-course-request/<int:request_id>/update-status/', corporate_views.update_new_course_request_status, name='update_new_course_request_status'),
    path('api/course-requests/', corporate_views.course_requests_api, name='course_requests_api'),
    # Course Requests for Corporate Admins (Form & List)
    path('request-course/', views.course_request_form, name='course_request_form'),
    path('request-course/<int:course_id>/', views.course_request_form, name='course_request_form_with_id'),
    # Add other website URLs here
    
    # Corporate user reservations
    path('corporate-user-reservations/', corporate_views.corporate_user_reservations_view, name='corporate_user_reservations'),
    
    # Corporate cancellation request API endpoints
    path('api/corporate-cancel-request/', corporate_views.request_cancellation_api, name='request_cancellation_api'),
    path('api/corporate-cancel-requests/', corporate_views.get_cancellation_requests_api, name='get_cancellation_requests_api'),
    path('api/corporate-cancel-requests/count/', corporate_views.get_cancellation_requests_count_api, name='get_cancellation_requests_count_api'),
    path('api/corporate-cancel-requests/process/', corporate_views.process_cancellation_request_api, name='process_cancellation_request_api'),
    
    path('api/corporate-user-reservations/', corporate_views.corporate_user_reservations_api, name='corporate_user_reservations_api'),
    path('api/corporate-user-attendance/', corporate_views.corporate_user_attendance_api, name='corporate_user_attendance_api'),
    path('api/admin-attendance/', corporate_views.admin_attendance_api, name='admin_attendance_api'),
    
    # OTP authentication endpoints moved to main URLs (without i18n prefix)
    
    # API endpoint for cancelling course requests
    path('api/course-request/cancel/', corporate_views.cancel_course_request_api, name='cancel_course_request_api'),
    
    # API endpoint for requesting cancellation of approved course requests
    path('api/course-request/request-cancellation/', corporate_views.request_cancel_approved_api, name='request_cancel_approved_api'),

    # Corporate user calendar
    path('corporate-user-calendar/', corporate_views.corporate_user_calendar_view, name='corporate_user_calendar'),
    path('corporate-user-calendar/events/', corporate_views.corporate_user_calendar_events_api, name='corporate_user_calendar_events_api'),

    # Corporate user specific views
    path('my-corporate-calendar/', corporate_views.corporate_user_calendar_view, name='corporate_user_calendar_view'),
    path('my-corporate-calendar/events/', corporate_views.corporate_user_calendar_events_api, name='corporate_user_calendar_events_api'),
    path('my-corporate-courses/', corporate_views.corporate_user_courses_view, name='corporate_user_courses_view'),
    path('api/my-corporate-courses/', corporate_views.corporate_user_courses_api, name='corporate_user_courses_api'),
    
    # Trainer specific views
    path('trainer/sessions/', views_trainer.trainer_sessions_view, name='trainer_sessions_view'),
    path('trainer/sessions/<str:session_id>/attendance/', views_trainer.trainer_session_attendance, name='trainer_session_attendance'),
    path('trainer/sessions/<str:session_id>/manage/', views_trainer.trainer_manage_session, name='trainer_manage_session'),
    
    # Survey management URLs
    path('surveys/', views.surveys_view, name='surveys'),
    path('surveys/add/', views.surveys_view, name='add_survey'),  # We can reuse the same view but add a flag for adding
    path('api/surveys/', views.surveys_api, name='surveys_api'),
    path('api/surveys/create/', views.create_survey_api, name='create_survey_api'),
    path('api/surveys/<int:survey_id>/', views.survey_delete_api, name='survey_delete_api'),
    path('api/surveys/bulk-action/<str:action>/', views.survey_bulk_action_api, name='survey_bulk_action_api'),
    
    # Additional Survey API endpoints
    path('api/course-instances/', views.course_instances_api, name='course_instances_api'),
    path('api/surveys/existing/', views.get_existing_surveys_api, name='get_existing_surveys_api'),
    path('api/surveys/<int:survey_id>/view/', views.view_survey_api, name='view_survey_api'),
    path('api/surveys/<int:survey_id>/edit/', views.edit_survey_api, name='edit_survey_api'),
    
    # Course Instance Management APIs
    path('api/course-instance/', views.course_instance_api, name='course_instance_api_create'),
    path('api/course-instance/<str:instance_id>/', views.course_instance_api, name='course_instance_api_detail'),
    path('api/course-instance/<str:instance_id>/toggle-publish/', views.toggle_publish_status, name='course_instance_toggle_publish'),

    # Corporate User survey endpoints
    path('my-surveys/', corporate_views.user_surveys_view, name='user_surveys'),
    path('my-surveys/take/<int:survey_id>/<int:reservation_id>/', corporate_views.take_survey_view, name='take_survey'),
    path('api/surveys/pending-count/', corporate_views.get_pending_surveys_count, name='get_pending_surveys_count'),
    path('api/surveys/submit-response/', corporate_views.create_survey_response_api, name='create_survey_response_api'),
    
    # Questionnaire endpoints
    path('my-questionnaires/', corporate_views.user_questionnaires_view, name='user_questionnaires'),
    path('my-questionnaires/take/<int:questionnaire_id>/', corporate_views.take_questionnaire_view, name='take_questionnaire'),
    path('my-questionnaires/take/<int:questionnaire_id>/<int:reservation_id>/', corporate_views.take_questionnaire_view, name='take_questionnaire_with_reservation'),
    path('api/questionnaires/pending-count/', corporate_views.get_pending_questionnaires_count, name='get_pending_questionnaires_count'),
    path('api/questionnaires/submit-response/', corporate_views.create_questionnaire_response_api, name='create_questionnaire_response_api'),
    
    # Individual User survey endpoints
    path('my-feedback/', individual_views.individual_user_surveys_view, name='individual_user_surveys'),
    path('api/surveys/individual/pending-count/', individual_views.get_individual_pending_surveys_count, name='get_individual_pending_surveys_count'),

    # Individual User questionnaire endpoints
    path('my-individual-questionnaires/', individual_views.individual_user_questionnaires_view, name='individual_user_questionnaires'),
    path('my-individual-questionnaires/take/<int:questionnaire_id>/', individual_views.individual_take_questionnaire_view, name='individual_take_questionnaire'),
    path('my-individual-questionnaires/take/<int:questionnaire_id>/<int:reservation_id>/', individual_views.individual_take_questionnaire_view, name='individual_take_questionnaire_with_reservation'),
    path('api/questionnaires/individual/pending-count/', individual_views.get_individual_pending_questionnaires_count, name='get_individual_pending_questionnaires_count'),
    path('api/questionnaires/individual/submit-response/', individual_views.individual_create_questionnaire_response_api, name='individual_create_questionnaire_response_api'),

    # Trainer questionnaire review endpoints
    path('trainer/questionnaire-response/review/<int:response_id>/', views_trainer.review_questionnaire_response, name='review_questionnaire_response'),

    # Request new course (not in system)
    path('request-new-course/', corporate_views.request_new_course, name='request_new_course'),

    # Dashboard URLs
    path('admin/dashboard/', dashboard_views.system_admin_dashboard, name='system_admin_dashboard'),
    path('super-admin/dashboard/', dashboard_views.super_admin_dashboard, name='super_admin_dashboard'),
    path('trainer/dashboard/', dashboard_views.trainer_dashboard, name='trainer_dashboard'),
    
    # Trainer dashboard related URLs
    path('trainer/manage-session/<str:session_id>/', dashboard_views.manage_session, name='manage_session'),
    path('trainer/sessions/', dashboard_views.trainer_sessions, name='trainer_sessions'),
    path('trainer/record-attendance/<str:session_id>/', dashboard_views.record_attendance, name='record_attendance'),
    path('trainer/questionnaires/', dashboard_views.trainer_questionnaires, name='trainer_questionnaires'),
    path('trainer/review-questionnaire/<int:questionnaire_id>/', dashboard_views.review_questionnaire, name='review_questionnaire'),
    path('trainer/session-report/<str:session_id>/', dashboard_views.session_report, name='session_report'),
    path('trainer/course-materials/', dashboard_views.trainer_course_materials, name='trainer_course_materials'),
    path('trainer/course-materials/<int:course_id>/', dashboard_views.trainer_course_materials, name='trainer_course_materials_with_id'),
    path('trainer/upload-course-material/<int:course_id>/', dashboard_views.upload_course_material, name='upload_course_material'),
    path('trainer/profile/', dashboard_views.trainer_profile, name='trainer_profile'),
    
    # User dashboard URLs
    path('individual/dashboard/', dashboard_views.individual_user_dashboard, name='individual_user_dashboard'),
    path('corporate/dashboard/', dashboard_views.corporate_user_dashboard, name='corporate_user_dashboard'),
    path('internal-gb/dashboard/', dashboard_views.internal_gb_dashboard, name='internal_gb_dashboard'),
    
    # Oracle SOAP API test endpoint (for development)
    path('test-oracle-auth/<str:employee_id>/', views.test_oracle_authentication, name='test_oracle_auth'),

    # Internal GB user specific URLs
    path('my-gb-reservations/', GB_views.gb_user_reservations_view, name='gb_user_reservations_view'),
    path('api/gb-cancellation-deadline/', GB_views.gb_get_cancellation_deadline, name='gb_get_cancellation_deadline'),
    path('gb-emergency-cancellation-request/', GB_views.gb_emergency_cancellation_request, name='gb_emergency_cancellation_request'),
    path('gb-enroll/<int:instance_id>/', GB_views.gb_enroll_in_instance, name='gb_enroll_in_instance'),
    
    # GB user survey and questionnaire URLs
    path('gb-surveys/', GB_views.gb_user_surveys_view, name='gb_user_surveys'),
    path('gb-questionnaires/', GB_views.gb_user_questionnaires_view, name='gb_user_questionnaires'),
    path('gb-surveys/take/<int:survey_id>/<int:reservation_id>/', GB_views.gb_take_survey_view, name='gb_take_survey'),
    path('gb-questionnaires/take/<int:questionnaire_id>/<int:reservation_id>/', GB_views.gb_take_questionnaire_view, name='gb_take_questionnaire'),
    
    # API URLs for GB users
    path('gb-api/surveys/pending/', GB_views.gb_pending_surveys_api, name='gb_pending_surveys_api'),
    path('gb-api/questionnaires/pending/', GB_views.gb_pending_questionnaires_api, name='gb_pending_questionnaires_api'),
    
    # GB calendar URLs
    path('gb-calendar/', GB_views.gb_user_calendar_view, name='gb_user_calendar'),
    path('gb-calendar/events/', GB_views.gb_user_calendar_events_api, name='gb_user_calendar_events_api'),
    
    # GB Supervisor approval URLs
    path('GB_supervisor/pending-requests/', GB_views.supervisor_pending_requests_view, name='supervisor_pending_requests'),
    path('GB_supervisor/approve-request/<str:request_id>/', GB_views.supervisor_approve_request_get, name='supervisor_approve_request'),
    path('GB_supervisor/process-request/<str:request_id>/', GB_views.supervisor_approve_request, name='supervisor_process_request'),
    path('GB_supervisor/my-team/', GB_views.my_team_view, name='supervisor_my_team'),
    path('api/GB_supervisor/pending-requests/', GB_views.supervisor_pending_requests_api, name='supervisor_pending_requests_api'),
    path('api/GB_supervisor/team-attendance/', GB_views.supervisor_team_attendance_api, name='supervisor_team_attendance_api'),
]
