#!/usr/bin/env python
"""
Test script to check email configuration and send a test email.
Run with: python test_email_config.py <EMAIL>
"""

import os
import sys
import django

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

from django.conf import settings
from django.core.mail import send_mail

def test_email_settings():
    """Print current email settings"""
    print("\n=== Email Configuration Check ===")
    
    email_settings = {
        'EMAIL_HOST': getattr(settings, 'EMAIL_HOST', None),
        'EMAIL_PORT': getattr(settings, 'EMAIL_PORT', None),
        'EMAIL_HOST_USER': getattr(settings, 'EMAIL_HOST_USER', None),
        'EMAIL_HOST_PASSWORD': '******' if getattr(settings, 'EMAIL_HOST_PASSWORD', None) else None,
        'EMAIL_USE_TLS': getattr(settings, 'EMAIL_USE_TLS', None),
        'EMAIL_USE_SSL': getattr(settings, 'EMAIL_USE_SSL', None),
        'EMAIL_BACKEND': getattr(settings, 'EMAIL_BACKEND', None),
        'DEFAULT_FROM_EMAIL': getattr(settings, 'DEFAULT_FROM_EMAIL', None),
    }
    
    all_settings_valid = True
    for name, value in email_settings.items():
        status = "✅ SET" if value else "❌ MISSING"
        if not value and name != 'EMAIL_USE_SSL':  # EMAIL_USE_SSL can be None
            all_settings_valid = False
        print(f"{name}: {value if name != 'EMAIL_HOST_PASSWORD' else '*****'} - {status}")
    
    print("\nConfiguration status:", "✅ COMPLETE" if all_settings_valid else "❌ INCOMPLETE")
    return all_settings_valid

def send_test_email(recipient):
    """Send a test email to verify the configuration"""
    if not recipient:
        print("Error: No recipient email provided")
        return False
        
    print(f"\nSending test email to {recipient}...")
    
    try:
        result = send_mail(
            subject='LMS Test Email',
            message='This is a plain text test email from the LMS system.',
            html_message=f'''
            <html>
                <body>
                    <h2>LMS Email Test</h2>
                    <p>This is a <b>HTML test email</b> from the LMS system.</p>
                    <p>If you're seeing this, your email configuration is working!</p>
                    <p>Sent from: {settings.EMAIL_HOST_USER}</p>
                </body>
            </html>
            ''',
            from_email=settings.EMAIL_HOST_USER,
            recipient_list=[recipient],
            fail_silently=False
        )
        
        if result:
            print("✅ Success! Test email sent successfully.")
        else:
            print("❌ Failed to send test email (no error, but result was False).")
        
        return result
    except Exception as e:
        print(f"❌ Error sending email: {str(e)}")
        
        # Gmail specific authentication error
        if '535' in str(e) and '5.7.3' in str(e) and 'gmail' in settings.EMAIL_HOST.lower():
            print("\nGmail Authentication Error Detected! This is typically because:")
            print("1. You're using your regular Gmail password instead of an App Password")
            print("2. Your App Password is incorrect or has expired")
            print("3. Your Gmail account has additional security settings preventing access")
            print("\nSolution:")
            print("1. Go to your Google Account → Security")
            print("2. Enable 2-Step Verification if not already enabled")
            print("3. Go to App passwords (search for 'App passwords')")
            print("4. Select 'Mail' and 'Other' (name it 'Django LMS')")
            print("5. Generate and use the new 16-character password in your .env file")
            
        return False

if __name__ == "__main__":
    # Check settings
    settings_valid = test_email_settings()
    
    # Get recipient email from command line argument or prompt
    if len(sys.argv) > 1:
        recipient = sys.argv[1]
    else:
        recipient = input("\nEnter recipient email for test: ")
    
    # Send test email if settings look valid
    if settings_valid:
        send_test_email(recipient)
    else:
        print("\n❌ Cannot send test email - configuration is incomplete.")
        print("Please update your .env file with correct email settings.") 