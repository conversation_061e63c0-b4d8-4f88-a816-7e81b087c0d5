import os
import requests
from PIL import Image
from io import BytesIO

# Create necessary directories
os.makedirs('static/images/slides', exist_ok=True)
os.makedirs('static/images/features', exist_ok=True)

def download_and_optimize_image(url, save_path, max_size=(1920, 1080)):
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(save_path), exist_ok=True)
    
    # Set headers to mimic a browser request
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
    }
    
    # Download image
    response = requests.get(url, headers=headers)
    img = Image.open(BytesIO(response.content))
    
    # Convert to RGB if needed
    if img.mode in ('RGBA', 'P'):
        img = img.convert('RGB')
    
    # Resize if larger than max_size
    if img.size[0] > max_size[0] or img.size[1] > max_size[1]:
        img.thumbnail(max_size, Image.Resampling.LANCZOS)
    
    # Save optimized image
    img.save(save_path, 'JPEG', quality=85, optimize=True)

# Download and optimize slides
slides = [
    ('https://images.unsplash.com/photo-1517245386807-bb43f82c33c4', 'static/images/slides/slide1.jpg'),  # Corporate training
    ('https://images.unsplash.com/photo-1552664730-d307ca884978', 'static/images/slides/slide2.jpg'),     # Professional meeting
    ('https://images.unsplash.com/photo-1524178232363-1fb2b075b655', 'static/images/slides/slide3.jpg')   # Learning environment
]

# Download and optimize feature images
features = [
    ('https://images.unsplash.com/photo-1524178232363-1fb2b075b655', 'static/images/features/expert-training.jpg'),
    ('https://images.unsplash.com/photo-1531482615713-2afd69097998', 'static/images/features/interactive-learning.jpg')
]

# Add Unsplash API parameters
for i, (url, path) in enumerate(slides + features):
    if 'unsplash.com' in url:
        slides[i] = (url + '?auto=format&fit=crop&w=1920&q=80', path)

# Download all images
for url, path in slides + features:
    try:
        download_and_optimize_image(url, path)
        print(f"Successfully downloaded and optimized: {path}")
    except Exception as e:
        print(f"Error downloading {path}: {e}")
