import logging
import uuid
from django.core.mail import send_mail
from django.conf import settings
from django.utils.translation import gettext as _
from django.utils import timezone

logger = logging.getLogger(__name__)

def send_individual_notification_email(recipient_email, subject, message, cc_emails=None):
    """
    Send email notification to individual users using the same approach as corporate emails.
    
    Args:
        recipient_email: Primary recipient's email address
        subject: Email subject
        message: HTML message content
        cc_emails: List of CC email addresses (optional)
    """
    if not cc_emails:
        cc_emails = []
    
    # Check if email settings are properly configured
    if not all([settings.EMAIL_HOST, settings.EMAIL_PORT, settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD]):
        logger.error(f"Email settings are not properly configured. Check EMAIL_HOST, EMAIL_PORT, EMAIL_HOST_USER, and EMAIL_HOST_PASSWORD settings.")
        
        # Try to log to database anyway
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='FAILED',
                error_message='Email settings not configured properly'
            )
        except Exception as db_err:
            logger.debug(f"Could not log to database: {str(db_err)}")
            
        return False

    # Generate a transaction ID for tracking this email
    transaction_id = uuid.uuid4().hex[:8]
    logger.info(f"[IndividualEmail:{transaction_id}] Preparing to send email to {recipient_email} with subject '{subject}'")

    try:
        # IMPORTANT: Always use EMAIL_HOST_USER as the sender email to avoid auth issues
        from_email = settings.EMAIL_HOST_USER
        
        # Log the actual settings being used
        logger.info(f"[IndividualEmail:{transaction_id}] Using EMAIL_HOST: {settings.EMAIL_HOST}, PORT: {settings.EMAIL_PORT}")
        logger.info(f"[IndividualEmail:{transaction_id}] Sending as: {from_email} to: {recipient_email}")
        
        # Handle CC recipients
        all_recipients = [recipient_email]
        cc_list = []
        
        if cc_emails and len(cc_emails) > 0:
            cc_list = cc_emails
            all_recipients.extend(cc_emails)
            logger.info(f"[IndividualEmail:{transaction_id}] Adding CC recipients: {', '.join(cc_emails)}")
        
        # Use Django's send_mail function with all recipients in recipient_list
        result = send_mail(
            subject=subject,
            message="",  # Empty plain text version
            html_message=message,  # HTML version
            from_email=from_email,
            recipient_list=all_recipients,
            fail_silently=False
        )
        
        # Log result and save to database
        if result:
            logger.info(f"[IndividualEmail:{transaction_id}] SUCCESS: Email sent to {recipient_email}, CC: {', '.join(cc_list) if cc_list else 'none'}")
            
            # Log to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='SUCCESS',
                    transaction_id=transaction_id,
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log successful email to database: {str(db_err)}")
        else:
            logger.warning(f"[IndividualEmail:{transaction_id}] FAILED: Email not sent to {recipient_email}")
            
            # Log failure to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='FAILED',
                    transaction_id=transaction_id,
                    error_message='Email send returned False',
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log failed email to database: {str(db_err)}")
            
        return result
    except Exception as e:
        logger.error(f"[IndividualEmail:{transaction_id}] ERROR: Failed to send email to {recipient_email}: {str(e)}", exc_info=True)
        
        # Log error to database
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='ERROR',
                transaction_id=transaction_id,
                error_message=str(e),
                sent_at=timezone.now()
            )
        except Exception as db_err:
            logger.debug(f"Could not log email error to database: {str(db_err)}")
            
        return False

# Email functions for different notification types

def send_questionnaire_submission_confirmation(user, questionnaire_title, score, max_score):
    """Email notification when a user submits a questionnaire"""
    logger.info(f"Preparing questionnaire submission confirmation email for {user.email}, questionnaire: {questionnaire_title}")
    
    subject = _('Questionnaire Submission: {0}').format(questionnaire_title)
    
    # Calculate percentage score for better context
    percentage = round((score / max_score * 100) if max_score > 0 else 0)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Thank you for submitting your responses to <strong>{questionnaire_title}</strong>.</p>
    <p>Your answers have been recorded and will be reviewed by a trainer.</p>
    <p>Your initial score: <strong>{score}/{max_score}</strong> ({percentage}%)</p>
    <p>This is an automated score based on the objective questions. The trainer may adjust your final score after review.</p>
    <p>Thank you for your participation.</p>
    """
    
    return send_individual_notification_email(user.email, subject, message)

def send_reservation_cancellation_confirmation(user, course_name, start_date, refund_info=None):
    """Email notification when a user cancels a reservation"""
    logger.info(f"Preparing reservation cancellation confirmation email for {user.email}, course: {course_name}")
    
    subject = _('Reservation Cancellation Confirmation: {0}').format(course_name)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>We confirm that your reservation for <strong>{course_name}</strong> on <strong>{start_date}</strong> has been cancelled.</p>
    """
    
    if refund_info:
        message += f"""
        <p>{refund_info}</p>
        """
    
    message += f"""
    <p>If you did not request this cancellation or have any questions, please contact us immediately.</p>
    <p>Thank you</p>
    """
    
    return send_individual_notification_email(user.email, subject, message)

def send_pending_survey_reminder(user, course_name, survey_title, days_remaining=None):
    """Email notification reminding user about pending surveys"""
    logger.info(f"Preparing pending survey reminder email for {user.email}, course: {course_name}")
    
    subject = _('Reminder: Course Feedback for {0}').format(course_name)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>We noticed you have not yet completed the survey <strong>{survey_title}</strong> for your recent course <strong>{course_name}</strong>.</p>
    """
    
    if days_remaining:
        message += f"""
        <p>The survey will be available for <strong>{days_remaining} more days</strong>.</p>
        """
    
    message += f"""
    <p>Your feedback is extremely valuable to us and helps improve our courses. Please take a few minutes to complete the survey.</p>
    <p>You can access the survey through your account dashboard.</p>
    <p>Thank you for your participation!</p>
    """
    
    return send_individual_notification_email(user.email, subject, message)

def send_pending_questionnaire_reminder(user, course_name, questionnaire_title, due_date=None):
    """Email notification reminding user about pending questionnaires"""
    logger.info(f"Preparing pending questionnaire reminder email for {user.email}, questionnaire: {questionnaire_title}")
    
    subject = _('Reminder: Complete Your Questionnaire for {0}').format(course_name)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>This is a reminder that you still need to complete the questionnaire <strong>{questionnaire_title}</strong> for your course <strong>{course_name}</strong>.</p>
    """
    
    if due_date:
        message += f"""
        <p>Please complete it by <strong>{due_date}</strong>.</p>
        """
    
    message += f"""
    <p>Completing questionnaires is an important part of your course participation.</p>
    <p>You can access the questionnaire through your account dashboard.</p>
    <p>Thank you!</p>
    """
    
    return send_individual_notification_email(user.email, subject, message)

def send_waitlist_notification(user, course_name, start_date):
    """Email notification when a user is moved from waitlist to confirmed registration"""
    logger.info(f"Preparing waitlist notification email for {user.email}, course: {course_name}")
    
    subject = _('Good News! You are now registered for {0}').format(course_name)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Great news! A spot has opened up in <strong>{course_name}</strong> starting on <strong>{start_date}</strong>.</p>
    <p>You have been automatically moved from the waiting list to a confirmed registration.</p>
    <p>All the course details are now available in your user dashboard.</p>
    <p>We look forward to your participation!</p>
    """
    
    return send_individual_notification_email(user.email, subject, message) 