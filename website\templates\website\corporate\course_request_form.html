{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Request a Course" %}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        <script>
            // Auto-dismiss Django flash messages
            document.addEventListener('DOMContentLoaded', function() {
                const messages = document.querySelectorAll('.notification-message');
                messages.forEach(message => {
                    const type = message.getAttribute('data-type');
                    let timeout = 5000; // Default 5 seconds
                    
                    // Different timeouts based on message type
                    if (type === 'success') {
                        timeout = 5000; // 5 seconds for success
                    } else if (type === 'warning') {
                        timeout = 7000; // 7 seconds for warnings
                    } else if (type === 'error') {
                        timeout = 10000; // 10 seconds for errors
                    }
                    
                    setTimeout(() => {
                        message.style.transition = 'opacity 0.5s ease';
                        message.style.opacity = '0';
                        setTimeout(() => {
                            message.remove();
                            
                            // If no more messages, remove the container
                            if (document.querySelectorAll('.notification-message').length === 0) {
                                const container = document.getElementById('notifications-container');
                                if (container) container.remove();
                            }
                        }, 500);
                    }, timeout);
                });
            });
        </script>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center space-x-3 mb-8">
            <a href="{% url 'website:main' %}" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-white">{% trans "Request a Course" %}</h1>
        </div>

        <!-- Main Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Course Request Form -->
            <div class="lg:col-span-2">
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-6">{% trans "Course Request Form" %}</h2>
                    
                    <form method="POST" action="{% url 'website:course_request_form' %}">
                        {% csrf_token %}
                        
                        {% if course %}
                        <input type="hidden" name="course_id" value="{{ course.course_id }}">
                        {% endif %}
                        
                        <div class="space-y-4">
                            <!-- Course Type -->
                            <div>
                                <label for="course_type" class="block text-sm font-medium text-white/70">{% trans "Course Type" %} *</label>
                                <select id="course_type" name="course_type" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md bg-white/10 text-white">
                                    <option value="" class="bg-gray-800 text-white">{% trans "Select Type" %}</option>
                                    <option value="ONLINE" class="bg-gray-800 text-white">{% trans "Online" %}</option>
                                    <option value="OFFLINE" class="bg-gray-800 text-white">{% trans "Offline" %}</option>
                                </select>
                            </div>
                            
                            <!-- Capacity -->
                            <div>
                                <label for="capacity" class="block text-sm font-medium text-white/70">{% trans "Capacity" %} ({% trans "seats" %})</label>
                                <input type="number" name="capacity" id="capacity" min="1" max="100" value="20" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white/10 text-white">
                            </div>
                            
                            <!-- Notes -->
                            <div>
                                <label for="notes" class="block text-sm font-medium text-white/70">{% trans "Notes" %}</label>
                                <textarea id="notes" name="notes" rows="4" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-gray-300 rounded-md bg-white/10 text-white" placeholder="{% trans 'Additional information or special requirements...' %}"></textarea>
                            </div>
                            
                            <!-- Submit Button -->
                            <div class="pt-4">
                                <button type="submit" class="w-full inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                                    {% trans "Submit Request" %}
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- Sidebar Info -->
            <div>
                <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Corporate Information" %}</h2>
                    <div class="space-y-3">
                        <div>
                            <span class="text-white/70 block text-sm">{% trans "Company Name" %}</span>
                            <span class="text-white">{{ corporate.legal_name }}</span>
                        </div>
                        <div>
                            <span class="text-white/70 block text-sm">{% trans "Corporate ID" %}</span>
                            <span class="text-white">{{ corporate.corporate_id }}</span>
                        </div>
                        <div>
                            <span class="text-white/70 block text-sm">{% trans "Category" %}</span>
                            <span class="text-white">{{ corporate.get_category_display }}</span>
                        </div>
                        <div>
                            <span class="text-white/70 block text-sm">{% trans "Capacity" %}</span>
                            <span class="text-white">{{ corporate.capacity }}</span>
                        </div>
                    </div>
                </div>
                
                <!-- Existing Requests -->
                {% if existing_requests %}
                <div class="mt-6 bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6">
                    <h2 class="text-xl font-semibold text-white mb-4">{% trans "Your Requests" %}</h2>
                    <div class="space-y-4">
                        {% for request in existing_requests|slice:":5" %}
                            <div class="p-3 border border-white/10 rounded-md relative">
                                <div class="flex justify-between items-start">
                                    <div>
                                        <span class="text-white font-medium block">{{ request.get_course_type_display }} {% trans "Course" %}</span>
                                        <span class="text-white/70 text-sm">{{ request.requested_date|date:"F j, Y, g:i a" }}</span>
                                    </div>
                                    <span class="text-xs px-2 py-1 rounded-full {% if request.status == 'PENDING' %}bg-amber-500/20 text-amber-400{% elif request.status == 'APPROVED' %}bg-green-500/20 text-green-400{% else %}bg-red-500/20 text-red-400{% endif %}">
                                        {{ request.get_status_display }}
                                    </span>
                                </div>
                                
                                <div class="mt-2">
                                    <span class="text-white/70 text-sm block">{% trans "Capacity" %}: {{ request.capacity }} {% trans "seats" %}</span>
                                    {% if request.notes %}
                                        <span class="text-white/70 text-sm block mt-1">{% trans "Notes" %}: {{ request.notes|truncatechars:100 }}</span>
                                    {% endif %}
                                </div>
                                
                                {% if request.admin_notes and request.status != 'PENDING' %}
                                    <div class="mt-2 p-2 bg-white/5 rounded-md">
                                        <span class="text-white/70 text-xs block">{% trans "Admin Response" %}:</span>
                                        <span class="text-white text-sm">{{ request.admin_notes }}</span>
                                    </div>
                                {% endif %}
                            </div>
                        {% endfor %}
                        
                        {% if existing_requests.count > 5 %}
                            <div class="text-center pt-2">
                                <span class="text-white/70 text-sm">{% trans "Showing" %} 5 {% trans "of" %} {{ existing_requests.count }} {% trans "requests" %}</span>
                            </div>
                        {% endif %}
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %} 