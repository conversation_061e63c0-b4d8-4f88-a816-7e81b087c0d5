import ssl
from django.core.mail.backends.smtp import EmailBackend as SMTPBackend
from django.utils.functional import cached_property
import logging

logger = logging.getLogger(__name__)

class EmailBackend(SMTPBackend):
    """
    Custom email backend that extends the default SMTP backend
    to handle SSL/TLS connections more reliably.
    """
    @cached_property
    def ssl_context(self):
        if self.ssl_certfile or self.ssl_keyfile:
            ssl_context = ssl.SSLContext(protocol=ssl.PROTOCOL_TLS_CLIENT)
            ssl_context.load_cert_chain(self.ssl_certfile, self.ssl_keyfile)
            return ssl_context
        else:
            # Create a more permissive SSL context for development
            ssl_context = ssl.create_default_context()
            ssl_context.check_hostname = False
            ssl_context.verify_mode = ssl.CERT_NONE
            return ssl_context
    
    def send_messages(self, email_messages):
        """
        Override send_messages to add logging for debugging purposes
        """
        try:
            # Log email attempt
            if len(email_messages) > 0:
                message = email_messages[0]
                logger.info(f"Attempting to send email: subject={message.subject}, "
                           f"from={message.from_email}, to={message.to}")
                
            # Call the original method
            result = super().send_messages(email_messages)
            logger.info(f"Email sending result: {result} messages sent")
            return result
        except Exception as e:
            # Log any exceptions that occur
            logger.error(f"Error sending email: {str(e)}", exc_info=True)
            raise  # Re-raise the exception after logging 