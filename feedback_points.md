
1- we need tempelate for emails (question to nancy)

2- soup ui  


question to nancy:

1- for external trainer, does he will want approve to enroll to course instance or he will pay as indvidual.

2- how payment workflow will processed if its cash

3- when we will create customer 

4- course costs for employees


14th feedback():

1- when user got terminated he should be inactive

2- i should have 1 id num and could have up to 2 user have same phone num



13th feedback(nancy):

1- trainer can enroll to course 

2- add check box published or not after crezting course instace.

3- when gb user are in waiting list fifo when user get approvel from supervisior

4- when creating questioner or feedback he could choose from existing questioner or feedback

5- course instance could be out of academy as online (it could be in park for ex)
---------------------------------------------
6- in all reservation tab for system admin i will have 2 tabs 1 for reservation and 1 for emergency cancel without need of notfication panel

7- all filter need search lens as users and course name, also add filter on user type in all user reservation tab for system admin

8- apply filter on emergency calcelation request page (user type and username)

9- remove enroll now from course details section also remove course details and session type and requiresd equipment section as course instance override details as loc capasity and etc for internal gb and indvidual user(this meen course details will have description section and number of session)

10- when click view in reservation tab i should redirect to reservation view (as when i click on attendance i will have session no and loc and type and attendance)

11- make Course Feedback,Course Questionnaires, Team Approval Requests and My Course Reservations tabs in my reservation tab in header (apply this design for all user type)

12- make course content and questioneer per session will be available when session be in progress as view (so we will add status for each session as upcoming inprogress and completed)

13- make email notfication to trainer and trainee when system admin update session 

14- make my course tab in courses tab (so i can see content and questioner and feedback per session)

15- i need when trainer create new questioner he could choose from existing also he could choose from existing then he could update (when he click update he will dublicate questioner template and he could updae as it will be saved as new one)

16- when creating course instanse i should have hybrid as location type so i should enter location for each session (shoul take link if online as teams or google maps)

17- add exam as when creating courses as trainer like the same way adding questioner
then when system admin create course instance so this exam session will take start and end datetime and type as physical or online (the same if online i will take docs and if physical choose room ) so it will be linked to course instance 

18- i should create new calender for trainer supervisor like the one of system admin including filter by trainer 

19- i should update trainer calender as i have session i will tech and session i will attende.(it will be filter on top right instead of sessions and events).

20- external trainer will be same as indvidual user when enroll to course instance and will have my reservation tab.

21- so in courses tab i will have 3 optional tabs (my courses, enroll course, manage course) 1st 2 tabs default for any trainee and last one for trainer and system/super admin

22- when creating new sesssion (course instance) i will display course capacity on left side and when choose room from dropdown menu (i will have room name and capacity)

23- when creating new course instanse i should enter capacity manualy 

24- i should have course instance mangment instead of session manegment, and when edit i should edit the whole course instance not updating session by one

25- 




12th feedback(azazy):

1-  when creating session i could make 1 trainer for all sessions as option 


11th feedback(ramy):

1- when gb emploeyy sign in (i should get or create user by using soap api)

2- i should validate for user when trying to enroll to any course 




10th feedback(nancy):

1- change rooms tab label to resourses 

2- make super admin add holiday in past (piority low)

3- make multi country calenders  including time zone (piority low)

4- add country for each user to know timezone for each user

5- when creating new course instance i should enter which country i will provide course to which user (make 1 to many relation as 1 course instance could be teached in many country at same time)
so i should write time in utc in db 

6- super/sytem admin can filter calender on country to see their session , event and holidas, also he will have all option that will combine all calenders

7- make user when creating survay could choose from old surveys





9th feedback (ramy):       not approved

1- user can got promoted from indvidual user to  corporate admin
no (2 separete accounts)

2- user should get approvel from system admin to got promoted
(no)

3- corporate admin can manege more than 1 corporate
no (every corp have 1 admin)

4- corporate admin will have profile attribute to switch from corporate admin to corporate user 
(no need)

5- gb employee will have creadentials by otp instead of password and employee number

6- gb user "traniee" should get qpproval for any course from his supervisor

7- easy log in python to log and gurdians 


8th feedback (nancy):

1- i want upcoming events to be scrollablr box that have evevnts up to the end of the week.

2- add filter on calender for trainer supervisor to filter on all trainer belong to him 

3- super system admin can add corporate user to corporate 


4- when gb employee try to login by employee code only then he will get otp as passcode for him to enter every time into lms (theis is where i will create new gb user in database) (ramy helmy) 


7th feedback(bakr UI):

1- admin and super admin dashboard:                     (done)

    1- in progress (trainer, rooms, trainee, sessions)

    2- upcoming sessions for the end of the week (sessions info like course instance name - trainer name - room no - time)

    3- notfications like (Corporate Course Requests - Emergency Cancellation Requests)

    4- number of external corporate admin 

    5- number of online course instance 


2- trainer dashboard:                     (done)

    1- active sessions (that will redirect him to manege this session)

    2- upcoming for the end of the week

    3- to do list (it will be questioneers to perview)


3- indvidual or corporate user:                     (done)

    1- active sessions 

    2- all upcoming sessions 

    3- to do list (questioneers and surveys)

4- corporate admin:                     (done)

    1- in progress (trainer, rooms, trainee, sessions)

    2- upcoming sessions for the end of the week (sessions info like course instance name - trainer name - room no - time)

    3- notfications like (Corporate Cancellation Requests)

    4- total users of corporate (in progress sessions)

    5- total users of corporate 

5- Gb trainee:                     (not done)

    1-it will be the same like corporate user but i will have in notfication if requested course is approved by maneger to enroll to course instance.


6- feedback points on system admin pages:

    1- header icons will be at the end of right and left of header, also icons will be upside words

    2- i dont need this big space in each tab upside tab title or tabel . etc 

    3- i dont need title for each tab as tab will be blue in header 

    4- all filters on each tab will work 

    5- no need for label on each filter

    6- make all reservation tab is ur refrence.

    7- any notfication panel colapse and expand as if its empty or have requests in it 

    8- also if i have filter it sholde be upside tabel not upside notfication panel.

in calender tab :
    1- i want to add icons for each tab like room 

    2- i want active tab to have white cokur to make contrast with background

    3- make plus icon on days that have event or sesion on it, also if day is empty make if i click on any day like i click on plus icon.

in trainer tab:

    1- i should write how many course asiigned to him without spicifing name but when i click on num it expand with course name


in sureys tab :

    1- i will have icons in action section instead of words. 





6th feedback(nancy):

1- super system admin can assign course to trainer
(done)

2- super system admin can create course as trainer
(done) 

3- system admin can view all corporate information
(done)

4- make system admin add any attendance to any user
(done) 

5- online course will work on teams

6- oracle hr ingration will take employee profile data ( blue or white, national id , phone ,email, username, empoyee id , maneger name , gender, bU, orgnization, job title, gb stars, termination date, termination status, cost center)

7- it will run automatic 

8- oracle finance integration internal or external 

9- in internal (cost , cost center, datetime , id )

10- in external (invoice)

11- ask in payment (instapay, visa , cash)

 


5th feedback (nancy):

1- super admin can change attendance of any user (not system admin)

2- request new course not existing on system like other

3- for others course system admin will change status to pending creation

4- when corporate admin request other course, request will sent to system admin and trainer supervisior with status pending creation 

5- when supervisor trainer have access to reject or approve create course, and if he approve he will create course and assign it to one of trainers 

6- if supevisr trainer approve request status will change to created, then system admin can approve corporate admin request and create sessions to corporate 



4th feedback(nancy):

1- cancel window of each course when indvidual user want to make reservation dont check on window time
(done)

2- make window 3 days as default (excluding holidays and weekend ) when i choose to cancel reservation as indvidual
(done)

3- make content not disbaled until course start (this mean status to be in progress)
(done)

4- make admin can reshdule any course instance at any time (while preserving users reservations) also edit reschule for holiday to maintain same reservation.

5- make for each course instance type (indvidual - corperate(for each corbrate has it special course) - gb)
(done)

6- corprete admin request (course - capacity - type (onlibe -offline) - date - notes )
(done)



3rd feedback(ramy):

9- course content table to display it to user when it in progress or when he paid. types could be powerpoint , pdf , image , video , doc  
(done)

10- make trainer specify topic contint and discription of each course when creating new course 

11- make backend validate if i want to cancel any reservation and etc

1- make admin edit any event from calender view 

2- make all (but not available make him dimt al note that he is not availble at that time) only availble trainer to choose when creating new session 

3- when creating new session i should validate if course require an equipment and it not fixed in room, i should validate that admin choose it from required equipment.

4- try using scrolable model in creating session or event
(done)

6- make admin filter on instance in reservations page.
(done)


5- make configration model using django solo (for now put default Cancellation Deadline)

7- ask nance if indvidual user can enroll to same course but diffrent instance ??
yes he can 


8- ask nance if i should send email notification to finance team (add finance team email in configration table) and create refund in case of canceld after payment.
we will send email to one of finance 

12- i should finsh ui for admin and check if its responsive  

13- ask nancy about ci 


2nd feedback:

1- when creating new course i could choose multible room type (making it from 1 to 1 to 1 to many as when create course i will take all possible roomtype for this course)
(done)

(i shoulde mention how many days of each room type)
done 


2- when displaying days in calender and i have 2 event at same time dont show them above each others 
(done)

3- add schdule for each course to create sessions automatic 

4- add recreate button in course page to take instance of course and edit on the new instance to create new course (duplicate course record by adding copied word to title)
(done)

5- when i click on plus in calender, i should diplay pop up to create event or create session (opening model direct)
(done)


6- when creating new course i shoulde mention amount an category of equipment not it self (category will be updated as laptop-chairs instead of electronics and furniture to know stock)
(from 1st feedback)
(done)

7- every record in database i should know who created it and when also updating it
(from 1st feedback)

8- i should have stock in category as i will count how many item of category, so i will not have stock for each model, i will have stock of equipment 
(done)


-----------------------------------------------------------------------------------

1st feedback:

1- when i delete event it doesnt delete busy slot from equipments busy slots 
(done)

2- i want to make fixed equipment for each room (lab - projector)  
(done)

3- i want when i create session i choose additional equipment to this session, so i will add equipment for each session instead of default equipment to all session
(done)

4- when i create session and i choose time from busy slot of equipment it should be invisible at that time as i did in create event  
(done)

5- when creating new course i shoulde mention amount an category of equipment not it self (category will be updated as laptop-chairs instead of electronics and furniture to know stock)

6- every record in database i should know who created it and when also updating it

7- i can change slot timing for ex ramadan custom time slots for creating and updating session
(done)

8- make availability tables from slots timeng to be table (start end id() ) 
(done)

9- event can be more than one day (i will have startdate and end date for each day choosen)
(done)

10 when adding new trainer i can choose from existing user and if it is not a user i can invite him to make new user by email
(postpone)

11- i want if i delete all equipment avalabilty bookings it return to ready status automaticaly
(done)

12- i should make only visble equibment when its available at that time and hidden if not 
(done)


