import json
import logging
from django.shortcuts import render
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from website.models import CourseInstance, Survey
from django.db.models import transaction
from django.utils import timezone

logger = logging.getLogger(__name__)

@login_required
@staff_member_required
@require_http_methods(["GET"])
def course_instances_api(request):
    """
    API endpoint for retrieving available course instances for surveys
    """
    try:
        # Get active course instances
        course_instances = CourseInstance.objects.filter(is_active=True).order_by('-start_date')
        
        # Format response
        instances_data = []
        for instance in course_instances:
            course_name = instance.course.name_en if instance.course else "Unknown Course"
            start_date = instance.start_date.strftime("%b %d, %Y") if instance.start_date else "No date"
            
            # Format the name to include course name and date
            instance_name = f"{course_name} ({start_date})"
            
            instances_data.append({
                'id': instance.instance_id,
                'name': instance_name,
                'course_id': instance.course.course_id if instance.course else None,
                'start_date': instance.start_date.isoformat() if instance.start_date else None
            })
        
        return JsonResponse({
            'status': 'success',
            'course_instances': instances_data
        })
    except Exception as e:
        logger.error(f"Error fetching course instances: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def create_survey_api(request):
    """
    API endpoint for creating a new survey with questions
    """
    try:
        # Parse request data
        data = json.loads(request.body)
        title = data.get('title')
        course_instance_id = data.get('course_instance_id')
        questions = data.get('questions', [])
        
        # Validate required fields
        if not title or not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': 'Title and course instance are required'
            }, status=400)
        
        if not questions:
            return JsonResponse({
                'status': 'error',
                'message': 'At least one question is required'
            }, status=400)
        
        # Get course instance
        try:
            course_instance = CourseInstance.objects.get(instance_id=course_instance_id)
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'Course instance not found'
            }, status=404)
        
        # Create survey with a transaction
        with transaction.atomic():
            # Create the survey
            survey = Survey.objects.create(
                title=title,
                course_instance=course_instance,
                is_active=True,
                start_date=timezone.now()  # Set start date to now
            )
            
            # Format the questions JSON
            survey_questions = []
            for i, question in enumerate(questions):
                q_data = {
                    'id': i + 1,
                    'text': question.get('text', ''),
                    'type': question.get('type', 'rating'),
                }
                
                # Add options for multiple choice questions
                if question.get('type') == 'multiple_choice' and 'options' in question:
                    q_data['options'] = []
                    for j, option_text in enumerate(question['options']):
                        q_data['options'].append({
                            'id': j + 1,
                            'text': option_text
                        })
                        
                survey_questions.append(q_data)
            
            # Save questions to survey
            survey.questions = survey_questions
            survey.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Survey created successfully',
                'survey_id': survey.survey_id
            })
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': 'Invalid JSON in request body'
        }, status=400)
    except Exception as e:
        logger.error(f"Error creating survey: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)
