<!DOCTYPE html>
{% load static %}
{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}" dir="{{ LANGUAGE_CODE|default:'ltr' }}" class="{% if LANGUAGE_CODE == 'ar' %}rtl-mode{% endif %}">
<head>
    {% block head %}

    {% endblock %}
    <meta charset="UTF-8">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Lexend">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="icon" href="{% static 'images/logo.png' %}"/>
    <link href="{% static 'css/flowbite.min.css' %}" rel="stylesheet"/>
    <script src="{% static 'js/flowbite.js' %}"></script>
    <script class="jsbin" src="{% static 'js/jquery.min.js' %}"></script>
    <script src="{% static 'js/spritespin.js' %}"></script>
    <link rel="stylesheet" type="text/css" href="{% static 'css/base.css' %}">
    <script src="{% static 'js/scripts.js' %}"></script>
    <title>{% block title %}GB Academy{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.3.0/flowbite.min.css" rel="stylesheet" />
    
    {% if debug %}
    <!-- Only use Tailwind CDN in development mode -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#2a51a3',
                    }
                }
            }
        }
    </script>
    {% else %}
    <!-- In production, use the compiled CSS file -->
    <link href="{% static 'css/tailwind.css' %}" rel="stylesheet" />
    {% endif %}
    
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="{% static 'css/custom.css' %}">
    <style>
        @font-face {
            font-family: 'GB Font';
            src: url('{% static "fonts/your-font.woff2" %}') format('woff2');
        }
        :root {
            --primary-color: #2a51a3;
        }
        body {
            font-family: 'Inter', sans-serif;
            background-color: #0a1c3d;
            color: #ffffff;
        }
        .gb-primary {
            background-color: var(--primary-color);
        }
        .gb-primary-text {
            color: var(--primary-color);
        }
        /* Base RTL Support */
        .rtl-mode {
            direction: rtl;
        }

        .rtl-mode .text-left {
            text-align: right;
        }

        .rtl-mode .text-right {
            text-align: left;
        }

        .rtl-mode .space-x-4 > :not([hidden]) ~ :not([hidden]) {
            --tw-space-x-reverse: 1;
        }

        .rtl-mode .space-x-8 > :not([hidden]) ~ :not([hidden]) {
            --tw-space-x-reverse: 1;
        }

        .rtl-mode .ml-auto {
            margin-left: 0;
            margin-right: auto;
        }

        .rtl-mode .mr-auto {
            margin-right: 0;
            margin-left: auto;
        }

        .rtl-mode .ml-2 {
            margin-left: 0;
            margin-right: 0.5rem;
        }

        .rtl-mode .mr-2 {
            margin-right: 0;
            margin-left: 0.5rem;
        }

        .rtl-mode .ml-4 {
            margin-left: 0;
            margin-right: 1rem;
        }

        .rtl-mode .mr-4 {
            margin-right: 0;
            margin-left: 1rem;
        }
        
        /* Enhanced Dropdown Styling */
        select {
            background-color: rgba(255, 255, 255, 0.2);
            border-color: rgba(255, 255, 255, 0.3);
            appearance: none;
            background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg xmlns=\'http://www.w3.org/2000/svg\' fill=\'none\' viewBox=\'0 0 20 20\'%3E%3Cpath stroke=\'%23ffffff\' stroke-linecap=\'round\' stroke-linejoin=\'round\' stroke-width=\'1.5\' d=\'M6 8l4 4 4-4\'/%3E%3C/svg%3E');
            background-repeat: no-repeat;
            background-position: right 0.5rem center;
            background-size: 1.5em 1.5em;
            padding-right: 2.5rem;
        }
        
        select option {
            background-color: #1e3a6b;
            color: white;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body class="{% if LANGUAGE_CODE == 'ar' %}rtl-mode{% endif %} min-h-screen">
    {% csrf_token %}
    {% include 'website/background.html' %}
    {% include 'website/header.html' %}
    
    <!-- Main Content with Gradient Overlay -->
    <div class="relative min-h-screen">
        <!-- Semi-transparent gradient overlay -->
        <div class="fixed top-0 left-0 w-full h-full bg-gradient-to-br from-blue-900/80 to-gray-900/80 -z-5"></div>
        
        <!-- Content -->
        <div class="relative z-10 pb-8">
            {% block content %}{% endblock %}
        </div>
    </div>

    {% block footer %}

    {% endblock %}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/flowbite/2.2.1/flowbite.min.js"></script>
    <script src="{% static 'js/slideshow.js' %}"></script>
    {% block extra_js %}{% endblock %}
</body>
</html>