document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.slide');
    let currentSlide = 0;
    let nextSlide = 1;
    let isAnimating = false;

    function updateSlideClasses() {
        slides.forEach((slide, index) => {
            if (index === currentSlide) {
                slide.classList.add('active');
                slide.classList.remove('previous');
            } else if (index === nextSlide) {
                slide.classList.remove('active', 'previous');
            } else {
                slide.classList.add('previous');
                slide.classList.remove('active');
            }
        });
    }

    function transitionSlide() {
        if (isAnimating) return;
        isAnimating = true;

        // Calculate next slide index
        nextSlide = (currentSlide + 1) % slides.length;

        // Start transition
        slides[nextSlide].style.zIndex = 2;
        slides[currentSlide].style.zIndex = 1;

        // Update classes for transition
        slides[nextSlide].classList.add('active');
        slides[currentSlide].classList.add('previous');
        slides[currentSlide].classList.remove('active');

        // Update current slide after transition
        setTimeout(() => {
            currentSlide = nextSlide;
            isAnimating = false;
        }, 1000);
    }

    // Show first slide
    updateSlideClasses();

    // Change slide every 6 seconds
    setInterval(transitionSlide, 6000);

    // Add mouse interaction
    const slideshowContainer = document.querySelector('.slideshow-container');
    
    slideshowContainer.addEventListener('mouseenter', () => {
        // Add a subtle scale effect on hover
        slides[currentSlide].style.transform = 'scale(1.02)';
    });

    slideshowContainer.addEventListener('mouseleave', () => {
        // Remove scale effect
        slides[currentSlide].style.transform = 'scale(1)';
    });

    // Add this after the DOM content loaded event
    const slideImages = document.querySelectorAll('.slide img');
    slideImages.forEach(img => {
        img.addEventListener('load', () => {
            img.classList.add('loaded');
        });
    });
}); 