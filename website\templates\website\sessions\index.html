{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Sessions & Events" %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-[#1a2542]">
    {% include 'website/header.html' %}
    
    <div class="container mx-auto px-0  pt-0 pb-1">
        <!-- Tabs -->
        <div class="flex space-x-4 mb-2 px-4">
            <button onclick="showTab('sessions')" class="text-white/70 hover:text-white px-3 py-1 rounded-md transition-colors tab-button active flex items-center" data-tab="sessions">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                </svg>
                {% trans "Sessions" %}
            </button>
            <button onclick="showTab('events')" class="text-white/70 hover:text-white px-3 py-1 rounded-md transition-colors tab-button flex items-center" data-tab="events">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"/>
                </svg>
                {% trans "Events" %}
            </button>
            <button onclick="showTab('course_instances')" class="text-white/70 hover:text-white px-3 py-1 rounded-md transition-colors tab-button flex items-center" data-tab="course_instances">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
                {% trans "Course Instances" %}
            </button>
        </div>

        <!-- Sessions Content -->
        <div id="sessions-tab" class="tab-content">
            <!-- Page Header -->
            <div class="flex justify-between items-center mb-2 px-4">
                <div class="flex items-center space-x-2">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <h1 class="text-xl font-bold text-white">{% trans "Sessions Management" %}</h1>
                </div>
                <button type="button" data-action="open-session-modal" class="inline-flex items-center px-3 py-1 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-1 focus:ring-primary">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                    </svg>
                    {% trans "Create Session" %}
                </button>
            </div>

            <!-- Search and Filters -->
            <div class="mb-2 flex items-center space-x-3 px-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" placeholder="{% trans 'Search sessions...' %}" class="w-full px-3 py-1 text-sm bg-white/5 border border-white/10 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-1 focus:ring-primary/50">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="h-5 w-5 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Course" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Type" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Status" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Sessions Table -->
            <div class="bg-white/5 backdrop-blur-md border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead>
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Session ID" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Course" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Trainers" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Room" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Required Equipment" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Start Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "End Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Slot Type" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Location" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-white/10">
                            {% for session in sessions %}
                            <tr class="hover:bg-white/5">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">{{ session.session_id }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ session.course.name_en }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">
                                        {% for trainer in session.trainers.all %}
                                            {{ trainer.user.get_full_name }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                    {{ session.room.name|default:"N/A" }}
                                </td>
                                <td class="px-6 py-4">
                                    <div class="flex flex-wrap gap-1">
                                        {% for equipment in session.required_equipment.all %}
                                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-700 text-gray-300">
                                                {{ equipment.name }} ({{ equipment.code }})
                                            </span>
                                        {% empty %}
                                            <span class="text-sm text-gray-500">{% trans "No equipment required" %}</span>
                                        {% endfor %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ session.start_date|date:"Y-m-d H:i" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ session.end_date|date:"Y-m-d H:i" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ session.get_slot_type_display }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ session.get_location_display }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button type="button" onclick="openSessionModal('{{ session.session_id }}')" class="text-primary hover:text-primary/80">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button type="button" onclick="deleteSession('{{ session.session_id }}')" class="text-red-400 hover:text-red-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="9" class="px-6 py-4 text-center text-white/70">{% trans "No sessions found" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Events Content -->
        <div id="events-tab" class="tab-content hidden">
            <!-- Page Header -->
            <div class="flex justify-between items-center mb-2 px-4">
                <div class="flex items-center space-x-2">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    <h1 class="text-xl font-bold text-white">{% trans "Events Management" %}</h1>
                </div>
                <button type="button" onclick="openEventModal()" class="inline-flex items-center px-3 py-1 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-1 focus:ring-primary">
                    <svg class="h-4 w-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Create Event" %}
                </button>
            </div>

            <!-- Search and Filters -->
            <div class="mb-2 flex items-center space-x-3 px-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" placeholder="{% trans 'Search events...' %}" class="w-full px-3 py-1 text-sm bg-white/5 border border-white/10 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-1 focus:ring-primary/50">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="h-5 w-5 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Type" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Status" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Events Table -->
            <div class="bg-white/5 backdrop-blur-md border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead>
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Event ID" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Name" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Type" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Room" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Start Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "End Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Required Equipment" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Capacity" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-white/10">
                            {% for event in events %}
                            <tr class="hover:bg-white/5">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">{{ event.event_id }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ event.name }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ event.event_type.type }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ event.room.name|default:"N/A" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ event.start_time|date:"Y-m-d H:i" }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ event.end_time|date:"Y-m-d H:i" }}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-white">
                                        {% if event.required_equipment.exists %}
                                            <ul class="list-disc list-inside">
                                                {% for equipment in event.required_equipment.all %}
                                                    <li>{{ equipment.name }} ({{ equipment.code }})</li>
                                                {% endfor %}
                                            </ul>
                                        {% else %}
                                            {% trans "None" %}
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">{{ event.capacity }}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button type="button" onclick="openEventModal('{{ event.event_id }}')" class="text-primary hover:text-primary/80">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                                            </svg>
                                        </button>
                                        <button type="button" onclick="deleteEvent('{{ event.event_id }}')" class="text-red-400 hover:text-red-500">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                            </svg>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% empty %}
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-white/70">{% trans "No events found" %}</td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Course Instances Content -->
        <div id="course_instances-tab" class="tab-content hidden">
            <!-- Page Header -->
            <div class="flex justify-between items-center mb-2 px-4">
                <div class="flex items-center space-x-2">
                    <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                    </svg>
                    <h1 class="text-xl font-bold text-white">{% trans "Course Instances Management" %}</h1>
                </div>
            </div>

            <!-- Search and Filters -->
            <div class="mb-2 flex items-center space-x-3 px-4">
                <div class="flex-1">
                    <div class="relative">
                        <input type="text" placeholder="{% trans 'Search course instances...' %}" class="w-full px-3 py-1 text-sm bg-white/5 border border-white/10 rounded-md text-white placeholder-white/50 focus:outline-none focus:ring-1 focus:ring-primary/50">
                        <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                            <svg class="h-5 w-5 text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="flex space-x-4">
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Course" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Type" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="relative">
                        <button class="px-4 py-2 bg-white/5 border border-white/10 rounded-md text-white flex items-center space-x-2">
                            <span>{% trans "Status" %}</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

            <!-- Course Instances Table -->
            <div class="bg-white/5 backdrop-blur-md border border-white/10 overflow-hidden shadow-lg">
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead>
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Instance ID" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Course" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Type" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Start Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "End Date" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Capacity" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Enrolled" %}</th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Published" %}</th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white/70 uppercase tracking-wider">{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody id="course-instances-table-body" class="divide-y divide-white/10">
                            <!-- Rows will be inserted by JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Session Modal -->
{% include 'website/sessions/modal.html' %}

<!-- Event Modal -->
{% include 'website/sessions/event_modal.html' %}

<!-- Add Course Instance Modal -->
<div id="course-instance-modal" class="modal hidden fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full z-50">
    <div class="relative top-20 mx-auto p-5 border w-3/4 shadow-lg rounded-md bg-white">
        <div class="flex justify-between items-center pb-3">
            <h3 class="text-xl font-semibold text-gray-900" id="course-instance-modal-title">{% trans "Create Course Instance" %}</h3>
            <button onclick="closeCourseInstanceModal()" class="text-gray-400 hover:text-gray-500">
                <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                </svg>
            </button>
        </div>
        <form id="course-instance-form" method="POST" class="mt-4">
            {% csrf_token %}
            <div class="grid grid-cols-2 gap-4">
                <div class="col-span-2">
                    <label class="block text-sm font-medium text-gray-700">{% trans "Course" %}</label>
                    <select name="course" required class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                        <option value="">{% trans "Select a course" %}</option>
                        {% for course in courses %}
                        <option value="{{ course.course_id }}">{{ course.name_en }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{% trans "Course Type" %}</label>
                    <select name="course_type" required class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                        <option value="INDIVIDUAL">{% trans "Individual" %}</option>
                        <option value="GB">{% trans "GB" %}</option>
                        <option value="CORPORATE">{% trans "Corporate" %}</option>
                    </select>
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{% trans "Capacity" %}</label>
                    <input type="number" name="capacity" required min="1" class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{% trans "Start Date" %}</label>
                    <input type="date" name="start_date" required class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                </div>
                <div>
                    <label class="block text-sm font-medium text-gray-700">{% trans "End Date" %}</label>
                    <input type="date" name="end_date" required class="mt-1 block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-primary focus:border-primary sm:text-sm">
                </div>
                <div class="col-span-2">
                    <div class="flex items-center">
                        <input type="checkbox" name="published" id="published" class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded">
                        <label for="published" class="ml-2 block text-sm text-gray-900">
                            {% trans "Publish this course instance" %}
                        </label>
                    </div>
                </div>
            </div>
            <div class="mt-5 flex justify-end space-x-3">
                <button type="button" onclick="closeCourseInstanceModal()" class="px-4 py-2 bg-white text-gray-700 rounded-md hover:bg-gray-50 border">
                    {% trans "Cancel" %}
                </button>
                <button type="submit" class="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary/90">
                    {% trans "Save" %}
                </button>
            </div>
        </form>
    </div>
</div>

<style>
.tab-button {
    @apply text-white/70 hover:text-white;
}
.tab-button.active {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}
</style>

<script>
    let editMode = false;
    let currentSessionId = null;
    let currentEventId = null;

    // Initialize the first tab as active
    document.addEventListener('DOMContentLoaded', function() {
        // Get saved tab from localStorage or default to 'sessions'
        let activeTab = localStorage.getItem('activeSessionTab') || 'sessions';
        
        // Check if we should open the session or event tab based on sessionStorage
        if (sessionStorage.getItem('openSessionModal')) {
            activeTab = 'sessions';
        } else if (sessionStorage.getItem('openEventModal')) {
            activeTab = 'events';
        }
        
        // Apply initial styling to tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            if(button.getAttribute('data-tab') === activeTab) {
                button.classList.add('active');
                button.classList.add('text-white');
                button.classList.remove('text-white/70');
            } else {
                button.classList.remove('active');
                button.classList.add('text-white/70');
                button.classList.remove('text-white');
            }
        });
        
        // Show the selected tab
        showTab(activeTab);
        loadCourseInstances(); // Load course instances when the page loads
        
        // Add event listener for table actions
        document.getElementById('course-instances-table-body').addEventListener('click', handleTableActions);
        
        // Check if we should automatically open a modal
        setTimeout(function() {
            // Check for session modal flag
            if (sessionStorage.getItem('openSessionModal')) {
                // Clear the flag
                sessionStorage.removeItem('openSessionModal');
                // Open the session modal
                openSessionModal();
            }
            
            // Check for event modal flag
            if (sessionStorage.getItem('openEventModal')) {
                // Clear the flag
                sessionStorage.removeItem('openEventModal');
                // Open the event modal
                openEventModal();
            }
        }, 100); // Small delay to ensure the page is ready
    });

    function showTab(tabName) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });
        
        // Remove active class from all tab buttons
        document.querySelectorAll('.tab-button').forEach(button => {
            button.classList.remove('active');
            button.classList.add('text-white/70');
            button.classList.remove('text-white');
        });
        
        // Show selected tab content and activate button
        document.getElementById(`${tabName}-tab`).classList.remove('hidden');
        const activeButton = document.querySelector(`[data-tab="${tabName}"]`);
        activeButton.classList.add('active');
        activeButton.classList.add('text-white');
        activeButton.classList.remove('text-white/70');

        // Save current tab to localStorage
        localStorage.setItem('activeSessionTab', tabName);

        if (tabName === 'course_instances') {
            loadCourseInstances();
        }
    }

    function openSessionModal(sessionId = null) {
        // Instead of directly manipulating the form, we should call the modalOpenSession function from modal.html
        // which properly initializes the form
        const modalOpenFunction = window.modalOpenSession || function() {
            const modal = document.getElementById('sessionModal');
            if (modal) modal.classList.remove('hidden');
        };
        
        // If we have a session ID, dispatch an event for editing
        if (sessionId) {
            // Dispatch a custom event that modal.html can listen for
            document.dispatchEvent(new CustomEvent('edit-session', { 
                detail: { sessionId: sessionId }
            }));
            return;
        }
        
        // For new sessions, call the modal's open function directly
        modalOpenFunction();
    }

    function openEventModal(eventId = null) {
        // If we have an event ID, dispatch a custom event for editing
        if (eventId) {
            document.dispatchEvent(new CustomEvent('edit-event', { 
                detail: { eventId: eventId }
            }));
            return;
        }
        
        // For new events, directly dispatch the event to open the modal
        document.dispatchEvent(new CustomEvent('open-new-event'));
    }

    function closeSessionModal() {
        const modal = document.getElementById('sessionModal');
        modal.classList.add('hidden');
        const form = document.getElementById('sessionForm');
        form.reset();
    }

    function closeEventModal() {
        const modal = document.getElementById('eventModal');
        modal.classList.add('hidden');
        const form = document.getElementById('eventForm');
        form.reset();
    }

    function deleteSession(sessionId) {
        if (confirm('{% trans "Are you sure you want to delete this session?" %}')) {
            fetch('{% url "website:create_session" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    session_id: sessionId,
                    action: 'delete'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    location.reload();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('{% trans "An error occurred while deleting the session" %}');
            });
        }
    }

    function deleteEvent(eventId) {
        if (confirm('{% trans "Are you sure you want to delete this event?" %}')) {
            fetch('{% url "website:create_event" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({
                    event_id: eventId,
                    action: 'delete'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    location.reload();
                } else {
                    showError(data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                showError('{% trans "An error occurred while deleting the event" %}');
            });
        }
    }

    function showError(message) {
        // You can implement your own error display logic here
        alert(message);
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function loadCourseInstances() {
        fetch("{% url 'website:course_instance_api_create' %}")
            .then(response => response.json())
            .then(data => {
                const tableBody = document.getElementById('course-instances-table-body');
                tableBody.innerHTML = ''; // Clear existing rows
                if (data.instances && data.instances.length > 0) {
                    data.instances.forEach(instance => {
                        const row = `
                            <tr class="hover:bg-white/5" data-instance-id="${instance.instance_id}">
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm font-medium text-white">${instance.instance_id}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-white">${instance.course_name}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-white">${instance.course_type}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-white">${instance.start_date}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-white">${instance.end_date}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-white">${instance.capacity}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap"><div class="text-sm text-white">${instance.enrolled_count}</div></td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm">
                                        ${instance.published 
                                            ? `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">{% trans "Published" %}</span>`
                                            : `<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">{% trans "Draft" %}</span>`
                                        }
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-3">
                                        <button type="button" data-action="toggle-publish" class="text-blue-400 hover:text-blue-500">
                                            ${instance.published
                                                ? `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.88 9.88l-3.29-3.29m7.532 7.532l3.29 3.29M3 3l3.59 3.59m0 0A9.953 9.953 0 0112 5c4.478 0 8.268 2.943 9.542 7a10.025 10.025 0 01-4.132 5.411m0 0L21 21"/></svg>`
                                                : `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/></svg>`
                                            }
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                        tableBody.innerHTML += row;
                    });
                } else {
                    tableBody.innerHTML = `<tr><td colspan="9" class="px-6 py-4 text-center text-white/70">{% trans "No course instances found" %}</td></tr>`;
                }
            })
            .catch(error => {
                console.error('Error fetching course instances:', error);
                const tableBody = document.getElementById('course-instances-table-body');
                tableBody.innerHTML = `<tr><td colspan="9" class="px-6 py-4 text-center text-red-400">{% trans "Error loading data." %}</td></tr>`;
            });
    }

    // Course Instance Modal Functions
    function openCourseInstanceModal() {
        const modal = document.getElementById('course-instance-modal');
        const form = document.getElementById('course-instance-form');
        const title = document.getElementById('course-instance-modal-title');

        title.textContent = '{% trans "Create Course Instance" %}';
        form.reset();
        form.removeAttribute('data-instance-id');

        modal.classList.remove('hidden');
    }

    function closeCourseInstanceModal() {
        document.getElementById('course-instance-modal').classList.add('hidden');
    }

    function handleTableActions(event) {
        const targetButton = event.target.closest('button');
        if (!targetButton) return;

        const action = targetButton.dataset.action;
        const instanceId = targetButton.closest('tr').dataset.instanceId;

        if (action === 'toggle-publish' && instanceId) {
            // Get the current language prefix from the URL
            const langPrefix = window.location.pathname.split('/')[1];
            fetch(`/${langPrefix}/api/course-instance/${instanceId}/toggle-publish/`, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({})
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const contentType = response.headers.get("content-type");
                if (contentType && contentType.indexOf("application/json") !== -1) {
                    return response.json();
                } else {
                    return { success: true };
                }
            })
            .then(data => {
                if (data.success) {
                    loadCourseInstances();
                } else {
                    alert('Failed to toggle status: ' + (data.error || 'Unknown error'));
                }
            })
            .catch(error => {
                console.error('Error toggling publish status:', error);
                alert('An error occurred while toggling the publish status.');
            });
        }
    }
</script>
{% endblock %} 