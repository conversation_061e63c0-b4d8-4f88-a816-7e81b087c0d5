#!/usr/bin/env python3
"""
Management command to sync existing GB_Employee records with Oracle data.
This command updates existing GB_Employee records with fresh data from Oracle SOAP API.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from website.models import GB_Employee
from website.soap_client import OracleSOAPClient
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Sync existing GB_Employee records with Oracle data using SOAP client'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making changes',
        )
        parser.add_argument(
            '--employee-id',
            type=str,
            help='Sync only a specific employee ID',
        )
        parser.add_argument(
            '--empty-only',
            action='store_true',
            help='Only sync records that have empty/missing Oracle data',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        specific_employee_id = options['employee_id']
        empty_only = options['empty_only']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no changes will be made')
            )
        
        try:
            # Initialize SOAP client
            soap_client = OracleSOAPClient()
            
            # Find GB_Employee records to sync
            if specific_employee_id:
                gb_employees = GB_Employee.objects.filter(employee_number=specific_employee_id)
                if not gb_employees.exists():
                    raise CommandError(f"GB_Employee with employee number {specific_employee_id} not found")
            else:
                gb_employees = GB_Employee.objects.filter(is_active=True)
            
            # Filter for empty records only if requested
            if empty_only:
                gb_employees = gb_employees.filter(
                    english_name__isnull=True,
                    department__isnull=True,
                    position_name__isnull=True
                ) | gb_employees.filter(
                    english_name='',
                    department='',
                    position_name=''
                )
            
            total_records = gb_employees.count()
            
            if total_records == 0:
                self.stdout.write(
                    self.style.SUCCESS('No GB_Employee records found to sync')
                )
                return
            
            self.stdout.write(
                f'Found {total_records} GB_Employee records to sync with Oracle'
            )
            
            updated_count = 0
            failed_count = 0
            skipped_count = 0
            no_data_count = 0
            
            for gb_employee in gb_employees:
                try:
                    employee_id = gb_employee.employee_number
                    
                    if dry_run:
                        self.stdout.write(
                            f'DRY-RUN: Would sync GB_Employee {employee_id}'
                        )
                        continue
                    
                    self.stdout.write(f'Syncing GB_Employee {employee_id}...')
                    
                    # Check if record already has comprehensive data
                    if not empty_only and gb_employee.english_name and gb_employee.department and gb_employee.position_name:
                        skipped_count += 1
                        self.stdout.write(f'  Skipped - record already has complete data')
                        continue
                    
                    # Fetch fresh data from Oracle
                    try:
                        oracle_data = soap_client.get_employee_data(employee_id)
                        
                        if oracle_data:
                            # Update the record with Oracle data
                            old_data = {
                                'english_name': gb_employee.english_name,
                                'department': gb_employee.department,
                                'position_name': gb_employee.position_name,
                            }
                            
                            # Use the class method to update
                            updated_gb_employee = GB_Employee.create_or_update_from_oracle(oracle_data)
                            
                            # Check what was actually updated
                            changes = []
                            if old_data['english_name'] != updated_gb_employee.english_name:
                                changes.append(f"name: '{old_data['english_name']}' -> '{updated_gb_employee.english_name}'")
                            if old_data['department'] != updated_gb_employee.department:
                                changes.append(f"department: '{old_data['department']}' -> '{updated_gb_employee.department}'")
                            if old_data['position_name'] != updated_gb_employee.position_name:
                                changes.append(f"position: '{old_data['position_name']}' -> '{updated_gb_employee.position_name}'")
                            
                            updated_count += 1
                            if changes:
                                self.stdout.write(f'  Updated: {", ".join(changes)}')
                            else:
                                self.stdout.write(f'  Refreshed data (no changes)')
                            
                            # Sync data to linked CustomUser if exists
                            try:
                                if hasattr(updated_gb_employee, 'user_profile') and updated_gb_employee.user_profile:
                                    updated_gb_employee.user_profile.sync_oracle_data()
                                    self.stdout.write(f'  Synced data to CustomUser profile')
                            except Exception as sync_err:
                                self.stdout.write(
                                    self.style.WARNING(f'  Failed to sync to CustomUser: {str(sync_err)}')
                                )
                            
                        else:
                            no_data_count += 1
                            self.stdout.write(
                                self.style.WARNING(f'  No Oracle data found for employee {employee_id}')
                            )
                        
                    except Exception as oracle_err:
                        failed_count += 1
                        self.stdout.write(
                            self.style.ERROR(f'  Oracle fetch failed: {str(oracle_err)}')
                        )
                    
                except Exception as e:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(f'Error syncing GB_Employee {gb_employee.employee_number}: {str(e)}')
                    )
            
            # Summary
            if not dry_run:
                self.stdout.write('\n' + '='*50)
                self.stdout.write(f'Sync Summary:')
                self.stdout.write(f'  Total records processed: {total_records}')
                self.stdout.write(f'  Records updated: {updated_count}')
                self.stdout.write(f'  Records skipped (already complete): {skipped_count}')
                self.stdout.write(f'  Records with no Oracle data: {no_data_count}')
                self.stdout.write(f'  Failed: {failed_count}')
                
                if failed_count == 0:
                    self.stdout.write(
                        self.style.SUCCESS(f'Successfully synced {updated_count} GB_Employee records!')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'Synced {updated_count} records successfully, {failed_count} failed')
                    )
            
        except Exception as e:
            raise CommandError(f'Sync command failed: {str(e)}') 