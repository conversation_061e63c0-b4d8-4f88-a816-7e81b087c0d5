{% if messages|length >= 1 %}
<div class="fixed top-5 start-5 flex flex-col gap-2 z-50">
    {% for message in messages %}

    {% if message.level == 40 %}
    <div class="w-fit flex items-center gap-2 p-4 text-sm text-red-700 bg-red-100 rounded-lg " role="alert">
        <span class="font-medium">{{ message }}</span>
        <button class="ml-auto -mx-1.5 -my-1.5 text-gray-400 hover:text-gray-900 hover:bg-gray-200 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex h-8 w-8"
                type="button" onclick="this.parentElement.remove()">
            <span class="sr-only">Close</span>
            <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" fill-rule="evenodd"></path>
            </svg>
        </button>
    </div>
    {% endif %}

    {% if message.level == 25 %}
    <div class="w-fit flex items-center gap-2 p-4 text-sm text-green-700 bg-green-100 rounded-lg" role="alert">
        <span class="font-medium">{{ message }}</span>
        <button class="ml-auto -mx-1.5 -my-1.5 text-gray-400 hover:text-gray-900 hover:bg-gray-200 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 inline-flex h-8 w-8"
                type="button" onclick="this.parentElement.remove()">
            <span class="sr-only">Close</span>
            <svg aria-hidden="true" class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z"
                      clip-rule="evenodd" fill-rule="evenodd"></path>
            </svg>
        </button>
    </div>
    {% endif %}

    {% endfor %}
</div>
{%endif%}