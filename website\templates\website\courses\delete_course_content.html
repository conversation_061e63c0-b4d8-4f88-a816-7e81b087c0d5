{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Delete Content" %} - {{ course.name_en }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center space-x-3 mb-8">
            <a href="{% url 'website:course_content_list' course.course_id %}" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-white">{% trans "Delete Content" %}</h1>
        </div>

        <!-- Confirmation Card -->
        <div class="max-w-2xl mx-auto">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
                <div class="p-6 border-b border-white/10 flex items-center">
                    <svg class="w-8 h-8 text-red-500 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <h2 class="text-xl font-semibold text-white">{% trans "Confirm Deletion" %}</h2>
                </div>

                <div class="p-6">
                    <p class="text-white/70 mb-6">
                        {% trans "Are you sure you want to delete the following content?" %}
                    </p>
                    
                    <div class="bg-white/5 border border-white/10 rounded-lg p-4 mb-6">
                        <h3 class="text-white font-medium">{{ course_content.title }}</h3>
                        <p class="text-white/70 text-sm">{{ course_content.get_content_type_display }}</p>
                        {% if course_content.description %}
                        <p class="text-white/70 text-sm mt-1">{{ course_content.description }}</p>
                        {% endif %}
                    </div>
                    
                    <p class="text-amber-400 mb-6">
                        {% trans "This action cannot be undone." %}
                    </p>

                    <div class="flex justify-end space-x-3">
                        <a href="{% url 'website:course_content_list' course.course_id %}" class="px-4 py-2 border border-white/10 hover:bg-white/5 rounded-md text-white">
                            {% trans "Cancel" %}
                        </a>
                        <form method="post">
                            {% csrf_token %}
                            <button type="submit" class="px-4 py-2 bg-red-600 hover:bg-red-700 rounded-md text-white">
                                {% trans "Delete" %}
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 