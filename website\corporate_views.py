import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.http import JsonResponse, HttpResponseRedirect
from django.views.decorators.http import require_http_methods
from django.urls import reverse
from django.db import transaction
from django.utils.translation import gettext_lazy as _
from django.core.paginator import Paginator
from django.core.exceptions import ValidationError
from django.contrib import messages
from django.db.models import Q, Count, F, Value, Char<PERSON>ield, Max, Min
from django.db.models.functions import Concat
from django.utils import timezone
import logging
import json

# Import email utility functions
from corporate_email_utils import (
    send_reservation_cancellation_email,
    send_cancellation_request_email,
    send_cancellation_request_approval_email,
    send_cancellation_request_rejection_email,
    send_new_course_request_notification_email,
    send_course_request_approval_email,
    send_course_request_rejection_email
)

from .models import (
    CustomUser, Course, Holiday, Session, Trainer, Corporate, CorporateAdmin, Reservation,
    CourseInstance, CorporateAdminRequest, CorporateUserCancellationRequest, 
    EmergencyCancellationRequest, CorporateAdminNewCourseRequest, Notification,
    UserType, Survey, SurveyResponse, Questionnaire, QuestionnaireResponse
)

# Add a logger for this module
logger = logging.getLogger(__name__)

def redirect_to_dashboard(request):
    """
    Redirects users to their appropriate dashboard based on their user type
    """
    user = request.user
    
    # Check if user is authenticated
    if not user.is_authenticated:
        return redirect('website:login')
    
    # Redirect based on user type
    if user.is_staff and user.is_superuser:
        return redirect('website:super_admin_dashboard')
    elif user.is_staff:
        return redirect('website:system_admin_dashboard')
    elif hasattr(user, 'user_type') and user.user_type:
        if user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN':
            return redirect('website:corporate_admin_dashboard')
        elif user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE':
            return redirect('website:corporate_user_dashboard')
        elif user.user_type.code == 'EXTERNAL_INDIVIDUAL':
            return redirect('website:individual_user_dashboard')
        elif user.user_type.code == 'INTERNAL_GB':
            return redirect('website:internal_gb_dashboard') # Temporary redirect for testing
        elif user.user_type.code == 'TRAINER':
            return redirect('website:trainer_dashboard')
    
    # Default fallback
    return redirect('website:main')

@login_required
@staff_member_required
def corporate_admins_view(request):
    """View to render the corporate admins management page"""
    # Get all corporates with their related corporate admin information
    corporates = Corporate.objects.all().select_related('corporate_admin')
    
    # Get emergency cancellation requests if they exist in your system
    emergency_requests_count = 0
    try:
        from .models import EmergencyCancellationRequest
        emergency_requests = EmergencyCancellationRequest.objects.filter(status='PENDING').count()
        emergency_requests_count = emergency_requests
    except:
        pass
    
    # Get all corporate users for quick access
    corporate_users = CustomUser.objects.filter(
        user_type__code='EXTERNAL_CORPORATE',
        is_active=True
    )
    
    return render(request, 'website/corporate/corporate_admins.html', {
        'corporates': corporates,
        'corporate_users': corporate_users,
        'emergency_requests_count': emergency_requests_count,
        'page_title': _('Corporate Management')
    })


@login_required
@staff_member_required
def corporate_admin_dashboard(request):
    """View to render the corporate admin dashboard"""
    # Check if user is a corporate admin
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('Only corporate admins can access this page'))
        return redirect('website:main')
    
    # Get all corporates managed by the current staff member
    corporates = Corporate.objects.all().select_related('corporate_admin')
    
    # Get recent reservations from users belonging to any corporate
    recent_reservations = Reservation.objects.filter(
        user__company_name=corporate.legal_name
    ).select_related('user', 'course_instance', 'course_instance__course').order_by('-created_at')[:10]
    
    # Get pending emergency cancellation requests
    emergency_requests_count = 0
    try:
        from .models import EmergencyCancellationRequest
        emergency_requests = EmergencyCancellationRequest.objects.filter(
            status='PENDING',
            reservation__user__company_name=corporate.legal_name
        ).count()
        emergency_requests_count = emergency_requests
    except:
        pass
    
    # Get upcoming sessions for this corporate
    from .models import CourseInstance, Session
    from datetime import datetime, timedelta
    
    today = datetime.now()
    upcoming_sessions = Session.objects.filter(
        course_instance__course_type='CORPORATE',
        course_instance__corporate=corporate,
        start_date__gte=today,
        end_date__lte=today + timedelta(days=30)  # Next 30 days
    ).select_related(
        'course', 'room'
    ).order_by('start_date')[:5]
    
    # Get statistics
    total_reservations = Reservation.objects.filter(
        user__company_name=corporate.legal_name,
        status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
    ).count()
    
    active_courses = CourseInstance.objects.filter(
        course_type='CORPORATE',
        corporate=corporate,
        is_active=True
    ).count()
    
    total_employees = CustomUser.objects.filter(
        company_name=corporate.legal_name,
        is_active=True
    ).count()
    
    available_capacity = corporate.capacity - total_reservations if corporate.capacity > total_reservations else 0
    
    # Get course requests
    course_requests = CorporateAdminRequest.objects.filter(
        corporate=corporate
    ).order_by('-created_at')[:5]
    
    return render(request, 'website/corporate/corporate_dashboard.html', {
        'corporate': corporate,
        'corporates': corporates,
        'recent_reservations': recent_reservations,
        'emergency_requests_count': emergency_requests_count,
        'upcoming_sessions': upcoming_sessions,
        'total_reservations': total_reservations,
        'active_courses': active_courses,
        'total_employees': total_employees,
        'available_capacity': available_capacity,
        'course_requests': course_requests,
        'page_title': _('Corporate Admin Dashboard')
    })


@login_required
@staff_member_required
def corporate_reservations_view(request, corporate_id=None):
    """View to render all reservations for a specific corporate or all corporates"""
    if corporate_id:
        corporate = get_object_or_404(Corporate, corporate_id=corporate_id)
        reservations = Reservation.objects.filter(
            user__company_name=corporate.legal_name
        ).select_related('user', 'course_instance', 'course_instance__course').order_by('-created_at')
        context = {
            'corporate': corporate,
            'reservations': reservations,
            'page_title': f'{corporate.legal_name} - {_("Reservations")}'
        }
    else:
        # Get all reservations from corporate users
        corporates = Corporate.objects.all()
        corporate_names = corporates.values_list('legal_name', flat=True)
        
        reservations = Reservation.objects.filter(
            user__company_name__in=corporate_names
        ).select_related('user', 'course_instance', 'course_instance__course').order_by('-created_at')
        
        context = {
            'corporates': corporates,
            'reservations': reservations,
            'page_title': _('All Corporate Reservations')
        }
    
    return render(request, 'website/corporate/corporate_reservations.html', context)


@staff_member_required
@require_http_methods(["GET"])
def list_corporates(request):
    """API to get all corporates"""
    corporates = Corporate.objects.all().select_related('corporate_admin')
    data = []
    
    for corporate in corporates:
        # Get the number of users associated with this corporate
        user_count = CustomUser.objects.filter(
            company_name=corporate.legal_name,
            is_active=True
        ).count()
        
        # Get the number of active reservations
        reservation_count = Reservation.objects.filter(
            user__company_name=corporate.legal_name,
            status__in=['UPCOMING', 'IN_PROGRESS']
        ).count()
        
        data.append({
            'corporate_id': corporate.corporate_id,
            'admin_name': f"{corporate.corporate_admin.first_name} {corporate.corporate_admin.last_name}",
            'admin_email': corporate.corporate_admin.email,
            'legal_name': corporate.legal_name,
            'phone_number': corporate.phone_number,
            'category': corporate.category,
            'category_display': corporate.get_category_display(),
            'capacity': corporate.capacity,
            'user_count': user_count,
            'active_reservations': reservation_count
        })
    
    return JsonResponse({'corporates': data})


@staff_member_required
@require_http_methods(["GET"])
def corporate_detail(request, corporate_id):
    """API to get corporate details"""
    corporate = get_object_or_404(Corporate, corporate_id=corporate_id)
    
    # Format date of birth to YYYY-MM-DD if it exists
    date_of_birth = None
    if corporate.corporate_admin.date_of_birth:
        date_of_birth = corporate.corporate_admin.date_of_birth.strftime('%Y-%m-%d')
    
    data = {
        'corporate_id': corporate.corporate_id,
        'admin_id': corporate.corporate_admin.id,
        'admin_name': f"{corporate.corporate_admin.first_name} {corporate.corporate_admin.last_name}",
        'admin_email': corporate.corporate_admin.email,
        'admin_username': corporate.corporate_admin.username,
        'legal_name': corporate.legal_name,
        'phone_number': corporate.phone_number,
        'address': corporate.address,
        'national_id': corporate.corporate_admin.national_id,
        'nationality': corporate.corporate_admin.nationality,
        'date_of_birth': date_of_birth,
        'tax_registration_number': corporate.tax_registration_number,
        'commercial_registration_number': corporate.commercial_registration_number,
        'category': corporate.category,
        'capacity': corporate.capacity,
    }
    
    return JsonResponse({'corporate': data})


@staff_member_required
@require_http_methods(["POST"])
@transaction.atomic
def create_corporate(request):
    """API to create a new corporate admin and corporate"""
    try:
        # Print request method and content type for debugging
        print(f"Request method: {request.method}")
        print(f"Content type: {request.content_type}")
        
        # Handle potential invalid JSON
        try:
            data = json.loads(request.body)
            print(f"Received data: {data}")
        except json.JSONDecodeError as e:
            print(f"JSON Decode Error: {str(e)}")
            return JsonResponse({
                'status': 'error',
                'message': f'Invalid JSON data: {str(e)}'
            }, status=400)
        
        # Get or create the corporate user type
        user_type_code = data.get('user_type', 'EXTERNAL_CORPORATE_ADMIN')
        user_type, _ = UserType.objects.get_or_create(
            code=user_type_code,
            defaults={'name': 'External Corporate Admin', 'description': 'External corporate client admin user'}
        )
        
        # Validate username uniqueness
        username = data.get('username')
        if not username:
            print("Username not provided, generating from email")
            username = data.get('email', '').split('@')[0]
            
        if CustomUser.objects.filter(username=username).exists():
            return JsonResponse({
                'status': 'error',
                'message': 'Username already exists'
            }, status=400)
            
        # Validate email uniqueness
        email = data.get('email')
        if not email:
            return JsonResponse({
                'status': 'error',
                'message': 'Email is required'
            }, status=400)
            
        if CustomUser.objects.filter(email=email).exists():
            return JsonResponse({
                'status': 'error',
                'message': 'Email already exists'
            }, status=400)
        
        # Parse date of birth if provided
        date_of_birth = None
        if data.get('date_of_birth'):
            try:
                date_of_birth = data.get('date_of_birth')
                # Handle string date format if needed
                if isinstance(date_of_birth, str):
                    from datetime import datetime
                    date_of_birth = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': f'Invalid date format: {str(e)}'
                }, status=400)
        
        # Create the admin user
        admin_user = CustomUser.objects.create_user(
            email=email,
            username=username,
            password=data.get('password'),
            first_name=data.get('first_name', ''),
            last_name=data.get('last_name', ''),
            user_type=user_type,
            company_name=data.get('legal_name', ''),
            phone_number=data.get('phone_number', ''),
            national_id=data.get('national_id', ''),
            nationality=data.get('nationality', ''),
            date_of_birth=date_of_birth,
            address=data.get('address', ''),
            is_profile_complete=True,
            account_status='ACTIVE'
        )
        
        # Create the corporate entry
        corporate = Corporate.objects.create(
            corporate_admin=admin_user,
            legal_name=data.get('legal_name'),
            address=data.get('address', ''),
            tax_registration_number=data.get('tax_registration_number', ''),
            commercial_registration_number=data.get('commercial_registration_number', ''),
            phone_number=data.get('phone_number', ''),
            capacity=data.get('capacity', 0),
            category=data.get('category', 'A')
        )
        
        # Create the corporate admin record
        CorporateAdmin.objects.create(
            user=admin_user,
            corporate=corporate,
            position=data.get('position', 'Admin'),
            department=data.get('department', ''),
            direct_phone=data.get('phone_number', ''),
            alternative_email=data.get('alternative_email', ''),
            notes=data.get('notes', ''),
            is_active=True
        )
        
        return JsonResponse({
            'status': 'success',
            'message': 'Corporate admin created successfully',
            'corporate_id': corporate.corporate_id
        })
        
    except Exception as e:
        import traceback
        print(f"Error in create_corporate: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@staff_member_required
@require_http_methods(["PUT"])
@transaction.atomic
def update_corporate(request, corporate_id):
    """API to update a corporate"""
    try:
        corporate = get_object_or_404(Corporate, corporate_id=corporate_id)
        data = json.loads(request.body)
        
        # Update admin user details
        admin_user = corporate.corporate_admin
        if 'admin_first_name' in data:
            admin_user.first_name = data['admin_first_name']
        if 'admin_last_name' in data:
            admin_user.last_name = data['admin_last_name']
        if 'admin_email' in data and data['admin_email'] != admin_user.email:
            # Check if email is unique
            if CustomUser.objects.filter(email=data['admin_email']).exclude(id=admin_user.id).exists():
                return JsonResponse({
                    'status': 'error',
                    'message': 'Email already exists'
                }, status=400)
            admin_user.email = data['admin_email']
            admin_user.username = data['admin_email'].split('@')[0]
        if 'admin_phone_number' in data:
            admin_user.phone_number = data['admin_phone_number']
        admin_user.save()
        
        # Update corporate details
        if 'legal_name' in data:
            corporate.legal_name = data['legal_name']
            admin_user.company_name = data['legal_name']
            admin_user.save()
        if 'address' in data:
            corporate.address = data['address']
        if 'tax_registration_number' in data:
            corporate.tax_registration_number = data['tax_registration_number']
        if 'commercial_registration_number' in data:
            corporate.commercial_registration_number = data['commercial_registration_number']
        if 'phone_number' in data:
            corporate.phone_number = data['phone_number']
        if 'capacity' in data:
            corporate.capacity = data['capacity']
        if 'category' in data:
            corporate.category = data['category']
        
        corporate.save()
        
        # Update CorporateAdmin record if it exists
        try:
            corporate_admin = CorporateAdmin.objects.get(user=admin_user, corporate=corporate)
            
            if 'position' in data:
                corporate_admin.position = data['position']
            if 'department' in data:
                corporate_admin.department = data['department']
            if 'phone_number' in data:
                corporate_admin.direct_phone = data['phone_number']
            if 'alternative_email' in data:
                corporate_admin.alternative_email = data['alternative_email']
            if 'notes' in data:
                corporate_admin.notes = data['notes']
                
            corporate_admin.save()
        except CorporateAdmin.DoesNotExist:
            # Create if it doesn't exist
            CorporateAdmin.objects.create(
                user=admin_user,
                corporate=corporate,
                position=data.get('position', 'Admin'),
                department=data.get('department', ''),
                direct_phone=data.get('phone_number', ''),
                alternative_email=data.get('alternative_email', ''),
                notes=data.get('notes', ''),
                is_active=True
            )
        
        return JsonResponse({
            'status': 'success',
            'message': 'Corporate updated successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@staff_member_required
@require_http_methods(["DELETE"])
@transaction.atomic
def delete_corporate(request, corporate_id):
    """API to delete a corporate"""
    try:
        corporate = get_object_or_404(Corporate, corporate_id=corporate_id)
        
        # Get the admin user
        admin_user = corporate.corporate_admin
        
        # Delete the CorporateAdmin record if it exists
        try:
            corporate_admin = CorporateAdmin.objects.get(user=admin_user, corporate=corporate)
            corporate_admin.delete()
        except CorporateAdmin.DoesNotExist:
            pass
        
        # Delete any reservations associated with this corporate
        from .models import Reservation
        Reservation.objects.filter(user__company_name=corporate.legal_name).delete()
        
        # Delete any corporate users (users with the same company name)
        CustomUser.objects.filter(company_name=corporate.legal_name).exclude(id=admin_user.id).delete()
        
        # Delete the corporate
        corporate.delete()
        
        # Finally, delete the admin user
        admin_user.delete()
        
        return JsonResponse({
            'status': 'success',
            'message': 'Corporate and associated admin user deleted successfully'
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@staff_member_required
@require_http_methods(["GET"])
def get_corporate_users(request, corporate_id):
    """API to get all users associated with a corporate"""
    corporate = get_object_or_404(Corporate, corporate_id=corporate_id)
    
    # Find all users with the same company name as the corporate
    corporate_users = CustomUser.objects.filter(
        company_name=corporate.legal_name,
        is_active=True
    ).exclude(id=corporate.corporate_admin.id)  # Exclude the admin user
    
    users_data = []
    for user in corporate_users:
        users_data.append({
            'id': user.id,
            'name': f"{user.first_name} {user.last_name}",
            'email': user.email,
            'phone_number': user.phone_number,
            'user_type': user.user_type.name if user.user_type else 'Unknown'
        })
    
    return JsonResponse({'users': users_data})


@staff_member_required
@require_http_methods(["POST"])
@transaction.atomic
def process_corporate_form(request):
    """Process the corporate admin creation form similar to registration"""
    try:
        # Get form data
        admin_first_name = request.POST.get('admin_first_name')
        admin_last_name = request.POST.get('admin_last_name')
        admin_email = request.POST.get('admin_email')
        admin_username = request.POST.get('admin_username') or request.POST.get('admin_email').split('@')[0]
        admin_password = request.POST.get('admin_password')
        
        # Check if username or email already exists
        if CustomUser.objects.filter(username=admin_username).exists():
            messages.error(request, 'Username already exists')
            return redirect('website:corporate_admins_view')
            
        if CustomUser.objects.filter(email=admin_email).exists():
            messages.error(request, 'Email already exists')
            return redirect('website:corporate_admins_view')
        
        # Get corporate details
        legal_name = request.POST.get('legal_name')
        phone_number = request.POST.get('phone_number', '')
        address = request.POST.get('address', '')
        tax_registration_number = request.POST.get('tax_registration_number', '')
        commercial_registration_number = request.POST.get('commercial_registration_number', '')
        capacity = request.POST.get('capacity', 10)
        category = request.POST.get('category', 'A')
        
        # Personal details
        national_id = request.POST.get('national_id', '')
        nationality = request.POST.get('nationality', '')
        date_of_birth = request.POST.get('date_of_birth')
        if date_of_birth:
            from datetime import datetime
            try:
                date_of_birth = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            except ValueError:
                date_of_birth = None
        
        # Get or create the user type
        user_type, _ = UserType.objects.get_or_create(
            code='EXTERNAL_CORPORATE_ADMIN',
            defaults={'name': 'External Corporate Admin', 'description': 'External corporate client admin user'}
        )
        
        # Create the user
        admin_user = CustomUser.objects.create_user(
            email=admin_email,
            username=admin_username,
            password=admin_password,
            first_name=admin_first_name,
            last_name=admin_last_name,
            user_type=user_type,
            company_name=legal_name,
            phone_number=phone_number,
            national_id=national_id,
            nationality=nationality,
            date_of_birth=date_of_birth,
            address=address,
            is_profile_complete=True,
            account_status='ACTIVE'
        )
        
        # Create the corporate entry
        corporate = Corporate.objects.create(
            corporate_admin=admin_user,
            legal_name=legal_name,
            address=address,
            tax_registration_number=tax_registration_number,
            commercial_registration_number=commercial_registration_number,
            phone_number=phone_number,
            capacity=capacity,
            category=category
        )
        
        # Create the corporate admin record
        CorporateAdmin.objects.create(
            user=admin_user,
            corporate=corporate,
            position=request.POST.get('position', 'Admin'),
            department=request.POST.get('department', ''),
            direct_phone=phone_number,
            alternative_email=request.POST.get('alternative_email', ''),
            notes=request.POST.get('notes', ''),
            is_active=True
        )
        
        messages.success(request, 'Corporate admin created successfully')
        return redirect('website:corporate_admins_view')
        
    except Exception as e:
        import traceback
        print(f"Error in process_corporate_form: {str(e)}")
        print(traceback.format_exc())
        messages.error(request, f'Error creating corporate admin: {str(e)}')
        return redirect('website:corporate_admins_view')


@staff_member_required
@require_http_methods(["POST"])
@transaction.atomic
def update_corporate_form(request):
    """Process the corporate admin update form similar to registration"""
    try:
        # Get the corporate ID from the form
        corporate_id = request.POST.get('corporate_id')
        if not corporate_id:
            messages.error(request, 'Corporate ID is required')
            return redirect('website:corporate_admins_view')
            
        # Get the corporate object
        try:
            corporate = Corporate.objects.get(corporate_id=corporate_id)
            admin_user = corporate.corporate_admin
        except Corporate.DoesNotExist:
            messages.error(request, 'Corporate not found')
            return redirect('website:corporate_admins_view')
        
        # Get form data
        admin_first_name = request.POST.get('admin_first_name')
        admin_last_name = request.POST.get('admin_last_name')
        admin_email = request.POST.get('admin_email')
        admin_username = request.POST.get('admin_username')
        
        # Check if email has changed and if it exists for another user
        if admin_email != admin_user.email and CustomUser.objects.filter(email=admin_email).exclude(id=admin_user.id).exists():
            messages.error(request, 'Email already exists for another user')
            return redirect('website:corporate_admins_view')
            
        # Check if username has changed and if it exists for another user
        if admin_username and admin_username != admin_user.username and CustomUser.objects.filter(username=admin_username).exclude(id=admin_user.id).exists():
            messages.error(request, 'Username already exists for another user')
            return redirect('website:corporate_admins_view')
        
        # Get corporate details
        legal_name = request.POST.get('legal_name')
        phone_number = request.POST.get('phone_number', '')
        address = request.POST.get('address', '')
        tax_registration_number = request.POST.get('tax_registration_number', '')
        commercial_registration_number = request.POST.get('commercial_registration_number', '')
        capacity = request.POST.get('capacity', 10)
        category = request.POST.get('category', 'A')
        
        # Get personal details
        national_id = request.POST.get('national_id', '')
        nationality = request.POST.get('nationality', '')
        date_of_birth = request.POST.get('date_of_birth')
        if date_of_birth:
            from datetime import datetime
            try:
                date_of_birth = datetime.strptime(date_of_birth, '%Y-%m-%d').date()
            except ValueError:
                date_of_birth = None
        
        # Update the user
        admin_user.first_name = admin_first_name
        admin_user.last_name = admin_last_name
        admin_user.email = admin_email
        if admin_username:
            admin_user.username = admin_username
        admin_user.company_name = legal_name
        admin_user.phone_number = phone_number
        admin_user.national_id = national_id
        admin_user.nationality = nationality
        if date_of_birth:
            admin_user.date_of_birth = date_of_birth
        admin_user.address = address
        admin_user.save()
        
        # Update the corporate entry
        corporate.legal_name = legal_name
        corporate.address = address
        corporate.tax_registration_number = tax_registration_number
        corporate.commercial_registration_number = commercial_registration_number
        corporate.phone_number = phone_number
        corporate.capacity = capacity
        corporate.category = category
        corporate.save()
        
        # Update or create the corporate admin record
        try:
            corporate_admin = CorporateAdmin.objects.get(user=admin_user, corporate=corporate)
            corporate_admin.direct_phone = phone_number
            corporate_admin.save()
        except CorporateAdmin.DoesNotExist:
            CorporateAdmin.objects.create(
                user=admin_user,
                corporate=corporate,
                position='Admin',
                direct_phone=phone_number,
                is_active=True
            )
        
        messages.success(request, 'Corporate admin updated successfully')
        return redirect('website:corporate_admins_view')
        
    except Exception as e:
        import traceback
        print(f"Error in update_corporate_form: {str(e)}")
        print(traceback.format_exc())
        messages.error(request, f'Error updating corporate admin: {str(e)}')
        return redirect('website:corporate_admins_view')


@login_required
def course_request_form(request, course_id=None):
    """View to render the course request form for corporate admins"""
    # Check if user is a corporate admin
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('Only corporate admins can access this page'))
        return redirect('website:main')
    
    # Get course if course_id is provided
    course = None
    if course_id:
        try:
            course = Course.objects.get(course_id=course_id)
        except Course.DoesNotExist:
            messages.error(request, _('Course not found'))
            return redirect('website:courses')
    
    # Get existing requests for this corporate
    existing_requests = CorporateAdminRequest.objects.filter(
        corporate=corporate
    ).order_by('-created_at')
    
    if request.method == 'POST':
        # Process the form submission
        try:
            course_type = request.POST.get('course_type')
            capacity = int(request.POST.get('capacity', 20))
            notes = request.POST.get('notes', '')
            requested_course_id = request.POST.get('course_id')
            
            # Validate data
            if not course_type:
                messages.error(request, _('Please fill in all required fields'))
                return redirect('website:course_request_form')
            
            # Get course if course_id was sent in form
            requested_course = None
            if requested_course_id:
                try:
                    requested_course = Course.objects.get(course_id=requested_course_id)
                except Course.DoesNotExist:
                    messages.error(request, _('Course not found'))
                    return redirect('website:course_request_form')
            
            # Create the request
            course_request = CorporateAdminRequest.objects.create(
                corporate=corporate,
                corporate_admin=corporate_admin,
                course=requested_course,
                course_type=course_type,
                capacity=capacity,
                notes=notes
            )
            
            messages.success(request, _('Course request submitted successfully'))
            
            # Redirect to courses page if this was a request from course detail
            if requested_course_id:
                return redirect('website:course_detail', course_id=requested_course_id)
            
            return redirect('website:course_request_form')
            
        except Exception as e:
            messages.error(request, f'{_("Error submitting request")}: {str(e)}')
            return redirect('website:course_request_form')
    
    return render(request, 'website/corporate/course_request_form.html', {
        'corporate': corporate,
        'existing_requests': existing_requests,
        'course': course,
        'page_title': _('Request a Course')
    })


@login_required
@staff_member_required
def course_requests_list(request):
    """View to list all corporate admin course requests for staff"""
    # Get all course requests
    all_course_requests = CorporateAdminRequest.objects.all().select_related(
        'corporate', 'corporate_admin', 'corporate_admin__user', 'course'
    ).order_by('-created_at')

    # Get all new course requests
    all_new_course_requests = CorporateAdminNewCourseRequest.objects.all().select_related(
        'corporate', 'corporate_admin', 'corporate_admin__user'
    ).order_by('-created_at')

    # Count stats for regular course requests
    pending_count = all_course_requests.filter(status='PENDING').count()
    approved_count = all_course_requests.filter(status='APPROVED').count()
    rejected_count = all_course_requests.filter(status='REJECTED').count()
    pending_cancel_count = all_course_requests.filter(status='PENDING_CANCEL').count()
    cancelled_count = all_course_requests.filter(status='CANCELLED').count()
    
    # Count stats for new course requests
    pending_new_course_count = all_new_course_requests.filter(status='PENDING').count()
    
    return render(request, 'website/corporate/course_requests_list.html', {
        'course_requests': all_course_requests,
        'new_course_requests': all_new_course_requests,
        'pending_count': pending_count,
        'approved_count': approved_count,
        'rejected_count': rejected_count,
        'pending_cancel_count': pending_cancel_count,
        'cancelled_count': cancelled_count,
        'pending_new_course_count': pending_new_course_count,
        'page_title': _('Corporate Course Requests')
    })


@login_required
@staff_member_required
@require_http_methods(["POST"])
def update_course_request_status(request, request_id):
    """API to update the status of a course request"""
    try:
        course_request = get_object_or_404(CorporateAdminRequest, request_id=request_id)
        data = json.loads(request.body)
        
        status = data.get('status')
        admin_notes = data.get('admin_notes', '')
        
        if status not in ['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED']:
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid status'
            }, status=400)
        
        # Handle special case for PENDING_CANCEL requests
        if course_request.status == 'PENDING_CANCEL':
            if status == 'APPROVED':
                # If approving a cancellation request, set status to CANCELLED
                course_request.status = 'CANCELLED'
                course_request.admin_notes = admin_notes + '\n\n' + _('Cancellation approved by system admin on %(date)s') % {
                    'date': timezone.now().strftime('%Y-%m-%d %H:%M')
                }
                course_request.save()
                
                # Cancel all reservations associated with this course instance (if any)
                if course_request.course:
                    # Find all course instances related to this course request
                    course_instances = CourseInstance.objects.filter(
                        course=course_request.course,
                        corporate=course_request.corporate
                    )
                    
                    # Update all reservations for these instances
                    canceled_count = 0
                    deactivated_instances = 0
                    deleted_sessions = 0
                    
                    for instance in course_instances:
                        # Cancel all reservations for this instance
                        reservations = Reservation.objects.filter(
                            course_instance=instance,
                            status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
                        )
                        for reservation in reservations:
                            reservation.status = 'CANCELLED'
                            reservation.cancellation_reason = _('Course cancelled by corporate admin and approved by system admin')
                            reservation.cancelled_at = timezone.now()
                            reservation.cancelled_by = request.user
                            reservation.save()
                            canceled_count += 1
                        
                        # Delete all sessions for this instance
                        try:
                            from .models import Session
                            sessions = Session.objects.filter(course_instance=instance)
                            session_count = sessions.count()
                            sessions.delete()
                            deleted_sessions += session_count
                        except Exception as session_error:
                            print(f"Error deleting sessions for instance {instance.instance_id}: {str(session_error)}")
                        
                        # Deactivate the course instance
                        instance.is_active = False
                        instance.deactivation_reason = _('Cancelled by corporate admin and approved by system admin')
                        instance.deactivated_at = timezone.now()
                        instance.deactivated_by = request.user.username
                        instance.save()
                        deactivated_instances += 1
                    
                    admin_note = _('Approved cancellation request. %(count)d reservations were automatically cancelled, %(instances)d course instances were deactivated, and %(sessions)d sessions were deleted.') % {
                        'count': canceled_count,
                        'instances': deactivated_instances,
                        'sessions': deleted_sessions
                    }
                    
                    return JsonResponse({
                        'status': 'success',
                        'message': 'Course request cancelled, all reservations cancelled, and course instances deactivated',
                        'admin_note': admin_note,
                        'cancelled_reservations': canceled_count,
                        'deactivated_instances': deactivated_instances,
                        'deleted_sessions': deleted_sessions
                    })
                
                return JsonResponse({
                    'status': 'success',
                    'message': 'Cancellation request approved'
                })
            
            elif status == 'REJECTED':
                # If rejecting a cancellation request, revert to APPROVED
                course_request.status = 'APPROVED'
                course_request.admin_notes = admin_notes + '\n\n' + _('Cancellation rejected by system admin on %(date)s') % {
                    'date': timezone.now().strftime('%Y-%m-%d %H:%M')
                }
                course_request.save()
                
                return JsonResponse({
                    'status': 'success',
                    'message': 'Cancellation request rejected'
                })
        else:
            # Normal status update for regular requests
            course_request.status = status
            course_request.admin_notes = admin_notes
            course_request.save()
            
            return JsonResponse({
                'status': 'success',
                'message': 'Course request updated successfully'
            })
        
    except Exception as e:
        import traceback
        print(f"Error in update_course_request_status: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)


@staff_member_required
@require_http_methods(["GET"])
def course_requests_api(request):
    """API to get all pending corporate admin course requests for notification panel"""
    try:
        # Get all pending and pending_cancel course requests
        pending_requests = CorporateAdminRequest.objects.filter(
            status__in=['PENDING', 'PENDING_CANCEL']
        ).select_related(
            'corporate', 'corporate_admin', 'corporate_admin__user', 'course'
        ).order_by('-created_at')
        
        # Get all pending new course requests
        pending_new_course_requests = CorporateAdminNewCourseRequest.objects.filter(
            status__in=['PENDING', 'PENDING_CANCEL']
        ).select_related(
            'corporate', 'corporate_admin', 'corporate_admin__user'
        ).order_by('-created_at')
        
        # Count of requests by status
        pending_count = pending_requests.filter(status='PENDING').count()
        pending_cancel_count = pending_requests.filter(status='PENDING_CANCEL').count()
        pending_new_course_count = pending_new_course_requests.filter(status='PENDING').count()
        pending_new_course_cancel_count = pending_new_course_requests.filter(status='PENDING_CANCEL').count()
        total_count = pending_count + pending_cancel_count + pending_new_course_count + pending_new_course_cancel_count
        
        # Format the requests for the API response
        requests_data = []
        
        # Process regular course requests
        for req in pending_requests:
            requests_data.append({
                'request_id': req.request_id,
                'request_type': 'regular',
                'corporate': {
                    'id': req.corporate.corporate_id,
                    'legal_name': req.corporate.legal_name
                },
                'corporate_admin': {
                    'id': req.corporate_admin.user.id,
                    'name': f"{req.corporate_admin.user.first_name} {req.corporate_admin.user.last_name}",
                    'email': req.corporate_admin.user.email
                },
                'course': {
                    'id': req.course.course_id if req.course else None,
                    'name': req.course.name_en if req.course else None
                } if req.course else None,
                'course_type': req.course_type,
                'course_type_display': req.get_course_type_display(),
                'capacity': req.capacity,
                'notes': req.notes,
                'created_at': req.created_at.isoformat(),
                'status': req.status,
                'status_display': req.get_status_display(),
                'is_cancellation_request': req.status == 'PENDING_CANCEL'
            })
        
        # Process new course requests
        for req in pending_new_course_requests:
            requests_data.append({
                'request_id': req.request_id,
                'request_type': 'new_course',
                'corporate': {
                    'id': req.corporate.corporate_id,
                    'legal_name': req.corporate.legal_name
                },
                'corporate_admin': {
                    'id': req.corporate_admin.user.id,
                    'name': f"{req.corporate_admin.user.first_name} {req.corporate_admin.user.last_name}",
                    'email': req.corporate_admin.user.email
                },
                'course_name': req.course_name,
                'course_description': req.course_description,
                'course_type': req.course_type,
                'course_type_display': req.get_course_type_display(),
                'capacity': req.capacity,
                'notes': req.notes,
                'created_at': req.created_at.isoformat(),
                'status': req.status,
                'status_display': req.get_status_display(),
                'is_new_course_request': True,
                'is_cancellation_request': req.status == 'PENDING_CANCEL'
            })
        
        # Sort all requests by created_at descending
        requests_data.sort(key=lambda x: x['created_at'], reverse=True)
        
        # Create a more robust deduplication key that includes course information
        seen_requests = set()
        unique_requests = []
        
        for req in requests_data:
            # Create a unique key based on corporate, request type, status, and course
            corp_id = req['corporate']['id']
            req_type = req['request_type']
            status = req['status']
            
            # Include course information in the key
            if req_type == 'new_course':
                course_key = req.get('course_name', '')
            else:
                course_id = req.get('course', {}).get('id') if req.get('course') else None
                course_key = str(course_id) if course_id else 'no_course'
            
            # Create a deduplication key
            dedup_key = f"{corp_id}_{req_type}_{status}_{course_key}"
            
            if dedup_key not in seen_requests:
                seen_requests.add(dedup_key)
                unique_requests.append(req)
        
        return JsonResponse({
            'status': 'success',
            'pending_count': pending_count,
            'pending_cancel_count': pending_cancel_count + pending_new_course_cancel_count,
            'pending_new_course_count': pending_new_course_count,
            'total_count': total_count,
            'pending_requests': unique_requests
        })
        
    except Exception as e:
        import traceback
        print(f"Error in course_requests_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def corporate_admin_calendar(request):
    """View to render the corporate calendar for corporate admins"""
    # Check if user is a corporate admin
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('Only corporate admins can access this page'))
        return redirect('website:main')
    
    # Get sessions for this corporate (both booked and available)
    from .models import CourseInstance, Session
    
    # Get reservations made by users from this corporate
    corporate_reservations = Reservation.objects.filter(
        user__company_name=corporate.legal_name,
        status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
    ).select_related('course_instance')
    
    # Get course instances that are corporate type for this corporate
    corporate_course_instances = CourseInstance.objects.filter(
        course_type='CORPORATE',
        corporate=corporate,
        is_active=True
    )
    
    # Get the sessions for these course instances
    corporate_sessions = Session.objects.filter(
        course_instance__in=corporate_course_instances
    ).exists()
    
    # Get stats for the sidebar
    total_reservations = Reservation.objects.filter(
        user__company_name=corporate.legal_name,
        status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
    ).count()
    
    active_courses = CourseInstance.objects.filter(
        course_type='CORPORATE',
        corporate=corporate,
        is_active=True
    ).count()
    
    available_capacity = corporate.capacity - total_reservations if corporate.capacity > total_reservations else 0
    
    return render(request, 'website/calendar/corporate_admin_calendar.html', {
        'corporate': corporate,
        'corporate_sessions': corporate_sessions,
        'total_reservations': total_reservations,
        'available_capacity': available_capacity,
        'active_courses': active_courses,
        'page_title': _('Corporate Course Calendar')
    })


@login_required
def corporate_calendar_events_api(request):
    """API to get calendar events for a corporate admin"""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'User is not a corporate admin'
            }, status=403)
        
        from .models import CourseInstance, Session, Reservation
        from datetime import datetime, timedelta
        
        # Get start and end dates from request or use default (±3 months)
        start_date = request.GET.get('start')
        end_date = request.GET.get('end')
        
        if start_date:
            start_date = datetime.strptime(start_date, '%Y-%m-%d')
        else:
            start_date = datetime.now() - timedelta(days=90)
            
        if end_date:
            end_date = datetime.strptime(end_date, '%Y-%m-%d')
        else:
            end_date = datetime.now() + timedelta(days=90)
        
        # Get corporate course instances
        corporate_course_instances = CourseInstance.objects.filter(
            course_type='CORPORATE',
            corporate=corporate,
            is_active=True
        )
        
        # Get sessions for these course instances
        corporate_sessions = Session.objects.filter(
            course_instance__in=corporate_course_instances,
            start_date__gte=start_date,
            end_date__lte=end_date
        ).select_related('course')
        
        # Format sessions for calendar display
        events = []
        
        for session in corporate_sessions:
            course_name = session.course.name_en
            trainer_names = ", ".join([f"{trainer.user.first_name} {trainer.user.last_name}" for trainer in session.trainers.all()]) or "TBD"
            room_name = session.room.name if session.room else "TBD"
            
            # Get attendees (employees of this corporate)
            attendees = []
            for course_instance in session.course_instance.all():
                if course_instance.corporate == corporate:
                    reservations = Reservation.objects.filter(
                        course_instance=course_instance,
                        user__company_name=corporate.legal_name,
                        status__in=['UPCOMING', 'IN_PROGRESS']
                    ).select_related('user')
                    
                    for reservation in reservations:
                        attendees.append({
                            'id': reservation.user.id,
                            'name': f"{reservation.user.first_name} {reservation.user.last_name}",
                            'email': reservation.user.email,
                            'reservation_id': reservation.reservation_id,
                            'status': reservation.status
                        })
            
            # Calculate occupancy rate
            course_instance = session.course_instance.filter(corporate=corporate).first()
            total_seats = course_instance.capacity if course_instance else 0
            reserved_seats = len(attendees)
            occupancy_rate = round((reserved_seats / total_seats * 100) if total_seats > 0 else 0, 1)
            
            events.append({
                'id': session.session_id,
                'title': f"{course_name} - {trainer_names} - {room_name}",
                'start': session.start_date.isoformat(),
                'end': session.end_date.isoformat(),
                'type': 'session',
                'is_corporate': True,
                'course_id': session.course.course_id,
                'room_id': session.room.room_id if session.room else None,
                'capacity': total_seats,
                'available_seats': total_seats - reserved_seats,
                'occupancy_rate': occupancy_rate,
                'attendees': attendees,
                'course_details': {
                    'name': session.course.name_en,
                    'description': session.course.description_en,
                    'category': session.course.category
                }
            })
        
        # Add any special corporate events (corporate-specific holidays, assessment days, etc.)
        try:
            from .models import CorporateEvent
            corporate_events = CorporateEvent.objects.filter(
                corporate=corporate,
                start_time__gte=start_date,
                end_time__lte=end_date
            )
            
            for event in corporate_events:
                events.append({
                    'id': f"corp_event_{event.id}",
                    'title': event.title,
                    'start': event.start_time.isoformat(),
                    'end': event.end_time.isoformat(),
                    'type': 'event',
                    'is_corporate': True,
                    'description': event.description,
                    'location': event.location
                })
        except:
            # CorporateEvent model might not exist
            pass
        
        # Also get any general holidays that might affect corporate sessions
        try:
            from .models import Holiday
            holidays = Holiday.objects.filter(
                date__gte=start_date,
                date__lte=end_date
            )
            
            for holiday in holidays:
                events.append({
                    'id': f"holiday_{holiday.holiday_id}",
                    'title': holiday.name,
                    'start': holiday.date.isoformat(),
                    'end': holiday.date.isoformat(),
                    'type': 'holiday',
                    'allDay': True,
                    'description': holiday.description,
                    'color': '#ff5c5c'  # Red color for holidays
                })
        except:
            pass
        
        return JsonResponse({'events': events})
    
    except Exception as e:
        import traceback
        print(f"Error in corporate_calendar_events_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def my_corporate_requests_view(request):
    """View to render the page showing requests for the current corporate admin."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('Only corporate admins can access this page.'))
        return redirect('website:main')

    return render(request, 'website/corporate/my_requests.html', {
        'page_title': _('My Course Requests'),
        'corporate': corporate_admin.corporate
    })


@login_required
@require_http_methods(["GET"])
def my_corporate_requests_api(request):
    """API endpoint to fetch CorporateAdminRequest entries for the logged-in corporate admin."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User is not a corporate admin'
        }, status=403)

    try:
        # Get regular course requests
        requests = CorporateAdminRequest.objects.filter(
            corporate=corporate
        ).select_related('course').order_by('-created_at')

        # Get new course requests
        new_course_requests = CorporateAdminNewCourseRequest.objects.filter(
            corporate=corporate
        ).order_by('-created_at')

        data = []
        
        # Process regular course requests
        for req in requests:
            data.append({
                'request_id': req.request_id,
                'request_type': 'regular', # Add type for frontend differentiation
                'course_name': req.course.name_en if req.course else _('General Request'),
                'course_type': req.get_course_type_display(),
                'capacity': req.capacity,
                'notes': req.notes,
                'status': req.get_status_display(),
                'status_code': req.status, # Added status code for styling
                'admin_notes': req.admin_notes,
                'created_at': req.created_at.strftime('%Y-%m-%d %H:%M'),
                'updated_at': req.updated_at.strftime('%Y-%m-%d %H:%M'),
            })
        
        # Process new course requests
        for req in new_course_requests:
            data.append({
                'request_id': req.request_id,
                'request_type': 'new_course', # Add type for frontend differentiation
                'course_name': req.course_name,
                'course_description': req.course_description,
                'course_type': req.get_course_type_display(),
                'capacity': req.capacity,
                'notes': req.notes,
                'status': req.get_status_display(),
                'status_code': req.status, # Added status code for styling
                'admin_notes': req.admin_notes,
                'created_at': req.created_at.strftime('%Y-%m-%d %H:%M'),
                'updated_at': req.updated_at.strftime('%Y-%m-%d %H:%M'),
            })
        
        # Sort all requests by created_at descending
        data.sort(key=lambda x: x['created_at'], reverse=True)

        return JsonResponse({
            'status': 'success',
            'requests': data
        })

    except Exception as e:
        import traceback
        print(f"Error in my_corporate_requests_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def my_corporate_users_view(request):
    """View to render the page showing users belonging to the corporate admin's company."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('Only corporate admins can access this page.'))
        return redirect('website:main')
    
    # Get the EXTERNAL_CORPORATE_TRAINEE user type for the form
    trainee_user_type = UserType.objects.get(code='EXTERNAL_CORPORATE_TRAINEE')
    
    return render(request, 'website/corporate/my_corporate_users.html', {
        'page_title': _('My Corporate Users'),
        'corporate': corporate,
        'trainee_user_type': trainee_user_type
    })


@login_required
@require_http_methods(["GET"])
def corporate_users_api(request):
    """API endpoint to fetch users belonging to the corporate admin's company."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User is not a corporate admin'
        }, status=403)
    
    # Get all users associated with this corporate
    users = CustomUser.objects.filter(
        company_name=corporate.legal_name,
        is_active=True
    ).order_by('first_name', 'last_name')
    
    # Format user data for the API response
    users_data = []
    for user in users:
        users_data.append({
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'user_type': user.user_type.code if user.user_type else None,
            'user_type_display': user.user_type.name if user.user_type else None,
            'date_joined': user.date_joined.strftime('%Y-%m-%d')
        })
    
    return JsonResponse({'users': users_data})


@login_required
@require_http_methods(["POST"])
@transaction.atomic
def add_corporate_user(request):
    """API endpoint to add a new user to the corporate and optionally enroll them in a course."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User is not a corporate admin'
        }, status=403)
    
    try:
        data = json.loads(request.body)
        
        # Get email and extract username from it
        email = data.get('email')
        if not email:
            return JsonResponse({
                'status': 'error',
                'message': 'Email is required'
            }, status=400)
        
        # Get optional course instance ID
        course_instance_id = data.get('course_instance_id')
        
        # Check if user already exists with this email
        user_exists = CustomUser.objects.filter(email=email).exists()
        user = None
        
        if user_exists:
            # Get the existing user
            user = CustomUser.objects.get(email=email)
            
            # Verify the user belongs to this corporate
            if user.company_name != corporate.legal_name:
                # If the user exists but belongs to another corporate, update company name
                user.company_name = corporate.legal_name
                user.save()
                
            creation_message = 'Existing user found'
        else:
            # Extract username from email (part before @)
            username = email.split('@')[0]
            
            # Check if username already exists, append numbers if needed
            base_username = username
            counter = 1
            while CustomUser.objects.filter(username=username).exists():
                username = f"{base_username}{counter}"
                counter += 1
            
            # Use username as password
            password = username
            
            # Get the user type for EXTERNAL_CORPORATE_TRAINEE
            user_type = UserType.objects.get(code='EXTERNAL_CORPORATE_TRAINEE')
            
            # Create the new user with hardcoded name and auto-generated password
            user = CustomUser.objects.create_user(
                username=username,
                email=email,
                password=password,
                first_name="John",
                last_name="Doe",
                user_type=user_type,
                is_active=True,
                company_name=corporate.legal_name,
                force_password_reset=True  # Force password reset on first login
            )
            
            creation_message = 'User created successfully'
        
        # Handle course enrollment if a course instance was provided
        enrollment_message = None
        if course_instance_id and user:
            try:
                # Get the course instance
                course_instance = CourseInstance.objects.get(
                    instance_id=course_instance_id, 
                    is_active=True,
                    published=True,  # Only allow enrollment in published course instances
                    course_type='CORPORATE',
                    corporate=corporate
                )
                
                # Check if there are available seats
                reserved_seats = Reservation.objects.filter(
                    course_instance=course_instance,
                    status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
                ).count()
                
                if reserved_seats >= course_instance.capacity:
                    enrollment_message = 'Course is fully booked. User was created but not enrolled.'
                else:
                    # Check if user is already enrolled in this instance
                    if Reservation.objects.filter(
                        user=user, 
                        course_instance=course_instance
                    ).exclude(
                        status='CANCELLED'
                    ).exists():
                        enrollment_message = 'User is already enrolled in this course.'
                    else:
                        # Check if this user previously had a cancelled reservation for this course
                        existing_cancelled = Reservation.objects.filter(
                            user=user,
                            course_instance=course_instance,
                            status='CANCELLED'
                        ).first()
                        
                        if existing_cancelled:
                            # Update existing cancelled reservation to UPCOMING status
                            existing_cancelled.status = 'UPCOMING'
                            existing_cancelled.save(update_fields=['status'])
                            reservation = existing_cancelled
                        else:
                            # Create a reservation with UPCOMING status for corporate users
                            reservation = Reservation.objects.create(
                                user=user,
                                course_instance=course_instance,
                                status='UPCOMING'
                            )
                        
                        enrollment_message = 'User successfully enrolled in the course.'
                        
            except CourseInstance.DoesNotExist:
                enrollment_message = 'Course instance not found or not available.'
        
        user_data = {
            'user_id': user.id,
            'username': user.username,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'email': user.email,
            'password': password if not user_exists else None,  # Only return password for new users
            'user_type': user.user_type.code if user.user_type else None,
            'user_type_display': user.user_type.name if user.user_type else None,
            'date_joined': user.date_joined.strftime('%Y-%m-%d')
        }
        
        return JsonResponse({
            'status': 'success',
            'message': creation_message,
            'enrollment_message': enrollment_message,
            'user': user_data
        })
    
    except Exception as e:
        import traceback
        print(f"Error in add_corporate_user: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["DELETE"])
@transaction.atomic
def delete_corporate_user(request, user_id):
    """API endpoint to delete a user from the corporate."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User is not a corporate admin'
        }, status=403)
    
    try:
        # Get the user to delete
        user = CustomUser.objects.get(id=user_id, company_name=corporate.legal_name)
        
        # Instead of deleting, deactivate the user
        user.is_active = False
        user.save()
        
        return JsonResponse({
            'status': 'success',
            'message': 'User deactivated successfully'
        })
    
    except CustomUser.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User not found or not associated with your corporate'
        }, status=404)
    except Exception as e:
        import traceback
        print(f"Error in delete_corporate_user: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def force_password_reset(request):
    """
    View to handle forced password reset for new users.
    When a corporate admin creates a new user, they are required to change
    their password on first login and complete their profile.
    """
    if request.method == 'POST':
        current_password = request.POST.get('current_password')
        new_password = request.POST.get('new_password')
        confirm_password = request.POST.get('confirm_password')
        
        # Get profile information
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        phone_number = request.POST.get('phone_number')
        nationality = request.POST.get('nationality')
        national_id = request.POST.get('national_id')
        date_of_birth_str = request.POST.get('date_of_birth') # Renamed to avoid conflict
        address = request.POST.get('address')
        
        # Validate all fields
        required_fields = {
            _('Current Password'): current_password,
            _('New Password'): new_password,
            _('Confirm New Password'): confirm_password,
            _('First name'): first_name,
            _('Last name'): last_name,
            _('Phone number'): phone_number,
            _('Nationality'): nationality,
            _('National id'): national_id,
            _('Date of birth'): date_of_birth_str,
            _('Address'): address,
        }
        
        for field_name, value in required_fields.items():
            if not value:
                messages.error(request, _('{field} is required').format(field=field_name))
                return render(request, 'website/corporate/force_password_reset.html', {
                    'page_title': _('Reset Password')
                })
        
        if not request.user.check_password(current_password):
            messages.error(request, _('Current password is incorrect'))
        elif new_password != confirm_password:
            messages.error(request, _('New passwords do not match'))
        # Add password complexity validation (at least 8 chars, letters, numbers, special chars)
        elif len(new_password) < 8 or not any(char.isalpha() for char in new_password) or \
             not any(char.isdigit() for char in new_password) or \
             not any(not char.isalnum() for char in new_password):
            messages.error(request, _('Password must be at least 8 characters and include letters, numbers, and special characters.'))
        else:
            # Update the password
            request.user.set_password(new_password)
            
            # Update profile information
            request.user.first_name = first_name
            request.user.last_name = last_name
            request.user.phone_number = phone_number
            request.user.nationality = nationality
            request.user.national_id = national_id
            request.user.address = address
            
            # Handle date of birth
            if date_of_birth_str: # Check again as it was validated for emptiness before
                from datetime import datetime
                try:
                    date_obj = datetime.strptime(date_of_birth_str, '%Y-%m-%d').date()
                    request.user.date_of_birth = date_obj
                except ValueError:
                    messages.error(request, _('Invalid date format for Date of birth. Please use YYYY-MM-DD.'))
                    return render(request, 'website/corporate/force_password_reset.html', {
                        'page_title': _('Reset Password')
                    })
            
            # Remove the force password reset flag
            request.user.force_password_reset = False
            request.user.is_profile_complete = True
            request.user.save()
            
            messages.success(request, _('Profile and password updated successfully'))
            
            # Re-authenticate the user to prevent logout
            user = authenticate(username=request.user.username, password=new_password)
            if user:
                login(request, user)
                
            # Redirect to main page
            return redirect('website:main')
    
    return render(request, 'website/corporate/force_password_reset.html', {
        'page_title': _('Reset Password')
    })


@login_required
@require_http_methods(["GET"])
def get_corporate_course_instances(request):
    """API endpoint to fetch course instances belonging to the corporate admin's company."""
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': 'User is not a corporate admin'
        }, status=403)
    
    # Get course instances for this corporate
    course_instances = CourseInstance.objects.filter(
        course_type='CORPORATE',
        corporate=corporate,
        is_active=True,
        published=True  # Only show published course instances for enrollment
    ).select_related('course')
    
    # Format course instances data for the API response
    instances_data = []
    for instance in course_instances:
        # Calculate available seats
        reserved_seats = Reservation.objects.filter(
            course_instance=instance,
            status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
        ).count()
        
        available_seats = instance.capacity - reserved_seats if instance.capacity > reserved_seats else 0
        
        instances_data.append({
            'instance_id': instance.instance_id,
            'course_name': instance.course.name_en,
            'start_date': instance.start_date.strftime('%Y-%m-%d'),
            'end_date': instance.end_date.strftime('%Y-%m-%d'),
            'capacity': instance.capacity,
            'available_seats': available_seats
        })
    
    return JsonResponse({
        'status': 'success',
        'course_instances': instances_data
    })


@login_required
def my_corporate_reservations_view(request):
    """View for corporate admins to see all reservations made by users in their corporate"""
    # Check if user is a corporate admin
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
        corporate = corporate_admin.corporate
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('Only corporate admins can access this page'))
        return redirect('website:main')
    
    # Get all users associated with this corporate
    corporate_user_emails = CustomUser.objects.filter(
        company_name=corporate.legal_name,
        is_active=True
    ).values_list('email', flat=True)
    
    # Get filter parameters from request
    course_id = request.GET.get('course_id')
    instance_id = request.GET.get('instance_id')
    user_id = request.GET.get('user_id')
    start_date = request.GET.get('start_date')
    end_date = request.GET.get('end_date')
    status = request.GET.get('status')
    
    # Get all reservations for this corporate's users
    reservations = Reservation.objects.filter(
        user__company_name=corporate.legal_name
    ).select_related(
        'user',
        'course_instance',
        'course_instance__course'
    )
    
    # Apply filters
    if course_id:
        reservations = reservations.filter(course_instance__course__course_id=course_id)
    
    if instance_id:
        reservations = reservations.filter(course_instance__instance_id=instance_id)
    
    if user_id:
        reservations = reservations.filter(user__id=user_id)
    
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
            reservations = reservations.filter(course_instance__start_date__gte=start_date_obj)
        except ValueError:
            pass
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
            reservations = reservations.filter(course_instance__end_date__lte=end_date_obj)
        except ValueError:
            pass
    
    if status:
        reservations = reservations.filter(status=status)
    
    # Final ordering
    reservations = reservations.order_by('-created_at')
    
    # Get courses and course instances for filter dropdowns (only those relevant to this corporate)
    courses = Course.objects.filter(
        instances__in=reservations.values_list('course_instance', flat=True)
    ).distinct().order_by('name_en')
    
    course_instances = CourseInstance.objects.filter(
        instance_id__in=reservations.values_list('course_instance__instance_id', flat=True)
    ).select_related('course').order_by('-start_date')
    
    # Get corporate users for filter dropdown
    corporate_users = CustomUser.objects.filter(
        company_name=corporate.legal_name,
        is_active=True
    ).order_by('first_name', 'last_name')
    
    now = timezone.now()
    # Try to update reservation statuses based on current time
    try:
        for reservation in reservations:
            # Skip if the status is already CANCELLED, WAITING_LIST, or WAITING_TO_PAY
            if hasattr(reservation, 'status') and reservation.status in ['CANCELLED', 'WAITING_LIST', 'WAITING_TO_PAY']:
                continue
                
            # Determine the correct status based on dates
            if reservation.course_instance.start_date > now:
                new_status = 'UPCOMING'
            elif reservation.course_instance.end_date < now:
                new_status = 'COMPLETED'
            else:
                new_status = 'IN_PROGRESS'
                
            # Update if status has changed and the field exists
            if hasattr(reservation, 'status') and reservation.status != new_status:
                reservation.status = new_status
                reservation.save(update_fields=['status'])
    except Exception as e:
        # Log the error but continue - field might not exist yet
        logger.error(f"Error updating reservation statuses: {e}", exc_info=True)
    
    
    context = {
        'corporate': corporate,
        'reservations': reservations,
        'courses': courses,
        'course_instances': course_instances,
        'corporate_users': corporate_users,
        'now': now,
        'filters': {
            'course_id': course_id,
            'instance_id': instance_id,
            'user_id': user_id,
            'start_date': start_date,
            'end_date': end_date,
            'status': status
        },
        'page_title': _('Corporate User Reservations')
    }
    
    return render(request, 'website/reservations/corporate_admin_reservations.html', context)


@login_required
@require_http_methods(["GET"])
def reservation_details_api(request, reservation_id):
    """API endpoint to get detailed information about a reservation"""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': 'User is not a corporate admin'
            }, status=403)
        
        # Get the reservation
        reservation = get_object_or_404(
            Reservation.objects.select_related(
                'user', 
                'course_instance', 
                'course_instance__course'
            ),
            reservation_id=reservation_id
        )
        
        # Check if this reservation belongs to a user from this corporate
        if reservation.user.company_name != corporate.legal_name:
            return JsonResponse({
                'status': 'error',
                'message': 'This reservation does not belong to your corporate users'
            }, status=403)
        
        # Get sessions data if available
        sessions_data = []
        try:
            # Get all sessions for this course instance
            from .models import Session
            sessions = Session.objects.filter(
                course_instance=reservation.course_instance
            ).order_by('start_date')
            
            for session in sessions:
                sessions_data.append({
                    'session_id': session.session_id,
                    'start_date': session.start_date.isoformat(),
                    'end_date': session.end_date.isoformat(),
                    'room_name': session.room.name if session.room else None
                })
        except:
            # If sessions are not available, we'll just return an empty list
            pass
        
        # Format the reservation data
        data = {
            'reservation_id': reservation.reservation_id,
            'status': reservation.status,
            'status_display': reservation.get_status_display(),
            'created_at': reservation.created_at.isoformat(),
            'user': {
                'id': reservation.user.id,
                'name': f"{reservation.user.first_name} {reservation.user.last_name}",
                'email': reservation.user.email
            },
            'course': {
                'id': reservation.course_instance.course.course_id,
                'name': reservation.course_instance.course.name_en,
                'category': reservation.course_instance.course.get_category_display()
            },
            'course_instance': {
                'id': reservation.course_instance.instance_id,
                'start_date': reservation.course_instance.start_date.isoformat(),
                'end_date': reservation.course_instance.end_date.isoformat(),
                'capacity': reservation.course_instance.capacity
            },
            'sessions': sessions_data
        }
        
        return JsonResponse(data)
    
    except Exception as e:
        import traceback
        print(f"Error in reservation_details_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET", "POST"])
def cancel_reservation_api(request, reservation_id):
    """API endpoint for corporate admins to cancel a reservation"""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            if request.method == 'GET':
                return JsonResponse({
                    'status': 'error',
                    'message': 'User is not a corporate admin'
                }, status=403)
            else:
                messages.error(request, _('Only corporate admins can cancel reservations'))
                return redirect('website:my_corporate_reservations')
        
        # Get the reservation
        reservation = get_object_or_404(Reservation, reservation_id=reservation_id)
        
        # Ensure the reservation belongs to a user from this corporate
        if reservation.user.company_name != corporate.legal_name:
            if request.method == 'GET':
                return JsonResponse({
                    'status': 'error',
                    'message': 'This reservation does not belong to your corporate users'
                }, status=403)
            else:
                messages.error(request, _('This reservation does not belong to your corporate users'))
                return redirect('website:my_corporate_reservations')
        
        # Check if reservation can be cancelled (not already cancelled or completed)
        if reservation.status in ['CANCELLED', 'COMPLETED']:
            if request.method == 'GET':
                return JsonResponse({
                    'status': 'error',
                    'message': f'Reservation cannot be cancelled (status: {reservation.get_status_display()})'
                }, status=400)
            else:
                messages.error(request, _('Reservation cannot be cancelled (status: %(status)s)') % {'status': reservation.get_status_display()})
                return redirect('website:my_corporate_reservations')
        
        # For GET requests, just perform the cancellation without parsing request body
        # For POST requests, check if it's a form submission or JSON data
        cancellation_reason = 'Cancelled by corporate admin'
        
        if request.method == 'POST':
            # Check if it's from a form or JSON data
            content_type = request.META.get('CONTENT_TYPE', '')
            if 'application/json' in content_type:
                try:
                    data = json.loads(request.body)
                    cancellation_reason = data.get('cancellation_reason', cancellation_reason)
                except:
                    pass
        
        # Update reservation status
        old_status = reservation.status
        reservation.status = 'CANCELLED'
        reservation.cancellation_reason = cancellation_reason
        reservation.cancelled_at = timezone.now()
        reservation.cancelled_by = request.user
        reservation.save()
        
        # Get the course instance for reference
        course_instance = reservation.course_instance
        
        # Log the action
        logger = logging.getLogger(__name__)
        logger.info(f"Corporate admin {request.user.username} cancelled reservation {reservation_id} (was {old_status})")
        
         # Create notification for the user whose reservation was cancelled
        try:
                from .models import Notification
                Notification.objects.create(
                    user=reservation.user,
                    message=_('Your reservation for course: {} ({}) has been cancelled by your corporate admin.').format(
                        course_instance.course.name_en,
                        course_instance.start_date.strftime('%Y-%m-%d')
                    ),
                    delivered_at=timezone.now()
                )
                
                # Send email notification
                send_reservation_cancellation_email(
                    user=reservation.user,
                    course_name=course_instance.course.name_en,
                    start_date=course_instance.start_date.strftime('%Y-%m-%d'),
                    cancelled_by=request.user.get_full_name()
                )
        except:
                # If notification system isn't available, continue anyway
                pass
        
        # Return appropriate response based on request method
        if request.method == 'GET':
            return JsonResponse({
                'status': 'success',
                'message': 'Reservation cancelled successfully'
            })
        else:
            messages.success(request, _('Reservation cancelled successfully'))
            return redirect('website:my_corporate_reservations')
    
    except Exception as e:
        import traceback
        print(f"Error in cancel_reservation_api: {str(e)}")
        print(traceback.format_exc())
        
        if request.method == 'GET':
            return JsonResponse({
                'status': 'error',
                'message': str(e)
            }, status=500)
        else:
            messages.error(request, _('An error occurred while cancelling the reservation'))
            return redirect('website:my_corporate_reservations')


@login_required
@require_http_methods(["GET"])
def corporate_user_reservations_api(request):
    """API endpoint to fetch reservations for a corporate user."""
    try:
        # Check if user is a corporate user
        if not request.user.company_name:
            return JsonResponse({
                'status': 'error',
                'message': 'User is not associated with any corporate'
            }, status=403)
        
        # Get filter parameters from request
        course_id = request.GET.get('course_id')
        instance_id = request.GET.get('instance_id')
        status = request.GET.get('status')
        start_date = request.GET.get('start_date')
        end_date = request.GET.get('end_date')
        
        # Get reservations for this user
        reservations = Reservation.objects.filter(
            user=request.user
        ).select_related(
            'course_instance',
            'course_instance__course'
        ).order_by('-created_at')
        
        # Apply filters
        if course_id:
            reservations = reservations.filter(course_instance__course__course_id=course_id)
        
        if instance_id:
            reservations = reservations.filter(course_instance__instance_id=instance_id)
        
        if status:
            reservations = reservations.filter(status=status)
        
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, '%Y-%m-%d').date()
                reservations = reservations.filter(course_instance__start_date__gte=start_date_obj)
            except ValueError:
                pass
        
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, '%Y-%m-%d').date()
                reservations = reservations.filter(course_instance__end_date__lte=end_date_obj)
            except ValueError:
                pass
        
        # Format reservation data for API response
        now = timezone.now()
        reservations_data = []
        
        for reservation in reservations:
            # Get sessions count if available
            sessions_count = 0
            try:
                sessions_count = reservation.course_instance.sessions.count()
            except:
                pass
            
            # Determine status display
            status_display = reservation.get_status_display() if reservation.status else ""
            if not status_display:
                if reservation.course_instance.start_date > now:
                    status_display = _("Upcoming")
                elif reservation.course_instance.end_date < now:
                    status_display = _("Completed")
                else:
                    status_display = _("In Progress")
            
            # Format the reservation data
            reservations_data.append({
                'reservation_id': reservation.reservation_id,
                'course': {
                    'id': reservation.course_instance.course.course_id,
                    'name': reservation.course_instance.course.name_en,
                    'category': reservation.course_instance.course.get_category_display()
                },
                'course_instance': {
                    'id': reservation.course_instance.instance_id,
                    'start_date': reservation.course_instance.start_date.isoformat(),
                    'start_date_formatted': reservation.course_instance.start_date.strftime('%b %d, %Y'),
                    'start_time_formatted': reservation.course_instance.start_date.strftime('%I:%M %p'),
                    'end_date': reservation.course_instance.end_date.isoformat(),
                    'end_date_formatted': reservation.course_instance.end_date.strftime('%b %d, %Y'),
                    'end_time_formatted': reservation.course_instance.end_date.strftime('%I:%M %p'),
                    'sessions_count': sessions_count
                },
                'status': reservation.status,
                'status_display': status_display,
                'created_at': reservation.created_at.isoformat(),
                'created_at_formatted': reservation.created_at.strftime('%b %d, %Y'),
                'created_time_formatted': reservation.created_at.strftime('%I:%M %p'),
                'can_cancel': reservation.status in ['UPCOMING', 'WAITING_TO_PAY', 'WAITING_LIST'] if reservation.status else (reservation.course_instance.start_date > now)
            })
        
        # Get corporate data
        corporate = None
        try:
            from .models import Corporate
            corporate = Corporate.objects.filter(legal_name=request.user.company_name).first()
            if corporate:
                corporate_data = {
                    'corporate_id': corporate.corporate_id,
                    'legal_name': corporate.legal_name,
                    'admin_name': f"{corporate.corporate_admin.first_name} {corporate.corporate_admin.last_name}",
                    'admin_email': corporate.corporate_admin.email,
                    'phone_number': corporate.phone_number,
                    'capacity': corporate.capacity,
                    'category': corporate.category,
                    'category_display': corporate.get_category_display()
                }
            else:
                corporate_data = {
                    'legal_name': request.user.company_name,
                }
        except:
            corporate_data = {
                'legal_name': request.user.company_name,
            }
        
        # Get available course instances for this corporate user (for potential enrollment)
        available_courses = []
        try:
            from .models import CourseInstance
            
            # Get course instances that are available for this corporate
            available_instances = CourseInstance.objects.filter(
                course_type='CORPORATE',
                corporate__legal_name=request.user.company_name,
                is_active=True,
                published=True,  # Only show published course instances
                start_date__gte=now.date()
            ).select_related('course')
            
            for instance in available_instances:
                # Check if user is already enrolled
                existing_reservation = Reservation.objects.filter(
                    user=request.user,
                    course_instance=instance
                ).exclude(status='CANCELLED').exists()
                
                if not existing_reservation:
                    # Check if seats are available
                    reserved_seats = Reservation.objects.filter(
                        course_instance=instance,
                        status__in=['COMPLETED', 'UPCOMING', 'IN_PROGRESS']
                    ).count()
                    
                    available_seats = instance.capacity - reserved_seats
                    
                    if available_seats > 0:
                        available_courses.append({
                            'instance_id': instance.instance_id,
                            'course_id': instance.course.course_id,
                            'course_name': instance.course.name_en,
                            'start_date': instance.start_date.strftime('%b %d, %Y'),
                            'end_date': instance.end_date.strftime('%b %d, %Y'),
                            'available_seats': available_seats
                        })
        except:
            pass
        
        return JsonResponse({
            'status': 'success',
            'corporate': corporate_data,
            'reservations': reservations_data,
            'available_courses': available_courses
        })
    
    except Exception as e:
        import traceback
        print(f"Error in corporate_user_reservations_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
def request_cancellation_api(request):
    """API endpoint for corporate users to submit cancellation requests."""
    try:
        # Check if the user is a corporate user
        if not request.user.company_name:
            return JsonResponse({
                'status': 'error',
                'message': _('Only corporate users can request cancellations')
            }, status=403)
            
        # Get form data
        reservation_id = request.POST.get('reservation_id')
        reason = request.POST.get('reason')
        
        # Validate inputs
        if not reservation_id or not reason:
            return JsonResponse({
                'status': 'error',
                'message': _('Reservation ID and reason are required')
            }, status=400)
            
        # Get the reservation
        try:
            reservation = Reservation.objects.get(
                reservation_id=reservation_id,
                user=request.user  # Ensure the reservation belongs to this user
            )
        except Reservation.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Reservation not found or does not belong to you')
            }, status=404)
            
        # Check if reservation can be cancelled (not already cancelled or completed)
        if reservation.status in ['CANCELLED', 'COMPLETED']:
            return JsonResponse({
                'status': 'error',
                'message': _('This reservation cannot be cancelled')
            }, status=400)
            
        # Check if there's already a pending cancellation request for this reservation
        try:
            from .models import CorporateUserCancellationRequest
            existing_request = CorporateUserCancellationRequest.objects.filter(
                reservation=reservation,
                status='PENDING'
            ).exists()
            
            if existing_request:
                return JsonResponse({
                    'status': 'error',
                    'message': _('There is already a pending cancellation request for this reservation')
                }, status=400)
                
        except:
            pass
            
        # Create the cancellation request
        from .models import CorporateUserCancellationRequest
        cancellation_request = CorporateUserCancellationRequest.objects.create(
            reservation=reservation,
            reason=reason,
            status='PENDING'
        )
        
        # Get corporate and create a notification for the corporate admin
        try:
            corporate = Corporate.objects.get(legal_name=request.user.company_name)
            admin_user = corporate.corporate_admin
            
            # Create notification for the corporate admin
            Notification.objects.create(
                user=admin_user,
                message=_('A user ({}) has requested cancellation for a course. Please review the request.').format(
                    request.user.get_full_name() or request.user.email
                ),
                delivered_at=timezone.now()
            )
            
            # Send email notification
            send_cancellation_request_email(
                admin_email=admin_user.email,
                user_name=request.user.get_full_name() or request.user.email,
                course_name=reservation.course_instance.course.name_en,
                start_date=reservation.course_instance.start_date.strftime('%Y-%m-%d')
            )
        except Exception as e:
            # Log but continue
            print(f"Error sending notification to corporate admin: {str(e)}")
        
        return JsonResponse({
            'status': 'success',
            'message': _('Your cancellation request has been submitted and is pending approval'),
            'request_id': cancellation_request.request_id
        })
        
    except Exception as e:
        import traceback
        print(f"Error in request_cancellation_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_cancellation_requests_api(request):
    """API endpoint for corporate admins to get cancellation requests."""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Only corporate admins can access this API')
            }, status=403)
            
        # Get pending cancellation requests for users from this corporate
        from .models import CorporateUserCancellationRequest
        
        # Get filter - default to pending only
        status = request.GET.get('status', 'PENDING')
        
        # Build the queryset
        requests_query = CorporateUserCancellationRequest.objects.filter(
            reservation__user__company_name=corporate.legal_name
        ).select_related(
            'reservation',
            'reservation__user',
            'reservation__course_instance',
            'reservation__course_instance__course'
        ).order_by('-created_at')
        
        # Apply status filter if specified
        if status != 'ALL':
            requests_query = requests_query.filter(status=status)
            
        # Format the data for API response
        requests_data = []
        for req in requests_query:
            reservation = req.reservation
            course_instance = reservation.course_instance
            course = course_instance.course
            
            requests_data.append({
                'request_id': req.request_id,
                'user': {
                    'id': reservation.user.id,
                    'name': reservation.user.get_full_name(),
                    'email': reservation.user.email
                },
                'reservation': {
                    'id': reservation.reservation_id,
                    'status': reservation.status,
                    'status_display': reservation.get_status_display()
                },
                'course': {
                    'id': course.course_id,
                    'name': course.name_en,
                    'start_date': course_instance.start_date.strftime('%b %d, %Y'),
                    'end_date': course_instance.end_date.strftime('%b %d, %Y')
                },
                'reason': req.reason,
                'status': req.status,
                'status_display': req.get_status_display(),
                'admin_notes': req.admin_notes,
                'created_at': req.created_at.strftime('%b %d, %Y %H:%M'),
                'updated_at': req.updated_at.strftime('%b %d, %Y %H:%M')
            })
        
        return JsonResponse({
            'status': 'success',
            'requests': requests_data
        })
        
    except Exception as e:
        import traceback
        print(f"Error in get_cancellation_requests_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_cancellation_requests_count_api(request):
    """API endpoint for corporate admins to get the count of pending cancellation requests."""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Only corporate admins can access this API')
            }, status=403)
            
        # Count pending cancellation requests for users from this corporate
        from .models import CorporateUserCancellationRequest
        count = CorporateUserCancellationRequest.objects.filter(
            reservation__user__company_name=corporate.legal_name,
            status='PENDING'
        ).count()
        
        return JsonResponse({
            'status': 'success',
            'count': count
        })
        
    except Exception as e:
        import traceback
        print(f"Error in get_cancellation_requests_count_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["POST"])
@transaction.atomic
def process_cancellation_request_api(request):
    """API endpoint for corporate admins to approve or reject cancellation requests."""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Only corporate admins can access this API')
            }, status=403)
            
        # Get form data
        request_id = request.POST.get('request_id')
        status = request.POST.get('status')
        admin_notes = request.POST.get('admin_notes', '')
        
        # Validate inputs
        if not request_id or not status:
            return JsonResponse({
                'status': 'error',
                'message': _('Request ID and status are required')
            }, status=400)
            
        if status not in ['APPROVED', 'REJECTED']:
            return JsonResponse({
                'status': 'error',
                'message': _('Invalid status value')
            }, status=400)
            
        # Get the cancellation request
        from .models import CorporateUserCancellationRequest
        try:
            cancel_request = CorporateUserCancellationRequest.objects.get(
                request_id=request_id,
                reservation__user__company_name=corporate.legal_name  # Ensure it belongs to this corporate
            )
        except CorporateUserCancellationRequest.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Cancellation request not found or does not belong to your corporate')
            }, status=404)
            
        # Check if it's already processed
        if cancel_request.status != 'PENDING':
            return JsonResponse({
                'status': 'error',
                'message': _('This cancellation request has already been processed')
            }, status=400)
            
        # Update the request
        cancel_request.status = status
        cancel_request.admin_notes = admin_notes
        cancel_request.processed_by = request.user
        cancel_request.updated_at = timezone.now()
        cancel_request.save()
        
        # If approved, actually cancel the reservation
        if status == 'APPROVED':
            reservation = cancel_request.reservation
            old_status = reservation.status
            
            # Update reservation
            reservation.status = 'CANCELLED'
            reservation.cancellation_reason = f"Cancelled by corporate admin: {request.user.get_full_name() or request.user.email}"
            reservation.cancelled_at = timezone.now()
            reservation.cancelled_by = request.user
            reservation.save()
            
            # Log the action
            logger = logging.getLogger(__name__)
            logger.info(f"Corporate admin {request.user.username} approved cancellation request {request_id} for reservation {reservation.reservation_id} (was {old_status})")
            
            # Create notification for the user whose reservation was cancelled
            try:
                Notification.objects.create(
                    user=reservation.user,
                    message=_('Your cancellation request for course: {} has been approved by your corporate admin.').format(
                        reservation.course_instance.course.name_en
                    ),
                    delivered_at=timezone.now()
                )
                
                # Send email notification
                send_cancellation_request_approval_email(
                    user=reservation.user,
                    course_name=reservation.course_instance.course.name_en,
                    start_date=reservation.course_instance.start_date.strftime('%Y-%m-%d')
                )
            except Exception as e:
                # Log but continue
                print(f"Error creating notification: {str(e)}")
                
            # Process waiting list if needed
            try:
                reservation.course_instance.process_waiting_list()
            except Exception as e:
                # Log but continue
                print(f"Error processing waiting list: {str(e)}")
        
        # If rejected, notify the user
        else:
            try:
                Notification.objects.create(
                    user=cancel_request.reservation.user,
                    message=_('Your cancellation request for course: {} has been rejected by your corporate admin.').format(
                        cancel_request.reservation.course_instance.course.name_en
                    ),
                    delivered_at=timezone.now()
                )
                
                # Send email notification
                send_cancellation_request_rejection_email(
                    user=cancel_request.reservation.user,
                    course_name=cancel_request.reservation.course_instance.course.name_en,
                    start_date=cancel_request.reservation.course_instance.start_date.strftime('%Y-%m-%d')
                )
            except Exception as e:
                # Log but continue
                print(f"Error creating notification: {str(e)}")
        
        return JsonResponse({
            'status': 'success',
            'message': _('Cancellation request has been processed')
        })
        
    except Exception as e:
        import traceback
        print(f"Error in process_cancellation_request_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def corporate_user_reservations_view(request):
    """View for corporate users to see their reservations"""
    # Check if user is a corporate user
    if not request.user.company_name:
        messages.error(request, _('This page is only for corporate users'))
        return redirect('website:main')
    
    # Get corporate info
    corporate = None
    try:
        from .models import Corporate
        corporate = Corporate.objects.filter(legal_name=request.user.company_name).first()
    except:
        pass
    
    # Fetch user's reservations to update statuses
    all_reservations = Reservation.objects.filter(
        user=request.user
    ).select_related('course_instance')
    
    # Try to update reservation statuses based on current time
    now = timezone.now()
    try:
        for reservation in all_reservations:
            # Skip if the status is already CANCELLED, WAITING_LIST, or WAITING_TO_PAY
            if hasattr(reservation, 'status') and reservation.status in ['CANCELLED', 'WAITING_LIST', 'WAITING_TO_PAY']:
                continue
                
            # Determine the correct status based on dates
            if reservation.course_instance.start_date > now:
                new_status = 'UPCOMING'
            elif reservation.course_instance.end_date < now:
                new_status = 'COMPLETED'
            else:
                new_status = 'IN_PROGRESS'
                
            # Update if status has changed and the field exists
            if hasattr(reservation, 'status') and reservation.status != new_status:
                reservation.status = new_status
                reservation.save(update_fields=['status'])
    except Exception as e:
        # Log the error but continue - field might not exist yet
        logger.error(f"Error updating reservation statuses: {e}", exc_info=True)
    
    # The actual reservations will be loaded via the API
    context = {
        'corporate': corporate,
        'now': timezone.now(),
        'page_title': _('My Reservations')
    }
    
    return render(request, 'website/reservations/corporate_user_reservations.html', context)


@login_required
@require_http_methods(["POST"])
@transaction.atomic
def cancel_course_request_api(request):
    """API endpoint for corporate admins to cancel their own course requests."""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            messages.error(request, _('Only corporate admins can cancel course requests'))
            return redirect('website:my_corporate_requests')
        
        # Get request_id from POST data
        request_id = request.POST.get('request_id')
        
        if not request_id:
            messages.error(request, _('Request ID is required'))
            return redirect('website:my_corporate_requests')
        
        # Find the request
        try:
            course_request = CorporateAdminRequest.objects.get(
                request_id=request_id,
                corporate=corporate  # Ensure the request belongs to this admin's corporate
            )
        except CorporateAdminRequest.DoesNotExist:
            messages.error(request, _('Course request not found or does not belong to your corporate'))
            return redirect('website:my_corporate_requests')
        
        # Check if the request can be canceled (only PENDING requests)
        if course_request.status != 'PENDING':
            messages.error(request, _('Only pending requests can be cancelled'))
            return redirect('website:my_corporate_requests')
        
        # Update the status to CANCELLED
        course_request.status = 'CANCELLED'
        course_request.admin_notes = (course_request.admin_notes or '') + '\n' + _('Cancelled by admin on %(date)s') % {'date': timezone.now().strftime('%Y-%m-%d %H:%M')}
        course_request.save()
        
        messages.success(request, _('Course request cancelled successfully'))
        return redirect('website:my_corporate_requests')
    
    except Exception as e:
        import traceback
        print(f"Error in cancel_course_request_api: {str(e)}")
        print(traceback.format_exc())
        messages.error(request, str(e))
        return redirect('website:my_corporate_requests')


@login_required
@require_http_methods(["POST"])
@transaction.atomic
def request_cancel_approved_api(request):
    """API endpoint for corporate admins to request cancellation of APPROVED course requests."""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            messages.error(request, _('Only corporate admins can request cancellation of approved courses'))
            return redirect('website:my_corporate_requests')
        
        # Get request_id from POST data
        request_id = request.POST.get('request_id')
        request_type = request.POST.get('request_type', 'regular')  # Default to regular if not specified
        reason = request.POST.get('reason', '')
        
        if not request_id:
            messages.error(request, _('Request ID is required'))
            return redirect('website:my_corporate_requests')
        
        if request_type == 'new_course':
            # Handle new course request cancellation
            try:
                course_request = CorporateAdminNewCourseRequest.objects.get(
                    request_id=request_id,
                    corporate=corporate  # Ensure the request belongs to this admin's corporate
                )
            except CorporateAdminNewCourseRequest.DoesNotExist:
                messages.error(request, _('New course request not found or does not belong to your corporate'))
                return redirect('website:my_corporate_requests')
            
            # Check if the request is in APPROVED status
            if course_request.status != 'APPROVED':
                messages.error(request, _('Only approved requests can be submitted for cancellation'))
                return redirect('website:my_corporate_requests')
            
            # Update the status to PENDING_CANCEL
            course_request.status = 'PENDING_CANCEL'
            
            # Add the cancellation reason to admin notes
            cancellation_note = _('Cancellation requested by admin on %(date)s\\nReason: %(reason)s') % {
                'date': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'reason': reason
            }
            
            course_request.admin_notes = (course_request.admin_notes or '') + '\\n\\n' + cancellation_note
            course_request.save()
            
            messages.success(request, _('Cancellation request submitted successfully and awaiting system admin approval'))
            return redirect('website:my_corporate_requests')
        else:
            # Original code for regular course request cancellation
            try:
                course_request = CorporateAdminRequest.objects.get(
                    request_id=request_id,
                    corporate=corporate  # Ensure the request belongs to this admin's corporate
                )
            except CorporateAdminRequest.DoesNotExist:
                messages.error(request, _('Course request not found or does not belong to your corporate'))
                return redirect('website:my_corporate_requests')
            
            # Check if the request is in APPROVED status
            if course_request.status != 'APPROVED':
                messages.error(request, _('Only approved requests can be submitted for cancellation'))
                return redirect('website:my_corporate_requests')
            
            # Update the status to PENDING_CANCEL
            course_request.status = 'PENDING_CANCEL'
            
            # Add the cancellation reason to admin notes
            cancellation_note = _('Cancellation requested by admin on %(date)s\\nReason: %(reason)s') % {
                'date': timezone.now().strftime('%Y-%m-%d %H:%M'),
                'reason': reason
            }
            
            course_request.admin_notes = (course_request.admin_notes or '') + '\\n\\n' + cancellation_note
            course_request.save()
            
            messages.success(request, _('Cancellation request submitted successfully and awaiting system admin approval'))
            return redirect('website:my_corporate_requests')
    
    except Exception as e:
        import traceback
        print(f"Error in request_cancel_approved_api: {str(e)}")
        print(traceback.format_exc())
        messages.error(request, str(e))
        return redirect('website:my_corporate_requests')


@login_required
def corporate_user_calendar_view(request):
    """
    Display calendar view for corporate users showing only
    sessions from corporate course instances they've reserved.
    """
    logger = logging.getLogger(__name__)
    
    # Ensure the user is a corporate trainee
    if not hasattr(request.user, 'user_type') or request.user.user_type.code != 'EXTERNAL_CORPORATE_TRAINEE':
        messages.error(request, _('This calendar is for corporate trainees only.'))
        return redirect('website:main')

    logger.info(f"Corporate user calendar view for user: {request.user.username}, user_type: {request.user.user_type.code if hasattr(request.user, 'user_type') else 'None'}")
    
    user_reservations = Reservation.objects.filter(
        user=request.user,
        course_instance__course_type='CORPORATE'
    ).exclude(status='CANCELLED').select_related('course_instance', 'course_instance__course')

    # Log the number of reservations found for debugging
    logger.info(f"Found {user_reservations.count()} corporate reservations for user {request.user.username}")
    
    # Get course instance IDs from reservations
    enrolled_instance_ids = [res.course_instance_id for res in user_reservations if res.course_instance_id]
    logger.info(f"Enrolled instance IDs: {enrolled_instance_ids}")
    
    # Check if we have any course instances
    user_sessions = []
    if enrolled_instance_ids:
        # Get all sessions for these course instances
        user_sessions = Session.objects.filter(
            course_instance__instance_id__in=enrolled_instance_ids
        ).select_related('course', 'room').prefetch_related('trainers', 'trainers__user')
        
        # Log the number of sessions found
        logger.info(f"Found {len(user_sessions)} sessions for user {request.user.username}")
    
    # Get corporate information if company_name is set
    corporate_info = None
    if request.user.company_name:
        try:
            corporate_info = Corporate.objects.get(legal_name=request.user.company_name)
            logger.info(f"Found corporate info for {request.user.company_name}")
        except Corporate.DoesNotExist:
            logger.warning(f"No corporate found with name {request.user.company_name}")
            pass # Corporate info is optional here

    context = {
        'is_user_calendar': True, # Kept for template compatibility
        'user_sessions': user_sessions, # Will be empty list if no sessions found, not None
        'user_reservations': user_reservations, # Pass reservations to template for My Courses list
        'corporate': corporate_info, # Pass corporate info
        'page_title': _('My Corporate Course Calendar')
    }
    return render(request, 'website/calendar/corporate_user_calendar.html', context)


@login_required
def corporate_user_calendar_events_api(request):
    """API endpoint to get only events for corporate courses the user has enrolled in"""
    logger = logging.getLogger(__name__)
    logger.info("Corporate User Calendar API called for user: %s", request.user.username)

    # Ensure the user is a corporate trainee
    if not hasattr(request.user, 'user_type') or request.user.user_type.code != 'EXTERNAL_CORPORATE_TRAINEE':
        return JsonResponse({
            'events': [],
            'error': 'Access denied. This API is for corporate trainees only.'
        }, status=403)

    try:
        events = []
        # Add holidays first to ensure they're always included regardless of user reservations
        try:
            # Add holidays to the response
            holidays_query = Holiday.objects.all()
            
            # Get date range from request if provided (for FullCalendar)
            start_param = request.GET.get('start')
            end_param = request.GET.get('end')
            
            if start_param and end_param:
                try:
                    start_date = datetime.fromisoformat(start_param.split('T')[0])
                    end_date = datetime.fromisoformat(end_param.split('T')[0])
                    holidays_query = holidays_query.filter(date__gte=start_date, date__lte=end_date)
                except ValueError:
                    logger.warning("Invalid start/end parameters for holiday fetching.")
            
            for holiday in holidays_query:
                events.append({
                    'id': f"holiday_{holiday.holiday_id}",
                    'title': holiday.name,
                    'start': holiday.date.isoformat(),
                    'end': holiday.date.isoformat(),
                    'allDay': True,
                    'type': 'holiday',
                    'color': '#DC2626',
                    'display': 'background'
                })
            logger.info(f"Added {holidays_query.count()} holidays to corporate user calendar.")
        except Exception as e:
            logger.error(f"Error fetching holidays: {e}", exc_info=True)
        
        # Now get user reservations and sessions
        user_reservations = Reservation.objects.filter(
            user=request.user,
            course_instance__course_type='CORPORATE'
        ).exclude(status='CANCELLED').select_related('course_instance')

        enrolled_instance_ids = [res.course_instance_id for res in user_reservations if res.course_instance_id]
        
        # Log for debugging
        logger.info(f"User {request.user.username} has {len(enrolled_instance_ids)} enrolled course instances")

        if not enrolled_instance_ids:
            logger.info("User has no corporate course reservations")            
            return JsonResponse({'events': events, 'message': 'You have not enrolled in any corporate courses yet'})

        user_sessions = Session.objects.filter(
            course_instance__instance_id__in=enrolled_instance_ids
        ).select_related('course', 'room').prefetch_related('trainers', 'trainers__user')

        logger.info(f"Found {user_sessions.count()} sessions for user's enrolled corporate courses")

        for session in user_sessions:
            try:
                if session.start_date and session.end_date:
                    start_iso = session.start_date.isoformat()
                    end_iso = session.end_date.isoformat()
                    course_name = session.course.name_en if session.course else 'Session'
                    trainer_name = 'No Trainer'
                    if session.trainers.exists():
                        trainer = session.trainers.first()
                        if trainer and trainer.user:
                            trainer_name = trainer.user.get_full_name() or trainer.user.username
                    room_name = session.room.name if session.room else 'No Room'
                    title = f"{course_name} - {trainer_name} - {room_name}"
                    events.append({
                        'id': session.session_id,
                        'title': title,
                        'start': start_iso,
                        'end': end_iso,
                        'type': 'session', # For calendar filtering
                        'course_id': session.course.course_id if session.course else None,
                        'room_id': session.room.room_id if session.room else None,
                        'color': 'green-600' # Default color for user sessions
                    })
                    logger.info(f"Added session: {title} from {start_iso} to {end_iso}")
                else:
                    logger.warning(f"Session {session.session_id} missing dates")
            except Exception as e:
                logger.error(f"Error processing session {session.session_id}: {e}", exc_info=True)
                continue

        return JsonResponse({'events': events})
    except Exception as e:
        logger.error(f"Corporate User Calendar API error: {str(e)}", exc_info=True)
        # Return 200 so FullCalendar doesn't show a browser error popup
        return JsonResponse({'events': [], 'error': str(e)}, status=200)


@login_required
def corporate_user_courses_view(request):
    """
    View to display only courses that a corporate user has reservations for.
    This is a dedicated page showing just the courses the user is enrolled in.
    """
    # Check if user is a corporate user
    if not request.user.company_name:
        messages.error(request, _('This page is only for corporate users'))
        return redirect('website:main')
    
    # Get the user's reservations that are not cancelled
    reservations = Reservation.objects.filter(
        user=request.user
    ).exclude(status='CANCELLED').select_related(
        'course_instance', 
        'course_instance__course'
    )
    
    # Extract unique courses from reservations
    course_instances = []
    courses = []
    course_ids = set()
    
    for reservation in reservations:
        if reservation.course_instance and reservation.course_instance.course:
            course_instance = reservation.course_instance
            course = course_instance.course
            
            # Only add if we haven't seen this course before
            if course.course_id not in course_ids:
                course_ids.add(course.course_id)
                courses.append(course)
                course_instances.append(course_instance)
    
    # Get corporate info if available
    corporate = None
    if request.user.company_name:
        try:
            corporate = Corporate.objects.get(legal_name=request.user.company_name)
        except Corporate.DoesNotExist:
            pass
            
    context = {
        'courses': courses,
        'course_instances': course_instances,
        'reservations': reservations,
        'corporate': corporate,
        'page_title': _('My Corporate Courses')
    }
    
    return render(request, 'website/courses/my_corporate_courses.html', context)


@login_required
@require_http_methods(["GET"])
def corporate_user_courses_api(request):
    """API endpoint to get courses that a corporate user has reservations for."""
    try:
        # Check if user is a corporate user
        if not request.user.company_name:
            return JsonResponse({
                'status': 'error',
                'message': 'User is not a corporate user'
            }, status=403)
        
        # Get active reservations
        reservations = Reservation.objects.filter(
            user=request.user
        ).exclude(status='CANCELLED').select_related(
            'course_instance', 
            'course_instance__course'
        )
        
        # Extract unique courses
        courses_data = []
        course_ids = set()
        
        for reservation in reservations:
            if not reservation.course_instance or not reservation.course_instance.course:
                continue
                
            course = reservation.course_instance.course
            
            # Only add unique courses
            if course.course_id in course_ids:
                continue
                
            course_ids.add(course.course_id)
            
            # Get all reservations for this course (might have multiple instances)
            course_reservations = [r for r in reservations 
                                if r.course_instance and 
                                r.course_instance.course and 
                                r.course_instance.course.course_id == course.course_id]
            
            # Format the course data
            courses_data.append({
                'course_id': course.course_id,
                'name': course.name_en,
                'description': course.description_en,
                'category': course.get_category_display(),
                'duration_hours': course.duration_hours,
                'thumbnail_url': course.image.url if course.image else None,
                'reservation_count': len(course_reservations),
                'most_recent_instance': {
                    'instance_id': course_reservations[0].course_instance.instance_id,
                    'start_date': course_reservations[0].course_instance.start_date.strftime('%Y-%m-%d'),
                    'end_date': course_reservations[0].course_instance.end_date.strftime('%Y-%m-%d'),
                    'status': course_reservations[0].status
                } if course_reservations else None
            })
        
        return JsonResponse({
            'status': 'success',
            'courses_count': len(courses_data),
            'courses': courses_data
        })
        
    except Exception as e:
        import traceback
        print(f"Error in corporate_user_courses_api: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def corporate_user_attendance_api(request):
    """
    API endpoint to get attendance data for a corporate user's course instance
    """
    try:
        course_instance_id = request.GET.get('course_instance_id')
        
        if not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance ID is required')
            })
        
        try:
            course_instance = CourseInstance.objects.get(
                instance_id=course_instance_id
            )
            
            # Check if this course instance belongs to the user's corporate
            if course_instance.course_type != 'CORPORATE' or not request.user.company_name:
                return JsonResponse({
                    'status': 'error',
                    'message': _('Access denied or invalid course instance')
                }, status=403)
            
            # Get all sessions for this course instance
            sessions = list(course_instance.sessions.filter(is_active=True).order_by('start_date'))
            
            # Get attendance records for this user and these sessions
            attendance_records = {}
            try:
                from .models import Attendance
                for attendance in Attendance.objects.filter(user=request.user, session__in=sessions):
                    attendance_records[str(attendance.session_id)] = {
                        'first_half': attendance.first_half,
                        'second_half': attendance.second_half
                    }
            except Exception as attendance_error:
                # If Attendance model isn't imported or doesn't exist, continue with empty records
                logger.warning(f"Error fetching attendance records: {str(attendance_error)}")
                pass
            
            sessions_data = []
            now = timezone.now()
            
            for session in sessions:
                # Determine session status
                if now < session.start_date:
                    status = 'upcoming'
                elif now > session.end_date:
                    status = 'completed'
                else:
                    status = 'in_progress'
                
                # Get attendance for this session
                attendance = attendance_records.get(str(session.session_id), {'first_half': False, 'second_half': False})
                attended = attendance['first_half'] or attendance['second_half']
                
                # Format session data
                room_name = session.room.name if session.room else _('Online')
                
                sessions_data.append({
                    'session_id': str(session.session_id),
                    'date': session.start_date.strftime('%b %d, %Y'),
                    'time': f"{session.start_date.strftime('%H:%M')} - {session.end_date.strftime('%H:%M')}",
                    'room': room_name,
                    'attended': attended,
                    'first_half': attendance['first_half'],
                    'second_half': attendance['second_half'],
                    'status': status
                })
            
            return JsonResponse({
                'status': 'success',
                'course_name': course_instance.course.name_en,
                'sessions': sessions_data
            })
            
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance not found')
            })
            
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in corporate_user_attendance_api: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching attendance data')
        })


@login_required
@require_http_methods(["GET"])
def admin_attendance_api(request):
    """
    API endpoint to get attendance data for a course instance that a corporate admin manages
    """
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Only corporate admins can access this API')
            }, status=403)
        
        course_instance_id = request.GET.get('course_instance_id')
        user_id = request.GET.get('user_id')
        
        if not course_instance_id:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance ID is required')
            })
        
        try:
            course_instance = CourseInstance.objects.get(
                instance_id=course_instance_id
            )
            
            # If user_id is provided, get attendance for that specific user
            if user_id:
                try:
                    user = CustomUser.objects.get(id=user_id)
                    # Verify the user belongs to this corporate
                    if user.company_name != corporate.legal_name:
                        return JsonResponse({
                            'status': 'error',
                            'message': _('This user does not belong to your corporate')
                        }, status=403)
                except CustomUser.DoesNotExist:
                    return JsonResponse({
                        'status': 'error',
                        'message': _('User not found')
                    }, status=404)
            
            # Get all sessions for this course instance
            sessions = list(course_instance.sessions.filter(is_active=True).order_by('start_date'))
            
            # Get attendance records for this user (or all users) and these sessions
            attendance_records = {}
            try:
                from .models import Attendance
                
                if user_id:
                    # Get attendance for specific user
                    attendances = Attendance.objects.filter(user_id=user_id, session__in=sessions)
                    for attendance in attendances:
                        if str(attendance.session_id) not in attendance_records:
                            attendance_records[str(attendance.session_id)] = {}
                        attendance_records[str(attendance.session_id)][str(user_id)] = {
                            'first_half': attendance.first_half,
                            'second_half': attendance.second_half
                        }
                else:
                    # Get attendance for all corporate users in this course instance
                    corporate_users = CustomUser.objects.filter(company_name=corporate.legal_name)
                    corporate_user_ids = list(corporate_users.values_list('id', flat=True))
                    
                    # Get all attendance records for these users and sessions
                    attendances = Attendance.objects.filter(
                        user_id__in=corporate_user_ids, 
                        session__in=sessions
                    ).select_related('user')
                    
                    for attendance in attendances:
                        session_id = str(attendance.session_id)
                        user_id = str(attendance.user_id)
                        
                        if session_id not in attendance_records:
                            attendance_records[session_id] = {}
                        
                        attendance_records[session_id][user_id] = {
                            'first_half': attendance.first_half,
                            'second_half': attendance.second_half,
                            'user_name': f"{attendance.user.first_name} {attendance.user.last_name}"
                        }
            except Exception as attendance_error:
                # If Attendance model isn't imported or doesn't exist, continue with empty records
                import logging
                logger = logging.getLogger(__name__)
                logger.warning(f"Error fetching attendance records: {str(attendance_error)}")
            
            sessions_data = []
            now = timezone.now()
            
            for session in sessions:
                # Determine session status
                if now < session.start_date:
                    status = 'upcoming'
                elif now > session.end_date:
                    status = 'completed'
                else:
                    status = 'in_progress'
                
                # Format session data
                room_name = session.room.name if session.room else _('Online')
                
                # Get user attendance info for this session
                session_attendance = {}
                if user_id:
                    # Single user mode
                    attendance = attendance_records.get(str(session.session_id), {}).get(str(user_id), {'first_half': False, 'second_half': False})
                    attended = attendance['first_half'] or attendance['second_half']
                    session_attendance = {
                        'user_id': user_id,
                        'attended': attended,
                        'first_half': attendance['first_half'],
                        'second_half': attendance['second_half']
                    }
                else:
                    # Multi-user mode - get all corporate users' attendance for this session
                    session_attendance = attendance_records.get(str(session.session_id), {})
                
                session_data = {
                    'session_id': str(session.session_id),
                    'date': session.start_date.strftime('%b %d, %Y'),
                    'time': f"{session.start_date.strftime('%H:%M')} - {session.end_date.strftime('%H:%M')}",
                    'room': room_name,
                    'status': status
                }
                
                # Add user attendance data depending on mode
                if user_id:
                    session_data['attendance'] = session_attendance
                else:
                    session_data['attendances'] = session_attendance
                
                sessions_data.append(session_data)
            
            # Add corporate users info if we're in multi-user mode
            users_data = []
            if not user_id:
                corporate_users = CustomUser.objects.filter(
                    company_name=corporate.legal_name,
                    is_active=True
                ).order_by('first_name', 'last_name')
                
                for user in corporate_users:
                    users_data.append({
                        'id': str(user.id),
                        'name': f"{user.first_name} {user.last_name}",
                        'email': user.email
                    })
            
            response_data = {
                'status': 'success',
                'course_name': course_instance.course.name_en,
                'sessions': sessions_data
            }
            
            if not user_id:
                response_data['users'] = users_data
            
            return JsonResponse(response_data)
            
        except CourseInstance.DoesNotExist:
            return JsonResponse({
                'status': 'error',
                'message': _('Course instance not found')
            }, status=404)
            
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error in admin_attendance_api: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching attendance data')
        }, status=500)

@login_required
@require_http_methods(["GET"])
def get_pending_surveys_count(request):
    """
    API endpoint to get the count of pending surveys for the current user.
    Returns count and a list of pending survey responses.
    """
    try:
        # Find completed reservations for this user
        completed_reservations = Reservation.objects.filter(
            user=request.user,
            status='COMPLETED',
            completed_at__isnull=False
        ).select_related('course_instance', 'course_instance__course')
        
        # Find survey responses that are pending
        pending_responses = SurveyResponse.objects.filter(
            user=request.user, 
            status='PENDING',
            reservation__in=completed_reservations
        ).select_related('survey', 'reservation', 'reservation__course_instance', 'reservation__course_instance__course')
        
        # Count pending surveys
        count = pending_responses.count()
        
        # Format response data
        surveys_data = []
        for response in pending_responses:
            course_instance = response.reservation.course_instance
            course = course_instance.course if course_instance else None
            
            if not course:
                continue
                
            surveys_data.append({
                'response_id': response.response_id,
                'survey': {
                    'survey_id': response.survey.survey_id,
                    'title': response.survey.title,
                    'question_count': len(response.survey.questions) if response.survey.questions else 0
                },
                'reservation': {
                    'reservation_id': response.reservation.reservation_id,
                    'completed_at': response.reservation.completed_at.isoformat() if response.reservation.completed_at else None
                },
                'course': {
                    'name': course.name_en,
                    'category': course.get_category_display()
                }
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count,
            'surveys': surveys_data
        })
        
    except Exception as e:
        logger.error(f"Error getting pending surveys count: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def user_surveys_view(request):
    """
    View to display all available surveys for user to complete.
    Shows both pending and completed survey responses.
    """
    # Get all survey responses for this user
    survey_responses = SurveyResponse.objects.filter(
        user=request.user
    ).select_related(
        'survey', 
        'reservation', 
        'reservation__course_instance', 
        'reservation__course_instance__course'
    ).order_by('-created_at')
    
    # Separate into pending and completed
    pending_responses = survey_responses.filter(status='PENDING')
    completed_responses = survey_responses.filter(status='COMPLETED')
    
    context = {
        'pending_responses': pending_responses,
        'completed_responses': completed_responses,
        'total_pending': pending_responses.count(),
        'total_completed': completed_responses.count()
    }
    
    return render(request, 'website/survays/corporate_user_surveys.html', context)

@login_required
def take_survey_view(request, survey_id, reservation_id):
    """
    View to take a specific survey for a completed reservation.
    """
    try:
        survey = Survey.objects.get(survey_id=survey_id)
        reservation = Reservation.objects.get(reservation_id=reservation_id, user=request.user)
        
        # Check if the user has already completed this survey
        existing_response = SurveyResponse.objects.filter(
            survey=survey,
            user=request.user,
            reservation=reservation,
            status='COMPLETED'
        ).exists()
        
        if existing_response:
            messages.warning(request, _("You have already completed this survey."))
            return redirect('website:user_surveys')
        
        # Get or create a pending response
        survey_response, created = SurveyResponse.objects.get_or_create(
            survey=survey,
            user=request.user,
            reservation=reservation,
            defaults={'status': 'PENDING'}
        )
        
        context = {
            'survey': survey,
            'reservation': reservation,
            'survey_response': survey_response
        }
        
        return render(request, 'website/survays/take_survey.html', context)
        
    except Survey.DoesNotExist:
        messages.error(request, _("Survey not found."))
        return redirect('website:user_surveys')
    except Reservation.DoesNotExist:
        messages.error(request, _("Reservation not found."))
        return redirect('website:user_surveys')
    except Exception as e:
        logger.error(f"Error taking survey: {str(e)}")
        messages.error(request, _("An error occurred while loading the survey."))
        return redirect('website:user_surveys')

@login_required
@require_http_methods(["POST"])
def create_survey_response_api(request):
    """
    API endpoint to submit a survey response.
    """
    try:
        data = json.loads(request.body)
        survey_id = data.get('survey_id')
        reservation_id = data.get('reservation_id')
        question_responses = data.get('question_responses', {})
        comment = data.get('comment', '')
        
        # Validate required fields
        if not survey_id or not reservation_id:
            return JsonResponse({
                'status': 'error',
                'message': _("Survey ID and Reservation ID are required.")
            }, status=400)
            
        # Get the survey and reservation
        survey = Survey.objects.get(survey_id=survey_id)
        reservation = Reservation.objects.get(reservation_id=reservation_id, user=request.user)
        
        # Get or create the survey response
        survey_response, created = SurveyResponse.objects.get_or_create(
            survey=survey,
            user=request.user,
            reservation=reservation,
            defaults={'status': 'PENDING'}
        )
        
        # Update the survey response
        survey_response.question_responses = question_responses
        survey_response.comment = comment
        survey_response.status = 'COMPLETED'
        survey_response.completed_at = timezone.now()
        survey_response.save()
        
        return JsonResponse({
            'status': 'success',
            'message': _("Survey response submitted successfully."),
            'response_id': survey_response.response_id,
            'user_type': request.user.user_type.code if request.user.user_type else 'UNKNOWN'
        })
        
    except Survey.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _("Survey not found.")
        }, status=404)
    except Reservation.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _("Reservation not found.")
        }, status=404)
    except json.JSONDecodeError:
        return JsonResponse({
            'status': 'error',
            'message': _("Invalid JSON data.")
        }, status=400)
    except Exception as e:
        logger.error(f"Error creating survey response: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["GET"])
def get_pending_questionnaires_count(request):
    """
    API endpoint to get count of pending questionnaires for the current user
    """
    try:
        # Count pending questionnaire responses
        count = QuestionnaireResponse.objects.filter(
            user=request.user,
            status='PENDING_SUBMISSION'
        ).count()
        
        # If detailed is requested, return questionnaire details
        if request.GET.get('detailed', 'false').lower() == 'true':
            responses = QuestionnaireResponse.objects.filter(
                user=request.user,
                status='PENDING_SUBMISSION'
            ).select_related('questionnaire', 'course').order_by('-created_at')
            
            # Try to fetch associated reservations
            questionnaires_data = []
            for response in responses:
                # Look for associated reservation
                reservation = Reservation.objects.filter(
                    user=request.user,
                    course_instance__course=response.course
                ).first()
                
                questionnaires_data.append({
                    'questionnaire': {
                        'id': response.questionnaire.questionnaire_id,
                        'questionnaire_id': response.questionnaire.questionnaire_id,  # Add this for compatibility
                        'title': response.questionnaire.title,
                        'session_number': response.questionnaire.session_number,
                        'question_count': len(response.questionnaire.questions) if response.questionnaire.questions else 0,
                        'total_score': response.questionnaire.total_score
                    },
                    'course': {
                        'name': response.course.name_en,
                        'category': response.course.get_category_display() if hasattr(response.course, 'get_category_display') else response.course.category
                    },
                    'response_id': response.response_id,
                    'created_at': response.created_at.isoformat() if response.created_at else None,
                    'reservation': {
                        'reservation_id': reservation.reservation_id if reservation else None,
                        'created_at': reservation.created_at.isoformat() if reservation and reservation.created_at else None
                    }
                })
        
            return JsonResponse({
                'status': 'success',
                'count': count,
                'questionnaires': questionnaires_data
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count
        })
        
    except Exception as e:
        logger.error(f"Error getting pending questionnaires count: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def user_questionnaires_view(request):
    """
    View to display all available questionnaires for user to complete.
    Shows both pending and completed questionnaire responses.
    """
    # Get all questionnaire responses for this user
    questionnaire_responses = QuestionnaireResponse.objects.filter(
        user=request.user
    ).select_related(
        'questionnaire', 
        'course'
    ).order_by('-created_at')
    
    # Separate by status
    pending_responses = questionnaire_responses.filter(status='PENDING_SUBMISSION')
    pending_review_responses = questionnaire_responses.filter(status='PENDING_TRAINER_REVIEW')
    completed_responses = questionnaire_responses.filter(
        status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED']
    )
    
    context = {
        'pending_responses': pending_responses,
        'pending_review_responses': pending_review_responses,
        'completed_responses': completed_responses,
        'total_pending': pending_responses.count(),
        'total_pending_review': pending_review_responses.count(),
        'total_completed': completed_responses.count()
    }
    
    return render(request, 'website/questionnaires/corporate_user_questionnaires.html', context)

@login_required
def take_questionnaire_view(request, questionnaire_id, reservation_id=None):
    """
    View to take a specific questionnaire.
    """
    try:
        questionnaire = Questionnaire.objects.get(questionnaire_id=questionnaire_id)
        
        # Find the reservation if ID is provided
        reservation = None
        course_instance = None
        session = None
        
        if reservation_id:
            reservation = Reservation.objects.get(reservation_id=reservation_id, user=request.user)
            course_instance = reservation.course_instance
            
            # Try to find an appropriate session based on the session number of the questionnaire
            if course_instance:
                session = Session.objects.filter(
                    course_instance=course_instance,
                    course=questionnaire.course
                ).first()
        
        # Check if the user has already completed this questionnaire
        existing_response = QuestionnaireResponse.objects.filter(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED', 'PENDING_TRAINER_REVIEW']
        ).exists()
        
        if existing_response:
            messages.warning(request, _("You have already submitted this questionnaire."))
            return redirect('website:user_questionnaires')
        
        # Get or create a pending response
        questionnaire_response, created = QuestionnaireResponse.objects.get_or_create(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            defaults={
                'status': 'PENDING',
                'course_instance': course_instance,
                'session': session
            }
        )
        
        context = {
            'questionnaire': questionnaire,
            'reservation': reservation,
            'questionnaire_response': questionnaire_response,
            'course_instance': course_instance,
            'session': session
        }
        
        return render(request, 'website/questionnaires/take_questionnaire.html', context)
        
    except Questionnaire.DoesNotExist:
        messages.error(request, _("Questionnaire not found."))
        return redirect('website:user_questionnaires')

@login_required
@require_http_methods(["POST"])
def create_questionnaire_response_api(request):
    """
    API endpoint to submit a questionnaire response.
    """
    try:
        data = json.loads(request.body)
        questionnaire_id = data.get('questionnaire_id')
        question_responses = data.get('question_responses', {})
        comment = data.get('comment', '')
        course_instance_id = data.get('course_instance_id')
        session_id = data.get('session_id')
        
        # Validate required fields
        if not questionnaire_id:
            return JsonResponse({
                'status': 'error',
                'message': _("Questionnaire ID is required.")
            }, status=400)
            
        # Get the questionnaire
        questionnaire = Questionnaire.objects.get(questionnaire_id=questionnaire_id)
        
        # Get optional related objects
        course_instance = None
        session = None
        
        if course_instance_id:
            try:
                course_instance = CourseInstance.objects.get(instance_id=course_instance_id)
            except CourseInstance.DoesNotExist:
                logger.warning(f"Course instance with ID {course_instance_id} not found")
        
        if session_id:
            try:
                session = Session.objects.get(session_id=session_id)
            except Session.DoesNotExist:
                logger.warning(f"Session with ID {session_id} not found")
        
        # Get or create the questionnaire response
        questionnaire_response, created = QuestionnaireResponse.objects.get_or_create(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            defaults={
                'status': 'PENDING_SUBMISSION',
                'max_total_score': questionnaire.total_score,
                'course_instance': course_instance,
                'session': session
            }
        )
        
        # Calculate the total score based on correct answers
        total_score = 0
        if questionnaire.questions:
            for i, question in enumerate(questionnaire.questions):
                question_type = question.get('type')
                question_score = question.get('score', 0)
                
                # Get user's answer for this question
                user_answer = question_responses.get(str(i))
                
                # For multiple choice and checkbox questions, check if answer is correct
                if question_type in ['multiplechoice', 'checkbox']:
                    correct_answers = set(question.get('correct_answers', []))
                    
                    # For multiplechoice, user can only select one option
                    if question_type == 'multiplechoice' and user_answer and int(user_answer) in correct_answers:
                        total_score += question_score
                    
                    # For checkbox, user can select multiple options
                    elif question_type == 'checkbox' and user_answer:
                        user_selections = set([int(ans) for ans in user_answer if ans])
                        if user_selections == correct_answers:
                            total_score += question_score
                
                # For other question types (text, rating), we can't auto-grade
                # These will need to be reviewed by a trainer
        
        # Update the questionnaire response
        questionnaire_response.question_responses = question_responses
        questionnaire_response.comment = comment
        questionnaire_response.max_total_score = questionnaire.total_score
        questionnaire_response.total_score = total_score
        questionnaire_response.status = 'PENDING_TRAINER_REVIEW'  # Change status to pending trainer review
        questionnaire_response.completed_at = timezone.now()
        
        # Update course instance and session if they weren't set earlier
        if course_instance and not questionnaire_response.course_instance:
            questionnaire_response.course_instance = course_instance
        if session and not questionnaire_response.session:
            questionnaire_response.session = session
            
        questionnaire_response.save()
        
        return JsonResponse({
            'status': 'success',
            'message': _("Questionnaire response submitted successfully and pending trainer review."),
            'response_id': questionnaire_response.response_id,
            'user_type': request.user.user_type.code if request.user.user_type else 'UNKNOWN',
            'total_score': total_score,
            'max_score': questionnaire.total_score
        })
        
    except Questionnaire.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _("Questionnaire not found.")
        }, status=404)
    except Exception as e:
        logger.error(f"Error submitting questionnaire response: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["GET", "POST"])
@transaction.atomic
def request_new_course(request):
    """
    View for corporate admin to request a new course that doesn't exist in the system yet
    """
    # Check if user is a corporate admin
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user, is_active=True)
    except CorporateAdmin.DoesNotExist:
        messages.error(request, _('You do not have permission to request new courses.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        # Process form submission
        course_name = request.POST.get('course_name')
        course_description = request.POST.get('course_description')
        course_type = request.POST.get('course_type')
        capacity = request.POST.get('capacity')
        notes = request.POST.get('notes')
        
        # Validate required fields
        if not course_name:
            messages.error(request, _('Course name is required.'))
            return redirect('website:request_new_course')
        
        try:
            # Import locally to ensure availability
            from django.utils import timezone
            from .models import UserType, Notification, CorporateAdminNewCourseRequest
            import logging
            local_logger = logging.getLogger(__name__)
            
            # Create new course request
            new_course_request = CorporateAdminNewCourseRequest.objects.create(
                corporate=corporate_admin.corporate,
                corporate_admin=corporate_admin,
                course_name=course_name,
                course_description=course_description,
                course_type=course_type or 'OFFLINE',
                capacity=int(capacity) if capacity else 20,
                notes=notes
            )
            
            
            messages.success(request, _('Your request for a new course has been submitted successfully.'))
            
            # Send notification to super admins
            super_admins = CustomUser.objects.filter(user_type__code='SUPER_ADMIN', is_active=True)
            admin_emails = [admin.email for admin in super_admins]
            
            for admin in super_admins:
                Notification.objects.create(
                    user=admin,
                    message=f"New course request from {corporate_admin.corporate.legal_name}: {course_name}"
                )
            
             # Send email notification to all super admins
                send_new_course_request_notification_email(
                 admin_emails=admin_emails,
                 corporate_name=corporate_admin.corporate.legal_name,
                 course_name=course_name,
                 details=course_description
                 )
                
            return redirect('website:my_corporate_requests')
            
        except Exception as e:
            local_logger.error(f"Error creating new course request: {str(e)}")
            messages.error(request, _('An error occurred while submitting your request. Please try again.'))
            return redirect('website:request_new_course')
    
    # GET request - show the form
    return render(request, 'website/corporate/request_new_course.html', {
        'corporate_admin': corporate_admin,
    })

@login_required
@require_http_methods(["POST"])
@transaction.atomic
def cancel_course_request_api(request):
    """API endpoint for corporate admins to cancel their own course requests."""
    try:
        # Check if user is a corporate admin
        try:
            corporate_admin = CorporateAdmin.objects.get(user=request.user)
            corporate = corporate_admin.corporate
        except CorporateAdmin.DoesNotExist:
            messages.error(request, _('Only corporate admins can cancel course requests'))
            return redirect('website:my_corporate_requests')
        
        # Get request_id and request_type from POST data
        request_id = request.POST.get('request_id')
        request_type = request.POST.get('request_type', 'regular')  # Default to regular if not specified
        
        if not request_id:
            messages.error(request, _('Request ID is required'))
            return redirect('website:my_corporate_requests')
        
        if request_type == 'new_course':
            # Handle cancellation of new course request
            try:
                course_request = CorporateAdminNewCourseRequest.objects.get(
                    request_id=request_id,
                    corporate=corporate  # Ensure the request belongs to this admin's corporate
                )
            except CorporateAdminNewCourseRequest.DoesNotExist:
                messages.error(request, _('New course request not found or does not belong to your corporate'))
                return redirect('website:my_corporate_requests')
            
            # Check if the request can be canceled (only PENDING requests)
            if course_request.status != 'PENDING':
                messages.error(request, _('Only pending requests can be cancelled'))
                return redirect('website:my_corporate_requests')
            
            # Update the status to CANCELLED
            course_request.status = 'CANCELLED'
            course_request.admin_notes = (course_request.admin_notes or '') + '\n' + _('Cancelled by admin on %(date)s') % {'date': timezone.now().strftime('%Y-%m-%d %H:%M')}
            course_request.save()
            
        else:
            # Handle cancellation of regular course request
            try:
                course_request = CorporateAdminRequest.objects.get(
                    request_id=request_id,
                    corporate=corporate  # Ensure the request belongs to this admin's corporate
                )
            except CorporateAdminRequest.DoesNotExist:
                messages.error(request, _('Course request not found or does not belong to your corporate'))
                return redirect('website:my_corporate_requests')
            
            # Check if the request can be canceled (only PENDING requests)
            if course_request.status != 'PENDING':
                messages.error(request, _('Only pending requests can be cancelled'))
                return redirect('website:my_corporate_requests')
            
            # Update the status to CANCELLED
            course_request.status = 'CANCELLED'
            course_request.admin_notes = (course_request.admin_notes or '') + '\n' + _('Cancelled by admin on %(date)s') % {'date': timezone.now().strftime('%Y-%m-%d %H:%M')}
            course_request.save()
        
        messages.success(request, _('Course request cancelled successfully'))
        return redirect('website:my_corporate_requests')
        
    except Exception as e:
        logger.error(f"Error cancelling course request: {str(e)}")
        messages.error(request, _('An error occurred while cancelling the request'))
        return redirect('website:my_corporate_requests')

@login_required
@staff_member_required
@require_http_methods(["POST"])
def update_new_course_request_status(request, request_id):
    """API to update the status of a new course request"""
    try:
        course_request = get_object_or_404(CorporateAdminNewCourseRequest, request_id=request_id)
        data = json.loads(request.body)
        
        status = data.get('status')
        admin_notes = data.get('admin_notes', '')
        
        if status not in ['PENDING', 'APPROVED', 'REJECTED', 'CANCELLED']:
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid status'
            }, status=400)
        
        # Handle special case for PENDING_CANCEL requests
        if course_request.status == 'PENDING_CANCEL':
            if status == 'APPROVED':
                # If approving a cancellation request, set status to CANCELLED
                course_request.status = 'CANCELLED'
                course_request.admin_notes = admin_notes + '\n\n' + _('Cancellation approved by system admin on %(date)s') % {
                    'date': timezone.now().strftime('%Y-%m-%d %H:%M')
                }
                course_request.save()
                
                return JsonResponse({
                    'status': 'success',
                    'message': 'Cancellation request approved'
                })
            
            elif status == 'REJECTED':
                # If rejecting a cancellation request, revert to APPROVED
                course_request.status = 'APPROVED'
                course_request.admin_notes = admin_notes + '\n\n' + _('Cancellation rejected by system admin on %(date)s') % {
                    'date': timezone.now().strftime('%Y-%m-%d %H:%M')
                }
                course_request.save()
                
                return JsonResponse({
                    'status': 'success',
                    'message': 'Cancellation request rejected'
                })
        else:
            # Normal status update for regular requests
            course_request.status = status
            course_request.admin_notes = admin_notes
            course_request.save()
            
            # If approving, create notification for the corporate admin
            if status == 'APPROVED':
                try:
                    # Create notification for the corporate admin
                    Notification.objects.create(
                        user=course_request.corporate_admin.user,
                        message=_('Your new course request "%(name)s" has been approved') % {
                            'name': course_request.course_name
                        },
                        delivered_at=timezone.now()
                    )
                    
                    # Send email notification for approval
                    send_course_request_approval_email(
                        admin_email=course_request.corporate_admin.user.email,
                        course_name=course_request.course_name
                    )
                except Exception as e:
                    logger.error(f"Error creating notification: {e}")
            
            # If rejecting, create a notification for the corporate admin
            elif status == 'REJECTED':
                try:
                    # Create notification for the corporate admin
                    Notification.objects.create(
                        user=course_request.corporate_admin.user,
                        message=_('Your new course request "%(name)s" has been rejected. Reason: %(reason)s') % {
                            'name': course_request.course_name,
                            'reason': admin_notes or _('No reason provided')
                        },
                        delivered_at=timezone.now()
                    )
                    
                    # Send email notification for rejection
                    send_course_request_rejection_email(
                        admin_email=course_request.corporate_admin.user.email,
                        course_name=course_request.course_name,
                        reason=admin_notes or _('No reason provided')
                    )
                except Exception as e:
                    logger.error(f"Error creating notification: {e}")
            
            return JsonResponse({
                'status': 'success',
                'message': 'New course request updated successfully'
            })
        
    except Exception as e:
        import traceback
        print(f"Error in update_new_course_request_status: {str(e)}")
        print(traceback.format_exc())
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=400)