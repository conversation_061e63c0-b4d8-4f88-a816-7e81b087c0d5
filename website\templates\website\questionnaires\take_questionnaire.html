{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Take Questionnaire" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    /* Custom styling for questionnaire form */
    .rating-label input:checked + span {
        background-color: rgba(16, 185, 129, 0.9) !important;
        border-color: rgba(52, 211, 153, 1) !important;
        color: white !important;
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(5, 150, 105, 0.5);
    }
    
    .rating-label span:hover {
        background-color: rgba(16, 185, 129, 0.4);
        border-color: rgba(52, 211, 153, 0.8);
        color: white;
        transform: scale(1.05);
    }
    
    .rating-label span {
        transition: all 0.2s ease;
    }

    .question-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .question-card:hover {
        border-left-color: #10b981;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }

    .submit-btn {
        transition: all 0.3s ease;
    }
    
    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.5);
    }
    
    .question-score {
        position: absolute;
        top: 10px;
        right: 10px;
        background-color: rgba(16, 185, 129, 0.2);
        color: #10b981;
        padding: 2px 8px;
        border-radius: 12px;
        font-weight: bold;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-6 flex items-center">
        <svg class="w-8 h-8 mr-3 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        {{ questionnaire.title }}
    </h1>

    <div class="mb-6 bg-green-900/30 backdrop-blur-md rounded-lg border border-green-600/40 p-6 shadow-lg">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-base font-medium text-green-300">{% trans "Course Information" %}</h3>
                <div class="mt-2 text-sm text-gray-200">
                    <p class="text-lg font-semibold">{{ questionnaire.course.name_en }}</p>
                    <p class="text-sm text-green-300 mt-2">{% trans "Session" %}: {{ questionnaire.session_number }}</p>
                    <p class="text-sm text-yellow-300 mt-2">{% trans "Total Possible Score" %}: {{ questionnaire.total_score }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 overflow-hidden shadow-xl">
        <div class="px-6 py-4 border-b border-white/20 bg-gradient-to-r from-green-900/60 to-green-800/60">
            <h2 class="text-xl font-semibold text-white">{% trans "Questionnaire Questions" %}</h2>
        </div>

        <form id="questionnaireForm" class="p-6 space-y-6">
            {% csrf_token %}
            <input type="hidden" id="questionnaire_id" value="{{ questionnaire.questionnaire_id }}">
            <input type="hidden" id="reservation_id" value="{{ reservation.reservation_id|default:'' }}">
            <input type="hidden" id="course_instance_id" value="{{ course_instance.instance_id|default:'' }}">
            <input type="hidden" id="session_id" value="{{ session.session_id|default:'' }}">

            <div id="questionsContainer">
                {% if questionnaire.questions %}
                    {% for question in questionnaire.questions %}
                    <div class="bg-gray-800/60 rounded-lg p-6 mb-6 shadow-lg question-card border-b border-gray-700 backdrop-blur-sm relative">
                        <h3 class="font-medium text-white text-lg mb-4">{{ question.text }}</h3>
                        
                        {% if question.score %}
                        <div class="question-score">
                            {% trans "Score" %}: {{ question.score }}
                        </div>
                        {% endif %}
                        
                        {% if question.type == 'rating' %}
                        <div class="flex items-center space-x-4 mt-4 justify-center" id="rating-{{ forloop.counter }}">
                            {% for i in "12345" %}
                            <label class="rating-label">
                                <input type="radio" name="question_{{ forloop.parentloop.counter }}" value="{{ i }}" class="absolute opacity-0 w-0 h-0" required>
                                <span class="w-12 h-12 flex items-center justify-center border-2 border-green-500/50 rounded-full text-white hover:bg-green-800/70 hover:border-green-400 hover:text-white cursor-pointer transition-all text-lg font-bold">
                                    {{ i }}
                                </span>
                            </label>
                            {% endfor %}
                        </div>
                        <div class="flex justify-between text-gray-300 text-sm mt-2 px-6 font-medium">
                            <span>{% trans "Poor" %}</span>
                            <span>{% trans "Excellent" %}</span>
                        </div>
                        
                        {% elif question.type == 'choice' or question.type == 'multiplechoice' %}
                        <div class="space-y-3 pl-4">
                            {% for option in question.options %}
                            <label class="flex items-center space-x-3 py-2 px-4 rounded-lg hover:bg-green-900/40 transition-colors">
                                <input type="radio" name="question_{{ forloop.parentloop.counter }}" value="{{ option.id }}" class="h-5 w-5 text-green-500 focus:ring-green-400 border-gray-600 bg-gray-700" required>
                                <span class="text-white text-lg">{{ option.text }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        
                        {% elif question.type == 'checkbox' %}
                        <div class="space-y-3 pl-4">
                            {% for option in question.options %}
                            <label class="flex items-center space-x-3 py-2 px-4 rounded-lg hover:bg-green-900/40 transition-colors">
                                <input type="checkbox" name="question_{{ forloop.parentloop.counter }}[]" value="{{ option.id }}" class="h-5 w-5 text-green-500 focus:ring-green-400 border-gray-600 bg-gray-700">
                                <span class="text-white text-lg">{{ option.text }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        
                        {% elif question.type == 'text' %}
                        <textarea name="question_{{ forloop.counter }}" rows="3" class="mt-2 block w-full bg-gray-700/80 border border-green-600/50 rounded-md shadow-sm py-3 px-4 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 sm:text-md" required placeholder="{% trans 'Your answer...' %}"></textarea>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-10 bg-gray-800/40 rounded-lg shadow-inner">
                        <svg class="mx-auto h-12 w-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="mt-4 text-lg text-gray-300 font-medium">{% trans "No questions available for this questionnaire." %}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Add a comment section -->
            <div class="bg-gray-800/60 rounded-lg p-6 shadow-lg border-l-4 border-green-500">
                <h3 class="font-medium text-white text-lg mb-3">{% trans "Additional Comments" %} <span class="text-sm text-gray-400">({% trans "Optional" %})</span></h3>
                <textarea id="comment" name="comment" rows="4" class="mt-2 block w-full bg-gray-700/80 border border-green-600/50 rounded-md shadow-sm py-3 px-4 text-white focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 sm:text-md" placeholder="{% trans 'Share your thoughts about the course...' %}"></textarea>
            </div>

            <div class="flex justify-end pt-4">
                <a href="{% url 'website:user_questionnaires' %}" class="mr-3 inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-md font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-gray-500 transition-all">
                    {% trans "Cancel" %}
                </a>
                <button type="submit" class="inline-flex justify-center py-3 px-8 border border-transparent shadow-md text-md font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-green-500 submit-btn">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {% trans "Submit Questionnaire" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Style selected ratings
        const ratingLabels = document.querySelectorAll('.rating-label');
        
        ratingLabels.forEach(label => {
            const input = label.querySelector('input');
            const span = label.querySelector('span');
            
            input.addEventListener('change', function() {
                // Reset all ratings in this group
                const name = this.name;
                document.querySelectorAll(`input[name="${name}"]`).forEach(inp => {
                    const s = inp.parentElement.querySelector('span');
                    if (inp.checked) {
                        s.classList.add('bg-green-800/90', 'border-green-400', 'text-white');
                        s.classList.remove('border-green-500/50', 'text-white');
                    } else {
                        s.classList.remove('bg-green-800/90', 'border-green-400', 'text-white');
                        s.classList.add('border-green-500/50', 'text-white');
                    }
                });
            });
        });
        
        // Handle form submission
        document.getElementById('questionnaireForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const questionnaireId = document.getElementById('questionnaire_id').value;
            const reservationId = document.getElementById('reservation_id').value;
            const courseInstanceId = document.getElementById('course_instance_id').value;
            const sessionId = document.getElementById('session_id').value;
            const comment = document.getElementById('comment').value;
            
            // Collect all question responses
            const questionResponses = {};
            const questions = document.querySelectorAll('#questionsContainer > div.question-card');
            
            questions.forEach((question, index) => {
                const inputName = `question_${index + 1}`;
                
                // Handle different question types
                const textInput = question.querySelector(`textarea[name="${inputName}"]`);
                if (textInput) {
                    questionResponses[index] = textInput.value;
                    return;
                }
                
                // Handle checkbox inputs (multiple selection)
                const checkboxInputs = question.querySelectorAll(`input[name="${inputName}[]"]:checked`);
                if (checkboxInputs && checkboxInputs.length) {
                    questionResponses[index] = Array.from(checkboxInputs).map(cb => cb.value);
                    return;
                }
                
                // Handle radio inputs (single selection)
                const selectedRadio = question.querySelector(`input[name="${inputName}"]:checked`);
                if (selectedRadio) {
                    questionResponses[index] = selectedRadio.value;
                }
            });
            
            // Show loading state on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {% trans "Submitting..." %}
            `;
            submitBtn.disabled = true;
            
            // Prepare data for submission
            const data = {
                questionnaire_id: questionnaireId,
                question_responses: questionResponses,
                comment: comment,
                course_instance_id: courseInstanceId || null,
                session_id: sessionId || null
            };
            
            // Submit the response
            fetch('{% url "website:create_questionnaire_response_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success message with score if available
                    let scoreText = '';
                    if (data.total_score !== undefined && data.max_score !== undefined) {
                        scoreText = `<div class="mt-2 text-sm">{% trans "Your score" %}: ${data.total_score}/${data.max_score}</div>`;
                    }
                    
                    submitBtn.innerHTML = `
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {% trans "Submitted successfully!" %}
                    `;
                    
                    // Create a message div to show the submission is completed
                    const messageDiv = document.createElement('div');
                    messageDiv.className = 'bg-green-800/80 text-green-100 p-4 rounded-md mt-4 text-center';
                    messageDiv.innerHTML = `
                        <p class="text-lg font-bold">{% trans "Your submission is completed!" %}</p>
                        ${scoreText}
                    `;
                    
                    // Insert the message div after the submit button
                    submitBtn.parentNode.parentNode.appendChild(messageDiv);
                    
                    submitBtn.classList.remove('bg-green-600', 'hover:bg-green-700');
                    submitBtn.classList.add('bg-green-500', 'hover:bg-green-500');
                    
                    // Redirect after a short delay
                    setTimeout(function() {
                        window.location.href = '{% url "website:user_questionnaires" %}';
                    }, 3000);
                } else {
                    // Show error and restore button
                    alert(data.message || '{% trans "An error occurred while submitting your response. Please try again." %}');
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{% trans "An error occurred while submitting your response. Please try again." %}');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            });
        });
    });
</script>
{% endblock %} 