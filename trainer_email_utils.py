import logging
import uuid
from django.core.mail import send_mail
from django.conf import settings
from django.utils.translation import gettext as _
from django.utils import timezone

logger = logging.getLogger(__name__)

def send_trainer_notification_email(recipient_email, subject, message, cc_emails=None):
    """
    Send email notification to trainees or trainers using the same approach as corporate emails.
    
    Args:
        recipient_email: Primary recipient's email address
        subject: Email subject
        message: HTML message content
        cc_emails: List of CC email addresses (optional)
    """
    if not cc_emails:
        cc_emails = []
    
    # Check if email settings are properly configured
    if not all([settings.EMAIL_HOST, settings.EMAIL_PORT, settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD]):
        logger.error(f"Email settings are not properly configured. Check EMAIL_HOST, EMAIL_PORT, EMAIL_HOST_USER, and EMAIL_HOST_PASSWORD settings.")
        
        # Try to log to database anyway
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='FAILED',
                error_message='Email settings not configured properly'
            )
        except Exception as db_err:
            logger.debug(f"Could not log to database: {str(db_err)}")
            
        return False

    # Generate a transaction ID for tracking this email
    transaction_id = uuid.uuid4().hex[:8]
    logger.info(f"[TrainerEmail:{transaction_id}] Preparing to send email to {recipient_email} with subject '{subject}'")

    try:
        # IMPORTANT: Always use EMAIL_HOST_USER as the sender email to avoid auth issues
        from_email = settings.EMAIL_HOST_USER
        
        # Log the actual settings being used
        logger.info(f"[TrainerEmail:{transaction_id}] Using EMAIL_HOST: {settings.EMAIL_HOST}, PORT: {settings.EMAIL_PORT}")
        logger.info(f"[TrainerEmail:{transaction_id}] Sending as: {from_email} to: {recipient_email}")
        
        # Handle CC recipients
        all_recipients = [recipient_email]
        cc_list = []
        
        if cc_emails and len(cc_emails) > 0:
            cc_list = cc_emails
            all_recipients.extend(cc_emails)
            logger.info(f"[TrainerEmail:{transaction_id}] Adding CC recipients: {', '.join(cc_emails)}")
        
        # Use Django's send_mail function with all recipients in recipient_list
        result = send_mail(
            subject=subject,
            message="",  # Empty plain text version
            html_message=message,  # HTML version
            from_email=from_email,
            recipient_list=all_recipients,
            fail_silently=False
        )
        
        # Log result and save to database
        if result:
            logger.info(f"[TrainerEmail:{transaction_id}] SUCCESS: Email sent to {recipient_email}, CC: {', '.join(cc_list) if cc_list else 'none'}")
            
            # Log to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='SUCCESS',
                    transaction_id=transaction_id,
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log successful email to database: {str(db_err)}")
        else:
            logger.warning(f"[TrainerEmail:{transaction_id}] FAILED: Email not sent to {recipient_email}")
            
            # Log failure to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='FAILED',
                    transaction_id=transaction_id,
                    error_message='Email send returned False',
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log failed email to database: {str(db_err)}")
            
        return result
    except Exception as e:
        logger.error(f"[TrainerEmail:{transaction_id}] ERROR: Failed to send email to {recipient_email}: {str(e)}", exc_info=True)
        
        # Log error to database
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='ERROR',
                transaction_id=transaction_id,
                error_message=str(e),
                sent_at=timezone.now()
            )
        except Exception as db_err:
            logger.debug(f"Could not log email error to database: {str(db_err)}")
            
        return False

# Email functions for different notification types

def send_attendance_update_email(user, course_name, session_date, attendance_status):
    """Email notification when a trainee's attendance is updated"""
    logger.info(f"Preparing attendance update email for {user.email}, course: {course_name}")
    
    subject = _('Attendance Update: {0}').format(course_name)
    
    # Format the attendance status message
    if attendance_status.get('first_half') and attendance_status.get('second_half'):
        status_message = _("fully present")
    elif attendance_status.get('first_half'):
        status_message = _("present for first half only")
    elif attendance_status.get('second_half'):
        status_message = _("present for second half only")
    else:
        status_message = _("not marked as present")
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Your attendance for <strong>{course_name}</strong> on {session_date} has been updated.</p>
    <p>Your attendance status: <strong>{status_message}</strong>.</p>
    <p>If you believe this is incorrect, please contact your trainer.</p>
    <p>Thank you</p>
    """
    return send_trainer_notification_email(user.email, subject, message)

def send_questionnaire_review_notification(user, questionnaire_title, score, max_score, trainer_comment=""):
    """Email notification when a questionnaire is reviewed"""
    logger.info(f"Preparing questionnaire review email for {user.email}, questionnaire: {questionnaire_title}")
    
    subject = _('Questionnaire Review: {0}').format(questionnaire_title)
    
    # Calculate percentage score for better context
    percentage = round((score / max_score * 100) if max_score > 0 else 0)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Your questionnaire <strong>{questionnaire_title}</strong> has been reviewed by your trainer.</p>
    <p>Your score: <strong>{score}/{max_score}</strong> ({percentage}%)</p>
    """
    
    # Add trainer comment if available
    if trainer_comment:
        message += f"""
        <p>Trainer's feedback:</p>
        <p><em>"{trainer_comment}"</em></p>
        """
    
    message += f"""
    <p>Thank you for your participation.</p>
    """
    
    return send_trainer_notification_email(user.email, subject, message)

def send_new_questionnaire_notification(user, course_name, questionnaire_title, due_date=None):
    """Email notification when a new questionnaire is assigned"""
    logger.info(f"Preparing new questionnaire notification for {user.email}, questionnaire: {questionnaire_title}")
    
    subject = _('New Questionnaire Available: {0}').format(questionnaire_title)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>A new questionnaire <strong>{questionnaire_title}</strong> is now available for your course <strong>{course_name}</strong>.</p>
    """
    
    if due_date:
        message += f"""
        <p>Please complete it by: <strong>{due_date}</strong>.</p>
        """
    
    message += f"""
    <p>You can access the questionnaire through your account dashboard.</p>
    <p>Thank you</p>
    """
    
    return send_trainer_notification_email(user.email, subject, message)

def send_session_management_notification(user, course_name, session_date, update_message):
    """Email notification for important session updates"""
    logger.info(f"Preparing session update notification for {user.email}, course: {course_name}")
    
    subject = _('Session Update: {0}').format(course_name)
    
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>There has been an update for your session on <strong>{session_date}</strong> for course <strong>{course_name}</strong>:</p>
    <p>{update_message}</p>
    <p>Thank you</p>
    """
    
    return send_trainer_notification_email(user.email, subject, message)

def send_bulk_attendance_notification(users, course_name, session_date):
    """
    Send attendance update emails to multiple users.
    Returns the number of successful emails sent.
    """
    success_count = 0
    for user, attendance_status in users:
        if send_attendance_update_email(user, course_name, session_date, attendance_status):
            success_count += 1
    
    logger.info(f"Sent {success_count} out of {len(users)} attendance notifications for course {course_name}")
    return success_count 