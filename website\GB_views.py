"""
Views for handling logic specific to Internal GB users.
"""
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.contrib import messages
from .models import CustomUser, Reservation, CourseInstance, Notification, SurveyResponse, QuestionnaireResponse, Survey, Questionnaire, Session, Holiday, GB_Employee, GBEmployeeRequest, UserType, EmergencyCancellationRequest
from django.utils import timezone
from django.db import transaction
import logging
import json
from django.db.models import Q
from django.urls import reverse
from django.core.exceptions import ObjectDoesNotExist

# Import email utility functions
from individual_email_utils import (
    send_questionnaire_submission_confirmation,
    send_reservation_cancellation_confirmation,
    send_pending_survey_reminder,
    send_pending_questionnaire_reminder,
    send_waitlist_notification
)

# Import GB email utilities
from .GB_email_utils import (
    send_supervisor_account_created_notification,
    send_supervisor_approval_request,
    send_employee_request_confirmation,
    send_approval_decision_notification
)

logger = logging.getLogger(__name__)

@login_required
def my_reservations_redirect(request):
    """
    Redirects user to the correct reservations page based on their user type.
    """
    if request.user.user_type:
        if request.user.user_type.code == 'INTERNAL_GB':
            return redirect('website:gb_user_reservations_view')
        elif request.user.user_type.code == 'CORPORATE':
            return redirect('website:corporate_user_reservations')
    
    # Default for individual users or users with no type
    return redirect('website:user_reservations')

@login_required
def gb_user_reservations_view(request):
    """
    Displays reservations for an INTERNAL_GB user.
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')

    # Handle cancellation request
    if request.method == 'POST' and 'cancel_reservation' in request.POST:
        from .views import calculate_business_days_deadline
        
        reservation_id = request.POST.get('reservation_id')
        if reservation_id:
            try:
                reservation = Reservation.objects.get(
                    reservation_id=reservation_id,
                    user=request.user
                )
                
                course_instance = reservation.course_instance
                now = timezone.now()
                
                # Check if course has already started
                if course_instance.start_date <= now:
                    messages.error(
                        request,
                        _('Cannot cancel a reservation for a course that has already started.')
                    )
                    return redirect('website:gb_user_reservations_view')
                
                # Get the business days for cancellation (default to 3 if not specified)
                business_days = getattr(course_instance, 'cancellation_deadline_business_days', 3)
                
                # Calculate the deadline based on business days
                deadline_time = calculate_business_days_deadline(course_instance.start_date, business_days)
                
                # Check if deadline has passed
                if now >= deadline_time:
                    messages.error(
                        request,
                        _('The cancellation deadline has passed. Please submit an emergency cancellation request if needed.')
                    )
                    return redirect('website:gb_user_reservations_view')
                
                # Update reservation status to CANCELLED
                reservation.status = 'CANCELLED'
                reservation.save()
                
                # Create a notification for the user
                Notification.objects.create(
                    user=request.user,
                    message=_('Your reservation for {} has been cancelled successfully.').format(
                        course_instance.course.name_en
                    ),
                    delivered_at=timezone.now()
                )
                
                # Log the cancellation
                logger.info(f"GB user {request.user.username} cancelled reservation {reservation_id}")
                
                messages.success(
                    request,
                    _('Your reservation has been cancelled successfully.')
                )
                
                # Move users from waiting list to waiting to pay if applicable
                waiting_list_reservations = Reservation.objects.filter(
                    course_instance=course_instance,
                    status='WAITING_LIST'
                )
                
                logger.info(f"Moving {waiting_list_reservations.count()} users from waiting list to WAITING_TO_PAY for course instance {course_instance.instance_id}")
                
                # Update all waiting list reservations
                for res in waiting_list_reservations:
                    res.status = 'WAITING_TO_PAY'
                    res.save()
                    
                    # Create notification for users moved from waiting list
                    Notification.objects.create(
                        user=res.user,
                        message=_('Great news! A spot opened up in {}. Please complete your payment to confirm your enrollment.').format(
                            course_instance.course.name_en
                        ),
                        delivered_at=timezone.now()
                    )
                
            except Reservation.DoesNotExist:
                messages.error(request, _('Reservation not found.'))
                logger.warning(f"GB user {request.user.username} attempted to cancel non-existent reservation {reservation_id}")
            except Exception as e:
                messages.error(request, _('An error occurred while cancelling your reservation.'))
                logger.error(f"Error cancelling reservation for GB user: {e}", exc_info=True)

    reservations = Reservation.objects.filter(user=request.user).order_by('-created_at')
    
    # Try to update reservation statuses based on current time
    now = timezone.now()
    try:
        for reservation in reservations:
            # Skip if the status is already CANCELLED, WAITING_LIST, or WAITING_TO_PAY
            if hasattr(reservation, 'status') and reservation.status in ['CANCELLED', 'WAITING_LIST', 'WAITING_TO_PAY']:
                continue
                
            # Determine the correct status based on dates
            if reservation.course_instance.start_date > now:
                new_status = 'UPCOMING'
            elif reservation.course_instance.end_date < now:
                new_status = 'COMPLETED'
                # Set the completed_at field if status is changing to COMPLETED
                if reservation.status != 'COMPLETED':
                    reservation.completed_at = now
            else:
                new_status = 'IN_PROGRESS'
                
            # Update if status has changed and the field exists
            if hasattr(reservation, 'status') and reservation.status != new_status:
                reservation.status = new_status
                reservation.save()  # Save all fields to ensure completed_at is saved
    except Exception as e:
        logger.error(f"Error updating reservation statuses for GB user {request.user.id}: {e}", exc_info=True)

    context = {
        'reservations': reservations,
        'page_title': _('My Reservations')
    }
    return render(request, 'website/reservations/gb_user_reservations.html', context)

@login_required
def gb_enroll_in_instance(request, instance_id):
    """Handle enrollment in a course instance for GB users with supervisor approval."""
    if not request.user.user_type or request.user.user_type.code != 'INTERNAL_GB':
        messages.error(request, _('Only Internal GB users can enroll this way.'))
        return redirect('website:courses')

    try:
        instance = CourseInstance.objects.get(instance_id=instance_id, is_active=True)

        # Check that this is an GB course type
        if instance.course_type != 'GB':
            messages.error(request, _('This course is available for GB enrollment.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)
        
        # Check if the course instance is published
        if not instance.published:
            messages.error(request, _('This course instance is not available for enrollment at this time.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        now = timezone.now()
        if now > instance.start_date:
            messages.error(request, _('Enrollment for this course instance is closed because the course has already started.'))
            Notification.objects.create(
                user=request.user,
                message=_('You cannot enroll in course: {} ({}). The course has already started.').format(
                    instance.course.name_en,
                    instance.start_date.strftime('%Y-%m-%d')
                ),
                delivered_at=timezone.now()
            )
            return redirect('website:course_detail', course_id=instance.course.course_id)

        # Check if user already has a pending request for this course
        existing_request = GBEmployeeRequest.objects.filter(
            user=request.user,
            course_instance=instance,
            status='PENDING'
        ).first()
        
        if existing_request:
            messages.warning(request, _('You already have a pending approval request for this course.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        # Check if user is already enrolled
        if Reservation.objects.filter(
            user=request.user, 
            course_instance=instance
        ).exclude(
            status='CANCELLED'
        ).exists():
            messages.warning(request, _('You are already enrolled in this course instance.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        # Get the GB employee record
        try:
            gb_employee = request.user.gb_employee
        except ObjectDoesNotExist:
            gb_employee = None

        if not gb_employee:
            messages.error(request, _('Your GB employee record was not found. Please contact system administrator.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        if not gb_employee.supervisor_id:
            messages.error(request, _('No supervisor found for your account. Please contact HR department.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        # Get supervisor's GB_Employee record
        supervisor_gb_employee = GB_Employee.objects.filter(
            employee_number=gb_employee.supervisor_id,
            is_active=True
        ).first()
        
        if not supervisor_gb_employee:
            # If supervisor not found in our system, try to create them via SOAP
            try:
                from .soap_client import OracleSOAPClient
                soap_client = OracleSOAPClient()
                supervisor_data = soap_client.get_employee_by_id(gb_employee.supervisor_id)
                
                if supervisor_data:
                    # Create GB_Employee record for supervisor
                    supervisor_gb_employee = GB_Employee.objects.create(
                        employee_number=supervisor_data.get('employee_number', gb_employee.supervisor_id),
                        full_name=supervisor_data.get('full_name', ''),
                        english_name=supervisor_data.get('english_name', ''),
                        arabic_name=supervisor_data.get('arabic_name', ''),
                        oracle_username=supervisor_data.get('oracle_username', ''),
                        oracle_email_address=supervisor_data.get('oracle_email_address', ''),
                        oracle_phone_number=supervisor_data.get('oracle_phone_number', ''),
                        department=supervisor_data.get('department', ''),
                        position_name=supervisor_data.get('position_name', ''),
                        organization_id=supervisor_data.get('organization_id', ''),
                        hire_date=supervisor_data.get('hire_date'),
                        grade_name=supervisor_data.get('grade_name', ''),
                        grade_id=supervisor_data.get('grade_id', ''),
                        supervisor_id=supervisor_data.get('supervisor_id', ''),
                        supervisor_user_name=supervisor_data.get('supervisor_user_name', ''),
                        head_department=supervisor_data.get('head_department', ''),
                        cost_center=supervisor_data.get('cost_center', ''),
                        payroll_id=supervisor_data.get('payroll_id', ''),
                        is_active=True
                    )
                    logger.info(f"Created GB_Employee record for supervisor {gb_employee.supervisor_id}")
                else:
                    messages.error(request, _('Supervisor information could not be retrieved from Oracle. Please contact system administrator.'))
                    return redirect('website:course_detail', course_id=instance.course.course_id)
            except Exception as e:
                logger.error(f"Error fetching supervisor data from Oracle: {e}")
                messages.error(request, _('Error retrieving supervisor information. Please contact system administrator.'))
                return redirect('website:course_detail', course_id=instance.course.course_id)

        # Check if supervisor has a user account
        supervisor_user = None
        if supervisor_gb_employee.oracle_email_address:
            supervisor_user = CustomUser.objects.filter(
                Q(email=supervisor_gb_employee.oracle_email_address) |
                Q(employee_id=supervisor_gb_employee.employee_number)
            ).first()

        # Get internal GB user type
        gb_user_type = UserType.objects.filter(code='INTERNAL_GB').first()
        if not gb_user_type:
            messages.error(request, _('Internal GB user type not found. Please contact system administrator.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        account_created = False
        
        # Create supervisor account if it doesn't exist
        if not supervisor_user and supervisor_gb_employee.oracle_email_address:
            try:
                # Generate username from email or employee number
                username = supervisor_gb_employee.oracle_email_address.split('@')[0]
                if CustomUser.objects.filter(username=username).exists():
                    username = f"{supervisor_gb_employee.employee_number}"
                
                # Create supervisor user account
                supervisor_user = CustomUser.objects.create_user(
                    username=username,
                    email=supervisor_gb_employee.oracle_email_address,
                    first_name=supervisor_gb_employee.english_name.split()[0] if supervisor_gb_employee.english_name else '',
                    last_name=' '.join(supervisor_gb_employee.english_name.split()[1:]) if supervisor_gb_employee.english_name and len(supervisor_gb_employee.english_name.split()) > 1 else '',
                    employee_id=supervisor_gb_employee.employee_number,
                    user_type=gb_user_type,
                    gb_employee=supervisor_gb_employee,
                    account_status='ACTIVE',
                    is_active=True,
                    force_password_reset=True  # Force password reset on first login
                )
                account_created = True
                logger.info(f"Created supervisor account for {supervisor_gb_employee.employee_number}")
                
            except Exception as e:
                logger.error(f"Error creating supervisor account: {e}")
                messages.error(request, _('Error creating supervisor account. Please contact system administrator.'))
                return redirect('website:course_detail', course_id=instance.course.course_id)

        if not supervisor_user:
            messages.error(request, _('Supervisor account could not be created or found. Please contact system administrator.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)

        # Create the enrollment request
        with transaction.atomic():
            enrollment_request = GBEmployeeRequest.objects.create(
                employee=gb_employee,
                supervisor=supervisor_gb_employee,
                course_instance=instance,
                user=request.user,
                supervisor_user=supervisor_user,
                status='PENDING',
                request_message=request.POST.get('message', ''),  # Optional message from employee
                supervisor_account_created=account_created
            )
            
            # Create approval URL
            request_url = request.build_absolute_uri(
                reverse('website:supervisor_approve_request', kwargs={'request_id': enrollment_request.request_id})
            )
            
            # Send appropriate email based on whether account was created
            employee_name = request.user.get_full_name() or request.user.username
            supervisor_name = supervisor_gb_employee.english_name or supervisor_gb_employee.full_name or supervisor_user.username
            course_name = instance.course.name_en
            course_date = instance.start_date.strftime('%Y-%m-%d')
            
            if account_created:
                # Send account creation notification
                send_supervisor_account_created_notification(
                    supervisor_user=supervisor_user,
                    employee_name=employee_name,
                    course_name=course_name,
                    course_date=course_date,
                    request_url=request_url
                )
                logger.info(f"Sent account creation notification to supervisor {supervisor_user.email}")
            else:
                # Send approval request to existing supervisor
                send_supervisor_approval_request(
                    supervisor_user=supervisor_user,
                    employee_name=employee_name,
                    course_name=course_name,
                    course_date=course_date,
                    request_url=request_url
                )
                logger.info(f"Sent approval request to supervisor {supervisor_user.email}")
            
            # Send confirmation to employee
            send_employee_request_confirmation(
                employee_user=request.user,
                supervisor_name=supervisor_name,
                course_name=course_name,
                course_date=course_date
            )
            
            # Create notification for employee
            Notification.objects.create(
                user=request.user,
                message=_('Your enrollment request for course "{}" has been submitted to your supervisor ({}) for approval.').format(
                    course_name,
                    supervisor_name
                ),
                delivered_at=timezone.now()
            )
            
            # Success message
            messages.success(request, _('Your enrollment request has been submitted to your supervisor for approval. You will be notified once a decision is made.'))
            return redirect('website:course_detail', course_id=instance.course.course_id)
            
    except CourseInstance.DoesNotExist:
        messages.error(request, _('Course instance not found.'))
        return redirect('website:courses')
    except Exception as e:
        messages.error(request, _('An error occurred during enrollment request. Please try again.'))
        logger.error(f"Error during GB enrollment request: {e}", exc_info=True)
        return redirect('website:courses')

# API Views for GB Users
@login_required
def gb_pending_surveys_api(request):
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({'status': 'error', 'message': 'Not authorized'}, status=403)

    try:
        completed_reservations = Reservation.objects.filter(
            user=request.user,
            status='COMPLETED',
            course_instance__course_type='GB'
        )

        pending_responses = SurveyResponse.objects.filter(
            user=request.user,
            status='PENDING',
            reservation__in=completed_reservations
        ).select_related(
            'survey', 
            'reservation', 
            'reservation__course_instance', 
            'reservation__course_instance__course'
        )
        
        detailed = request.GET.get('detailed', 'false').lower() == 'true'
        
        response_data = {
            'status': 'success',
            'count': pending_responses.count(),
        }

        if detailed:
            surveys_data = []
            for response in pending_responses:
                surveys_data.append({
                    'survey': {
                        'survey_id': response.survey.survey_id,
                        'title': response.survey.title,
                        'question_count': len(response.survey.questions) if response.survey.questions else 0,
                    },
                    'reservation': {
                        'reservation_id': response.reservation.reservation_id,
                    },
                    'course': {
                        'name': response.reservation.course_instance.course.name_en,
                    }
                })
            response_data['surveys'] = surveys_data

        return JsonResponse(response_data)
    except Exception as e:
        logger.error(f"Error in gb_pending_surveys_api: {e}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Internal Server Error'}, status=500)


@login_required
def gb_pending_questionnaires_api(request):
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({'status': 'error', 'message': 'Not authorized'}, status=403)
    
    try:
        pending_responses = QuestionnaireResponse.objects.filter(
            user=request.user,
            status='PENDING_SUBMISSION'
        ).select_related(
            'questionnaire',
            'course',
            'course_instance'
        ).order_by('-created_at')

        pending_responses = pending_responses.filter(course_instance__course_type='GB')

        detailed = request.GET.get('detailed', 'false').lower() == 'true'

        response_data = {
            'status': 'success',
            'count': pending_responses.count(),
        }
        
        if detailed:
            questionnaires_data = []
            for response in pending_responses:
                reservation = Reservation.objects.filter(
                    user=request.user,
                    course_instance=response.course_instance
                ).first()

                questionnaires_data.append({
                    'questionnaire': {
                        'questionnaire_id': response.questionnaire.questionnaire_id,
                        'title': response.questionnaire.title,
                        'question_count': len(response.questionnaire.questions) if response.questionnaire.questions else 0,
                    },
                    'reservation': {
                        'reservation_id': reservation.reservation_id if reservation else None,
                    },
                    'course': {
                        'name': response.course.name_en,
                    }
                })
            response_data['questionnaires'] = questionnaires_data

        return JsonResponse(response_data)
    except Exception as e:
        logger.error(f"Error in gb_pending_questionnaires_api: {e}", exc_info=True)
        return JsonResponse({'status': 'error', 'message': 'Internal Server Error'}, status=500)


@login_required
def gb_take_survey_view(request, survey_id, reservation_id):
    """
    View for GB users to take a survey
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    # For now, redirect to general survey taking page
    # This can be customized later for GB-specific styling/functionality
    return redirect('website:take_survey', survey_id=survey_id, reservation_id=reservation_id)


@login_required
def gb_take_questionnaire_view(request, questionnaire_id, reservation_id):
    """
    View for GB users to take a questionnaire
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    # For now, redirect to general questionnaire taking page
    # This can be customized later for GB-specific styling/functionality
    return redirect('website:take_questionnaire_with_reservation', questionnaire_id=questionnaire_id, reservation_id=reservation_id)


@login_required
def gb_user_surveys_view(request):
    """
    View to display all available surveys for GB users to complete.
    Shows both pending and completed survey responses.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    # Get all survey responses for this user (filtered for GB courses)
    survey_responses = SurveyResponse.objects.filter(
        user=request.user,
        reservation__course_instance__course_type='GB'
    ).select_related(
        'survey', 
        'reservation', 
        'reservation__course_instance', 
        'reservation__course_instance__course'
    ).order_by('-created_at')
    
    # Separate into pending and completed
    pending_responses = survey_responses.filter(status='PENDING')
    completed_responses = survey_responses.filter(status='COMPLETED')
    
    context = {
        'pending_responses': pending_responses,
        'completed_responses': completed_responses,
        'total_pending': pending_responses.count(),
        'total_completed': completed_responses.count(),
        'page_title': _('My Course Feedback')
    }
    
    return render(request, 'website/survays/gb_user_surveys.html', context)


@login_required
@require_http_methods(["GET"])
def get_gb_pending_surveys_count(request):
    """
    API endpoint to get the count of pending surveys for GB users.
    Returns count and a list of pending survey responses.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for Internal GB users only.')
        }, status=403)
    
    try:
        # Find completed reservations for this user (GB courses only)
        completed_reservations = Reservation.objects.filter(
            user=request.user,
            status='COMPLETED',
            completed_at__isnull=False,
            course_instance__course_type='GB'
        ).select_related('course_instance', 'course_instance__course')
        
        # Find survey responses that are pending
        pending_responses = SurveyResponse.objects.filter(
            user=request.user, 
            status='PENDING',
            reservation__in=completed_reservations
        ).select_related('survey', 'reservation', 'reservation__course_instance', 'reservation__course_instance__course')
        
        # Count pending surveys
        count = pending_responses.count()
        
        # Format response data
        surveys_data = []
        for response in pending_responses:
            course_instance = response.reservation.course_instance
            course = course_instance.course if course_instance else None
            
            if not course:
                continue
                
            surveys_data.append({
                'response_id': response.response_id,
                'survey': {
                    'survey_id': response.survey.survey_id,
                    'title': response.survey.title,
                    'question_count': len(response.survey.questions) if response.survey.questions else 0
                },
                'reservation': {
                    'reservation_id': response.reservation.reservation_id,
                    'completed_at': response.reservation.completed_at.isoformat() if response.reservation.completed_at else None
                },
                'course': {
                    'name': course.name_en,
                    'category': course.get_category_display()
                }
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count,
            'surveys': surveys_data
        })
        
    except Exception as e:
        logger.error(f"Error getting pending surveys count for GB user: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
@require_http_methods(["GET"])
def get_gb_pending_questionnaires_count(request):
    """
    API endpoint to get count of pending questionnaires for GB users
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for Internal GB users only.')
        }, status=403)
    
    try:
        # Count pending questionnaire responses (filter for GB courses)
        count = QuestionnaireResponse.objects.filter(
            user=request.user,
            status='PENDING_SUBMISSION',
            course_instance__course_type='GB'
        ).count()
        
        # If detailed is requested, return questionnaire details
        if request.GET.get('detailed', 'false').lower() == 'true':
            responses = QuestionnaireResponse.objects.filter(
                user=request.user,
                status='PENDING_SUBMISSION',
                course_instance__course_type='GB'
            ).select_related('questionnaire', 'course').order_by('-created_at')
            
            # Try to fetch associated reservations
            questionnaires_data = []
            for response in responses:
                # Look for associated reservation
                reservation = Reservation.objects.filter(
                    user=request.user,
                    course_instance__course=response.course,
                    course_instance__course_type='GB'
                ).first()
                
                questionnaires_data.append({
                    'questionnaire': {
                        'id': response.questionnaire.questionnaire_id,
                        'questionnaire_id': response.questionnaire.questionnaire_id,  # Add this for compatibility
                        'title': response.questionnaire.title,
                        'session_number': response.questionnaire.session_number,
                        'question_count': len(response.questionnaire.questions) if response.questionnaire.questions else 0,
                        'total_score': response.questionnaire.total_score
                    },
                    'course': {
                        'name': response.course.name_en,
                        'category': response.course.get_category_display() if hasattr(response.course, 'get_category_display') else response.course.category
                    },
                    'response_id': response.response_id,
                    'created_at': response.created_at.isoformat() if response.created_at else None,
                    'reservation': {
                        'reservation_id': reservation.reservation_id if reservation else None,
                        'created_at': reservation.created_at.isoformat() if reservation and reservation.created_at else None
                    }
                })
        
            return JsonResponse({
                'status': 'success',
                'count': count,
                'questionnaires': questionnaires_data
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count
        })
        
    except Exception as e:
        logger.error(f"Error getting pending questionnaires count for GB user: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def gb_user_questionnaires_view(request):
    """
    View to display all available questionnaires for GB users to complete.
    Shows both pending and completed questionnaire responses.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    # Get all questionnaire responses for this user (filtered for GB courses)
    questionnaire_responses = QuestionnaireResponse.objects.filter(
        user=request.user,
        course_instance__course_type='GB'
    ).select_related(
        'questionnaire', 
        'course'
    ).order_by('-created_at')
    
    # Separate by status
    pending_responses = questionnaire_responses.filter(status='PENDING_SUBMISSION')
    pending_review_responses = questionnaire_responses.filter(status='PENDING_TRAINER_REVIEW')
    completed_responses = questionnaire_responses.filter(
        status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED']
    )
    
    context = {
        'pending_responses': pending_responses,
        'pending_review_responses': pending_review_responses,
        'completed_responses': completed_responses,
        'total_pending': pending_responses.count(),
        'total_pending_review': pending_review_responses.count(),
        'total_completed': completed_responses.count(),
        'page_title': _('My Course Questionnaires')
    }
    
    return render(request, 'website/questionnaires/gb_user_questionnaires.html', context)


@login_required
def gb_take_questionnaire_view(request, questionnaire_id, reservation_id=None):
    """
    View for GB users to take a specific questionnaire.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    try:
        questionnaire = Questionnaire.objects.get(questionnaire_id=questionnaire_id)
        
        # Find the reservation if ID is provided
        reservation = None
        course_instance = None
        session = None
        
        if reservation_id:
            reservation = Reservation.objects.get(
                reservation_id=reservation_id, 
                user=request.user,
                course_instance__course_type='GB'
            )
            course_instance = reservation.course_instance
            
            # Try to find an appropriate session based on the session number of the questionnaire
            if course_instance:
                session = Session.objects.filter(
                    course_instance=course_instance,
                    course=questionnaire.course
                ).first()
        
        # Check if the user has already completed this questionnaire
        existing_response = QuestionnaireResponse.objects.filter(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED', 'PENDING_TRAINER_REVIEW']
        ).exists()
        
        if existing_response:
            messages.warning(request, _("You have already submitted this questionnaire."))
            return redirect('website:gb_user_questionnaires')
        
        # Get or create a pending response
        questionnaire_response, created = QuestionnaireResponse.objects.get_or_create(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            defaults={
                'status': 'PENDING',
                'course_instance': course_instance,
                'session': session
            }
        )
        
        context = {
            'questionnaire': questionnaire,
            'reservation': reservation,
            'questionnaire_response': questionnaire_response,
            'course_instance': course_instance,
            'session': session,
            'page_title': _('Take Questionnaire')
        }
        
        return render(request, 'website/questionnaires/take_questionnaire.html', context)
        
    except Questionnaire.DoesNotExist:
        messages.error(request, _("Questionnaire not found."))
        return redirect('website:gb_user_questionnaires')


@login_required
@require_http_methods(["POST"])
def gb_create_questionnaire_response_api(request):
    """
    API endpoint for GB users to submit a questionnaire response.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for Internal GB users only.')
        }, status=403)
    
    try:
        data = json.loads(request.body)
        questionnaire_id = data.get('questionnaire_id')
        question_responses = data.get('question_responses', {})
        comment = data.get('comment', '')
        course_instance_id = data.get('course_instance_id')
        session_id = data.get('session_id')
        
        # Validate required fields
        if not questionnaire_id:
            return JsonResponse({
                'status': 'error',
                'message': _("Questionnaire ID is required.")
            }, status=400)
            
        # Get the questionnaire
        questionnaire = Questionnaire.objects.get(questionnaire_id=questionnaire_id)
        
        # Get optional related objects
        course_instance = None
        session = None
        
        if course_instance_id:
            try:
                course_instance = CourseInstance.objects.get(
                    instance_id=course_instance_id,
                    course_type='GB'
                )
            except CourseInstance.DoesNotExist:
                logger.warning(f"GB Course instance with ID {course_instance_id} not found")
        
        if session_id:
            try:
                session = Session.objects.get(session_id=session_id)
            except Session.DoesNotExist:
                logger.warning(f"Session with ID {session_id} not found")
        
        # Get or create the questionnaire response
        questionnaire_response, created = QuestionnaireResponse.objects.get_or_create(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            defaults={
                'status': 'PENDING_SUBMISSION',
                'max_total_score': questionnaire.total_score,
                'course_instance': course_instance,
                'session': session
            }
        )
        
        # Calculate the total score based on correct answers
        total_score = 0
        if questionnaire.questions:
            for i, question in enumerate(questionnaire.questions):
                question_type = question.get('type')
                question_score = question.get('score', 0)
                
                # Get user's answer for this question
                user_answer = question_responses.get(str(i))
                
                # For multiple choice and checkbox questions, check if answer is correct
                if question_type in ['multiplechoice', 'checkbox']:
                    correct_answers = set(question.get('correct_answers', []))
                    
                    # For multiplechoice, user can only select one option
                    if question_type == 'multiplechoice' and user_answer and int(user_answer) in correct_answers:
                        total_score += question_score
                    
                    # For checkbox, user can select multiple options
                    elif question_type == 'checkbox' and user_answer:
                        user_selections = set([int(ans) for ans in user_answer if ans])
                        if user_selections == correct_answers:
                            total_score += question_score
        
        # Update the questionnaire response
        questionnaire_response.question_responses = question_responses
        questionnaire_response.comment = comment
        questionnaire_response.max_total_score = questionnaire.total_score
        questionnaire_response.total_score = total_score
        questionnaire_response.status = 'PENDING_TRAINER_REVIEW'
        questionnaire_response.completed_at = timezone.now()
        
        # Update course instance and session if they weren't set earlier
        if course_instance and not questionnaire_response.course_instance:
            questionnaire_response.course_instance = course_instance
        if session and not questionnaire_response.session:
            questionnaire_response.session = session
            
        questionnaire_response.save()
        
        # Send confirmation email to the user
        try:
            send_questionnaire_submission_confirmation(
                user=request.user,
                questionnaire_title=questionnaire.title,
                score=total_score,
                max_score=questionnaire.total_score
            )
            logger.info(f"Sent questionnaire submission confirmation to {request.user.email}")
        except Exception as e:
            logger.error(f"Failed to send questionnaire submission email: {str(e)}")
        
        return JsonResponse({
            'status': 'success',
            'message': _("Questionnaire response submitted and recorded successfully."),
            'response_id': questionnaire_response.response_id,
            'user_type': request.user.user_type.code if request.user.user_type else 'UNKNOWN',
            'total_score': total_score,
            'max_score': questionnaire.total_score
        })
        
    except Questionnaire.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _("Questionnaire not found.")
        }, status=404)
    except Exception as e:
        logger.error(f"Error submitting questionnaire response for GB user: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def gb_user_calendar_view(request):
    """
    Calendar view for INTERNAL_GB users showing their course sessions and events.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    # Get user's GB reservations
    user_reservations = Reservation.objects.filter(
        user=request.user,
        course_instance__course_type='GB'
    ).select_related(
        'course_instance', 
        'course_instance__course'
    ).exclude(status='CANCELLED')
    
    # Get sessions from user's enrolled courses
    user_sessions = []
    for reservation in user_reservations:
        course_instance = reservation.course_instance
        if course_instance:
            # Get sessions for this course instance
            sessions = Session.objects.filter(
                course_instance=course_instance
            ).select_related('room', 'course')
            user_sessions.extend(sessions)
    
    context = {
        'user_reservations': user_reservations,
        'user_sessions': user_sessions,
        'page_title': _('My Course Calendar')
    }
    
    return render(request, 'website/calendar/gb_user_calendar.html', context)


@login_required
@require_http_methods(["GET"])
def gb_user_calendar_events_api(request):
    """
    API endpoint to fetch calendar events for GB users.
    Returns sessions and events in JSON format for calendar display.
    """
    # Check if this is a GB user
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for Internal GB users only.')
        }, status=403)
    
    try:
        events = []
        
        # Add holidays first
        try:
            holidays_query = Holiday.objects.filter(
                Q(is_active=True) | Q(is_active__isnull=True)
            )
            
            # Get date range from request if provided
            start_param = request.GET.get('start')
            end_param = request.GET.get('end')
            
            if start_param and end_param:
                try:
                    from datetime import datetime
                    start_date = datetime.fromisoformat(start_param.split('T')[0])
                    end_date = datetime.fromisoformat(end_param.split('T')[0])
                    holidays_query = holidays_query.filter(date__gte=start_date, date__lte=end_date)
                except ValueError:
                    logger.warning("Invalid start/end parameters for holiday fetching.")
            
            logger.info(f"Including {holidays_query.count()} holidays in GB calendar events")
            
            for holiday in holidays_query:
                # Format the date to ISO format  
                holiday_date_str = holiday.date.strftime('%Y-%m-%d')
                
                # Add holiday as an all-day event (matching main calendar format)
                events.append({
                    'id': f"holiday-{holiday.holiday_id}",
                    'title': f"🎉 {holiday.name}",
                    'start': holiday_date_str,
                    'end': holiday_date_str,
                    'allDay': True,
                    'type': 'holiday',
                    'description': holiday.description,
                    'color': 'red-500',  # Red color for holidays
                    'textColor': 'white',
                    'borderColor': 'red-700'
                })
        except Exception as e:
            logger.error(f"Error fetching holidays: {e}", exc_info=True)
        
        # Get user's GB reservations (following same logic as individual user calendar)
        user_reservations = Reservation.objects.filter(
            user=request.user,
            course_instance__course_type='GB'  # Only show GB course types to GB users
        ).exclude(status='CANCELLED').select_related('course_instance')
        
        # Collect all course instances the user is enrolled in
        enrolled_instance_ids = [res.course_instance_id for res in user_reservations if res.course_instance_id]
        
        logger.info(f"User {request.user.username} has {len(enrolled_instance_ids)} enrolled GB course instances")
        
        if not enrolled_instance_ids:
            logger.info("User has no GB course reservations")
            return JsonResponse({
                'events': events,
                'message': 'You have not enrolled in any GB courses yet'
            })
        
        # Get all sessions belonging to these course instances
        user_sessions = Session.objects.filter(
            course_instance__instance_id__in=enrolled_instance_ids
        ).select_related('course', 'room').prefetch_related('trainers', 'trainers__user')
        
        logger.info(f"Found {user_sessions.count()} sessions for user's enrolled GB courses")
        
        # Add sessions to events array (following same structure as individual user calendar)
        for session in user_sessions:
            try:
                if session.start_date and session.end_date:
                    start_iso = session.start_date.isoformat()
                    end_iso = session.end_date.isoformat()
                    
                    course_name = session.course.name_en if session.course else 'Session'
                    
                    # Get trainer name from the first trainer (if any)
                    trainer_name = 'No Trainer'
                    if session.trainers.exists():
                        trainer = session.trainers.first()
                        if trainer and trainer.user:
                            trainer_name = trainer.user.get_full_name() or trainer.user.username
                    
                    room_name = session.room.name if session.room else 'No Room'
                    
                    title = f"{course_name} - {trainer_name} - {room_name}"
                    
                    events.append({
                        'id': f"session_{session.session_id}",
                        'title': title,
                        'start': start_iso,
                        'end': end_iso,
                        'type': 'session',
                        'session_id': session.session_id,
                        'course_name': course_name,
                        'trainer_name': trainer_name,
                        'room_name': room_name,
                        'course_instance_id': session.course_instance.first().instance_id if session.course_instance.exists() else None,
                        'resourceId': session.room.room_id if session.room else None,
                        'trainerId': session.trainers.first().trainer_id if session.trainers.exists() else None,
                        'is_active': session.is_active,
                        'color': 'green-600'  # Use green color for GB sessions (same as individual users)
                    })
                    logger.info(f"Added GB session: {title} from {start_iso} to {end_iso}")
                else:
                    logger.warning(f"Session {session.session_id} missing dates")
            except Exception as e:
                logger.error(f"Error processing session {session.session_id}: {e}", exc_info=True)
                continue
        
        # Return found events with additional info (following same structure as individual user calendar)
        return JsonResponse({
            'events': events,
            'total_sessions': len([e for e in events if e.get('type') == 'session']),
            'message': f'Found {len([e for e in events if e.get("type") == "session"])} GB sessions for your enrolled courses'
        })
        
    except Exception as e:
        logger.error(f"Error fetching calendar events for GB user: {str(e)}")
        return JsonResponse({'events': [], 'error': str(e)}, status=200) 


# Supervisor Approval Views
@login_required
def supervisor_pending_requests_view(request):
    """
    View for supervisors to see all pending enrollment requests from their subordinates.
    """
    # Check if this user is a supervisor with pending requests
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    if not request.user.gb_employee:
        messages.error(request, _('GB employee record not found.'))
        return redirect('website:dashboard_redirect')
    
    # Get all pending requests where this user is the supervisor
    pending_requests = GBEmployeeRequest.objects.filter(
        supervisor_user=request.user,
        status='PENDING'
    ).select_related(
        'employee', 'course_instance', 'course_instance__course', 'user'
    ).order_by('-created_at')
    
    # Also get all processed requests (no time limit) for reference, limited to recent 20
    recent_processed = GBEmployeeRequest.objects.filter(
        supervisor_user=request.user,
        status__in=['APPROVED', 'REJECTED']
    ).select_related(
        'employee', 'course_instance', 'course_instance__course', 'user'
    ).order_by('-processed_at')[:20]
    
    context = {
        'pending_requests': pending_requests,
        'recent_processed': recent_processed,
        'total_pending': pending_requests.count(),
        'page_title': _('My Team Approval Requests')
    }
    
    return render(request, 'website/GB_supervisor/pending_requests.html', context)


@login_required
@require_http_methods(["POST"])
def supervisor_approve_request(request, request_id):
    """
    API endpoint for supervisors to approve or reject enrollment requests.
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('Only Internal GB users can access this feature.'))
        return redirect('website:dashboard_redirect')
    
    try:
        # Get the request
        enrollment_request = get_object_or_404(
            GBEmployeeRequest,
            request_id=request_id,
            supervisor_user=request.user,
            status='PENDING'
        )
        
        action = request.POST.get('action')  # 'approve' or 'reject'
        notes = request.POST.get('notes', '')
        
        if action == 'approve':
            # Check if course still has available seats
            if enrollment_request.course_instance.available_seats <= 0:
                messages.error(request, _('This course instance is now fully booked. Cannot approve enrollment.'))
                return redirect('website:supervisor_pending_requests')
            
            # Approve the request
            reservation = enrollment_request.approve(
                processed_by_user=request.user,
                notes=notes
            )
            
            # Send notification email to employee
            send_approval_decision_notification(
                employee_user=enrollment_request.user,
                supervisor_name=request.user.get_full_name() or request.user.username,
                course_name=enrollment_request.course_instance.course.name_en,
                course_date=enrollment_request.course_instance.start_date.strftime('%Y-%m-%d'),
                approved=True,
                notes=notes
            )
            
            messages.success(request, _('Enrollment request approved successfully.'))
            logger.info(f"Supervisor {request.user.username} approved enrollment request {request_id}")
            
        elif action == 'reject':
            # Reject the request
            enrollment_request.reject(
                processed_by_user=request.user,
                notes=notes
            )
            
            # Send notification email to employee
            send_approval_decision_notification(
                employee_user=enrollment_request.user,
                supervisor_name=request.user.get_full_name() or request.user.username,
                course_name=enrollment_request.course_instance.course.name_en,
                course_date=enrollment_request.course_instance.start_date.strftime('%Y-%m-%d'),
                approved=False,
                notes=notes
            )
            
            messages.success(request, _('Enrollment request rejected.'))
            logger.info(f"Supervisor {request.user.username} rejected enrollment request {request_id}")
            
        else:
            messages.error(request, _('Invalid action specified.'))
            
    except GBEmployeeRequest.DoesNotExist:
        messages.error(request, _('Request not found or you do not have permission to process it.'))
    except Exception as e:
        logger.error(f"Error processing approval request {request_id}: {e}", exc_info=True)
        messages.error(request, _('An error occurred while processing the request.'))
    
    return redirect('website:supervisor_pending_requests')


@login_required
@require_http_methods(["GET"])
def supervisor_approve_request_get(request, request_id):
    """
    GET endpoint for supervisors to view a specific approval request (for email links).
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('Only Internal GB users can access this feature.'))
        return redirect('website:dashboard_redirect')
    
    try:
        # Get the request
        enrollment_request = get_object_or_404(
            GBEmployeeRequest,
            request_id=request_id,
            supervisor_user=request.user,
            status='PENDING'
        )
        
        context = {
            'enrollment_request': enrollment_request,
            'page_title': _('Review Enrollment Request')
        }
        
        return render(request, 'website/GB_supervisor/review_request.html', context)
        
    except GBEmployeeRequest.DoesNotExist:
        messages.error(request, _('Request not found or you do not have permission to view it.'))
        return redirect('website:supervisor_pending_requests')


@login_required
@require_http_methods(["GET"])
def supervisor_pending_requests_api(request):
    """
    API endpoint to get count of pending approval requests for supervisors.
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({
            'status': 'error',
            'message': _('Only Internal GB users can access this API.')
        }, status=403)
    
    try:
        # Count pending requests where this user is the supervisor
        count = GBEmployeeRequest.objects.filter(
            supervisor_user=request.user,
            status='PENDING'
        ).count()
        
        # If detailed is requested, return request details
        if request.GET.get('detailed', 'false').lower() == 'true':
            requests = GBEmployeeRequest.objects.filter(
                supervisor_user=request.user,
                status='PENDING'
            ).select_related(
                'employee', 'course_instance', 'course_instance__course', 'user'
            ).order_by('-created_at')
            
            requests_data = []
            for req in requests:
                requests_data.append({
                    'request_id': req.request_id,
                    'employee': {
                        'name': req.employee.english_name or req.employee.full_name,
                        'employee_number': req.employee.employee_number,
                        'user_email': req.user.email
                    },
                    'course': {
                        'name': req.course_instance.course.name_en,
                        'start_date': req.course_instance.start_date.isoformat(),
                        'available_seats': req.course_instance.available_seats
                    },
                    'request_message': req.request_message,
                    'created_at': req.created_at.isoformat()
                })
        
            return JsonResponse({
                'status': 'success',
                'count': count,
                'requests': requests_data
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count
        })
        
    except Exception as e:
        logger.error(f"Error getting pending approval requests for supervisor: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)


@login_required
def my_team_view(request):
    """
    View for supervisors to see all employees under their supervision.
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This page is only for Internal GB users.'))
        return redirect('website:dashboard_redirect')
    
    if not request.user.gb_employee:
        messages.error(request, _('GB employee record not found.'))
        return redirect('website:dashboard_redirect')
    
    # Get all employees who have this user as their supervisor
    my_employees = GB_Employee.objects.filter(
        supervisor_id=request.user.gb_employee.employee_number,
        is_active=True
    ).select_related().order_by('english_name')
    
    # Get employees who have user accounts
    employees_with_accounts = []
    employees_without_accounts = []
    
    for employee in my_employees:
        # Try to find user account for this employee
        user_account = CustomUser.objects.filter(
            Q(employee_id=employee.employee_number) |
            Q(email=employee.oracle_email_address) |
            Q(gb_employee=employee)
        ).first()
        
        if user_account:
            employees_with_accounts.append({
                'employee': employee,
                'user': user_account,
                'recent_requests': GBEmployeeRequest.objects.filter(
                    employee=employee,
                    supervisor_user=request.user
                ).order_by('-created_at')[:3]
            })
        else:
            employees_without_accounts.append(employee)
    
    context = {
        'employees_with_accounts': employees_with_accounts,
        'employees_without_accounts': employees_without_accounts,
        'total_employees': my_employees.count(),
        'page_title': _('My Team')
    }
    
    return render(request, 'website/GB_supervisor/my_team.html', context)


@login_required
@require_http_methods(["GET"])
def supervisor_team_attendance_api(request):
    """
    API endpoint for supervisors to view team member attendance records.
    Read-only access to attendance data for their subordinates.
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        return JsonResponse({
            'status': 'error',
            'message': _('Only Internal GB users can access this API.')
        }, status=403)
    
    if not request.user.gb_employee:
        return JsonResponse({
            'status': 'error',
            'message': _('GB employee record not found.')
        }, status=404)
    
    user_id = request.GET.get('user_id')
    if not user_id:
        return JsonResponse({
            'status': 'error',
            'message': _('User ID is required.')
        }, status=400)
    
    try:
        # Get the team member user
        team_member = CustomUser.objects.get(id=user_id)
        
        # Verify this user is actually a subordinate of the supervisor
        if not team_member.gb_employee:
            return JsonResponse({
                'status': 'error',
                'message': _('Team member GB employee record not found.')
            }, status=404)
        
        # Check if current user is the supervisor of this team member
        if team_member.gb_employee.supervisor_id != request.user.gb_employee.employee_number:
            return JsonResponse({
                'status': 'error',
                'message': _('You can only view attendance for your direct reports.')
            }, status=403)
        
        # Get all reservations for this team member (GB courses only)
        reservations = Reservation.objects.filter(
            user=team_member,
            course_instance__course_type='GB'
        ).select_related(
            'course_instance', 
            'course_instance__course'
        ).exclude(status='CANCELLED').order_by('-course_instance__start_date')
        
        courses_data = []
        
        for reservation in reservations:
            course_instance = reservation.course_instance
            if not course_instance:
                continue
                
            course = course_instance.course
            if not course:
                continue
            
            # Get all sessions for this course instance
            from .models import Session, Attendance
            sessions = Session.objects.filter(
                course_instance=course_instance
            ).select_related('room').order_by('start_date')
            
            sessions_data = []
            
            for session in sessions:
                # Get attendance record for this user and session
                attendance = Attendance.objects.filter(
                    user=team_member,
                    session=session
                ).first()
                
                # Determine session status based on dates
                now = timezone.now()
                if session.start_date > now:
                    session_status = 'Upcoming'
                elif session.end_date < now:
                    session_status = 'Completed'
                else:
                    session_status = 'In Progress'
                
                sessions_data.append({
                    'session_id': session.session_id,
                    'date': session.start_date.strftime('%b %d, %Y') if session.start_date else 'TBD',
                    'time': f"{session.start_date.strftime('%H:%M')} - {session.end_date.strftime('%H:%M')}" if session.start_date and session.end_date else 'TBD',
                    'room': session.room.name if session.room else 'TBD',
                    'status': session_status,
                    'first_half': attendance.first_half if attendance else False,
                    'second_half': attendance.second_half if attendance else False
                })
            
            # Only add course if it has sessions
            if sessions_data:
                courses_data.append({
                    'course_name': course.name_en,
                    'start_date': course_instance.start_date.strftime('%b %d, %Y') if course_instance.start_date else 'TBD',
                    'end_date': course_instance.end_date.strftime('%b %d, %Y') if course_instance.end_date else 'TBD',
                    'reservation_status': reservation.status,
                    'sessions': sessions_data
                })
        
        return JsonResponse({
            'status': 'success',
            'team_member': {
                'name': team_member.get_full_name() or team_member.username,
                'email': team_member.email,
                'employee_id': team_member.employee_id
            },
            'courses': courses_data,
            'total_courses': len(courses_data)
        })
        
    except CustomUser.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('Team member not found.')
        }, status=404)
    except Exception as e:
        logger.error(f"Error fetching team member attendance: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching attendance data.')
        }, status=500)

@login_required
@require_http_methods(["GET"])
def gb_get_cancellation_deadline(request):
    """
    API endpoint to get the remaining time until cancellation deadline for GB users
    Returns remaining time in hours and minutes, or zero if deadline has passed
    """
    from .views import calculate_business_days_deadline
    
    reservation_id = request.GET.get('reservation_id')
    if not reservation_id:
        return JsonResponse({
            'status': 'error',
            'message': _('Reservation ID is required')
        }, status=400)
    
    try:
        # Ensure the user can only get their own reservation deadlines and is GB user
        if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
            return JsonResponse({
                'status': 'error',
                'message': _('This endpoint is only for Internal GB users')
            }, status=403)
            
        reservation = Reservation.objects.get(
            reservation_id=reservation_id,
            user=request.user
        )
        
        course_instance = reservation.course_instance
        now = timezone.now()
        
        # Get the business days for cancellation (default to 3 if not specified)
        business_days = getattr(course_instance, 'cancellation_deadline_business_days', 3)
        
        # Calculate the deadline based on business days
        deadline_time = calculate_business_days_deadline(course_instance.start_date, business_days)
        
        # Calculate remaining time until deadline
        if now >= deadline_time:
            # Deadline has passed
            hours_remaining = 0
            minutes_remaining = 0
            can_cancel = False
        else:
            # Calculate time difference
            time_diff = deadline_time - now
            total_seconds = time_diff.total_seconds()
            hours_remaining = int(total_seconds // 3600)
            minutes_remaining = int((total_seconds % 3600) // 60)
            can_cancel = True
        
        # Check if there's already an emergency cancellation request for this reservation
        has_emergency_request = EmergencyCancellationRequest.objects.filter(
            reservation=reservation
        ).exists()
        
        return JsonResponse({
            'status': 'success',
            'can_cancel': can_cancel,
            'hours_remaining': hours_remaining,
            'minutes_remaining': minutes_remaining,
            'cancellation_deadline_business_days': business_days,
            'has_emergency_request': has_emergency_request
        })
        
    except Reservation.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _('Reservation not found or you do not have permission to view it')
        }, status=404)
    except Exception as e:
        logger.error(f"Error calculating cancellation deadline for GB user: {e}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@require_http_methods(["POST"])
def gb_emergency_cancellation_request(request):
    """
    Handle emergency cancellation requests for GB users when the regular cancellation deadline has passed
    but before the course starts
    """
    if not (request.user.user_type and request.user.user_type.code == 'INTERNAL_GB'):
        messages.error(request, _('This functionality is only for Internal GB users.'))
        return redirect('website:gb_user_reservations_view')
    
    reservation_id = request.POST.get('reservation_id')
    reason = request.POST.get('reason')
    
    if not reservation_id or not reason:
        messages.error(request, _('Reservation ID and reason are required.'))
        return redirect('website:gb_user_reservations_view')
    
    try:
        # Ensure the user can only cancel their own reservations
        reservation = Reservation.objects.get(
            reservation_id=reservation_id,
            user=request.user
        )
        
        course_instance = reservation.course_instance
        now = timezone.now()
        
        # Check if course has already started
        if course_instance.start_date <= now:
            messages.error(
                request,
                _('Cannot submit an emergency cancellation request for a course that has already started.')
            )
            return redirect('website:gb_user_reservations_view')
        
        # Check if there's already a pending request for this reservation
        existing_request = EmergencyCancellationRequest.objects.filter(
            reservation=reservation,
            status='PENDING'
        ).exists()
        
        if existing_request:
            messages.warning(
                request,
                _('You already have a pending emergency cancellation request for this reservation.')
            )
            return redirect('website:gb_user_reservations_view')
        
        # Create the emergency cancellation request
        cancellation_request = EmergencyCancellationRequest(
            reservation=reservation,
            reason=reason
        )
        
        # Handle file attachment if provided
        attachment = request.FILES.get('attachment')
        if attachment:
            cancellation_request.attachment = attachment
        
        cancellation_request.save()
        
        # Create a notification for the user
        Notification.objects.create(
            user=request.user,
            message=_('Your emergency cancellation request for {} has been submitted and is pending review.').format(
                course_instance.course.name_en
            ),
            delivered_at=timezone.now()
        )
        
        # Log the emergency cancellation request
        logger.info(f"Emergency cancellation request submitted by GB user {request.user.username} for reservation {reservation_id}")
        
        messages.success(
            request,
            _('Your emergency cancellation request has been submitted and is pending review by administration.')
        )
        
    except Reservation.DoesNotExist:
        messages.error(request, _('Reservation not found.'))
        logger.warning(f"GB user {request.user.username} attempted to submit emergency cancellation for non-existent reservation {reservation_id}")
    except Exception as e:
        messages.error(request, _('An error occurred. Please try again.'))
        logger.error(f"Error processing emergency cancellation request for GB user: {e}", exc_info=True)
    
    return redirect('website:gb_user_reservations_view')