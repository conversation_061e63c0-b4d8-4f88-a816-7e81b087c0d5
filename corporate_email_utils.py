import logging
import uuid
from datetime import datetime
from django.core.mail import EmailMessage, send_mail
from django.conf import settings
from django.utils.translation import gettext as _
from django.utils import timezone

logger = logging.getLogger(__name__)

def send_corporate_notification_email(recipient_email, subject, message, cc_emails=None):
    """
    Send email notification to corporate users using the same approach as the working test script.
    
    Args:
        recipient_email: Primary recipient's email address
        subject: Email subject
        message: HTML message content
        cc_emails: List of CC email addresses (optional)
    """
    if not cc_emails:
        cc_emails = []
    
    # Check if email settings are properly configured
    if not all([settings.EMAIL_HOST, settings.EMAIL_PORT, settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD]):
        logger.error(f"Email settings are not properly configured. Check EMAIL_HOST, EMAIL_PORT, EMAIL_HOST_USER, and EMAIL_HOST_PASSWORD settings.")
        
        # Try to log to database anyway
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='FAILED',
                error_message='Email settings not configured properly'
            )
        except Exception as db_err:
            logger.debug(f"Could not log to database: {str(db_err)}")
            
        return False

    # Generate a transaction ID for tracking this email
    transaction_id = uuid.uuid4().hex[:8]
    logger.info(f"[Email:{transaction_id}] Preparing to send email to {recipient_email} with subject '{subject}'")

    try:
        # IMPORTANT: Always use EMAIL_HOST_USER as the sender email to avoid auth issues
        from_email = settings.EMAIL_HOST_USER
        
        # Log the actual settings being used
        logger.info(f"[Email:{transaction_id}] Using EMAIL_HOST: {settings.EMAIL_HOST}, PORT: {settings.EMAIL_PORT}")
        logger.info(f"[Email:{transaction_id}] Sending as: {from_email} to: {recipient_email}")
        
        # Handle CC recipients - send_mail() doesn't support cc parameter
        # so we need to add CC recipients to the recipient_list
        all_recipients = [recipient_email]
        cc_list = []
        
        if cc_emails and len(cc_emails) > 0:
            cc_list = cc_emails
            all_recipients.extend(cc_emails)
            logger.info(f"[Email:{transaction_id}] Adding CC recipients: {', '.join(cc_emails)}")
        
        # Use Django's send_mail function with all recipients in recipient_list
        result = send_mail(
            subject=subject,
            message="",  # Empty plain text version
            html_message=message,  # HTML version
            from_email=from_email,
            recipient_list=all_recipients,
            fail_silently=False
        )
        
        # Log result and save to database
        if result:
            logger.info(f"[Email:{transaction_id}] SUCCESS: Email sent to {recipient_email}, CC: {', '.join(cc_list) if cc_list else 'none'}")
            
            # Log to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='SUCCESS',
                    transaction_id=transaction_id,
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log successful email to database: {str(db_err)}")
        else:
            logger.warning(f"[Email:{transaction_id}] FAILED: Email not sent to {recipient_email}")
            
            # Log failure to database
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='FAILED',
                    transaction_id=transaction_id,
                    error_message='Email send returned False',
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log failed email to database: {str(db_err)}")
            
        return result
    except Exception as e:
        logger.error(f"[Email:{transaction_id}] ERROR: Failed to send email to {recipient_email}: {str(e)}", exc_info=True)
        
        # Log specific Gmail authentication errors with helpful message
        if '535' in str(e) and '5.7.3' in str(e) and 'gmail' in settings.EMAIL_HOST.lower():
            logger.error(f"[Email:{transaction_id}] Gmail Authentication Error: You need to use an App Password instead of your regular password.")
            logger.error("Instructions: Go to your Google Account → Security → App passwords → Create a new app password for 'Mail'")
        
        # Log error to database
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='ERROR',
                transaction_id=transaction_id,
                error_message=str(e),
                sent_at=timezone.now()
            )
        except Exception as db_err:
            logger.debug(f"Could not log email error to database: {str(db_err)}")
            
        return False

# Email functions for different notification types

def send_reservation_cancellation_email(user, course_name, start_date, cancelled_by):
    """Email notification for reservation cancellation"""
    logger.info(f"Preparing reservation cancellation email for {user.email}, course: {course_name}")
    subject = _('Reservation Cancellation Notice')
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Your reservation for course: <strong>{course_name}</strong> ({start_date}) has been cancelled by your corporate admin.</p>
    <p>If you have any questions, please contact your administrator.</p>
    <p>Thank you</p>
    """
    return send_corporate_notification_email(user.email, subject, message)

def send_cancellation_request_email(admin_email, user_name, course_name, start_date):
    """Email notification for cancellation request to admin"""
    logger.info(f"Preparing cancellation request email to admin {admin_email}, from user: {user_name}, course: {course_name}")
    subject = _('Course Cancellation Request')
    message = f"""
    <p>Dear Admin,</p>
    <p>A user ({user_name}) has requested cancellation for course: <strong>{course_name}</strong> ({start_date}).</p>
    <p>Please review the request in your admin dashboard.</p>
    <p>Thank you</p>
    """
    return send_corporate_notification_email(admin_email, subject, message)

def send_cancellation_request_approval_email(user, course_name, start_date):
    """Email notification for cancellation request approval"""
    logger.info(f"Preparing cancellation request approval email to {user.email}, course: {course_name}")
    subject = _('Cancellation Request Approved')
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Your cancellation request for course: <strong>{course_name}</strong> ({start_date}) has been approved by your corporate admin.</p>
    <p>Thank you</p>
    """
    return send_corporate_notification_email(user.email, subject, message)

def send_cancellation_request_rejection_email(user, course_name, start_date):
    """Email notification for cancellation request rejection"""
    logger.info(f"Preparing cancellation request rejection email to {user.email}, course: {course_name}")
    subject = _('Cancellation Request Rejected')
    message = f"""
    <p>Dear {user.get_full_name()},</p>
    <p>Your cancellation request for course: <strong>{course_name}</strong> ({start_date}) has been rejected by your corporate admin.</p>
    <p>If you have any questions, please contact your administrator.</p>
    <p>Thank you</p>
    """
    return send_corporate_notification_email(user.email, subject, message)

def send_new_course_request_notification_email(admin_emails, corporate_name, course_name, details):
    """Email notification for new course request to system admins"""
    logger.info(f"Preparing new course request emails to {len(admin_emails)} admins, corporate: {corporate_name}, course: {course_name}")
    subject = _('New Course Request')
    message = f"""
    <p>Dear Admin,</p>
    <p>New course request from {corporate_name}: <strong>{course_name}</strong></p>
    <p>Details: {details}</p>
    <p>Please review this request in your admin dashboard.</p>
    <p>Thank you</p>
    """
    # Send to all system admins
    results = []
    for admin_email in admin_emails:
        results.append(send_corporate_notification_email(admin_email, subject, message))
    
    # Return True if at least one email was sent successfully
    return any(results)

def send_course_request_approval_email(admin_email, course_name):
    """Email notification for course request approval"""
    logger.info(f"Preparing course request approval email to {admin_email}, course: {course_name}")
    subject = _('Course Request Approved')
    message = f"""
    <p>Dear Admin,</p>
    <p>Your new course request "<strong>{course_name}</strong>" has been approved.</p>
    <p>The course is now available in the system.</p>
    <p>Thank you</p>
    """
    return send_corporate_notification_email(admin_email, subject, message)

def send_course_request_rejection_email(admin_email, course_name, reason):
    """Email notification for course request rejection"""
    logger.info(f"Preparing course request rejection email to {admin_email}, course: {course_name}, reason: {reason}")
    subject = _('Course Request Rejected')
    message = f"""
    <p>Dear Admin,</p>
    <p>Your new course request "<strong>{course_name}</strong>" has been rejected.</p>
    <p>Reason: {reason}</p>
    <p>If you have any questions, please contact system support.</p>
    <p>Thank you</p>
    """
    return send_corporate_notification_email(admin_email, subject, message) 