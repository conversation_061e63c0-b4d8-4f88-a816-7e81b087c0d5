{% load i18n %}

<!-- Room Modal -->
<div id="roomModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="modalTitle">
                            {% trans "Create Room" %}
                        </h3>
                        <!-- Error and Success Messages -->
                        <div id="roomModalError" class="mt-2 p-2 bg-red-500 text-white rounded hidden"></div>
                        <div id="roomModalSuccess" class="mt-2 p-2 bg-green-500 text-white rounded hidden"></div>
                        <div class="mt-4">
                            <form id="roomForm" class="space-y-4">
                                <div>
                                    <label for="name" class="block text-sm font-medium text-gray-300">{% trans "Room Name" %}</label>
                                    <input type="text" name="name" id="name" required class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                </div>
                                <div>
                                    <label for="room_type" class="block text-sm font-medium text-gray-300">{% trans "Room Type" %}</label>
                                    <select id="room_type" name="room_type" required class="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-600 bg-gray-800 text-white focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md">
                                        <option value="">{% trans "Select a room type" %}</option>
                                        {% for room_type in room_types %}
                                        <option value="{{ room_type.room_type_id }}">{{ room_type.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div>
                                    <label for="capacity" class="block text-sm font-medium text-gray-300">{% trans "Capacity" %}</label>
                                    <input type="number" name="capacity" id="capacity" required min="1" class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50">
                                </div>
                                <!-- Fixed Equipment Selection - Only shown when editing -->
                                <div id="equipmentSection" class="mb-4 hidden">
                                    <label class="block text-sm font-medium text-gray-300">{% trans "Fixed Equipment" %}</label>
                                    <div class="mt-2 max-h-48 overflow-y-auto border border-gray-600 rounded-md bg-gray-800 p-3">
                                        <div class="space-y-2" id="equipmentContainer">
                                            <!-- Equipment items will be loaded here dynamically -->
                                        </div>
                                    </div>
                                </div>
                                <div>
                                    <label for="description" class="block text-sm font-medium text-gray-300">{% trans "Description" %}</label>
                                    <textarea name="description" id="description" rows="3" class="mt-1 block w-full rounded-md bg-gray-700 border-gray-600 text-white shadow-sm focus:border-primary focus:ring focus:ring-primary focus:ring-opacity-50"></textarea>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" onclick="handleSave()" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Save" %}
                </button>
                <button type="button" onclick="closeRoomModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// Function to show or hide the equipment section based on edit mode
function toggleEquipmentSection(isEditing) {
    const equipmentSection = document.getElementById('equipmentSection');
    const modalTitle = document.getElementById('modalTitle');
    
    if (isEditing) {
        // Show equipment section when editing
        equipmentSection.classList.remove('hidden');
        modalTitle.textContent = '{% trans "Edit Room" %}';
    } else {
        // Hide equipment section when creating new
        equipmentSection.classList.add('hidden');
        modalTitle.textContent = '{% trans "Create Room" %}';
    }
}

// Function to open the room modal
function openRoomModal() {
    const modal = document.getElementById('roomModal');
    if (modal) {
        modal.classList.remove('hidden');
        
        // Set editing mode to false for create
        window.isEditing = false;
        window.currentRoomId = null;
        
        // Reset modal title to create mode
        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            modalTitle.textContent = '{% trans "Create Room" %}';
        }
        
        // Reset form
        const form = document.getElementById('roomForm');
        if (form) form.reset();
        
        // Clear equipment container
        const equipmentContainer = document.getElementById('equipmentContainer');
        if (equipmentContainer) {
            equipmentContainer.innerHTML = '';
        }
        
        // Update UI for create mode
        toggleEquipmentSection(false);
    }
}

// Function to open the modal for editing an existing room
function openEditRoomModal(roomId, roomData) {
    console.log("Opening edit room modal", roomId, roomData);
    
    const modal = document.getElementById('roomModal');
    if (modal) {
        modal.classList.remove('hidden');
        
        // Set editing mode to true
        window.isEditing = true;
        window.currentRoomId = roomId;
        
        // Update modal title to show we're editing
        const modalTitle = document.getElementById('modalTitle');
        if (modalTitle) {
            modalTitle.textContent = '{% trans "Edit Room" %}';
        }
        
        // Fill form with existing data
        const form = document.getElementById('roomForm');
        if (form) {
            form.querySelector('#name').value = roomData.name || '';
            form.querySelector('#room_type').value = roomData.room_type || '';
            form.querySelector('#capacity').value = roomData.capacity || '';
            form.querySelector('#description').value = roomData.description || '';
        }
        
        // Update UI for edit mode
        console.log("Showing equipment section");
        toggleEquipmentSection(true);
        
        // Double check the equipment section is visible
        const equipmentSection = document.getElementById('equipmentSection');
        if (equipmentSection) {
            console.log("Equipment section before:", equipmentSection.classList.contains('hidden') ? "hidden" : "visible");
            equipmentSection.classList.remove('hidden');
            console.log("Equipment section after:", equipmentSection.classList.contains('hidden') ? "hidden" : "visible");
        }
        
        // Load available equipment for this room
        loadAvailableEquipment(roomId, roomData.fixed_equipment || []);
    }
}

// Function to load available equipment for a room
async function loadAvailableEquipment(roomId, currentEquipment) {
    try {
        const response = await fetch(`{% url 'website:room_available_equipment' room_id=1 %}`.replace('1', roomId), {
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        });
        
        if (!response.ok) {
            throw new Error('Failed to load available equipment');
        }
        
        const data = await response.json();
        const container = document.getElementById('equipmentContainer');
        
        if (container) {
            // Clear existing content
            container.innerHTML = '';
            
            if (data.equipment && data.equipment.length > 0) {
                // Create HTML for each equipment
                data.equipment.forEach(equipment => {
                    const isChecked = equipment.is_fixed ? 'checked' : '';
                    
                    const equipmentItem = document.createElement('label');
                    equipmentItem.className = 'inline-flex items-center w-full cursor-pointer hover:bg-gray-700 p-2 rounded-md';
                    equipmentItem.innerHTML = `
                        <input type="checkbox" 
                               name="fixed_equipment" 
                               value="${equipment.equipment_id}" 
                               ${isChecked}
                               class="form-checkbox h-4 w-4 text-primary border-gray-600 rounded bg-gray-700 focus:ring-primary">
                        <span class="ml-2 text-sm text-white">${equipment.name} (${equipment.code})</span>
                    `;
                    container.appendChild(equipmentItem);
                });
            } else {
                container.innerHTML = '<p class="text-white/70 text-center py-4">{% trans "No available equipment found" %}</p>';
            }
        }
    } catch (error) {
        console.error('Error loading available equipment:', error);
        const container = document.getElementById('equipmentContainer');
        if (container) {
            container.innerHTML = '<p class="text-red-500 text-center py-4">{% trans "Error loading equipment" %}</p>';
        }
    }
}

function handleSave() {
    const form = document.getElementById('roomForm');
    if (!form) return;

    // Get form data
    const formData = {
        name: form.querySelector('#name').value,
        room_type: form.querySelector('#room_type').value,
        capacity: form.querySelector('#capacity').value,
        description: form.querySelector('#description').value || ''
    };

    // Check if we're editing an existing room
    const isEditing = window.isEditing;
    const currentRoomId = window.currentRoomId;
    
    // Only include equipment data when editing
    if (isEditing) {
        formData.fixed_equipment = Array.from(form.querySelectorAll('input[name="fixed_equipment"]:checked')).map(cb => cb.value);
    }
    
    let url = '{% url "website:create_room" %}';
    let method = 'POST';
    
    if (isEditing && currentRoomId) {
        console.log(currentRoomId);
        url = '{% url "website:update_room" room_id=1 %}'.replace('1', currentRoomId);
        method = 'PUT';
    }

    // Send request to create/update room
    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify(formData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            showSuccess(data.message);
            setTimeout(() => {
                closeRoomModal();
                window.location.reload();
            }, 1500);
        } else {
            showError(data.message);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showError('{% trans "An error occurred while saving the room" %}');
    });
}

function showError(message) {
    const errorDiv = document.getElementById('roomModalError');
    const successDiv = document.getElementById('roomModalSuccess');
    if (errorDiv && successDiv) {
        successDiv.classList.add('hidden');
        errorDiv.textContent = message;
        errorDiv.classList.remove('hidden');
    }
}

function showSuccess(message) {
    const errorDiv = document.getElementById('roomModalError');
    const successDiv = document.getElementById('roomModalSuccess');
    if (errorDiv && successDiv) {
        errorDiv.classList.add('hidden');
        successDiv.textContent = message;
        successDiv.classList.remove('hidden');
    }
}

function closeRoomModal() {
    const modal = document.getElementById('roomModal');
    if (modal) {
        modal.classList.add('hidden');
        // Reset form
        const form = document.getElementById('roomForm');
        if (form) form.reset();
        // Hide messages
        const errorDiv = document.getElementById('roomModalError');
        const successDiv = document.getElementById('roomModalSuccess');
        if (errorDiv) errorDiv.classList.add('hidden');
        if (successDiv) successDiv.classList.add('hidden');
    }
}

function getCsrfToken() {
    const csrfInput = document.querySelector('input[name="csrfmiddlewaretoken"]');
    return csrfInput ? csrfInput.value : '';
}
</script> 