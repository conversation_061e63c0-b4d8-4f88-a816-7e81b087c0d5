{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Trainers Management" %}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <!-- Page Header -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
            </svg>
            {% trans "Trainers Management" %}
        </h1>
        <button data-modal-toggle="trainerModal" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {% trans "Add Trainer" %}
        </button>
    </div>

    <!-- Filters and Search -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 mb-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div class="col-span-2">
                <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Search" %}</label>
                <div class="relative">
                    <input type="text" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 pl-10 pr-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="{% trans 'Search trainers...' %}">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <svg class="h-5 w-5 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                </div>
            </div>
            <div>
                <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Specialization" %}</label>
                <select class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="" class="custom-blue">{% trans "All Specializations" %}</option>
                    <option value="python" class="custom-blue">Python Programming</option>
                    <option value="web" class="custom-blue">Web Development</option>
                    <option value="data" class="custom-blue">Data Science</option>
                </select>
            </div>
            <div>
                <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Status" %}</label>
                <select class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                    <option value="" class="custom-blue">{% trans "All Status" %}</option>
                    <option value="active" class="custom-blue">{% trans "Active" %}</option>
                    <option value="inactive" class="custom-blue">{% trans "Inactive" %}</option>
                    <option value="onleave" class="custom-blue">{% trans "On Leave" %}</option>
                </select>
            </div>
        </div>
    </div>

    <!-- Trainers Table -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Trainer" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Contact" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Specializations" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Current Courses" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for trainer in trainers %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex items-center">
                                <div class="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white">
                                    {{ trainer.user.first_name|first|upper }}{{ trainer.user.last_name|first|upper }}
                                </div>
                                <div class="ml-4">
                                    <div class="text-sm font-medium text-white">{{ trainer.user.get_full_name }}</div>
                                    <div class="text-sm text-white/70">ID: #TR{{ trainer.id }}</div>
                                </div>
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ trainer.user.email }}</div>
                            <div class="text-sm text-white/70">{{ trainer.phone_number|default:_("No phone number") }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex flex-wrap gap-2">
                                {% for specialization in trainer.specializations.all %}
                                <span class="px-2 py-1 text-xs rounded-full bg-primary/20 text-primary border border-primary/20">
                                    {{ specialization }}
                                </span>
                                {% empty %}
                                <span class="text-sm text-white/70">{% trans "No specializations" %}</span>
                                {% endfor %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if trainer.courses.all %}
                                {% for course in trainer.courses.all %}
                                <div class="text-sm {% if forloop.first %}text-white{% else %}text-white/70{% endif %}">
                                    {{ course.name_en }}
                                </div>
                                {% endfor %}
                            {% else %}
                                <div class="text-sm text-white/70">{% trans "No active courses" %}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full {% if trainer.is_active %}bg-green-100 text-green-800{% else %}bg-red-100 text-red-800{% endif %}">
                                {% if trainer.is_active %}
                                    {% trans "Active" %}
                                {% else %}
                                    {% trans "Inactive" %}
                                {% endif %}
                            </span>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <div class="flex justify-end space-x-3">
                                <button onclick="deactivateTrainer({{ trainer.id }})" class="text-red-400 hover:text-red-500">
                                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                    </svg>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% empty %}
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-white/70">
                            {% trans "No trainers found" %}
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        <!-- Pagination -->
        <div class="bg-white/5 px-4 py-3 flex items-center justify-between border-t border-white/10 sm:px-6">
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
                <div>
                    <p class="text-sm text-white/70">
                        {% trans "Showing" %} <span class="font-medium">1</span> {% trans "to" %} <span class="font-medium">10</span> {% trans "of" %} <span class="font-medium">20</span> {% trans "results" %}
                    </p>
                </div>
                <div>
                    <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-l-md border border-white/10 bg-white/5 text-sm font-medium text-white/70 hover:bg-white/10">
                            <span class="sr-only">Previous</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-white/10 bg-primary text-sm font-medium text-white">1</a>
                        <a href="#" class="relative inline-flex items-center px-4 py-2 border border-white/10 bg-white/5 text-sm font-medium text-white/70 hover:bg-white/10">2</a>
                        <a href="#" class="relative inline-flex items-center px-2 py-2 rounded-r-md border border-white/10 bg-white/5 text-sm font-medium text-white/70 hover:bg-white/10">
                            <span class="sr-only">Next</span>
                            <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                            </svg>
                        </a>
                    </nav>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add/Edit Trainer Modal -->
<div id="trainerModal" class="hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-[#1a2542] rounded-lg shadow-xl w-full max-w-2xl">
            <!-- Modal Header -->
            <div class="flex items-center justify-between p-4 border-b border-white/10">
                <h3 class="text-xl font-semibold text-white" id="modalTitle">
                    {% trans "Add Trainer" %}
                </h3>
                <button onclick="closeTrainerModal()" class="text-white/70 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Modal Content -->
            <div class="p-4">
                <!-- Tab Navigation -->
                <div class="flex border-b border-white/10 mb-4">
                    <button onclick="switchTab('existing')" class="tab-btn active-tab px-4 py-2 text-white/70 hover:text-white border-b-2 border-transparent">
                        {% trans "Existing User" %}
                    </button>
                    {% comment %} <button onclick="switchTab('new')" class="tab-btn px-4 py-2 text-white/70 hover:text-white border-b-2 border-transparent">
                        {% trans "New User" %}
                    </button> {% endcomment %}
                </div>

                <!-- Existing User Tab -->
                <div id="existing-tab" class="tab-content">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Search Users" %}</label>
                        <div class="relative">
                            <input type="text" id="userSearch" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 pl-10 pr-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="{% trans 'Search by name or email...' %}">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-5 w-5 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                                </svg>
                            </div>
                        </div>
                    </div>

                    <!-- Users List -->
                    <div class="max-h-60 overflow-y-auto mb-4">
                        <div id="usersList" class="space-y-2">
                            <!-- Users will be populated here via JavaScript -->
                        </div>
                    </div>
                </div>

                {% comment %} <!-- New User Tab -->
                <div id="new-tab" class="tab-content hidden">
                    <form id="newTrainerForm" class="space-y-4">
                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-white/70 mb-1">{% trans "First Name" %}</label>
                                <input type="text" name="first_name" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Last Name" %}</label>
                                <input type="text" name="last_name" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Email" %}</label>
                            <input type="email" name="email" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Username" %}</label>
                            <input type="text" name="username" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-white/70 mb-1">{% trans "Password" %}</label>
                            <input type="password" name="password" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                        </div>
                    </form>
                </div> {% endcomment %}
            </div>

            <!-- Modal Footer -->
            <div class="flex items-center justify-end p-4 border-t border-white/10">
                <button onclick="closeTrainerModal()" class="px-4 py-2 text-white/70 hover:text-white mr-2">
                    {% trans "Cancel" %}
                </button>
                <button onclick="saveTrainer()" class="px-4 py-2 bg-primary hover:bg-primary/90 text-white rounded-md">
                    {% trans "Save" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    // Add this script section at the bottom of the file
    function openTrainerModal() {
        document.getElementById('trainerModal').classList.remove('hidden');
        loadUsers(); // Load existing users when modal opens
    }

    function closeTrainerModal() {
        document.getElementById('trainerModal').classList.add('hidden');
    }

    function switchTab(tab) {
        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
            content.classList.add('hidden');
        });
        
        // Show selected tab content
        document.getElementById(`${tab}-tab`).classList.remove('hidden');
        
        // Update tab button styles
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.classList.remove('active-tab');
        });
        event.currentTarget.classList.add('active-tab');
    }

    async function loadUsers() {
        try {
            const response = await fetch('{% url "website:get_users" %}');
            const users = await response.json();
            const usersList = document.getElementById('usersList');
            usersList.innerHTML = users.map(user => `
                <div class="flex items-center justify-between p-2 hover:bg-white/5 rounded">
                    <div class="flex items-center">
                        <div class="h-8 w-8 rounded-full bg-primary flex items-center justify-center text-white text-sm">
                            ${user.first_name[0]}${user.last_name[0]}
                        </div>
                        <div class="ml-3">
                            <div class="text-sm font-medium text-white">${user.first_name} ${user.last_name}</div>
                            <div class="text-sm text-white/70">${user.email}</div>
                        </div>
                    </div>
                    <button onclick="promoteToTrainer(${user.id})" class="px-3 py-1 bg-primary hover:bg-primary/90 text-white text-sm rounded">
                        {% trans "Make Trainer" %}
                    </button>
                </div>
            `).join('');
        } catch (error) {
            console.error('Error loading users:', error);
        }
    }

    async function promoteToTrainer(userId) {
        try {
            const response = await fetch('{% url "website:promote_to_trainer" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify({ user_id: userId })
            });
            
            if (response.ok) {
                window.location.reload();
            } else {
                throw new Error('Failed to promote user');
            }
        } catch (error) {
            console.error('Error promoting user:', error);
        }
    }

    // Add event listener to the Add Trainer button
    document.querySelector('[data-modal-toggle="trainerModal"]').addEventListener('click', openTrainerModal);

    // CSRF token helper function
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    async function saveTrainer() {
        const activeTab = document.querySelector('.tab-btn.active-tab').textContent.trim();
        
        if (activeTab === '{% trans "New User" %}') {
            if (!validateTrainerForm()) {
                alert('{% trans "Please fill in all required fields" %}');
                return;
            }
            const form = document.getElementById('newTrainerForm');
            const formData = {
                username: form.querySelector('[name="username"]').value,
                email: form.querySelector('[name="email"]').value,
                password: form.querySelector('[name="password"]').value,
                first_name: form.querySelector('[name="first_name"]').value,
                last_name: form.querySelector('[name="last_name"]').value
            };

            try {
                const response = await fetch('{% url "website:create_trainer" %}', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-CSRFToken': getCookie('csrftoken')
                    },
                    body: JSON.stringify(formData)
                });

                const data = await response.json();
                if (response.ok) {
                    window.location.reload();
                } else {
                    alert(data.message || '{% trans "Failed to create trainer" %}');
                }
            } catch (error) {
                console.error('Error creating trainer:', error);
                alert('{% trans "An error occurred while creating the trainer" %}');
            }
        }
        // Close the modal
        closeTrainerModal();
    }

    // Add form validation
    function validateTrainerForm() {
        const form = document.getElementById('newTrainerForm');
        const requiredFields = ['username', 'email', 'password', 'first_name', 'last_name'];
        let isValid = true;

        requiredFields.forEach(field => {
            const input = form.querySelector(`[name="${field}"]`);
            if (!input.value.trim()) {
                input.classList.add('border-red-500');
                isValid = false;
            } else {
                input.classList.remove('border-red-500');
            }
        });

        return isValid;
    }
</script>

<style>
    .active-tab {
        @apply text-white border-primary;
    }
</style>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} 

{% block extra_css %}
<style>
    /* Style for dropdown options */
    select option {
        background-color: #1a2542;
        color: white;
        padding: 8px;
    }
    
    /* Style for dropdown hover state */
    select option:hover,
    select option:focus,
    select option:active,
    select option:checked {
        background-color: #2a3b5e !important;
    }
</style>
{% endblock %} 