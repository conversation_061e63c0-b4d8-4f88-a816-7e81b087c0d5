# Generated by Django 4.2.18 on 2025-06-22 14:14

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomUser',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.<PERSON>r<PERSON><PERSON>(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.BooleanField(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('email', models.EmailField(max_length=254, unique=True, verbose_name='Email')),
                ('username', models.CharField(max_length=150, unique=True, verbose_name='Username')),
                ('phone_number', models.CharField(blank=True, max_length=20, verbose_name='Phone Number')),
                ('nationality', models.CharField(blank=True, max_length=100, verbose_name='Nationality')),
                ('passport_number', models.CharField(blank=True, max_length=50, verbose_name='Passport Number')),
                ('national_id', models.CharField(blank=True, max_length=50, verbose_name='National ID')),
                ('address', models.TextField(blank=True, verbose_name='Address')),
                ('date_of_birth', models.DateField(blank=True, null=True, verbose_name='Date of Birth')),
                ('company_name', models.CharField(blank=True, max_length=200, verbose_name='Company Name')),
                ('commercial_registration_number', models.CharField(blank=True, max_length=100, verbose_name='Commercial Registration Number')),
                ('tax_registration_number', models.CharField(blank=True, max_length=100, verbose_name='Tax Registration Number')),
                ('key_person_name', models.CharField(blank=True, max_length=200, verbose_name='Key Person Name')),
                ('key_person_phone', models.CharField(blank=True, max_length=20, verbose_name='Key Person Phone')),
                ('key_person_email', models.EmailField(blank=True, max_length=254, verbose_name='Key Person Email')),
                ('employee_id', models.CharField(blank=True, max_length=50, verbose_name='Employee ID')),
                ('oracle_department', models.CharField(blank=True, max_length=200, verbose_name='Oracle Department')),
                ('oracle_position', models.CharField(blank=True, max_length=300, verbose_name='Oracle Position')),
                ('oracle_supervisor_id', models.CharField(blank=True, max_length=50, verbose_name='Oracle Supervisor ID')),
                ('oracle_hire_date', models.DateTimeField(blank=True, null=True, verbose_name='Oracle Hire Date')),
                ('oracle_grade_name', models.CharField(blank=True, max_length=100, verbose_name='Oracle Grade')),
                ('is_profile_complete', models.BooleanField(default=False, verbose_name='Is Profile Complete')),
                ('force_password_reset', models.BooleanField(default=False, verbose_name='Force Password Reset')),
                ('account_status', models.CharField(choices=[('ACTIVE', 'Active'), ('INACTIVE', 'Inactive'), ('PENDING', 'Pending Approval')], default='PENDING', max_length=20, verbose_name='Account Status')),
            ],
            options={
                'verbose_name': 'User',
                'verbose_name_plural': 'Users',
            },
        ),
        migrations.CreateModel(
            name='Brand',
            fields=[
                ('brand_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Brand',
                'verbose_name_plural': 'Brands',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('category_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='Name')),
                ('stock', models.PositiveIntegerField(default=0, verbose_name='Stock')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Category',
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='ContactInfo',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('info_type', models.CharField(choices=[('ADDRESS', 'Address'), ('EMAIL', 'Email'), ('PHONE', 'Phone'), ('HOTLINE', 'Hotline')], default='ADDRESS', max_length=50, verbose_name='Information Type')),
                ('value_en', models.CharField(max_length=500, verbose_name='Value (English)')),
                ('value_ar', models.CharField(max_length=500, verbose_name='Value (Arabic)')),
                ('icon_svg', models.TextField(blank=True, default='', verbose_name='Icon SVG')),
                ('order', models.IntegerField(default=0, verbose_name='Display Order')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Contact Information',
                'verbose_name_plural': 'Contact Information',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Corporate',
            fields=[
                ('corporate_id', models.AutoField(primary_key=True, serialize=False)),
                ('address', models.TextField(verbose_name='Address')),
                ('tax_registration_number', models.CharField(max_length=100, verbose_name='Tax Registration Number')),
                ('legal_name', models.CharField(max_length=200, verbose_name='Legal Name')),
                ('commercial_registration_number', models.CharField(max_length=100, verbose_name='Commercial Registration Number')),
                ('capacity', models.IntegerField(verbose_name='Capacity')),
                ('phone_number', models.CharField(max_length=20, verbose_name='Phone Number')),
                ('category', models.CharField(choices=[('A', 'Category A'), ('B', 'Category B'), ('C', 'Category C')], max_length=1, verbose_name='Category')),
                ('corporate_admin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Corporate',
                'verbose_name_plural': 'Corporates',
            },
        ),
        migrations.CreateModel(
            name='CorporateAdmin',
            fields=[
                ('admin_id', models.AutoField(primary_key=True, serialize=False)),
                ('position', models.CharField(blank=True, max_length=100, verbose_name='Position')),
                ('department', models.CharField(blank=True, max_length=100, verbose_name='Department')),
                ('direct_phone', models.CharField(blank=True, max_length=20, verbose_name='Direct Phone')),
                ('alternative_email', models.EmailField(blank=True, max_length=254, verbose_name='Alternative Email')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('corporate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='admin_details', to='website.corporate')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='corporate_admin_profile', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Corporate Admin',
                'verbose_name_plural': 'Corporate Admins',
                'ordering': ['corporate__legal_name', 'user__last_name'],
            },
        ),
        migrations.CreateModel(
            name='Course',
            fields=[
                ('course_id', models.AutoField(primary_key=True, serialize=False)),
                ('name_en', models.CharField(max_length=200, verbose_name='Name (English)')),
                ('name_ar', models.CharField(max_length=200, verbose_name='Name (Arabic)')),
                ('description_en', models.TextField(blank=True, default='', verbose_name='Description (English)')),
                ('description_ar', models.TextField(blank=True, default='', verbose_name='Description (Arabic)')),
                ('category', models.CharField(choices=[('AUTOMOTIVE', 'Automotive'), ('BUSINESS', 'Business Skills'), ('TECHNICAL', 'Technical'), ('PROFESSIONAL', 'Professional Development')], default='PROFESSIONAL', max_length=50, verbose_name='Category')),
                ('location', models.CharField(choices=[('ONLINE', 'Online'), ('PHYSICAL', 'Physical'), ('HYBRID', 'Hybrid')], default='PHYSICAL', max_length=50, verbose_name='Location')),
                ('session_type', models.CharField(choices=[('FULL_DAY', 'Full Day'), ('HALF_DAY', 'Half Day'), ('CUSTOM', 'Custom')], default='FULL_DAY', max_length=20, verbose_name='Session Type')),
                ('num_of_sessions', models.PositiveIntegerField(default=1, verbose_name='Number of Sessions')),
                ('capacity', models.PositiveIntegerField(default=20, verbose_name='Capacity')),
                ('prerequisite', models.TextField(blank=True, default='', verbose_name='Prerequisites')),
                ('equipment_categories', models.JSONField(blank=True, default=dict, help_text='Categories of equipment required for this course with quantities', null=True, verbose_name='Required Equipment Categories')),
                ('session_room_types', models.JSONField(blank=True, default=dict, help_text='Room type required for each session number (e.g. {"1": 1, "2": 2} where numbers are room_type_id)', null=True, verbose_name='Session Room Types')),
                ('icon_svg', models.TextField(blank=True, default='', verbose_name='Icon SVG')),
                ('image', models.ImageField(blank=True, null=True, upload_to='courses/', verbose_name='Image')),
                ('order', models.IntegerField(default=1, verbose_name='Display Order')),
                ('status', models.CharField(choices=[('ACTIVE', 'Active'), ('CANCELLED', 'Cancelled'), ('DRAFT', 'Draft'), ('ARCHIVED', 'Archived')], default='ACTIVE', max_length=20, verbose_name='Status')),
                ('cancellation_reason', models.TextField(blank=True, verbose_name='Cancellation Reason')),
                ('cancelled_at', models.DateTimeField(blank=True, null=True, verbose_name='Cancelled At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('cancelled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cancelled_courses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Course',
                'verbose_name_plural': 'Courses',
                'ordering': ['category', 'order'],
            },
        ),
        migrations.CreateModel(
            name='CourseInstance',
            fields=[
                ('instance_id', models.AutoField(primary_key=True, serialize=False)),
                ('batch_id', models.CharField(blank=True, db_index=True, help_text='Identifier for grouping sessions created in the same batch', max_length=100, null=True, verbose_name='Batch ID')),
                ('start_date', models.DateTimeField(verbose_name='Start Date')),
                ('end_date', models.DateTimeField(verbose_name='End Date')),
                ('capacity', models.PositiveIntegerField(default=0, help_text='Maximum number of students that can enroll in this course instance', verbose_name='Capacity')),
                ('course_type', models.CharField(choices=[('INDIVIDUAL', 'Individual'), ('CORPORATE', 'Corporate'), ('GB', 'GB')], default='INDIVIDUAL', max_length=20, verbose_name='Course Type')),
                ('cancellation_deadline_hours', models.PositiveIntegerField(default=72, help_text='Number of hours before course start when students can no longer cancel their reservations. Default is 72 hours (3 days).', verbose_name='Cancellation Deadline (hours)')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('deactivation_reason', models.TextField(blank=True, null=True, verbose_name='Deactivation Reason')),
                ('deactivated_at', models.DateTimeField(blank=True, null=True, verbose_name='Deactivated At')),
                ('deactivated_by', models.CharField(blank=True, max_length=255, null=True, verbose_name='Deactivated By')),
                ('corporate', models.ForeignKey(blank=True, help_text='Only required when course type is Corporate', null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.corporate', verbose_name='Corporate')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='instances', to='website.course', verbose_name='Course')),
            ],
            options={
                'verbose_name': 'Course Instance',
                'verbose_name_plural': 'Course Instances',
                'ordering': ['-start_date'],
            },
        ),
        migrations.CreateModel(
            name='EmailLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('recipient', models.EmailField(max_length=254, verbose_name='Recipient')),
                ('subject', models.CharField(max_length=255, verbose_name='Subject')),
                ('cc_emails', models.TextField(blank=True, verbose_name='CC')),
                ('status', models.CharField(max_length=20, verbose_name='Status')),
                ('transaction_id', models.CharField(blank=True, max_length=20, verbose_name='Transaction ID')),
                ('error_message', models.TextField(blank=True, verbose_name='Error Message')),
                ('sent_at', models.DateTimeField(auto_now_add=True, verbose_name='Sent At')),
            ],
            options={
                'verbose_name': 'Email Log',
                'verbose_name_plural': 'Email Logs',
                'ordering': ['-sent_at'],
            },
        ),
        migrations.CreateModel(
            name='Equipment',
            fields=[
                ('equipment_id', models.AutoField(primary_key=True, serialize=False)),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Equipment Code')),
                ('name', models.CharField(max_length=200, verbose_name='Name')),
                ('status', models.CharField(choices=[('AVAILABLE', 'Available'), ('BUSY', 'Busy'), ('BOOKED', 'Booked'), ('READY', 'Ready'), ('FIXED', 'Fixed in Room'), ('MAINTENANCE', 'Maintenance'), ('SCRAP', 'Scrap')], default='READY', max_length=20, verbose_name='Status')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='website.brand', verbose_name='Brand')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='website.category', verbose_name='Category')),
            ],
            options={
                'verbose_name': 'Equipment',
                'verbose_name_plural': 'Equipment',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='Event',
            fields=[
                ('event_id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.CharField(default='Untitled Event', max_length=200, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('capacity', models.PositiveIntegerField(default=0, verbose_name='Capacity')),
                ('start_time', models.DateTimeField(blank=True, null=True, verbose_name='Start Time')),
                ('end_time', models.DateTimeField(blank=True, null=True, verbose_name='End Time')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Event',
                'verbose_name_plural': 'Events',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='EventType',
            fields=[
                ('event_type_id', models.AutoField(primary_key=True, serialize=False)),
                ('type', models.CharField(max_length=100, verbose_name='Type')),
            ],
            options={
                'verbose_name': 'Event Type',
                'verbose_name_plural': 'Event Types',
            },
        ),
        migrations.CreateModel(
            name='FormContent',
            fields=[
                ('content_id', models.AutoField(primary_key=True, serialize=False)),
                ('content', models.TextField(verbose_name='Content')),
                ('content_type', models.CharField(choices=[('TEXT', 'Text'), ('FILE', 'File'), ('LINK', 'Link')], max_length=20, verbose_name='Content Type')),
            ],
            options={
                'verbose_name': 'Form Content',
                'verbose_name_plural': 'Form Contents',
            },
        ),
        migrations.CreateModel(
            name='Holiday',
            fields=[
                ('holiday_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=200, verbose_name='Holiday Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('date', models.DateField(verbose_name='Holiday Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(blank=True, default=True, null=True, verbose_name='Is Active')),
            ],
            options={
                'verbose_name': 'Holiday',
                'verbose_name_plural': 'Holidays',
                'ordering': ['-date'],
            },
        ),
        migrations.CreateModel(
            name='Questionnaire',
            fields=[
                ('questionnaire_id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(default='Untitled Questionnaire', max_length=200, verbose_name='Title')),
                ('session_number', models.PositiveIntegerField(default=1, verbose_name='Session Number')),
                ('questions', models.JSONField(blank=True, default=list, null=True, verbose_name='Questions')),
                ('comments', models.TextField(blank=True, verbose_name='Comments')),
                ('total_score', models.PositiveIntegerField(default=0, help_text='Automatically calculated from the sum of all question scores', verbose_name='Total Score')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.course')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_questionnaires', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Questionnaire',
                'verbose_name_plural': 'Questionnaires',
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('role_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='Role Name')),
            ],
            options={
                'verbose_name': 'Role',
                'verbose_name_plural': 'Roles',
            },
        ),
        migrations.CreateModel(
            name='Room',
            fields=[
                ('room_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(default='Room', max_length=100, verbose_name='Name')),
                ('capacity', models.IntegerField(verbose_name='Capacity')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('status', models.CharField(choices=[('BUSY', 'Busy'), ('BOOKED', 'Booked'), ('READY', 'Ready')], default='READY', max_length=20, verbose_name='Status')),
                ('fixed_equipment', models.ManyToManyField(blank=True, help_text='Equipment permanently assigned to this room', related_name='fixed_in_rooms', to='website.equipment', verbose_name='Fixed Equipment')),
            ],
            options={
                'verbose_name': 'Room',
                'verbose_name_plural': 'Rooms',
            },
        ),
        migrations.CreateModel(
            name='RoomType',
            fields=[
                ('room_type_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Room Type',
                'verbose_name_plural': 'Room Types',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Section',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Code')),
                ('name_en', models.CharField(max_length=100, verbose_name='Name (English)')),
                ('name_ar', models.CharField(max_length=100, verbose_name='Name (Arabic)')),
                ('description_en', models.TextField(blank=True, verbose_name='Description (English)')),
                ('description_ar', models.TextField(blank=True, verbose_name='Description (Arabic)')),
                ('icon_svg', models.TextField(blank=True, verbose_name='Icon SVG')),
                ('order', models.IntegerField(default=0, verbose_name='Display Order')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Section',
                'verbose_name_plural': 'Sections',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Session',
            fields=[
                ('session_id', models.CharField(editable=False, max_length=50, primary_key=True, serialize=False, verbose_name='Session ID')),
                ('start_date', models.DateTimeField(verbose_name='Start Date')),
                ('end_date', models.DateTimeField(verbose_name='End Date')),
                ('slot_type', models.CharField(choices=[('MORNING', 'Morning (9:00 AM - 12:00 PM)'), ('AFTERNOON', 'Afternoon (1:00 PM - 4:00 PM)'), ('FULL_DAY', 'Full Day (Both Slots)'), ('CUSTOM', 'Custom Time')], default='FULL_DAY', max_length=20, verbose_name='Slot Type')),
                ('location', models.CharField(choices=[('PHYSICAL', 'Physical'), ('ONLINE', 'Online')], default='PHYSICAL', max_length=10, verbose_name='Location')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(default=django.utils.timezone.now, verbose_name='Updated At')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.course', verbose_name='Course')),
                ('required_equipment', models.ManyToManyField(blank=True, help_text='Equipment required for this session', to='website.equipment', verbose_name='Required Equipment')),
                ('room', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='website.room', verbose_name='Room')),
            ],
            options={
                'verbose_name': 'Session',
                'verbose_name_plural': 'Sessions',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Slide',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_en', models.CharField(max_length=200, verbose_name='Title (English)')),
                ('title_ar', models.CharField(max_length=200, verbose_name='Title (Arabic)')),
                ('description_en', models.TextField(blank=True, default='', verbose_name='Description (English)')),
                ('description_ar', models.TextField(blank=True, default='', verbose_name='Description (Arabic)')),
                ('image', models.ImageField(upload_to='slides/', verbose_name='Image')),
                ('order', models.IntegerField(default=0, verbose_name='Display Order')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'Slide',
                'verbose_name_plural': 'Slides',
                'ordering': ['order'],
            },
        ),
        migrations.CreateModel(
            name='Survey',
            fields=[
                ('survey_id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(help_text='The main title of the survey.', max_length=200, verbose_name='Title')),
                ('questions', models.JSONField(blank=True, default=list, help_text='Survey questions in JSON format', null=True, verbose_name='Questions')),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='Start Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('course_instance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='surveys', to='website.courseinstance', verbose_name='Course Instance')),
            ],
            options={
                'verbose_name': 'Survey',
                'verbose_name_plural': 'Surveys',
            },
        ),
        migrations.CreateModel(
            name='Trainer',
            fields=[
                ('trainer_id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('BUSY', 'Busy'), ('BOOKED', 'Booked'), ('READY', 'Ready')], default='READY', max_length=20, verbose_name='Status')),
                ('is_supervisor', models.BooleanField(default=False, help_text='Supervisors can assign courses to trainers', verbose_name='Is Supervisor')),
                ('courses', models.ManyToManyField(to='website.course')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Trainer',
                'verbose_name_plural': 'Trainers',
            },
        ),
        migrations.CreateModel(
            name='UserType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='Code')),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'User Type',
                'verbose_name_plural': 'User Types',
            },
        ),
        migrations.CreateModel(
            name='TrainerAvailability',
            fields=[
                ('trainer_availability_id', models.AutoField(primary_key=True, serialize=False)),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='Start Date')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='End Date')),
                ('booking_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='Booking ID')),
                ('booking_type', models.CharField(blank=True, choices=[('SESSION', 'Session'), ('EVENT', 'Event')], max_length=10, null=True, verbose_name='Booking Type')),
                ('event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.event')),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.session')),
                ('trainer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_slots', to='website.trainer')),
            ],
            options={
                'verbose_name': 'Trainer Availability',
                'verbose_name_plural': 'Trainer Availabilities',
            },
        ),
        migrations.AddField(
            model_name='session',
            name='trainers',
            field=models.ManyToManyField(related_name='sessions', to='website.trainer', verbose_name='Trainers'),
        ),
        migrations.AddField(
            model_name='room',
            name='room_type',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='rooms', to='website.roomtype', verbose_name='Room Type'),
        ),
        migrations.CreateModel(
            name='Reservation',
            fields=[
                ('reservation_id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('WAITING_TO_PAY', 'Waiting to Pay'), ('WAITING_LIST', 'Waiting List'), ('UPCOMING', 'Upcoming'), ('IN_PROGRESS', 'In Progress'), ('COMPLETED', 'Completed'), ('CANCELLED', 'Cancelled')], default='WAITING_TO_PAY', max_length=20, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('cancellation_reason', models.TextField(blank=True, null=True, verbose_name='Cancellation Reason')),
                ('cancelled_at', models.DateTimeField(blank=True, null=True, verbose_name='Cancelled At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('cancelled_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='cancelled_reservations', to=settings.AUTH_USER_MODEL)),
                ('course_instance', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='website.courseinstance', verbose_name='Course Instance')),
                ('event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.event')),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to='website.session')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('course_instance', 'user')},
            },
        ),
        migrations.CreateModel(
            name='Payment',
            fields=[
                ('payment_id', models.AutoField(primary_key=True, serialize=False)),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='Amount')),
                ('payment_method', models.CharField(choices=[('CASH', 'Cash'), ('CREDIT_CARD', 'Credit Card'), ('BANK_TRANSFER', 'Bank Transfer')], max_length=20, verbose_name='Payment Method')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed'), ('FAILED', 'Failed'), ('REFUNDED', 'Refunded')], max_length=20, verbose_name='Status')),
                ('payment_source', models.CharField(choices=[('INDIVIDUAL', 'Individual'), ('EXTERNAL_COMPANY', 'External Company'), ('INTERNAL_GB', 'Internal GB')], max_length=20, verbose_name='Payment Source')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Payment',
                'verbose_name_plural': 'Payments',
            },
        ),
        migrations.CreateModel(
            name='OTPVerification',
            fields=[
                ('otp_id', models.AutoField(primary_key=True, serialize=False)),
                ('employee_id', models.CharField(max_length=50, verbose_name='Employee ID')),
                ('email', models.EmailField(max_length=254, verbose_name='Email')),
                ('otp_code', models.CharField(max_length=6, verbose_name='OTP Code')),
                ('attempts', models.PositiveIntegerField(default=0, verbose_name='Attempts')),
                ('max_attempts', models.PositiveIntegerField(default=3, verbose_name='Max Attempts')),
                ('is_used', models.BooleanField(default=False, verbose_name='Is Used')),
                ('is_expired', models.BooleanField(default=False, verbose_name='Is Expired')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('expires_at', models.DateTimeField(verbose_name='Expires At')),
                ('used_at', models.DateTimeField(blank=True, null=True, verbose_name='Used At')),
            ],
            options={
                'verbose_name': 'OTP Verification',
                'verbose_name_plural': 'OTP Verifications',
                'ordering': ['-created_at'],
                'indexes': [models.Index(fields=['employee_id', 'created_at'], name='website_otp_employe_02e562_idx'), models.Index(fields=['email', 'otp_code'], name='website_otp_email_5a08f8_idx')],
            },
        ),
        migrations.CreateModel(
            name='Notification',
            fields=[
                ('notification_id', models.AutoField(primary_key=True, serialize=False)),
                ('message', models.TextField(verbose_name='Message')),
                ('status', models.BooleanField(default=False, verbose_name='Read Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('delivered_at', models.DateTimeField(blank=True, null=True, verbose_name='Delivered At')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Notification',
                'verbose_name_plural': 'Notifications',
            },
        ),
        migrations.CreateModel(
            name='Model',
            fields=[
                ('model_id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, verbose_name='Name')),
                ('stock', models.PositiveIntegerField(default=0, verbose_name='Stock Quantity')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('brand', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='models', to='website.brand')),
            ],
            options={
                'verbose_name': 'Model',
                'verbose_name_plural': 'Models',
                'ordering': ['brand__name', 'name'],
                'unique_together': {('brand', 'name')},
            },
        ),
        migrations.CreateModel(
            name='GB_Employee',
            fields=[
                ('employee_number', models.CharField(max_length=50, primary_key=True, serialize=False, unique=True, verbose_name='Employee Number')),
                ('full_name', models.CharField(blank=True, max_length=500, verbose_name='Full Name')),
                ('arabic_name', models.CharField(blank=True, max_length=250, verbose_name='Arabic Name')),
                ('english_name', models.CharField(blank=True, max_length=250, verbose_name='English Name')),
                ('oracle_username', models.CharField(blank=True, max_length=100, verbose_name='Oracle Username')),
                ('department', models.CharField(blank=True, max_length=200, verbose_name='Department')),
                ('position_name', models.CharField(blank=True, max_length=300, verbose_name='Position Name')),
                ('organization_id', models.CharField(blank=True, max_length=50, verbose_name='Organization ID')),
                ('supervisor_id', models.CharField(blank=True, max_length=50, verbose_name='Supervisor ID')),
                ('supervisor_user_name', models.CharField(blank=True, max_length=500, verbose_name='Supervisor User Name')),
                ('hire_date', models.DateTimeField(blank=True, null=True, verbose_name='Hire Date')),
                ('payroll_id', models.CharField(blank=True, max_length=50, verbose_name='Payroll ID')),
                ('grade_name', models.CharField(blank=True, max_length=100, verbose_name='Grade Name')),
                ('grade_id', models.CharField(blank=True, max_length=50, verbose_name='Grade ID')),
                ('oracle_phone_number', models.CharField(blank=True, max_length=20, null=True, verbose_name='Oracle Phone Number')),
                ('oracle_email_address', models.EmailField(blank=True, max_length=254, verbose_name='Oracle Email Address')),
                ('head_department', models.CharField(blank=True, max_length=50, null=True, verbose_name='Head Department Employee ID')),
                ('cost_center', models.IntegerField(blank=True, null=True, verbose_name='Cost Center')),
                ('last_sync_date', models.DateTimeField(auto_now=True, verbose_name='Last Sync Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
            ],
            options={
                'verbose_name': 'GB Employee',
                'verbose_name_plural': 'GB Employees',
                'ordering': ['employee_number'],
                'indexes': [models.Index(fields=['employee_number'], name='website_gb__employe_8388c3_idx'), models.Index(fields=['oracle_email_address'], name='website_gb__oracle__4522f3_idx'), models.Index(fields=['oracle_username'], name='website_gb__oracle__769ff7_idx'), models.Index(fields=['supervisor_id'], name='website_gb__supervi_b4d039_idx'), models.Index(fields=['organization_id'], name='website_gb__organiz_d5793b_idx')],
            },
        ),
        migrations.CreateModel(
            name='Form',
            fields=[
                ('form_id', models.AutoField(primary_key=True, serialize=False)),
                ('type', models.CharField(choices=[('EXAM', 'Exam'), ('QUIZ', 'Quiz'), ('REVIEW', 'Review'), ('ASSESSMENT', 'Assessment')], max_length=20, verbose_name='Type')),
                ('content', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.formcontent')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.course')),
            ],
            options={
                'verbose_name': 'Form',
                'verbose_name_plural': 'Forms',
            },
        ),
        migrations.CreateModel(
            name='Feature',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title_en', models.CharField(max_length=200, verbose_name='Title (English)')),
                ('title_ar', models.CharField(max_length=200, verbose_name='Title (Arabic)')),
                ('description_en', models.TextField(blank=True, verbose_name='Description (English)')),
                ('description_ar', models.TextField(blank=True, verbose_name='Description (Arabic)')),
                ('icon_svg', models.TextField(blank=True, verbose_name='Icon SVG')),
                ('order', models.IntegerField(default=0, verbose_name='Display Order')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('section', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='features', to='website.section')),
            ],
            options={
                'verbose_name': 'Feature',
                'verbose_name_plural': 'Features',
                'ordering': ['section', 'order'],
            },
        ),
        migrations.AddField(
            model_name='event',
            name='event_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='website.eventtype', verbose_name='Event Type'),
        ),
        migrations.AddField(
            model_name='event',
            name='required_equipment',
            field=models.ManyToManyField(blank=True, help_text='Equipment required for this event', to='website.equipment', verbose_name='Required Equipment'),
        ),
        migrations.AddField(
            model_name='event',
            name='room',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='events', to='website.room', verbose_name='Room'),
        ),
        migrations.CreateModel(
            name='EquipmentMaintenance',
            fields=[
                ('maintenance_id', models.AutoField(primary_key=True, serialize=False)),
                ('maintenance_date', models.DateTimeField(verbose_name='Maintenance Date')),
                ('description', models.TextField(verbose_name='Maintenance Description')),
                ('performed_by', models.CharField(max_length=200, verbose_name='Performed By')),
                ('cost', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='Cost')),
                ('next_maintenance_date', models.DateTimeField(blank=True, null=True, verbose_name='Next Maintenance Date')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='maintenance_records', to='website.equipment')),
            ],
            options={
                'verbose_name': 'Equipment Maintenance',
                'verbose_name_plural': 'Equipment Maintenance Records',
                'ordering': ['-maintenance_date'],
            },
        ),
        migrations.AddField(
            model_name='equipment',
            name='model',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='website.model', verbose_name='Model'),
        ),
        migrations.CreateModel(
            name='EmergencyCancellationRequest',
            fields=[
                ('request_id', models.AutoField(primary_key=True, serialize=False)),
                ('reason', models.TextField(help_text='Please explain why you need to cancel after the deadline', verbose_name='Cancellation Reason')),
                ('attachment', models.FileField(blank=True, help_text='Upload any supporting documents (e.g., medical certificate)', null=True, upload_to='cancellation_requests/', verbose_name='Supporting Document')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='PENDING', max_length=20, verbose_name='Status')),
                ('admin_notes', models.TextField(blank=True, help_text='Notes for administrative use only', verbose_name='Admin Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Requested At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_cancellations', to=settings.AUTH_USER_MODEL)),
                ('reservation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='emergency_cancellations', to='website.reservation')),
            ],
            options={
                'verbose_name': 'Emergency Cancellation Request',
                'verbose_name_plural': 'Emergency Cancellation Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='courseinstance',
            name='sessions',
            field=models.ManyToManyField(related_name='course_instance', to='website.session', verbose_name='Sessions'),
        ),
        migrations.CreateModel(
            name='CourseContent',
            fields=[
                ('content_id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.CharField(max_length=200, verbose_name='Title')),
                ('description', models.TextField(blank=True, verbose_name='Description')),
                ('file', models.FileField(upload_to='course_contents/', verbose_name='File')),
                ('content_type', models.CharField(choices=[('PRESENTATION', 'Presentation'), ('PDF', 'PDF Document'), ('IMAGE', 'Image'), ('VIDEO', 'Video'), ('DOCUMENT', 'Document'), ('OTHER', 'Other')], default='DOCUMENT', max_length=20, verbose_name='Content Type')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='contents', to='website.course', verbose_name='Course')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_contents', to=settings.AUTH_USER_MODEL, verbose_name='Created By')),
            ],
            options={
                'verbose_name': 'Course Content',
                'verbose_name_plural': 'Course Contents',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CorporateUserCancellationRequest',
            fields=[
                ('request_id', models.AutoField(primary_key=True, serialize=False)),
                ('reason', models.TextField(help_text='Please explain why you need to cancel this reservation', verbose_name='Cancellation Reason')),
                ('status', models.CharField(choices=[('PENDING', 'Pending Review'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected')], default='PENDING', max_length=20, verbose_name='Status')),
                ('admin_notes', models.TextField(blank=True, help_text='Notes from corporate admin', verbose_name='Admin Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Requested At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Last Updated')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_corporate_cancellations', to=settings.AUTH_USER_MODEL)),
                ('reservation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='corporate_cancellations', to='website.reservation')),
            ],
            options={
                'verbose_name': 'Corporate User Cancellation Request',
                'verbose_name_plural': 'Corporate User Cancellation Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CorporateAdminRequest',
            fields=[
                ('request_id', models.AutoField(primary_key=True, serialize=False)),
                ('course_type', models.CharField(choices=[('ONLINE', 'Online'), ('OFFLINE', 'Offline')], default='OFFLINE', max_length=10, verbose_name='Course Type')),
                ('capacity', models.PositiveIntegerField(default=20, verbose_name='Capacity')),
                ('notes', models.TextField(blank=True, verbose_name='Notes')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled'), ('PENDING_CANCEL', 'Pending Cancellation')], default='PENDING', max_length=14, verbose_name='Status')),
                ('admin_notes', models.TextField(blank=True, verbose_name='Admin Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('corporate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_requests', to='website.corporate', verbose_name='Corporate')),
                ('corporate_admin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='course_requests', to='website.corporateadmin', verbose_name='Corporate Admin')),
                ('course', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='corporate_requests', to='website.course', verbose_name='Course')),
            ],
            options={
                'verbose_name': 'Corporate Admin Request',
                'verbose_name_plural': 'Corporate Admin Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='CorporateAdminNewCourseRequest',
            fields=[
                ('request_id', models.AutoField(primary_key=True, serialize=False)),
                ('course_name', models.CharField(max_length=200, verbose_name='Course Name')),
                ('course_description', models.TextField(blank=True, verbose_name='Course Description')),
                ('course_type', models.CharField(choices=[('ONLINE', 'Online'), ('OFFLINE', 'Offline')], default='OFFLINE', max_length=10, verbose_name='Course Type')),
                ('capacity', models.PositiveIntegerField(default=20, verbose_name='Capacity')),
                ('notes', models.TextField(blank=True, verbose_name='Additional Notes')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled'), ('PENDING_CANCEL', 'Pending Cancellation')], default='PENDING', max_length=14, verbose_name='Status')),
                ('admin_notes', models.TextField(blank=True, verbose_name='Admin Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='Updated At')),
                ('corporate', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_course_requests', to='website.corporate', verbose_name='Corporate')),
                ('corporate_admin', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='new_course_requests', to='website.corporateadmin', verbose_name='Corporate Admin')),
            ],
            options={
                'verbose_name': 'Corporate Admin New Course Request',
                'verbose_name_plural': 'Corporate Admin New Course Requests',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Attendance',
            fields=[
                ('attendance_id', models.AutoField(primary_key=True, serialize=False)),
                ('first_half', models.BooleanField(verbose_name='First Half')),
                ('second_half', models.BooleanField(verbose_name='Second Half')),
                ('session', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.session')),
                ('trainer', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='recorded_attendances', to='website.trainer')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Attendance',
                'verbose_name_plural': 'Attendances',
            },
        ),
        migrations.AddField(
            model_name='customuser',
            name='gb_employee',
            field=models.OneToOneField(blank=True, help_text='Link to Oracle employee data for internal GB employees', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='user_profile', to='website.gb_employee', verbose_name='GB Employee Profile'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='custom_user_set', to='auth.group', verbose_name='Groups'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='custom_user_set', to='auth.permission', verbose_name='User Permissions'),
        ),
        migrations.AddField(
            model_name='customuser',
            name='user_type',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.PROTECT, to='website.usertype', verbose_name='User Type'),
        ),
        migrations.CreateModel(
            name='SurveyResponse',
            fields=[
                ('response_id', models.AutoField(primary_key=True, serialize=False)),
                ('question_responses', models.JSONField(default=dict, verbose_name='Question Responses')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='Comment')),
                ('status', models.CharField(choices=[('PENDING', 'Pending'), ('COMPLETED', 'Completed')], default='PENDING', max_length=20, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('reservation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='survey_responses', to='website.reservation')),
                ('survey', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='website.survey')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Survey Response',
                'verbose_name_plural': 'Survey Responses',
                'unique_together': {('survey', 'user', 'reservation')},
            },
        ),
        migrations.CreateModel(
            name='RoomAvailability',
            fields=[
                ('room_availability_id', models.AutoField(primary_key=True, serialize=False)),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='Start Date')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='End Date')),
                ('booking_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='Booking ID')),
                ('booking_type', models.CharField(blank=True, choices=[('SESSION', 'Session'), ('EVENT', 'Event')], max_length=10, null=True, verbose_name='Booking Type')),
                ('event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.event')),
                ('room', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_slots', to='website.room')),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.session')),
            ],
            options={
                'verbose_name': 'Room Availability',
                'verbose_name_plural': 'Room Availabilities',
                'indexes': [models.Index(fields=['room', 'start_date', 'end_date'], name='website_roo_room_id_5a5723_idx'), models.Index(fields=['booking_type', 'booking_id'], name='website_roo_booking_45cc12_idx')],
            },
        ),
        migrations.CreateModel(
            name='QuestionnaireResponse',
            fields=[
                ('response_id', models.AutoField(primary_key=True, serialize=False)),
                ('session_number', models.PositiveIntegerField(default=1, verbose_name='Session Number')),
                ('question_responses', models.JSONField(default=dict, verbose_name='Question Responses')),
                ('comment', models.TextField(blank=True, null=True, verbose_name='Comment')),
                ('max_total_score', models.PositiveIntegerField(default=0, help_text='Maximum possible score for this questionnaire', verbose_name='Maximum Total Score')),
                ('total_score', models.PositiveIntegerField(default=0, help_text='Total score achieved by the user', verbose_name='User Total Score')),
                ('status', models.CharField(choices=[('PENDING_SUBMISSION', 'Pending Submission'), ('COMPLETED_SUBMITTED', 'Completed and Submitted'), ('PENDING_TRAINER_REVIEW', 'Pending Trainer Review'), ('COMPLETED_TRAINER_REVIEWED', 'Completed and Trainer Reviewed')], default='PENDING_SUBMISSION', max_length=30, verbose_name='Status')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('completed_at', models.DateTimeField(blank=True, null=True, verbose_name='Completed At')),
                ('course', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='questionnaire_responses', to='website.course')),
                ('course_instance', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='questionnaire_responses', to='website.courseinstance')),
                ('questionnaire', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='responses', to='website.questionnaire')),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='questionnaire_responses', to='website.session')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Questionnaire Response',
                'verbose_name_plural': 'Questionnaire Responses',
                'unique_together': {('questionnaire', 'user', 'course', 'session_number')},
            },
        ),
        migrations.CreateModel(
            name='EquipmentAvailability',
            fields=[
                ('equipment_availability_id', models.AutoField(primary_key=True, serialize=False)),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='Start Date')),
                ('end_date', models.DateTimeField(blank=True, null=True, verbose_name='End Date')),
                ('booking_id', models.CharField(blank=True, max_length=50, null=True, verbose_name='Booking ID')),
                ('booking_type', models.CharField(blank=True, choices=[('SESSION', 'Session'), ('EVENT', 'Event')], max_length=10, null=True, verbose_name='Booking Type')),
                ('equipment', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availability_slots', to='website.equipment')),
                ('event', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.event')),
                ('session', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='website.session')),
            ],
            options={
                'verbose_name': 'Equipment Availability',
                'verbose_name_plural': 'Equipment Availabilities',
                'indexes': [models.Index(fields=['equipment', 'start_date', 'end_date'], name='website_equ_equipme_a6003d_idx'), models.Index(fields=['booking_type', 'booking_id'], name='website_equ_booking_fd5483_idx')],
            },
        ),
    ]
