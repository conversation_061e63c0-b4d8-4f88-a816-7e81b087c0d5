from django.core.management.base import BaseCommand
from django.db import connection
from website.models import Equipment

class Command(BaseCommand):
    help = 'Patch Equipment model with save/delete methods for category stock'

    def handle(self, *args, **options):
        self.stdout.write('Patching Equipment model for stock tracking...')

        # Monkey patch the Equipment model
        orig_save = Equipment.save
        
        def patched_save(self, *args, **kwargs):
            # Check if this is a new equipment (to update category stock)
            is_new = self.pk is None
            
            # Call the original save method
            result = orig_save(self, *args, **kwargs)
            
            # Update category stock for new equipment
            if is_new:
                self.category.stock += 1
                self.category.save(update_fields=['stock'])
                
            return result
        
        # Replace the original save method with our patched version
        Equipment.save = patched_save
        
        # Monkey patch the delete method
        orig_delete = Equipment.delete
        
        def patched_delete(self, *args, **kwargs):
            # Get category reference before deletion
            category = self.category
            
            # Call the original delete method
            result = orig_delete(self, *args, **kwargs)
            
            # Update category stock after deletion
            category.stock -= 1
            category.save(update_fields=['stock'])
            
            return result
        
        # Replace the original delete method with our patched version
        Equipment.delete = patched_delete
        
        self.stdout.write(self.style.SUCCESS('Equipment model patched successfully for stock tracking!')) 