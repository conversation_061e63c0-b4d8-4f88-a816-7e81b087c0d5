# Learning Management System (LMS) Business Requirements Document

## User Types
1. **Internal GB Corp User**
   - All Active Employee Profiles should be directly integrated to LMS
   - Internal users are created for all Active White Collar through Integration
   - Profiles are assigned to GB Customer since they are not eligible for payment cycle
   - Profile data is not editable since it is retrieved from Oracle EBS
   - Profile Extra field: Employee ID

2. **External Individual**
   - User registers through website that integrates Oracle EBS to Define Customer in Oracle
   - A new profile is created with basic data that can be modified later by External Individual/Sys Admin

3. **External Corporate Contact Person**
   - User registers through website that integrates Oracle EBS to Define Customer in Oracle
   - A new profile is created for the organization with basic data that can be modified later by External Corporate/Sys Admin
   - Profile Extra fields: Key Person Name, Key Person Phone, Key Person Email, Commercial registration Number

4. **External Corporate -Trainee**
   - User registers through website to be linked with External Corporate Customer
   - A new profile is created to the organization with basic data that can be modified later either by External Corporate/Sys Admin/External Corporate User

5. **System Admin**
   - Profile is already created through integration with Oracle EBS
   - User is created through integration and roles are added through LMS User Setup screen
   - Customer is assigned as GB

6. **Trainer/Trainer Manager**
   - Profile is already created through integration with Oracle EBS
   - User is created through integration and roles are added through LMS User Setup screen
   - Customer is assigned as GB

## Interface Structure

### Backend
1. Create/Update Users
2. Create/Update/Delete Responsibility
3. Create/Update/Delete Profiles
4. Create/Update/Delete Customers
5. Create/Update/Delete Items Category
6. Create/Update/Delete Items
7. Create/Update/Delete Rooms
8. Create/Update/Delete Courses categories
9. Create/Update/Delete Learning Path
10. Create/Update/Delete Courses
11. Create/Update/Delete Schedules
12. Enroll Batch of trainee to courses
13. Create batch of users
14. Create batch of profiles
15. Schedule Calendar
16. Trainers Calendar
17. Rooms Calendar
18. Add/remove materials to a schedule
19. Insert grading to batch of trainee
20. Create/Add Assignments/Quizzes/Exams to batch of trainee
21. Requests Approval
   - Trainee Schedule enrollment
   - Payment Request
   - Course Cancelation
22. Support Tracking (Receive/Reply/track) complaints.
23. Submit/reply/receive messages
24. Tracking requests status
25. Reports
26. Dashboards
27. Create certificates
28. Create badges
29. Add certificates to trainee
30. Create badges to trainee
31. Add Attendance details (Batch/Individual)
32. Defining Rating Scale
33. Defining Templates
34. Defining Items linked with templates
35. Calendar Definition

### Frontend
1. Home Page
2. Notifications
3. User Registration
4. Forgot/Change Password
5. View overview for sample of courses
6. Searching courses with filtration criteria (Course Code/Name/Category)
7. User Enroll himself /Subordinate in a course
8. Approve on subordinate's course request
9. View enrolled course material/Scores & Grading /Assignments/atteendance
10. Add/Delete/Update course's material/Scores & Grading /Assignments
11. Submit/reply/receive complain/Support request
12. Submit/reply/receive messages
13. View his/subordinate calendar
14. Edit profile data
15. Track Course enrollment status (Submitted/approved/paid....etc.)
16. Certificates
17. Badges
18. Trainee requests to cancel course enrollment
19. Trainer requests to cancel course/session
20. Assign group of trainees to a course
21. Request to create users/profiles
22. Chatbot
23. Reserve room

### Integrations
1. Oracle HR for Internal user to get profile data
2. Payment Gate Way
3. Chatting tool
4. Meeting tool
5. Integration with Oracle Finance (Costing/Expenses/GL)

## Processes

### User Registration
- Internal users are created automatically via integration with Oracle EBS
- External User: Integration with Oracle to create customer
- User Creation Form is accessible by System Admin to create users

### Course Registration
1. Course Registration (Internal):
   - Attendee/Manager: Request for a course (for himself or subordinate) (Submit) – Status "Pending Approval"
   - System Enroll in Workflow (Direct Manger  Admin)
   - After being approved System enroll candidate and send notification for admin – Status "Enrolled"
2. Course Registration (External):
   - Attendee: Request for a course (submit)
   - In case there is a place Status "Pending Payment", if there is no place "Waiting list"
   - Attendee: After paying Status is converted to "Enrolled"
3. Registration (Group):
   - Sys admins enroll attendees on a created schedule "Payment"
   - Group Contact Person receives payment request and after payment,
   - attendees' status is converted to "Enrolled".

### Course Enrollment
- Notification is sent to the next waiting list applicant and sys admin.
- If he does not pay within 3 days, notification is sent to the next waiting list applicant

### Course Updates
- Trainer/Admin are responsible for adding Materials/Recordings
- Trainer/Admin are responsible for adding online meeting invitation link
- Trainer/Admin are responsible for posting online evaluation and assignments
- Trainer/Admin are responsible for grading

### Course Cancellation
- System admin can cancel course / session and system send notification for all trainee and trainer
- Trainer may request to cancel a course/session the request and have to be approved by Admin
- Trainee may cancel registration before specific number of days

## Tables

### Customer
1. Customer Type (Internal/External/Corporate..etc.)
2. Account Number
3. Date of Birth
4. Name
5. Status
6. Identify Number (ID for Person/Tax for Organization)
7. Phone

### User:
1. User Type (Internal GB/External/Corporate/Corporate Indiv)
2. User Name
3. Password
4. Linked Profile
5. Status (Active/Inactive)

### Profile
Internal GB Corp	Profile ID	Name 	Nationality	Passport no.	National ID 	Phone Number	Address	Email	Linked Customer				Employee Id
External Individual	Profile ID	Name 	Nationality	Passport no.	National ID 	Phone Number	Address	Email	Linked Customer				
External Corporate	Profile ID	Name 	Nationality	Passport no.	National ID 	Phone Number	Address	Email	Linked Customer	Tax reg.
number	Commercial registration Number	Key Person Name/ Phone /Email
External Corporate -Trainee	Profile ID	Name 	Nationality	Passport no.	National ID 	Phone Number	Address	Email	Linked Customer				
System Admin	Profile ID	Name 	Nationality	Passport no.	National ID 	Phone Number	Address	Email	Linked Customer				
Trainer	Profile ID	Name 	Nationality	Passport no.	National ID 	Phone Number	Address	Email	Linked Customer				

### Rooms
1. ID
2. Name (Room1, Room2..etc.)
3. Description
4. Category (Technician Lab/Class Room) Mandatory
5. Location (Floor No)
6. Capacity
7. Status (Active/Invalid...etc.)
8. Internal Cost
9. Price List
10. Rooms Controls (Invalid for specific days )??

### Calendar Definition
1. Year
2. Country
3. Days Off
4. Holidays
6. Items Category
1. ID
2. Name (Projector, Notebook,Laptop,electric tool
3. Description
4. Type (Shared/Individual)
5. Status (Active/Inactive)

### Items
1. ID
2. Item Category
3. Name (Projector L71k, Notebook,Laptop L7878,electric tool ACDF12321
4. Description
5. Status (Active/Damaged/Maintenance...etc.)
6. Internal Cost
7. Price List

## Use Cases

### UC01: Sign Up/Sign In
**Actors:** Trainee/System admin
**Precondition:** N/A
**Triggers:** Visit Sign up/Sign in page
**Flow of Events:**
- User Actions: Choose create account for individual/corporate
- System Responses: Generate customer code, display success message

### UC02: Administration
**Actors:** System Admin
**Precondition:** Sign in/Sign up for any user
**Triggers:** Receiving notification
**Flow of Events:**
- User Actions: System admin to fill needed data:
  - Mandatory field for number of days
  - Mandatory drop menu for trainers
  - Mandatory field of capacity
  (number of attendees)
  - Mandatory field for location (online or physical)
  - If physical, Mandatory field for room number (predefined with capacity and system must check room availability in the needed time slot)
  - Mandatory field for needed time per day.
  - Mandatory field with number of days allowed for cancellation before course start.
  - Mandatory drop-down menu with courses types as follows:
    - Course
    - BU internal meeting (internal only)
  - Add prerequisite course if available
  - Choose internal/external training
  (If 2 hours or less 50% of room/online cost applied) based on calculated training cost.
  - System should identify the creator and date of creation.
  - Create Course

### UC03: Online Platform
**Actors:** All Users
**Precondition:** Online Course
**Triggers:** Start date of any online course
**Flow of Events:**
- User Actions: Request new course/Request course for himself or for subordinate
- System Responses: View user schedule/Evaluate course/Attend online training/Post-course assessment

## Reporting Requirements

### Attendance Tracking
1. Extract attendance reports and send to each BU
2. Total number of man days for each BU/all employees
3. Extract Finance report for each BU (attendance records + cost)

### Revenue Report (A)
1. To show the Actual Vs Budget progress per BU

### Revenue Report (B)
1. To show the Revenue Progress for the Academy from the Internal Trainings with following data

### Trainers Report
1. Export report for each Trainer with his/her scheduled sessions and number of training days and the number of enrolled employees and the cancellation reason if a session is cancelled.

### Course Session Report
2. This report need to be exported all the sessions for a specific date even it was Online or Offline

### Room expenses Report
3. This report is requested to export the cost for the rooms that were reserved

### Training Evaluation
4. This report is requested to export the summary of the Evaluation per Course & Trainer, so the Academy can follow up on the Trainers Progress

### Attendance tracking (External A)
1. Extract attendance report for all the attendees per Month or Course

### Attendance tracking (External B)
1. Extract attendance report for all the attendees from a Corporate per Month or Course

### Course Report (External A)
1. This report to show number of attendees per course (Course name - number of attendees - date of attendance - individual or corporate - if corporate what is the corporate name)

### Course Report (External B)
1. This report to show number of runs per training course (Course name - Number of runs - date per run - number of trainees per run)

## Glossary of Terms

## Notes
We recommend including:
1. List of user types and responsibilities
2. List of fields (mandatory/optional) for each item
3. List of required notifications/alerts and their layout
4. Setting scope/phases and priority for required functions
5. List of workflow approval paths
6. Report layout with required columns

