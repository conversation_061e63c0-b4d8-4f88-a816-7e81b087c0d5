{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Corporate Course Calendar" %}{% endblock %}

{% block content %}
<div class="mx-auto py-5">
    <!-- Page Header -->
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
            </svg>
            {% trans "My Corporate Course Calendar" %}
        </h1>
    </div>

    <div class="grid grid-cols-1 lg:grid-cols-4 gap-4">
        <!-- Calendar -->
        <div class="lg:col-span-3">
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible">
                <!-- Calendar Header -->
                <div class="px-6 py-4 flex items-center justify-between border-b border-white/10">
                    <div class="flex items-center">
                        <button id="prevMonth" class="p-2 hover:bg-white/5 rounded-md">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
                            </svg>
                        </button>
                        <h2 id="current-month-year" class="text-lg font-semibold text-white mx-4">{% now "F Y" %}</h2>
                        <button id="nextMonth" class="p-2 hover:bg-white/5 rounded-md">
                            <svg class="h-5 w-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
                            </svg>
                        </button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="todayBtn" class="text-sm text-white hover:text-primary ml-2 px-3 py-1 rounded-md bg-blue-700">{% trans "Today" %}</button>
                    </div>
                </div>

                <div id="calendarGrid" class="grid grid-cols-7 text-sm text-white">
                    <!-- Calendar cells will be generated via JavaScript -->
                </div>
                
                <!-- Empty state message that will be shown/hidden by JavaScript -->
                <div id="emptyCalendarMessage" class="hidden p-6 text-center">
                    <p class="text-white text-lg mb-4">{% trans "You haven't enrolled in any corporate courses yet." %}</p>
                    <a href="{% url 'website:main' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {% trans "View Corporate Dashboard" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="space-y-4">
            <!-- Calendar Filters -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Calendar Filters" %}</h3>
                <div class="space-y-3 calendar-filters">
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-green-600 h-4 w-4 rounded" checked data-filter="session">
                        <span class="ml-2 text-sm text-white">{% trans "Sessions" %}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-blue-500 h-4 w-4 rounded" checked data-filter="event">
                        <span class="ml-2 text-sm text-white">{% trans "Events" %}</span>
                    </label>
                    <label class="flex items-center">
                        <input type="checkbox" class="form-checkbox text-red-500 h-4 w-4 rounded" checked data-filter="holiday">
                        <span class="ml-2 text-sm text-white">{% trans "Holidays" %}</span>
                    </label>
                </div>
            </div>

            <!-- Upcoming Sessions -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Upcoming Events" %}</h3>
                <div id="upcomingSessions" class="space-y-4">
                    <!-- Loading state -->
                    <div class="flex items-start space-x-4">
                        <div class="flex-shrink-0 w-12 text-center">
                            <div class="text-sm font-semibold text-primary">{% now "d" %}</div>
                            <div class="text-xs text-white/70">{% now "M" %}</div>
                        </div>
                        <div class="flex-1 min-w-0">
                            <p class="text-sm font-medium text-white">Loading events...</p>
                            <p class="text-xs text-white/70">Please wait</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- My Courses -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "My Corporate Courses" %}</h3>
                <div class="space-y-3">
                    {% for reservation in user_reservations %}
                        {% if reservation.course_instance %}
                            <div class="p-3 border border-white/10 rounded-md hover:bg-white/5">
                                <div class="text-sm font-medium text-white">{{ reservation.course_instance.course.name_en }}</div>
                                <div class="text-xs text-white/70">{{ reservation.course_instance.start_date|date:"M d, Y" }} - {{ reservation.course_instance.end_date|date:"M d, Y" }}</div>
                            </div>
                        {% endif %}
                    {% empty %}
                        <p class="text-sm text-white/70">{% trans "No corporate courses enrolled" %}</p>
                    {% endfor %}
                </div>
            </div>

            {% if corporate %}
            <!-- Corporate Info -->
            <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-visible p-6">
                <h3 class="text-lg font-semibold text-white mb-4">{% trans "Corporate Info" %}</h3>
                <div class="space-y-2">
                    <div class="flex flex-col">
                        <span class="text-xs text-white/60">{% trans "Company" %}</span>
                        <span class="text-sm text-white">{{ corporate.legal_name }}</span>
                    </div>
                    {% if corporate.category %}
                    <div class="flex flex-col">
                        <span class="text-xs text-white/60">{% trans "Category" %}</span>
                        <span class="text-sm text-white">{{ corporate.get_category_display }}</span>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    console.log("Corporate calendar script loaded");
    
    // Current date information
    let currentDate = new Date();
    let currentMonth = currentDate.getMonth();
    let currentYear = currentDate.getFullYear();
    let currentView = 'month';
    
    // Always initialize events with empty array
    let events = [];

    // Calendar functions
    function loadCalendar(month, year) {
        // Update header if it exists
        const monthYearEl = document.getElementById('current-month-year');
        if (monthYearEl) {
            const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
            monthYearEl.textContent = `${monthNames[month]} ${year}`;
        }

        console.log("Fetching calendar events from API");
        // Fetch events
        fetch('{% url "website:corporate_user_calendar_events_api" %}')
            .then(response => response.json())
            .then(data => {
                console.log(`Received ${data.events ? data.events.length : 0} events from API`);
                events = data.events || [];
                
                // Only try to render calendar if element exists
                const calendarGrid = document.getElementById('calendarGrid');
                if (calendarGrid) {
                    console.log("Rendering calendar");
                    // Show/hide empty state message
                    const emptyMessage = document.getElementById('emptyCalendarMessage');
                    if (emptyMessage) {
                        if (events.length === 0) {
                            calendarGrid.classList.add('hidden');
                            emptyMessage.classList.remove('hidden');
                        } else {
                            calendarGrid.classList.remove('hidden');
                            emptyMessage.classList.add('hidden');
                            renderCalendar(month, year);
                        }
                    } else {
                        renderCalendar(month, year);
                    }
                } else {
                    console.log("Calendar grid element not found");
                }
                
                // Update upcoming sessions if element exists
                updateUpcomingSessions(events);
                
                // Set up filter listeners
                setupFilterListeners();
            })
            .catch(error => {
                console.error('Error fetching calendar events:', error);
            });
    }

    // Calendar grid rendering function (implement this based on your requirements)
    function renderCalendar(month, year) {
        const filteredEvents = getFilteredEvents();
        // Implementation depends on your specific calendar UI requirements
        console.log(`Rendering calendar for ${month+1}/${year} with ${filteredEvents.length} filtered events`);
        
        // Basic implementation - you would expand this based on your needs
        const calendarGrid = document.getElementById('calendarGrid');
        if (!calendarGrid) return;
        
        calendarGrid.innerHTML = '';
        
        // Add header row with day names
        const dayNames = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
        dayNames.forEach(day => {
            const dayNameCell = document.createElement('div');
            dayNameCell.className = 'text-center py-2 font-semibold border-b border-white/10';
            dayNameCell.textContent = day;
            calendarGrid.appendChild(dayNameCell);
        });
        
        // Get the first day of the month and the total days in month
        const firstDay = new Date(year, month, 1).getDay();
        const daysInMonth = new Date(year, month + 1, 0).getDate();
        
        // Create grid cells for the days
        // First, add empty cells for days before the 1st of the month
        for (let i = 0; i < firstDay; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'p-2 min-h-[80px] border border-white/10 bg-white/5';
            calendarGrid.appendChild(emptyCell);
        }
        
        // Add cells for each day of the month
        for (let day = 1; day <= daysInMonth; day++) {
            const dayCell = document.createElement('div');
            dayCell.className = 'p-2 min-h-[80px] border border-white/10 bg-white/5 relative';
            
            // Add day number
            const dayNumber = document.createElement('div');
            dayNumber.className = 'text-sm font-semibold mb-2';
            dayNumber.textContent = day;
            dayCell.appendChild(dayNumber);
            
            // Check for events on this day
            const dayDate = new Date(year, month, day);
            const dayEvents = filteredEvents.filter(event => {
                const eventDate = new Date(event.start);
                return eventDate.getDate() === day && 
                       eventDate.getMonth() === month &&
                       eventDate.getFullYear() === year;
            });
            
            // Add events to the day cell
            dayEvents.forEach(event => {
                const eventElem = document.createElement('div');
                eventElem.className = `text-xs p-1 mb-1 rounded truncate ${getEventColor(event)}`;
                eventElem.textContent = event.title;
                dayCell.appendChild(eventElem);
            });
            
            calendarGrid.appendChild(dayCell);
        }
        
        // Fill remaining cells to complete the grid
        const totalCells = Math.ceil((firstDay + daysInMonth) / 7) * 7;
        for (let i = firstDay + daysInMonth; i < totalCells; i++) {
            const emptyCell = document.createElement('div');
            emptyCell.className = 'p-2 min-h-[80px] border border-white/10 bg-white/5';
            calendarGrid.appendChild(emptyCell);
        }
    }
    
    // Helper function to get event color based on type
    function getEventColor(event) {
        switch(event.type) {
            case 'session':
                return 'bg-green-800 text-white';
            case 'holiday':
                return 'bg-red-800 text-white';
            case 'event':
                return 'bg-blue-800 text-white';
            default:
                return 'bg-gray-800 text-white';
        }
    }

    // Load and display calendar
    loadCalendar(currentMonth, currentYear);

    // Calendar navigation - only set up if elements exist
    const prevMonthBtn = document.getElementById('prevMonth');
    if (prevMonthBtn) {
        prevMonthBtn.addEventListener('click', function() {
            if (currentView === 'month') {
                // Move back one month
                currentMonth--;
                if (currentMonth < 0) {
                    currentMonth = 11;
                    currentYear--;
                }
            } else if (currentView === 'week') {
                // Move back one week
                currentDate.setDate(currentDate.getDate() - 7);
                currentMonth = currentDate.getMonth();
                currentYear = currentDate.getFullYear();
            } else if (currentView === 'day') {
                // Move back one day
                currentDate.setDate(currentDate.getDate() - 1);
                currentMonth = currentDate.getMonth();
                currentYear = currentDate.getFullYear();
            }
            loadCalendar(currentMonth, currentYear);
        });
    }

    const nextMonthBtn = document.getElementById('nextMonth');
    if (nextMonthBtn) {
        nextMonthBtn.addEventListener('click', function() {
            if (currentView === 'month') {
                // Move forward one month
                currentMonth++;
                if (currentMonth > 11) {
                    currentMonth = 0;
                    currentYear++;
                }
            } else if (currentView === 'week') {
                // Move forward one week
                currentDate.setDate(currentDate.getDate() + 7);
                currentMonth = currentDate.getMonth();
                currentYear = currentDate.getFullYear();
            } else if (currentView === 'day') {
                // Move forward one day
                currentDate.setDate(currentDate.getDate() + 1);
                currentMonth = currentDate.getMonth();
                currentYear = currentDate.getFullYear();
            }
            loadCalendar(currentMonth, currentYear);
        });
    }

    const todayBtn = document.getElementById('todayBtn');
    if (todayBtn) {
        todayBtn.addEventListener('click', function() {
            currentDate = new Date();
            currentMonth = currentDate.getMonth();
            currentYear = currentDate.getFullYear();
            loadCalendar(currentMonth, currentYear);
        });
    }

    // View switchers
    const viewMonthBtn = document.getElementById('viewMonth');
    if (viewMonthBtn) {
        viewMonthBtn.addEventListener('click', function() {
            currentView = 'month';
            this.className = 'px-3 py-1 text-sm rounded-md bg-primary text-white';
            document.getElementById('viewWeek').className = 'px-3 py-1 text-sm rounded-md text-gray-300';
            document.getElementById('viewDay').className = 'px-3 py-1 text-sm rounded-md text-gray-300';
            loadCalendar(currentMonth, currentYear);
        });
    }

    const viewWeekBtn = document.getElementById('viewWeek');
    if (viewWeekBtn) {
        viewWeekBtn.addEventListener('click', function() {
            currentView = 'week';
            this.className = 'px-3 py-1 text-sm rounded-md bg-primary text-white';
            document.getElementById('viewMonth').className = 'px-3 py-1 text-sm rounded-md text-gray-300';
            document.getElementById('viewDay').className = 'px-3 py-1 text-sm rounded-md text-gray-300';
            loadCalendar(currentMonth, currentYear);
        });
    }

    const viewDayBtn = document.getElementById('viewDay');
    if (viewDayBtn) {
        viewDayBtn.addEventListener('click', function() {
            currentView = 'day';
            this.className = 'px-3 py-1 text-sm rounded-md bg-primary text-white';
            document.getElementById('viewMonth').className = 'px-3 py-1 text-sm rounded-md text-gray-300';
            document.getElementById('viewWeek').className = 'px-3 py-1 text-sm rounded-md text-gray-300';
            loadCalendar(currentMonth, currentYear);
        });
    }
    
    // Set up filters if they exist
    setupFilterListeners();

    // Setup event listeners for filters
    function setupFilterListeners() {
        const filterCheckboxes = document.querySelectorAll('.calendar-filters input[type="checkbox"]');
        
        if (filterCheckboxes && filterCheckboxes.length > 0) {
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // Re-render the calendar with filtered events
                    if (document.getElementById('calendarGrid')) {
                        renderCalendar(currentMonth, currentYear);
                    }
                });
            });
        }
    }
    
    // Filter events based on checked filters
    function getFilteredEvents() {
        // Default to return all events if no filters found
        const filterCheckboxes = document.querySelectorAll('.calendar-filters input[type="checkbox"]');
        if (!filterCheckboxes || filterCheckboxes.length === 0) {
            return events;
        }
        
        // Get active filters
        const activeFilters = Array.from(filterCheckboxes)
            .filter(checkbox => checkbox.checked)
            .map(checkbox => checkbox.getAttribute('data-filter'));
        
        // If no filters are active, return all events
        if (activeFilters.length === 0) {
            return events;
        }
        
        // Filter events based on type
        return events.filter(event => activeFilters.includes(event.type));
    }

    // Function to update the upcoming sessions panel
    function updateUpcomingSessions(events) {
        const upcomingSessionsContainer = document.getElementById('upcomingSessions');
        if (!upcomingSessionsContainer) return;
        
        upcomingSessionsContainer.innerHTML = '';
        
        // Sort events by start date
        events.sort((a, b) => new Date(a.start) - new Date(b.start));
        
        // Get today's date
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        
        // Filter for upcoming events (today and future)
        const upcomingEvents = events.filter(event => {
            const eventDate = new Date(event.start);
            return eventDate >= today;
        }).slice(0, 5); // Take only the first 5
        
        if (upcomingEvents.length === 0) {
            upcomingSessionsContainer.innerHTML = `
                <p class="text-sm text-white/70">{% trans "No upcoming events" %}</p>
            `;
            return;
        }
        
        // Add each upcoming event to the panel
        upcomingEvents.forEach(event => {
            if (!event || !event.start) return;
            
            const eventDate = new Date(event.start);
            const day = eventDate.getDate();
            const month = eventDate.toLocaleString('default', { month: 'short' });
            const time = eventDate.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
            
            // Determine if this is a session, event, or holiday
            const type = event.type || 'session';
            const typeClass = type === 'session' ? 'text-green-400' : 
                            type === 'holiday' ? 'text-red-400' : 'text-blue-400';
            
            const eventElement = document.createElement('div');
            eventElement.className = 'flex items-start space-x-4 p-3 border border-white/10 rounded-md hover:bg-white/5';
            eventElement.innerHTML = `
                <div class="flex-shrink-0 w-12 text-center">
                    <div class="text-sm font-semibold text-primary">${day}</div>
                    <div class="text-xs text-white/70">${month}</div>
                </div>
                <div class="flex-1 min-w-0">
                    <p class="text-sm font-medium text-white">${event.title}</p>
                    <p class="text-xs ${typeClass}">${type} · ${time}</p>
                </div>
            `;
            upcomingSessionsContainer.appendChild(eventElement);
        });
    }
});
</script>
{% endblock %} 