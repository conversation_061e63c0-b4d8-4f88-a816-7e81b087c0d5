Looking through the corporate_views.py file, I found the following APIs that include email sending functionality:

1. `cancel_reservation_api` - Sends cancellation emails when a corporate admin cancels a reservation using `send_reservation_cancellation_email`

2. `request_cancellation_api` - Notifies corporate admins about user cancellation requests using `send_cancellation_request_email`

3. `process_cancellation_request_api` - Sends emails when admins approve or reject cancellation requests using:
   - `send_cancellation_request_approval_email`
   - `send_cancellation_request_rejection_email`

4. `request_new_course` - Notifies system admins about new course requests using `send_new_course_request_notification_email`

5. `update_new_course_request_status` - Sends emails when course requests are approved or rejected using:
   - `send_course_request_approval_email`
   - `send_course_request_rejection_email`

All these functions import their email-sending utilities from `corporate_email_utils.py`.

## Trainer Email Notifications

The following email triggers have been added to `views_trainer.py`, using utilities from `trainer_email_utils.py`:

1. `trainer_session_attendance` - Sends email notifications to trainees when their attendance status is updated using `send_attendance_update_email`

2. `trainer_manage_session` - Also sends email notifications when attendance is updated through the session management view using `send_attendance_update_email`

3. `review_questionnaire_response` - Notifies users when their questionnaire has been reviewed by a trainer, including their score and any feedback using `send_questionnaire_review_notification`

4. `add_questionnaire` - Notifies all enrolled users in active course instances when a new questionnaire is created for their course using `send_new_questionnaire_notification`

These notifications help keep students informed about their attendance records, questionnaire reviews, and new assignments, improving communication between trainers and trainees.

## Individual User Email Notifications

The following email triggers have been added to `individual_views.py`, using utilities from `individual_email_utils.py`:

1. `individual_create_questionnaire_response_api` - Sends confirmation emails to users when they submit a questionnaire, including their initial score using `send_questionnaire_submission_confirmation`

2. `user_reservations_view` - Sends confirmation emails when users cancel their own reservations, including refund information if applicable using `send_reservation_cancellation_confirmation`

Additional email utility functions have been created for future implementation:

- `send_pending_survey_reminder` - For reminding users to complete pending surveys
- `send_pending_questionnaire_reminder` - For reminding users to complete pending questionnaires
- `send_waitlist_notification` - For notifying users when they are moved from a waiting list to a confirmed registration

These notifications provide individual users with important confirmations and updates related to their course activities.

## GB Employee Email Notifications

The following email triggers have been added for GB internal employee workflows, using utilities from `GB_email_utils.py`:

### Course Enrollment Request Process

1. **gb_enroll_in_instance** (in `GB_views.py`) - When a GB employee requests to enroll in a course that requires supervisor approval:
   - `send_employee_request_confirmation` - Confirms to the employee that their request has been submitted to their supervisor
   - `send_supervisor_approval_request` - Notifies existing supervisors about the pending enrollment request with a direct link to review it
   - `send_supervisor_account_created_notification` - Welcome email for new supervisors whose accounts are automatically created during the request process

### Supervisor Decision Process

2. **supervisor_approve_request** (in `GB_views.py`) - When a supervisor processes an enrollment request:
   - `send_approval_decision_notification` - Notifies the employee of the supervisor's decision (approved/rejected) with optional supervisor notes, sent for both approval and rejection actions

### Email Functions in GB_email_utils.py

- **send_gb_notification_email** - Generic function for sending GB-related emails with proper logging, error handling, and database logging via EmailLog model

- **send_supervisor_approval_request** - Request email to existing supervisors for course enrollment approval, includes course details and direct review link

- **send_supervisor_account_created_notification** - Welcome email for newly created supervisor accounts, combines account creation notification with pending request information

- **send_employee_request_confirmation** - Confirmation email to employees after submitting enrollment requests, includes supervisor and course information

- **send_approval_decision_notification** - Decision notification email (approval/rejection) to employees, includes supervisor notes and appropriate messaging based on decision

### Email Triggers Location

All GB email notifications are triggered from functions in `website/GB_views.py`:
- **Line 296**: Employee confirmation email in enrollment request process
- **Line 276**: New supervisor account notification 
- **Line 286**: Existing supervisor approval request
- **Line 1158**: Approval decision notification (approval case)
- **Line 1178**: Approval decision notification (rejection case)

These notifications ensure proper communication flow in the GB internal employee course enrollment process, keeping both employees and supervisors informed at each step of the approval workflow with professional HTML-formatted emails.