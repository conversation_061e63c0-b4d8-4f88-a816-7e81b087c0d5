from django.core.management.base import BaseCommand
from django.db import connection
from website.models import Category, Equipment

class Command(BaseCommand):
    help = 'Update category stock counts based on equipment counts'

    def handle(self, *args, **options):
        # First, verify if the stock field exists on Category model
        with connection.cursor() as cursor:
            try:
                # For SQLite
                if connection.vendor == 'sqlite':
                    cursor.execute("""
                        SELECT 1 FROM pragma_table_info('website_category')
                        WHERE name='stock'
                    """)
                    column_exists = cursor.fetchone()
                    
                    # If column doesn't exist, add it
                    if not column_exists:
                        self.stdout.write('Adding stock column to Category table...')
                        cursor.execute("PRAGMA foreign_keys=off;")
                        cursor.execute("""
                            ALTER TABLE website_category 
                            ADD COLUMN stock INTEGER NOT NULL DEFAULT 0
                        """)
                        cursor.execute("PRAGMA foreign_keys=on;")
                        self.stdout.write(self.style.SUCCESS('Stock column added successfully!'))
                else:
                    # For PostgreSQL, MySQL, etc.
                    cursor.execute("""
                        SELECT column_name FROM information_schema.columns
                        WHERE table_name='website_category' AND column_name='stock'
                    """)
                    column_exists = cursor.fetchone()
                    
                    # If column doesn't exist, add it
                    if not column_exists:
                        self.stdout.write('Adding stock column to Category table...')
                        cursor.execute("""
                            ALTER TABLE website_category 
                            ADD COLUMN stock INTEGER NOT NULL DEFAULT 0
                        """)
                        self.stdout.write(self.style.SUCCESS('Stock column added successfully!'))
            except Exception as e:
                self.stdout.write(self.style.WARNING(f'Error checking/adding stock column: {e}'))
                self.stdout.write(self.style.NOTICE('Continuing with stock update...'))

        # Update each category's stock count
        self.stdout.write('Updating category stock counts...')
        updated_categories = 0
        
        for category in Category.objects.all():
            # Count equipment in this category
            count = Equipment.objects.filter(category=category).count()
            category.stock = count
            category.save(update_fields=['stock'])
            updated_categories += 1
            
        self.stdout.write(self.style.SUCCESS(f'Successfully updated {updated_categories} categories!')) 