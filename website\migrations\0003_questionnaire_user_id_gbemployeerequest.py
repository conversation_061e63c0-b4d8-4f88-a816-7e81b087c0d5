# Generated by Django 4.2.18 on 2025-06-23 16:11

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', '0002_remove_questionnaire_score'),
    ]

    operations = [
        migrations.AddField(
            model_name='questionnaire',
            name='user_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='User ID'),
        ),
        migrations.CreateModel(
            name='GBEmployeeRequest',
            fields=[
                ('request_id', models.AutoField(primary_key=True, serialize=False)),
                ('status', models.CharField(choices=[('PENDING', 'Pending Approval'), ('APPROVED', 'Approved'), ('REJECTED', 'Rejected'), ('CANCELLED', 'Cancelled')], default='PENDING', max_length=20, verbose_name='Status')),
                ('request_message', models.TextField(blank=True, help_text='Optional message from employee to supervisor', verbose_name='Request Message')),
                ('approval_notes', models.TextField(blank=True, help_text='Notes from supervisor regarding the decision', verbose_name='Approval Notes')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('processed_at', models.DateTimeField(blank=True, null=True, verbose_name='Processed At')),
                ('supervisor_account_created', models.BooleanField(default=False, help_text='Whether the supervisor account was created for this request', verbose_name='Supervisor Account Created')),
                ('course_instance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='website.courseinstance', verbose_name='Course Instance')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='enrollment_requests', to='website.gb_employee', verbose_name='Employee')),
                ('processed_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='processed_gb_requests', to=settings.AUTH_USER_MODEL, verbose_name='Processed By')),
                ('supervisor', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='pending_approvals', to='website.gb_employee', verbose_name='Supervisor')),
                ('supervisor_user', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='supervised_enrollment_requests', to=settings.AUTH_USER_MODEL, verbose_name='Supervisor User Account')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='gb_enrollment_requests', to=settings.AUTH_USER_MODEL, verbose_name='User')),
            ],
            options={
                'verbose_name': 'GB Employee Request',
                'verbose_name_plural': 'GB Employee Requests',
                'ordering': ['-created_at'],
                'unique_together': {('employee', 'course_instance', 'status')},
            },
        ),
    ]
