{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Emergency Cancellation Requests" %} | GB Academy{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-8 flex items-center">
        <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
        </svg>
        {% trans "Emergency Cancellation Requests" %}
    </h1>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Filters Section -->
    <div class="bg-indigo-900 rounded-lg border border-indigo-700 p-4 mb-8">
        <form id="filter-form" method="GET" action="{% url 'website:admin_cancellation_requests' %}" class="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
            <div>
                <label for="user_type" class="block text-sm font-medium text-white mb-1">{% trans "Filter by User Type" %}</label>
                <select id="user_type" name="user_type" class="block w-full bg-indigo-800 border-indigo-700 text-white rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">{% trans "All User Types" %}</option>
                    {% for type in user_types %}
                        <option value="{{ type.code }}" {% if type.code == selected_user_type %}selected{% endif %}>
                            {{ type.name }}
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div>
                <label for="user_id" class="block text-sm font-medium text-white mb-1">{% trans "Filter by Username" %}</label>
                <select id="user_id" name="user_id" class="block w-full bg-indigo-800 border-indigo-700 text-white rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-2 focus:ring-indigo-500">
                    <option value="">{% trans "All Users" %}</option>
                    {% for user in all_users %}
                        <option value="{{ user.id }}" {% if user.id|stringformat:"s" == selected_user_id %}selected{% endif %}>
                            {{ user.get_full_name }} ({{ user.email }})
                        </option>
                    {% endfor %}
                </select>
            </div>
            <div class="flex items-center space-x-2">
                {% comment %} <button type="submit" class="w-full bg-indigo-600 text-white px-4 py-2 rounded-md hover:bg-indigo-700">
                    {% trans "Apply Filters" %}
                </button> {% endcomment %}
                <a href="{% url 'website:admin_cancellation_requests' %}" class="w-full text-center bg-indigo-800 text-white px-4 py-2 rounded-md hover:bg-indigo-900">
                    {% trans "Clear" %}
                </a>
            </div>
        </form>
    </div>

    <!-- Pending Requests Section -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg mb-8">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <span class="inline-flex items-center justify-center w-8 h-8 mr-2 bg-yellow-500 text-white rounded-full font-bold">
                    {{ pending_requests|length }}
                </span>
                {% trans "Pending Emergency Cancellation Requests" %}
            </h2>
        </div>

        {% if pending_requests %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "User" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Reason" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Requested On" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Attachment" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for request in pending_requests %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ request.reservation.user.get_full_name }}</div>
                            <div class="text-xs text-gray-400">{{ request.reservation.user.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">
                                {% if request.reservation.course_instance %}
                                    {{ request.reservation.course_instance.course.name_en }}
                                {% elif request.reservation.session %}
                                    {{ request.reservation.session.course.name_en }}
                                {% else %}
                                    {% trans "Unknown Course" %}
                                {% endif %}
                            </div>
                            <div class="text-xs text-gray-400">
                                {% if request.reservation.course_instance %}
                                    {{ request.reservation.course_instance.start_date|date:"M d, Y" }}
                                {% elif request.reservation.session %}
                                    {{ request.reservation.session.start_date|date:"M d, Y" }}
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4">
                            <div class="text-sm text-white line-clamp-2">{{ request.reason }}</div>
                            <button class="text-xs text-blue-400 hover:text-blue-300 view-full-reason" data-reason="{{ request.reason }}">
                                {% trans "View Full Reason" %}
                            </button>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ request.created_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if request.attachment %}
                            <a href="{{ request.attachment.url }}" target="_blank" class="text-blue-400 hover:text-blue-300 flex items-center">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"/>
                                </svg>
                                {% trans "View" %}
                            </a>
                            {% else %}
                            <span class="text-gray-400">{% trans "No attachment" %}</span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right">
                            <form method="POST" action="{% url 'website:admin_cancellation_requests' %}" class="inline">
                                {% csrf_token %}
                                <input type="hidden" name="request_id" value="{{ request.request_id }}">
                                <input type="hidden" name="action" value="approve">
                                <button type="submit" class="bg-green-600 text-white px-3 py-1 rounded text-sm hover:bg-green-700 mr-2">
                                    {% trans "Approve" %}
                                </button>
                            </form>
                            <form method="POST" action="{% url 'website:admin_cancellation_requests' %}" class="inline">
                                {% csrf_token %}
                                <input type="hidden" name="request_id" value="{{ request.request_id }}">
                                <input type="hidden" name="action" value="reject">
                                <button type="submit" class="bg-red-600 text-white px-3 py-1 rounded text-sm hover:bg-red-700">
                                    {% trans "Reject" %}
                                </button>
                            </form>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="p-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <h3 class="mt-2 text-lg font-medium text-white">{% trans "No Pending Requests" %}</h3>
            <p class="mt-1 text-gray-400">{% trans "There are no pending emergency cancellation requests to review." %}</p>
        </div>
        {% endif %}
    </div>

    <!-- Recent Processed Requests Section -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white">{% trans "Recent Processed Requests" %}</h2>
        </div>

        {% if processed_requests %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "User" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Processed On" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Notes" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for request in processed_requests %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ request.reservation.user.get_full_name }}</div>
                            <div class="text-xs text-gray-400">{{ request.reservation.user.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">
                                {% if request.reservation.course_instance %}
                                    {{ request.reservation.course_instance.course.name_en }}
                                {% elif request.reservation.session %}
                                    {{ request.reservation.session.course.name_en }}
                                {% else %}
                                    {% trans "Unknown Course" %}
                                {% endif %}
                            </div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if request.status == 'APPROVED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                {% trans "Approved" %}
                            </span>
                            {% elif request.status == 'REJECTED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-800/30 text-red-400">
                                {% trans "Rejected" %}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.updated_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ request.updated_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.admin_notes|default:"-" }}</div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="p-12 text-center">
            <h3 class="text-lg font-medium text-white">{% trans "No processed requests yet" %}</h3>
            <p class="mt-1 text-gray-400">{% trans "There are no recently processed emergency cancellation requests." %}</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Full Reason Modal -->
<div id="reasonModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="modal-title">
                            {% trans "Cancellation Reason" %}
                        </h3>
                        <div class="mt-4 mb-4">
                            <p id="fullReason" class="text-sm text-gray-300"></p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="closeReasonModal" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Close" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // View full reason functionality
        const viewButtons = document.querySelectorAll('.view-full-reason');
        const reasonModal = document.getElementById('reasonModal');
        const fullReasonText = document.getElementById('fullReason');
        const closeButton = document.getElementById('closeReasonModal');
        
        viewButtons.forEach(button => {
            button.addEventListener('click', function() {
                const reason = this.getAttribute('data-reason');
                fullReasonText.textContent = reason;
                reasonModal.classList.remove('hidden');
            });
        });
        
        closeButton.addEventListener('click', function() {
            reasonModal.classList.add('hidden');
        });
        
        // Close modal when clicking outside
        reasonModal.addEventListener('click', function(e) {
            if (e.target === reasonModal) {
                reasonModal.classList.add('hidden');
            }
        });

        // Auto-submit filter form on change
        const filterForm = document.getElementById('filter-form');
        if (filterForm) {
            const selects = filterForm.querySelectorAll('select');
            selects.forEach(select => {
                select.addEventListener('change', function() {
                    filterForm.submit();
                });
            });
        }
    });
</script>
{% endblock %} 