{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Take Survey" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    /* Custom styling for survey form */
    .rating-label input:checked + span {
        background-color: rgba(79, 70, 229, 0.9) !important;
        border-color: rgba(129, 140, 248, 1) !important;
        color: white !important;
        transform: scale(1.1);
        box-shadow: 0 0 10px rgba(99, 102, 241, 0.5);
    }
    
    .rating-label span:hover {
        background-color: rgba(79, 70, 229, 0.4);
        border-color: rgba(129, 140, 248, 0.8);
        color: white;
        transform: scale(1.05);
    }
    
    .rating-label span {
        transition: all 0.2s ease;
    }

    .question-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .question-card:hover {
        border-left-color: #4f46e5;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }

    .submit-btn {
        transition: all 0.3s ease;
    }
    
    .submit-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(79, 70, 229, 0.5);
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <h1 class="text-3xl font-bold text-white mb-6 flex items-center">
        <svg class="w-8 h-8 mr-3 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
        </svg>
        {{ survey.title }}
    </h1>

    <div class="mb-6 bg-indigo-900/30 backdrop-blur-md rounded-lg border border-indigo-600/40 p-6 shadow-lg">
        <div class="flex items-start">
            <div class="flex-shrink-0">
                <svg class="h-6 w-6 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
            </div>
            <div class="ml-3">
                <h3 class="text-base font-medium text-indigo-300">{% trans "Course Information" %}</h3>
                <div class="mt-2 text-sm text-gray-200">
                    <p class="text-lg font-semibold">{{ reservation.course_instance.course.name_en }}</p>
                    <p class="text-sm text-indigo-300 mt-2">{% trans "Completed on" %}: {{ reservation.completed_at|date:"F j, Y" }}</p>
                </div>
            </div>
        </div>
    </div>

    <div class="bg-white/10 backdrop-blur-md rounded-lg border border-white/20 overflow-hidden shadow-xl">
        <div class="px-6 py-4 border-b border-white/20 bg-gradient-to-r from-indigo-900/60 to-blue-900/60">
            <h2 class="text-xl font-semibold text-white">{% trans "Survey Questions" %}</h2>
        </div>

        <form id="surveyForm" class="p-6 space-y-6">
            {% csrf_token %}
            <input type="hidden" id="survey_id" value="{{ survey.survey_id }}">
            <input type="hidden" id="reservation_id" value="{{ reservation.reservation_id }}">

            <div id="questionsContainer">
                {% if survey.questions %}
                    {% for question in survey.questions %}
                    <div class="bg-gray-800/60 rounded-lg p-6 mb-6 shadow-lg question-card border-b border-gray-700 backdrop-blur-sm">
                        <h3 class="font-medium text-white text-lg mb-4">{{ question.text }}</h3>
                        
                        {% if question.type == 'rating' %}
                        <div class="flex items-center space-x-4 mt-4 justify-center" id="rating-{{ forloop.counter }}">
                            {% for i in "12345" %}
                            <label class="rating-label">
                                <input type="radio" name="question_{{ forloop.parentloop.counter }}" value="{{ i }}" class="absolute opacity-0 w-0 h-0" required>
                                <span class="w-12 h-12 flex items-center justify-center border-2 border-indigo-500/50 rounded-full text-white hover:bg-indigo-800/70 hover:border-indigo-400 hover:text-white cursor-pointer transition-all text-lg font-bold">
                                    {{ i }}
                                </span>
                            </label>
                            {% endfor %}
                        </div>
                        <div class="flex justify-between text-gray-300 text-sm mt-2 px-6 font-medium">
                            <span>{% trans "Poor" %}</span>
                            <span>{% trans "Excellent" %}</span>
                        </div>
                        
                        {% elif question.type == 'choice' %}
                        <div class="space-y-3 pl-4">
                            {% for option in question.options %}
                            <label class="flex items-center space-x-3 py-2 px-4 rounded-lg hover:bg-indigo-900/40 transition-colors">
                                <input type="radio" name="question_{{ forloop.parentloop.counter }}" value="{{ option }}" class="h-5 w-5 text-indigo-500 focus:ring-indigo-400 border-gray-600 bg-gray-700" required>
                                <span class="text-white text-lg">{{ option }}</span>
                            </label>
                            {% endfor %}
                        </div>
                        
                        {% elif question.type == 'text' %}
                        <textarea name="question_{{ forloop.counter }}" rows="3" class="mt-2 block w-full bg-gray-700/80 border border-indigo-600/50 rounded-md shadow-sm py-3 px-4 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-md" required placeholder="{% trans 'Your answer...' %}"></textarea>
                        {% endif %}
                    </div>
                    {% endfor %}
                {% else %}
                    <div class="text-center py-10 bg-gray-800/40 rounded-lg shadow-inner">
                        <svg class="mx-auto h-12 w-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                        <p class="mt-4 text-lg text-gray-300 font-medium">{% trans "No questions available for this survey." %}</p>
                    </div>
                {% endif %}
            </div>

            <!-- Add a comment section -->
            <div class="bg-gray-800/60 rounded-lg p-6 shadow-lg border-l-4 border-indigo-500">
                <h3 class="font-medium text-white text-lg mb-3">{% trans "Additional Comments" %} <span class="text-sm text-gray-400">({% trans "Optional" %})</span></h3>
                <textarea id="comment" name="comment" rows="4" class="mt-2 block w-full bg-gray-700/80 border border-indigo-600/50 rounded-md shadow-sm py-3 px-4 text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 sm:text-md" placeholder="{% trans 'Share your thoughts about the course...' %}"></textarea>
            </div>

            <div class="flex justify-end pt-4">
                {% if request.user.user_type and request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE' %}
                <a href="{% url 'website:corporate_user_reservations' %}" class="mr-3 inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-md font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-gray-500 transition-all">
                    {% trans "Cancel" %}
                </a>
                {% else %}
                <a href="{% url 'website:user_reservations' %}" class="mr-3 inline-flex justify-center py-3 px-6 border border-transparent shadow-sm text-md font-medium rounded-md text-white bg-gray-600 hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-gray-500 transition-all">
                    {% trans "Cancel" %}
                </a>
                {% endif %}
                <button type="submit" class="inline-flex justify-center py-3 px-8 border border-transparent shadow-md text-md font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-800 focus:ring-indigo-500 submit-btn">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                    </svg>
                    {% trans "Submit Survey" %}
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Style selected ratings
        const ratingLabels = document.querySelectorAll('.rating-label');
        
        ratingLabels.forEach(label => {
            const input = label.querySelector('input');
            const span = label.querySelector('span');
            
            input.addEventListener('change', function() {
                // Reset all ratings in this group
                const name = this.name;
                document.querySelectorAll(`input[name="${name}"]`).forEach(inp => {
                    const s = inp.parentElement.querySelector('span');
                    if (inp.checked) {
                        s.classList.add('bg-indigo-800/90', 'border-indigo-400', 'text-white');
                        s.classList.remove('border-indigo-500/50', 'text-white');
                    } else {
                        s.classList.remove('bg-indigo-800/90', 'border-indigo-400', 'text-white');
                        s.classList.add('border-indigo-500/50', 'text-white');
                    }
                });
            });
        });
        
        // Handle form submission
        document.getElementById('surveyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const surveyId = document.getElementById('survey_id').value;
            const reservationId = document.getElementById('reservation_id').value;
            const comment = document.getElementById('comment').value;
            
            // Collect all question responses
            const questionResponses = {};
            const questions = document.querySelectorAll('#questionsContainer > div');
            
            questions.forEach((question, index) => {
                const questionNumber = index + 1;
                const inputName = `question_${questionNumber}`;
                
                // Handle different question types
                const textInput = question.querySelector(`textarea[name="${inputName}"]`);
                if (textInput) {
                    questionResponses[questionNumber] = textInput.value;
                    return;
                }
                
                const selectedRadio = question.querySelector(`input[name="${inputName}"]:checked`);
                if (selectedRadio) {
                    questionResponses[questionNumber] = selectedRadio.value;
                }
            });
            
            // Show loading state on submit button
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = `
                <svg class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                {% trans "Submitting..." %}
            `;
            submitBtn.disabled = true;
            
            // Prepare data for submission
            const data = {
                survey_id: surveyId,
                reservation_id: reservationId,
                question_responses: questionResponses,
                comment: comment
            };
            
            // Submit the response
            fetch('{% url "website:create_survey_response_api" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]').value
                },
                body: JSON.stringify(data)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Show success message and redirect
                    submitBtn.innerHTML = `
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                        {% trans "Success!" %}
                    `;
                    submitBtn.classList.remove('bg-indigo-600');
                    submitBtn.classList.add('bg-green-600');
                    
                    // Set timeout to briefly show success before redirecting
                    setTimeout(() => {
                        // First check API response for user_type, fall back to server-side condition
                        if (data.user_type === 'EXTERNAL_CORPORATE_TRAINEE') {
                            window.location.href = '{% url "website:corporate_user_reservations" %}';
                        } else {
                            // For all other user types (EXTERNAL_INDIVIDUAL, etc.)
                            window.location.href = '{% url "website:user_reservations" %}';
                        }
                    }, 1000);
                } else {
                    // Show error message
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                    alert(data.message || '{% trans "An error occurred while submitting your survey." %}');
                }
            })
            .catch(error => {
                console.error('Error submitting survey:', error);
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
                alert('{% trans "An error occurred while submitting your survey." %}');
            });
        });
    });
</script>
{% endblock %} 