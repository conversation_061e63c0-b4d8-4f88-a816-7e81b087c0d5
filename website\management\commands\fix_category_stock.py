from django.core.management.base import BaseCommand
from website.utilities import update_all_category_stocks

class Command(BaseCommand):
    help = 'Fix category stock counts by recounting all equipment'

    def handle(self, *args, **options):
        self.stdout.write('Updating category stock counts...')
        
        # Use the utility function to update all stocks
        updated = update_all_category_stocks()
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully updated {updated} categories!')
        ) 