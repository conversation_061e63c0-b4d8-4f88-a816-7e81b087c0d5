{% load static %}

<div id="bg-canvas" class="fixed top-0 left-0 w-full h-full -z-10"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Scene setup
    const scene = new THREE.Scene();
    const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
    const renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
    
    renderer.setSize(window.innerWidth, window.innerHeight);
    renderer.setClearColor(0x000000, 0);
    document.getElementById('bg-canvas').appendChild(renderer.domElement);

    // Create particles with increased count and size
    const particlesGeometry = new THREE.BufferGeometry();
    const particlesCount = 1500; // Increased particle count
    const posArray = new Float32Array(particlesCount * 3);

    for(let i = 0; i < particlesCount * 3; i++) {
        posArray[i] = (Math.random() - 0.5) * 20; // Wider spread
    }

    particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

    // Create material with larger particles
    const particlesMaterial = new THREE.PointsMaterial({
        size: 0.008, // Slightly larger particles
        color: '#4a90e2',
        transparent: true,
        opacity: 0.6, // Slightly reduced opacity
        blending: THREE.AdditiveBlending
    });

    // Create mesh
    const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
    scene.add(particlesMesh);

    camera.position.z = 5;

    // Enhanced mouse movement effect
    let mouseX = 0;
    let mouseY = 0;
    let targetRotationX = 0;
    let targetRotationY = 0;
    
    document.addEventListener('mousemove', (event) => {
        mouseX = (event.clientX / window.innerWidth - 0.5) * 2;
        mouseY = (event.clientY / window.innerHeight - 0.5) * 2;
        targetRotationX = mouseY * 0.5;
        targetRotationY = mouseX * 0.5;
    });

    // Faster base rotation speed
    const baseRotationSpeed = 0.001;
    let time = 0;

    // Animation with smoother movement
    function animate() {
        requestAnimationFrame(animate);
        time += 0.01;

        // Base rotation
        particlesMesh.rotation.x += baseRotationSpeed * 2;
        particlesMesh.rotation.y += baseRotationSpeed * 3;

        // Smooth mouse following with easing
        particlesMesh.rotation.x += (targetRotationX - particlesMesh.rotation.x) * 0.1;
        particlesMesh.rotation.y += (targetRotationY - particlesMesh.rotation.y) * 0.1;

        // Add subtle wave motion
        particlesMesh.position.y = Math.sin(time) * 0.1;
        particlesMesh.position.x = Math.cos(time) * 0.1;

        renderer.render(scene, camera);
    }

    // Handle resize
    window.addEventListener('resize', () => {
        camera.aspect = window.innerWidth / window.innerHeight;
        camera.updateProjectionMatrix();
        renderer.setSize(window.innerWidth, window.innerHeight);
    });

    animate();
});
</script> 