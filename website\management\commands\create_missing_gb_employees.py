#!/usr/bin/env python3
"""
Management command to create missing GB_Employee records for existing internal GB users.
This command should be run after implementing the automatic GB_Employee creation feature
to ensure all existing internal GB users have corresponding GB_Employee records.
"""

from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from website.models import CustomUser, GB_Employee, UserType
import logging

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'Create missing GB_Employee records for existing internal GB users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Run in dry-run mode without making changes',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force creation even if user has no employee_id',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        force = options['force']
        
        if dry_run:
            self.stdout.write(
                self.style.WARNING('Running in DRY-RUN mode - no changes will be made')
            )
        
        try:
            # Get internal GB user type
            try:
                gb_user_type = UserType.objects.get(code='INTERNAL_GB')
            except UserType.DoesNotExist:
                raise CommandError("INTERNAL_GB user type not found")
            
            # Find internal GB users without GB_Employee records
            internal_gb_users = CustomUser.objects.filter(
                user_type=gb_user_type,
                gb_employee__isnull=True
            )
            
            if not force:
                # Only include users with employee_id
                internal_gb_users = internal_gb_users.exclude(employee_id='')
                internal_gb_users = internal_gb_users.exclude(employee_id__isnull=True)
            
            total_users = internal_gb_users.count()
            
            if total_users == 0:
                self.stdout.write(
                    self.style.SUCCESS('No internal GB users found that need GB_Employee records')
                )
                return
            
            self.stdout.write(
                f'Found {total_users} internal GB users without GB_Employee records'
            )
            
            created_count = 0
            linked_count = 0
            failed_count = 0
            
            for user in internal_gb_users:
                try:
                    with transaction.atomic():
                        if dry_run:
                            self.stdout.write(
                                f'DRY-RUN: Would process user {user.username} (Employee ID: {user.employee_id})'
                            )
                            continue
                        
                        # Check if user has employee_id
                        if not user.employee_id:
                            if force:
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'User {user.username} has no employee_id, skipping'
                                    )
                                )
                                failed_count += 1
                                continue
                            else:
                                continue
                        
                        # Check if GB_Employee already exists for this employee_id
                        existing_gb_employee = GB_Employee.objects.filter(
                            employee_number=user.employee_id
                        ).first()
                        
                        if existing_gb_employee:
                            # Link to existing GB_Employee record
                            user.gb_employee = existing_gb_employee
                            user.save(update_fields=['gb_employee'])
                            
                            # Sync Oracle data
                            user.sync_oracle_data()
                            
                            linked_count += 1
                            self.stdout.write(
                                f'Linked user {user.username} to existing GB_Employee {user.employee_id}'
                            )
                        else:
                            # Try to fetch from Oracle and create GB_Employee record
                            try:
                                from website.soap_client import OracleSOAPClient
                                soap_client = OracleSOAPClient()
                                oracle_data = soap_client.get_employee_data(user.employee_id)
                                
                                if oracle_data:
                                    # Create GB_Employee record from Oracle data
                                    gb_employee = GB_Employee.create_or_update_from_oracle(oracle_data)
                                    
                                    # Link to the new GB_Employee record
                                    user.gb_employee = gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    
                                    # Sync Oracle data
                                    user.sync_oracle_data()
                                    
                                    created_count += 1
                                    self.stdout.write(
                                        f'Created GB_Employee from Oracle for user {user.username} (Employee ID: {user.employee_id})'
                                    )
                                else:
                                    # Create a basic GB_Employee record
                                    gb_employee = GB_Employee.objects.create(
                                        employee_number=user.employee_id,
                                        english_name=f"{user.first_name} {user.last_name}".strip(),
                                        oracle_email_address=user.email,
                                        is_active=True
                                    )
                                    
                                    # Link to the new GB_Employee record
                                    user.gb_employee = gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    
                                    created_count += 1
                                    self.stdout.write(
                                        f'Created basic GB_Employee for user {user.username} (Employee ID: {user.employee_id})'
                                    )
                                    
                            except Exception as oracle_err:
                                # If Oracle fetch fails, create a basic GB_Employee record
                                self.stdout.write(
                                    self.style.WARNING(
                                        f'Could not fetch Oracle data for employee {user.employee_id}: {str(oracle_err)}'
                                    )
                                )
                                
                                try:
                                    gb_employee = GB_Employee.objects.create(
                                        employee_number=user.employee_id,
                                        english_name=f"{user.first_name} {user.last_name}".strip(),
                                        oracle_email_address=user.email,
                                        is_active=True
                                    )
                                    
                                    # Link to the new GB_Employee record
                                    user.gb_employee = gb_employee
                                    user.save(update_fields=['gb_employee'])
                                    
                                    created_count += 1
                                    self.stdout.write(
                                        f'Created basic GB_Employee for user {user.username} (Employee ID: {user.employee_id}) without Oracle data'
                                    )
                                    
                                except Exception as create_err:
                                    failed_count += 1
                                    self.stdout.write(
                                        self.style.ERROR(
                                            f'Failed to create GB_Employee for user {user.username}: {str(create_err)}'
                                        )
                                    )
                        
                except Exception as e:
                    failed_count += 1
                    self.stdout.write(
                        self.style.ERROR(
                            f'Error processing user {user.username}: {str(e)}'
                        )
                    )
            
            # Summary
            if not dry_run:
                self.stdout.write('\n' + '='*50)
                self.stdout.write(f'Summary:')
                self.stdout.write(f'  Total users processed: {total_users}')
                self.stdout.write(f'  GB_Employee records created: {created_count}')
                self.stdout.write(f'  Users linked to existing records: {linked_count}')
                self.stdout.write(f'  Failed: {failed_count}')
                
                if failed_count == 0:
                    self.stdout.write(
                        self.style.SUCCESS('All internal GB users now have GB_Employee records!')
                    )
                else:
                    self.stdout.write(
                        self.style.WARNING(f'{failed_count} users could not be processed')
                    )
            
        except Exception as e:
            raise CommandError(f'Command failed: {str(e)}') 