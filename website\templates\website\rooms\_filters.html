{% load i18n %}

<!-- Filters and Search -->
<div class="bg-white/5 backdrop-blur-md rounded-lg p-6 mb-8">
    <div class="flex flex-wrap items-center gap-6">
        <!-- Search -->
        <div class="flex-1 min-w-[300px]">
            <div class="relative">
                <input type="text" class="block w-full bg-white/5 border border-white/10 rounded-md py-2 pl-10 pr-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent" placeholder="{% trans 'Search rooms...' %}">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg class="h-5 w-5 text-white/30" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                    </svg>
                </div>
            </div>
        </div>

        <!-- Course Filter Group -->
        <div class="flex items-center gap-2">
            <span class="text-white/70 text-sm flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
                {% trans "Course:" %}
            </span>
            <div class="flex bg-white/5 rounded-md p-1 gap-1">
                <!-- Course filter buttons -->
                <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'All Courses' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                    </svg>
                </button>
                <!-- Add other course filter buttons here -->
            </div>
        </div>

        <!-- Type Filter Group -->
        <div class="flex items-center gap-2">
            <span class="text-white/70 text-sm flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
                </svg>
                {% trans "Type:" %}
            </span>
            <div class="flex bg-white/5 rounded-md p-1 gap-1">
                <!-- Room type filter buttons -->
                <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'All Types' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                    </svg>
                </button>
                <!-- Add other type filter buttons here -->
            </div>
        </div>

        <!-- Status Filter Group -->
        <div class="flex items-center gap-2">
            <span class="text-white/70 text-sm flex items-center">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% trans "Status:" %}
            </span>
            <div class="flex bg-white/5 rounded-md p-1 gap-1">
                <!-- Status filter buttons -->
                <button class="group relative p-2 rounded hover:bg-white/10 text-white/70 hover:text-white" title="{% trans 'All Status' %}">
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"/>
                    </svg>
                </button>
                <!-- Add other status filter buttons here -->
            </div>
        </div>
    </div>
</div> 