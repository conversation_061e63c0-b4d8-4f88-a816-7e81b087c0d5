{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Assign Course to Trainers" %}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- <PERSON>er -->
        <div class="flex justify-between items-center mb-8">
            <div class="flex items-center space-x-3">
                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                </svg>
                <h1 class="text-2xl font-bold text-white">{% trans "Assign Course to Trainers" %}</h1>
            </div>
            <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary/20 hover:bg-primary/30">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12"/>
                </svg>
                {% trans "Back to Courses" %}
            </a>
        </div>

        <!-- Assignment Form -->
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-6 mb-8 shadow-xl">
            <form method="post" action="{% url 'website:assign_course_to_trainers' %}">
                {% csrf_token %}

                <div class="mb-8">
                    <label for="course" class="block text-white font-medium text-lg mb-3">{% trans "Select Course" %}</label>
                    <select name="course" id="course" class="block w-full bg-white/10 border-2 border-white/20 rounded-md py-3 px-4 text-white text-base focus:outline-none focus:ring-2 focus:ring-primary focus:border-primary" required>
                        <option value="" class="bg-gray-800 text-white">{% trans "Select a course" %}</option>
                        {% for course in courses %}
                            <option value="{{ course.course_id }}" class="bg-gray-800 text-white py-2" {% if preselected_course and preselected_course.course_id == course.course_id %}selected{% endif %}>{{ course.name_en }} ({{ course.course_id }})</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="mb-8">
                    <label class="block text-white font-medium text-lg mb-3">{% trans "Selected Trainers" %}</label>
                    <p class="text-white/80 text-base mb-4">{% trans "Select one or more trainers to assign to this course" %}</p>
                    
                    <div class="bg-white/10 border-2 border-white/20 rounded-md max-h-96 overflow-y-auto">
                        {% for trainer in trainers %}
                            <div class="trainer-item p-5 border-b border-white/20 flex items-center justify-between hover:bg-white/10 transition-colors cursor-pointer {% cycle 'bg-transparent' 'bg-white/5' %}">
                                <div class="flex items-center">
                                    <input type="checkbox" name="trainers" value="{{ trainer.trainer_id }}" id="trainer_{{ trainer.trainer_id }}" class="mr-4 h-5 w-5 rounded border-white/30 text-primary focus:ring-primary">
                                    <label for="trainer_{{ trainer.trainer_id }}" class="text-white text-base font-medium">
                                        {{ trainer.user.get_full_name }} ({{ trainer.user.username }})
                                    </label>
                                </div>
                                <span class="text-sm px-3 py-1.5 rounded-full font-medium {% if trainer.status == 'READY' %}bg-green-500{% elif trainer.status == 'BUSY' %}bg-red-500{% else %}bg-orange-500{% endif %} text-white">
                                    {{ trainer.get_status_display }}
                                </span>
                            </div>
                        {% empty %}
                            <div class="p-5 text-white/70">{% trans "No trainers available" %}</div>
                        {% endfor %}
                    </div>
                </div>

                <div class="flex justify-end">
                    <button type="submit" class="inline-flex items-center px-6 py-3 border border-transparent rounded-md shadow-sm text-base font-medium text-white bg-primary hover:bg-primary/90 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {% trans "Assign Course" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Enhance dropdown visibility
        const courseSelect = document.getElementById('course');
        
        // Add click handler for the entire trainer item row
        document.querySelectorAll('.trainer-item').forEach(function(item) {
            item.addEventListener('click', function(e) {
                // Don't trigger if clicking directly on the checkbox
                if (e.target.type !== 'checkbox') {
                    const checkbox = this.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;
                    
                    // Add visual feedback
                    if (checkbox.checked) {
                        this.classList.add('bg-primary/20');
                    } else {
                        this.classList.remove('bg-primary/20');
                    }
                }
            });
            
            // Set initial state based on checkbox
            const checkbox = item.querySelector('input[type="checkbox"]');
            if (checkbox.checked) {
                item.classList.add('bg-primary/20');
            }
        });
    });
</script>
{% endblock %} 