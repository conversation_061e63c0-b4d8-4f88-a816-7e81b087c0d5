{% extends "website/login_base.html" %}
{% load static %}
{% load i18n %}

{% block content %}
<!-- Mobile Splash Screen -->
<div id="splash-screen" class="lg:hidden fixed inset-0 z-50 bg-gradient-to-br from-blue-900 to-black flex flex-col items-center justify-center transition-all duration-700">
    <div class="animate-pulse transform scale-110">
        <img src="{% static 'images/logo.png' %}" alt="GB Academy Logo" class="w-80 h-80 object-contain mb-12 drop-shadow-2xl">
    </div>
    <div class="text-white text-center space-y-4">
        <h1 class="text-4xl font-bold">{% if LANGUAGE_CODE == 'ar' %}{{ section_data.home.section.name_ar }}{% else %}{{ section_data.home.section.name_en }}{% endif %}</h1>
        <p class="text-xl text-white/80">{% if LANGUAGE_CODE == 'ar' %}{{ section_data.home.section.description_ar }}{% else %}{{ section_data.home.section.description_en }}{% endif %}</p>
    </div>
</div>

<!-- Mobile Login Button -->
<div class="lg:hidden fixed top-4 right-4 z-30">
    <button id="mobile-login-button" class="bg-white/10 backdrop-blur-md p-2 rounded-lg">
        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
        </svg>
    </button>
</div>

<!-- Mobile Login Screen -->
<div id="mobile-login" class="lg:hidden fixed inset-0 z-40 bg-gradient-to-br from-blue-900/95 to-black/95 transform transition-transform duration-500 translate-x-full">
    <div class="min-h-screen flex flex-col justify-between p-6">
        <!-- Top Section with Back Button -->
        <div class="relative text-center pt-8">
            <!-- Back Button -->
            <button id="mobile-login-back" class="absolute left-0 top-0 p-2 text-white/90 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </button>
            <div class="bg-white/90 inline-block p-4 rounded-lg mb-4">
                <img class="mx-auto h-16 w-auto" src="{% static 'images/logo.png' %}" alt="GB Academy Logo">
            </div>
            <h2 class="text-2xl font-bold text-white mb-1">{% trans "Welcome Back" %}</h2>
            <p class="text-sm text-white/80">{% trans "Sign in to your GB Academy account" %}</p>
        </div>

        <!-- Login Form -->
        <div class="flex-1 flex items-center">
            <form class="w-full space-y-4" action="{% url 'website:home' %}" method="POST">
                {% csrf_token %}
                <input type="hidden" name="user_type" id="mobile_user_type" value="external">
                {% if messages %}
                <div class="mb-4">
                    {% for message in messages %}
                        <div class="p-3 rounded-md text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% endif %}">
                            {{ message }}
                        </div>
                    {% endfor %}
                </div>
                {% endif %}
                
                <div class="space-y-3">
                    <div>
                        <label for="mobile_login_identifier" id="mobile_login_identifier_label" class="block text-sm font-medium text-white/90">{% trans "Email or Username" %}</label>
                        <div class="relative mt-1">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg id="mobile_login_identifier_icon" class="h-4 w-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <input id="mobile_login_identifier" name="login_identifier" type="text" required 
                                placeholder="{% trans 'Enter your email or username' %}"
                                class="block w-full pl-10 px-3 py-3 text-white bg-white/10 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent">
                        </div>
                    </div>
                    <div id="mobile_password_field" class="transition-all duration-300">
                        <label for="mobile_password" class="block text-sm font-medium text-white/90">{% trans "Password" %}</label>
                        <div class="relative mt-1">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg class="h-4 w-4 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                </svg>
                            </div>
                            <input id="mobile_password" name="password" type="password" required 
                                class="block w-full pl-10 px-3 py-3 text-white bg-white/10 border border-white/20 rounded-lg focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent">
                        </div>
                    </div>
                    
                    <!-- Mobile OTP Field (Hidden by default) -->
                    <div id="mobile_otp_field" class="transition-all duration-300" style="display: none;">
                        <label class="block text-sm font-medium text-white/90 mb-2">{% trans "Verification Code" %}</label>
                        <div class="flex mb-2 space-x-2 rtl:space-x-reverse justify-center">
                            <div>
                                <label for="mobile-code-1" class="sr-only">First code</label>
                                <input type="text" maxlength="1" data-focus-input-init data-focus-input-next="mobile-code-2" id="mobile-code-1" class="otp-digit block w-9 h-9 py-3 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                            </div>
                            <div>
                                <label for="mobile-code-2" class="sr-only">Second code</label>
                                <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="mobile-code-1" data-focus-input-next="mobile-code-3" id="mobile-code-2" class="otp-digit block w-9 h-9 py-3 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                            </div>
                            <div>
                                <label for="mobile-code-3" class="sr-only">Third code</label>
                                <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="mobile-code-2" data-focus-input-next="mobile-code-4" id="mobile-code-3" class="otp-digit block w-9 h-9 py-3 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                            </div>
                            <div>
                                <label for="mobile-code-4" class="sr-only">Fourth code</label>
                                <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="mobile-code-3" data-focus-input-next="mobile-code-5" id="mobile-code-4" class="otp-digit block w-9 h-9 py-3 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                            </div>
                            <div>
                                <label for="mobile-code-5" class="sr-only">Fifth code</label>
                                <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="mobile-code-4" data-focus-input-next="mobile-code-6" id="mobile-code-5" class="otp-digit block w-9 h-9 py-3 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                            </div>
                            <div>
                                <label for="mobile-code-6" class="sr-only">Sixth code</label>
                                <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="mobile-code-5" id="mobile-code-6" class="otp-digit block w-9 h-9 py-3 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                            </div>
                        </div>
                        <p class="text-center text-xs text-white/70 mb-2">{% trans "Please enter the 6 digit code we sent via email." %}</p>
                        <div id="mobile_otp_timer" class="text-center text-xs text-white/70"></div>
                        <input id="mobile_otp_code" name="otp_code" type="hidden" />
                    </div>
                </div>

                <div id="mobile_auth_options" class="flex items-center justify-between mt-4 transition-all duration-300">
                    <div class="flex items-center">
                        <input id="mobile-remember-me" name="remember-me" type="checkbox" 
                            class="h-4 w-4 bg-white/10 border-white/20 rounded">
                        <label for="mobile-remember-me" class="ml-2 block text-sm text-white/90">
                            {% trans "Remember me" %}
                        </label>
                    </div>
                    <a href="#" id="mobile_forgot_password_link" class="text-sm font-medium text-white/90 hover:text-white">
                        {% trans "Forgot?" %}
                    </a>
                </div>

                <button type="submit" id="mobile_login_submit_btn"
                    class="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg py-3 px-4 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-300">
                    {% trans "Sign in" %}
                </button>
                
                <!-- Mobile Resend OTP Button (Hidden by default) -->
                <button type="button" id="mobile_resend_otp_btn" style="display: none;"
                    class="w-full bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 rounded-lg py-3 px-4 text-sm font-medium focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-colors duration-300">
                    {% trans "Resend Code" %}
                </button>
            </form>
        </div>

        <!-- Bottom Section -->
        <div class="pb-8">
            <div class="text-center">
                <p class="text-sm text-white/80">
                    {% trans "Don't have an account?" %}
                    <a href="{% url 'website:register_step1' %}" class="font-medium text-blue-400 hover:text-blue-300">
                        {% trans "Register here" %}
                    </a>
                </p>
            </div>
            <!-- Mobile User Type Toggle -->
            <div class="flex justify-center mt-6">
                <div class="inline-flex">
                    <div class="inline-flex bg-white/10 rounded-lg p-1">
                        <button type="button" data-type="external" class="mobile-user-type-button px-4 py-2 rounded-md text-sm font-medium text-white/90 mobile-user-type-btn-active">
                            {% trans "External" %}
                        </button>
                        <button type="button" data-type="internal" class="mobile-user-type-button px-4 py-2 rounded-md text-sm font-medium text-white/90">
                            {% trans "Internal" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Background Slideshow -->
<div class="fixed inset-0 z-0">
    <div class="slideshow-container">
        <div class="slide">
            <img src="{% static 'images/slides/slide1.png' %}" class="slide-image zoom-animation" alt="Background 1">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide2.png' %}" class="slide-image zoom-animation" alt="Background 2">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide3.png' %}" class="slide-image zoom-animation" alt="Background 3">
            <div class="overlay"></div>
        </div>
    </div>
</div>

<!-- Sidebar Navigation -->
<div class="fixed left-0 top-0 h-screen w-14 bg-black/20 backdrop-blur-md border-r border-white/10 flex items-center justify-center z-20">
    <nav class="py-4">
        <ul class="space-y-8" id="nav-links">
            {% for section in section_data.values %}
            <li>
                <a href="#{{ section.section.code }}" class="nav-link {% if forloop.first %}active{% endif %} flex items-center justify-center w-10 h-10 text-white/90 hover:bg-white/10 rounded-lg group transition-all duration-300" title="{% if LANGUAGE_CODE == 'ar' %}{{ section.section.name_ar }}{% else %}{{ section.section.name_en }}{% endif %}" data-section="{{ section.section.code }}">
                    <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        {{ section.section.icon_svg|safe }}
                    </svg>
                </a>
            </li>
            {% endfor %}
        </ul>
    </nav>
</div>

<!-- Main Container with Fixed Login -->
<div class="flex min-h-screen relative z-10">
    <!-- Content Area (Scrollable) -->
    <div class="flex-1 ml-14 overflow-x-hidden">
        <div class="container pl-8 pr-4 py-8 lg:pr-6">
            <!-- Content Sections Container -->
            <div id="content-sections" class="w-full max-w-3xl space-y-0">
                {% for section_code, data in section_data.items %}
                <section id="{{ section_code }}" class="content-section min-h-screen flex items-center transform transition-transform duration-700 ease-in-out">
                    <div class="w-full space-y-8 py-16">
                        <div class="p-8 bg-white/5 backdrop-blur-md rounded-xl border border-white/10">
                            <div class="flex items-center space-x-4 mb-4">
                                <svg class="h-10 w-10 text-white/90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    {{ data.section.icon_svg|safe }}
                                </svg>
                                <h3 class="text-4xl font-bold text-white bg-gradient-to-r from-white/95 to-white/75 bg-clip-text text-transparent">
                                    {% if LANGUAGE_CODE == 'ar' %}{{ data.section.name_ar }}{% else %}{{ data.section.name_en }}{% endif %}
                                </h3>
                            </div>
                            <p class="text-xl leading-relaxed text-white/90">
                                {% if LANGUAGE_CODE == 'ar' %}{{ data.section.description_ar }}{% else %}{{ data.section.description_en }}{% endif %}
                            </p>
                        </div>
                        
                        {% if section_code == 'courses' %}
                        <!-- Courses Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% for category, courses in course_categories.items %}
                            <div class="p-6 bg-white/5 backdrop-blur-md rounded-lg">
                                <div class="flex items-center space-x-3 mb-3">
                                    <svg class="h-6 w-6 text-white/90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21l-7-5-7 5V5a2 2 0 012-2h10a2 2 0 012 2v16z"/>
                                    </svg>
                                    <h4 class="text-xl font-semibold text-white">{% trans category %}</h4>
                                </div>
                                <ul class="space-y-2 text-white/80">
                                    {% for course in courses %}
                                    <li class="flex items-center space-x-2">
                                        <svg class="h-4 w-4 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4"/>
                                        </svg>
                                        <span>{% if LANGUAGE_CODE == 'ar' %}{{ course.name_ar }}{% else %}{{ course.name_en }}{% endif %}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endfor %}
                        </div>
                        {% elif section_code == 'contact' %}
                        <!-- Contact Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="p-6 bg-white/5 backdrop-blur-md rounded-lg">
                                <div class="flex items-center space-x-3 mb-3">
                                    <svg class="h-6 w-6 text-white/90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"/>
                                    </svg>
                                    <h4 class="text-xl font-semibold text-white">{% trans "Contact Information" %}</h4>
                                </div>
                                <ul class="space-y-4 text-white/80">
                                    {% for info in contact_info %}
                                    <li class="flex items-center space-x-3">
                                        <svg class="h-5 w-5 text-white/60" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            {{ info.icon_svg|safe }}
                                        </svg>
                                        <span>{% if LANGUAGE_CODE == 'ar' %}{{ info.value_ar }}{% else %}{{ info.value_en }}{% endif %}</span>
                                    </li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        {% else %}
                        <!-- Features Grid -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% for feature in data.features %}
                            <div class="p-6 bg-white/5 backdrop-blur-md rounded-lg transform hover:scale-105 transition-all duration-300">
                                <div class="flex items-center space-x-3 mb-3">
                                    <svg class="h-6 w-6 text-white/90" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        {{ feature.icon_svg|safe }}
                                    </svg>
                                    <h4 class="text-xl font-semibold text-white">{% if LANGUAGE_CODE == 'ar' %}{{ feature.title_ar }}{% else %}{{ feature.title_en }}{% endif %}</h4>
                                </div>
                                <p class="text-white/80">{% if LANGUAGE_CODE == 'ar' %}{{ feature.description_ar }}{% else %}{{ feature.description_en }}{% endif %}</p>
                            </div>
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </section>
                {% endfor %}
            </div>
        </div>
    </div>

    <!-- Fixed Login Section -->
    <div class="hidden lg:block w-[400px] fixed right-0 top-0 h-screen bg-black/10 backdrop-blur-md border-l border-white/10">
        <div class="h-full flex items-center justify-center p-8">
            <div class="w-full space-y-6">
                <div class="text-center">
                    <div class="bg-white/90 inline-block p-4 rounded-lg mb-4">
                        <img class="mx-auto h-16 w-auto" src="{% static 'images/logo.png' %}" alt="GB Academy Logo">
                    </div>
                    <h2 class="text-2xl font-bold tracking-tight text-white mb-1">
                        {% trans "Welcome Back" %}
                    </h2>
                    <p class="text-sm text-white/80">
                        {% trans "Sign in to your GB Academy account" %}
                    </p>
                    <!-- User Type Toggle -->
                    <div class="flex items-center justify-center mt-3">
                        <div class="inline-flex">
                            <div class="flex bg-white/10 p-1 rounded-xl">
                                <button type="button" data-type="external" class="user-type-button flex items-center space-x-2 px-4 py-2 rounded-lg text-white/70 hover:bg-white/10 transition-all duration-300 user-type-btn-active">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                    </svg>
                                    <span class="text-sm font-medium">{% trans "External" %}</span>
                                </button>
                                <button type="button" data-type="internal" class="user-type-button flex items-center space-x-2 px-4 py-2 rounded-lg text-white/70 hover:bg-white/10 transition-all duration-300">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                    </svg>
                                    <span class="text-sm font-medium">{% trans "Internal" %}</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <form class="mt-6 space-y-4" action="{% url 'website:home' %}" method="POST">
                    {% csrf_token %}
                    <input type="hidden" name="user_type" id="user_type" value="external">
                    {% if messages %}
                    <div class="mb-4">
                        {% for message in messages %}
                            <div class="p-3 rounded-md text-sm {% if message.tags == 'error' %}bg-red-100 text-red-700{% endif %}">
                                {{ message }}
                            </div>
                        {% endfor %}
                    </div>
                    {% endif %}
                    
                    <div class="space-y-3">
                        <div>
                            <label for="login_identifier" id="login_identifier_label" class="block text-sm font-medium text-white/90">{% trans "Email or Username" %}</label>
                            <div class="relative mt-1">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg id="login_identifier_icon" class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                    </svg>
                                </div>
                                <input id="login_identifier" name="login_identifier" type="text" required 
                                    placeholder="{% trans 'Enter your email or username' %}"
                                    class="block w-full pl-10 px-3 py-2 text-sm bg-white/10 border border-white/20 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent">
                            </div>
                        </div>
                        <div id="password_field" class="transition-all duration-300">
                            <label for="password" class="block text-sm font-medium text-white/90">{% trans "Password" %}</label>
                            <div class="relative mt-1">
                                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                    <svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                                    </svg>
                                </div>
                                <input id="password" name="password" type="password" required 
                                    class="block w-full pl-10 px-3 py-2 text-sm bg-white/10 border border-white/20 text-white rounded-md focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent">
                            </div>
                        </div>
                        
                        <!-- OTP Field (Hidden by default) -->
                        <div id="otp_field" class="transition-all duration-300" style="display: none;">
                            <label class="block text-sm font-medium text-white/90 mb-2">{% trans "Verification Code" %}</label>
                            <div class="flex mb-2 space-x-2 rtl:space-x-reverse justify-center">
                                <div>
                                    <label for="code-1" class="sr-only">First code</label>
                                    <input type="text" maxlength="1" data-focus-input-init data-focus-input-next="code-2" id="code-1" class="otp-digit block w-8 h-8 py-2 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                                </div>
                                <div>
                                    <label for="code-2" class="sr-only">Second code</label>
                                    <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="code-1" data-focus-input-next="code-3" id="code-2" class="otp-digit block w-8 h-8 py-2 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                                </div>
                                <div>
                                    <label for="code-3" class="sr-only">Third code</label>
                                    <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="code-2" data-focus-input-next="code-4" id="code-3" class="otp-digit block w-8 h-8 py-2 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                                </div>
                                <div>
                                    <label for="code-4" class="sr-only">Fourth code</label>
                                    <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="code-3" data-focus-input-next="code-5" id="code-4" class="otp-digit block w-8 h-8 py-2 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                                </div>
                                <div>
                                    <label for="code-5" class="sr-only">Fifth code</label>
                                    <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="code-4" data-focus-input-next="code-6" id="code-5" class="otp-digit block w-8 h-8 py-2 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                                </div>
                                <div>
                                    <label for="code-6" class="sr-only">Sixth code</label>
                                    <input type="text" maxlength="1" data-focus-input-init data-focus-input-prev="code-5" id="code-6" class="otp-digit block w-8 h-8 py-2 text-sm font-extrabold text-center text-white bg-white/10 border border-white/20 rounded-lg focus:ring-2 focus:ring-white/50 focus:border-white/50" />
                                </div>
                            </div>
                            <p class="text-center text-xs text-white/70 mb-2">{% trans "Please enter the 6 digit code we sent via email." %}</p>
                            <div id="otp_timer" class="text-center text-xs text-white/70"></div>
                            <input id="otp_code" name="otp_code" type="hidden" />
                        </div>
                    </div>

                    <div id="auth_options" class="flex items-center justify-between mt-4 transition-all duration-300">
                        <div class="flex items-center">
                            <input id="remember-me" name="remember-me" type="checkbox" 
                                class="h-3 w-3 bg-white/10 border-white/20 rounded">
                            <label for="remember-me" class="ml-2 block text-xs text-white/90">
                                {% trans "Remember me" %}
                            </label>
                        </div>
                        <a href="#" id="forgot_password_link" class="text-xs font-medium text-white/90 hover:text-white transition-colors duration-300">
                            {% trans "Forgot password?" %}
                        </a>
                    </div>

                    <button type="submit" id="login_submit_btn"
                        class="w-full bg-white/20 hover:bg-white/30 text-white text-sm rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-300 transform hover:scale-[1.02]">
                        {% trans "Sign in" %}
                    </button>
                    
                    <!-- Resend OTP Button (Hidden by default) -->
                    <button type="button" id="resend_otp_btn" style="display: none;"
                        class="w-full bg-blue-600/20 hover:bg-blue-600/30 text-blue-300 text-sm rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-300 transform hover:scale-[1.02]">
                        {% trans "Resend Code" %}
                    </button>
                </form>

                <div class="mt-4 text-center">
                    <p class="text-xs text-white/80">
                        {% trans "Don't have an account?" %}
                        <a href="{% url 'website:register_step1' %}" class="font-medium text-white hover:text-white/80 transition-colors duration-300">
                            {% trans "Register here" %}
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    /* Active navigation link styles with enhanced glow */
    .nav-link.active {
        @apply bg-white/20 ring-2 ring-white/50;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.3),
                   0 0 30px rgba(255, 255, 255, 0.2),
                   0 0 45px rgba(255, 255, 255, 0.1);
    }
    
    /* Slideshow and Zoom Animation Styles */
    .slideshow-container {
        position: relative;
        width: 100%;
        height: 100vh;
        overflow: hidden;
    }

    .slide {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        transition: opacity 1.5s ease-in-out;
    }

    .slide.active {
        opacity: 1;
    }

    .slide-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .zoom-animation {
        animation: zoomEffect 20s infinite;
    }

    @keyframes zoomEffect {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.1);
        }
        100% {
            transform: scale(1);
        }
    }

    /* Content section transitions */
    .content-section {
        opacity: 0;
        transform: translateY(20px);
        transition: opacity 0.7s ease-out, transform 0.7s ease-out;
    }
    
    .content-section.active {
        opacity: 1;
        transform: translateY(0);
    }
    
    /* Hide scrollbar but keep functionality */
    .overflow-x-hidden {
        scrollbar-width: none;
        -ms-overflow-style: none;
    }
    
    .overflow-x-hidden::-webkit-scrollbar {
        display: none;
    }
    
    /* Smooth scrolling */
    html {
        scroll-behavior: smooth;
    }

    /* User type toggle styles with glow effect */
    .user-type-btn-active {
        @apply bg-white/20 text-white;
        box-shadow: 0 0 15px rgba(255, 255, 255, 0.3),
                   0 0 30px rgba(255, 255, 255, 0.2),
                   0 0 45px rgba(255, 255, 255, 0.1);
    }
    
    .user-type-btn-active svg {
        @apply text-white;
        filter: drop-shadow(0 0 3px rgba(255, 255, 255, 0.5));
    }

    .user-type-btn-active span {
        @apply text-white;
    }

    /* RTL specific adjustments for language toggle */
    .rtl-mode .space-x-2 > :not([hidden]) ~ :not([hidden]) {
        --tw-space-x-reverse: 1;
    }
    
    /* Position transitions */
    .position-transition {
        transition: all 0.5s ease-in-out;
    }
    
    /* RTL specific adjustments */
    .rtl-mode {
        direction: rtl;
    }
    
    .rtl-mode .left-0 {
        left: auto !important;
        right: 0 !important;
    }
    
    .rtl-mode .right-0 {
        right: auto !important;
        left: 0 !important;
    }
    
    .rtl-mode .border-r {
        border-right: none;
        border-left: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .rtl-mode .border-l {
        border-left: none;
        border-right: 1px solid rgba(255, 255, 255, 0.1);
    }

    .rtl-mode .pl-3 {
        padding-left: 0;
        padding-right: 0.75rem;
    }

    .rtl-mode .pl-10 {
        padding-left: 0;
        padding-right: 2.5rem;
    }

    .rtl-mode .ml-2 {
        margin-left: 0;
        margin-right: 0.5rem;
    }

    .rtl-mode .ml-14 {
        margin-left: 0;
        margin-right: 3.5rem;
    }

    /* Mobile-specific styles */
    @media (max-width: 1024px) {
        .mobile-user-type-btn-active {
            @apply bg-white/20 text-white;
            box-shadow: 0 0 10px rgba(255, 255, 255, 0.2);
        }

        #mobile-login.show {
            transform: translateX(0);
        }

        #splash-screen.hide {
            opacity: 0;
            pointer-events: none;
        }

        .mobile-menu-open {
            overflow: hidden;
        }

        .mobile-nav-active {
            @apply bg-white/20;
        }

        /* RTL adjustments for back button */
        .rtl-mode #mobile-login-back {
            left: auto;
            right: 0;
            transform: scaleX(-1);
        }
    }
</style>

<script>
// Global functions (outside DOMContentLoaded to avoid scope issues)
function showMessage(message, type = 'info', isMobile = false) {
    // Create or update message element
    const messageId = isMobile ? 'mobile_message' : 'desktop_message';
    let messageEl = document.getElementById(messageId);
    
    if (!messageEl) {
        messageEl = document.createElement('div');
        messageEl.id = messageId;
        messageEl.className = 'mt-3 p-3 rounded text-sm';
        
        // Find container to append message
        const form = isMobile ? 
            document.querySelector('#mobile-login form') : 
            document.querySelector('.fixed.lg\\:block form');
            
        if (form) {
            form.appendChild(messageEl);
        }
    }
    
    // Update message content and styling
    messageEl.textContent = message;
    messageEl.className = `mt-3 p-3 rounded text-sm ${
        type === 'success' ? 'bg-green-100/20 text-green-300 border border-green-400/20' :
        type === 'error' ? 'bg-red-100/20 text-red-300 border border-red-400/20' :
        'bg-blue-100/20 text-blue-300 border border-blue-400/20'
    }`;
    
    // Auto-hide success messages after 3 seconds
    if (type === 'success') {
        setTimeout(() => {
            if (messageEl && messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 3000);
    }
}

function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function getOtpCode(isMobile = false) {
    const prefix = isMobile ? 'mobile-' : '';
    let code = '';
    for (let i = 1; i <= 6; i++) {
        const input = document.getElementById(`${prefix}code-${i}`);
        if (input) {
            code += input.value || '';
        }
    }
    return code;
}

function clearOtpInputs(isMobile = false) {
    const prefix = isMobile ? 'mobile-' : '';
    for (let i = 1; i <= 6; i++) {
        const input = document.getElementById(`${prefix}code-${i}`);
        if (input) {
            input.value = '';
        }
    }
    // Focus first input
    const firstInput = document.getElementById(`${prefix}code-1`);
    if (firstInput) firstInput.focus();
}

function updateHiddenOtpInput(isMobile = false) {
    const code = getOtpCode(isMobile);
    const hiddenInput = isMobile ? 
        document.getElementById('mobile_otp_code') : 
        document.getElementById('otp_code');
    if (hiddenInput) {
        hiddenInput.value = code;
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.nav-link');
    const sections = document.querySelectorAll('.content-section');
    const navLinksArray = Array.from(navLinks);
    let isScrolling = false;
    
    // Update active section based on scroll position
    function updateActiveSection() {
        if (isScrolling) return;
        
        const scrollPosition = window.scrollY;
        const windowHeight = window.innerHeight;
        const threshold = windowHeight / 2;
        
        sections.forEach((section, index) => {
            const rect = section.getBoundingClientRect();
            const sectionMiddle = rect.top + rect.height / 2;
            
            if (Math.abs(sectionMiddle - windowHeight / 2) < threshold) {
                // Remove active class from all sections and links
                sections.forEach(s => s.classList.remove('active'));
                navLinks.forEach(link => link.classList.remove('active'));
                
                // Add active class to current section and link
                section.classList.add('active');
                navLinksArray[index].classList.add('active');
            }
        });
    }
    
    // Handle click events with smooth scroll
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            isScrolling = true;
            
            const sectionId = this.getAttribute('data-section');
            const targetSection = document.getElementById(sectionId);
            
            // Remove active from all sections
            sections.forEach(s => s.classList.remove('active'));
            
            // Smooth scroll to target
            window.scrollTo({
                top: targetSection.offsetTop,
                behavior: 'smooth'
            });
            
            // Add active class after scroll
            setTimeout(() => {
                targetSection.classList.add('active');
                isScrolling = false;
            }, 700);
        });
    });
    
    // Throttled scroll event listener
    let scrollTimeout;
    window.addEventListener('scroll', () => {
        if (scrollTimeout) {
            window.cancelAnimationFrame(scrollTimeout);
        }
        
        scrollTimeout = window.requestAnimationFrame(() => {
            updateActiveSection();
        });
    });
    
    // Initial activation
    setTimeout(() => {
        document.querySelector('.content-section').classList.add('active');
        updateActiveSection();
    }, 100);

    // Add slideshow functionality
    const slides = document.querySelectorAll('.slide');
    let currentSlide = 0;

    function showSlide(index) {
        slides.forEach(slide => {
            slide.classList.remove('active');
        });
        slides[index].classList.add('active');
    }

    function nextSlide() {
        currentSlide = (currentSlide + 1) % slides.length;
        showSlide(currentSlide);
    }

    // Show first slide and start slideshow
    showSlide(0);
    setInterval(nextSlide, 6000); // Change slide every 6 seconds

    // User Type Toggle Functionality
    const userTypeButtons = document.querySelectorAll('.user-type-button');
    const mobileUserTypeButtons = document.querySelectorAll('.mobile-user-type-button');
    const userTypeInput = document.getElementById('user_type');
    const mobileUserTypeInput = document.getElementById('mobile_user_type');
    
    // Form elements for desktop
    const loginIdentifierLabel = document.getElementById('login_identifier_label');
    const loginIdentifierIcon = document.getElementById('login_identifier_icon');
    const loginIdentifierInput = document.getElementById('login_identifier');
    const passwordField = document.getElementById('password_field');
    const passwordInput = document.getElementById('password');
    const otpField = document.getElementById('otp_field');
    const otpInput = document.getElementById('otp_code');
    const otpTimer = document.getElementById('otp_timer');
    const authOptions = document.getElementById('auth_options');
    const forgotPasswordLink = document.getElementById('forgot_password_link');
    const loginSubmitBtn = document.getElementById('login_submit_btn');
    const resendOtpBtn = document.getElementById('resend_otp_btn');
    
    // Form elements for mobile
    const mobileLoginIdentifierLabel = document.getElementById('mobile_login_identifier_label');
    const mobileLoginIdentifierIcon = document.getElementById('mobile_login_identifier_icon');
    const mobileLoginIdentifierInput = document.getElementById('mobile_login_identifier');
    const mobilePasswordField = document.getElementById('mobile_password_field');
    const mobilePasswordInput = document.getElementById('mobile_password');
    const mobileOtpField = document.getElementById('mobile_otp_field');
    const mobileOtpInput = document.getElementById('mobile_otp_code');
    const mobileOtpTimer = document.getElementById('mobile_otp_timer');
    const mobileAuthOptions = document.getElementById('mobile_auth_options');
    const mobileForgotPasswordLink = document.getElementById('mobile_forgot_password_link');
    const mobileLoginSubmitBtn = document.getElementById('mobile_login_submit_btn');
    const mobileResendOtpBtn = document.getElementById('mobile_resend_otp_btn');
    
    // OTP state variables
    let otpCountdown = null;
    let currentEmployeeId = null;
    let otpRequested = false;
    
    function updateFormFields(userType) {
        if (userType === 'internal') {
            // Update desktop form
            loginIdentifierLabel.textContent = 'Employee ID';
            loginIdentifierInput.placeholder = 'Enter your employee ID';
            loginIdentifierIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-4 0v2m0 0h.01M19 13.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>';
            
            // Hide password fields and show OTP setup for desktop
            passwordField.style.display = 'none';
            passwordInput.removeAttribute('required');
            otpField.style.display = 'none'; // Initially hidden until OTP is requested
            authOptions.style.display = 'none';
            resendOtpBtn.style.display = 'none';
            
            // Update mobile form
            mobileLoginIdentifierLabel.textContent = 'Employee ID';
            mobileLoginIdentifierInput.placeholder = 'Enter your employee ID';
            mobileLoginIdentifierIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V8a2 2 0 00-2-2h-5m-4 0V4a2 2 0 114 0v2m-4 0a2 2 0 104 0m-4 0v2m0 0h.01M19 13.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0z"/>';
            
            // Hide password fields and show OTP setup for mobile
            mobilePasswordField.style.display = 'none';
            mobilePasswordInput.removeAttribute('required');
            mobileOtpField.style.display = 'none'; // Initially hidden until OTP is requested
            mobileAuthOptions.style.display = 'none';
            mobileResendOtpBtn.style.display = 'none';
            
            // Reset OTP state
            resetOtpState();
        } else {
            // Update desktop form
            loginIdentifierLabel.textContent = 'Email or Username';
            loginIdentifierInput.placeholder = 'Enter your email or username';
            loginIdentifierIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />';
            
            // Show password fields and hide OTP for desktop
            passwordField.style.display = 'block';
            passwordInput.setAttribute('required', '');
            otpField.style.display = 'none';
            authOptions.style.display = 'flex';
            resendOtpBtn.style.display = 'none';
            
            // Update mobile form
            mobileLoginIdentifierLabel.textContent = 'Email or Username';
            mobileLoginIdentifierInput.placeholder = 'Enter your email or username';
            mobileLoginIdentifierIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />';
            
            // Show password fields and hide OTP for mobile
            mobilePasswordField.style.display = 'block';
            mobilePasswordInput.setAttribute('required', '');
            mobileOtpField.style.display = 'none';
            mobileAuthOptions.style.display = 'flex';
            mobileResendOtpBtn.style.display = 'none';
            
            // Reset OTP state
            resetOtpState();
        }
        
        // Update hidden inputs
        userTypeInput.value = userType;
        mobileUserTypeInput.value = userType;
    }
    
    function resetOtpState() {
        otpRequested = false;
        currentEmployeeId = null;
        
        // Remove required attributes from OTP inputs
        for (let i = 1; i <= 6; i++) {
            const desktopInput = document.getElementById(`code-${i}`);
            const mobileInput = document.getElementById(`mobile-code-${i}`);
            if (desktopInput) desktopInput.removeAttribute('required');
            if (mobileInput) mobileInput.removeAttribute('required');
        }
        
        // Clear OTP inputs
        clearOtpInputs(false);
        clearOtpInputs(true);
    }
    
    function oldResetOtpState() {
        otpRequested = false;
        currentEmployeeId = null;
        if (otpCountdown) {
            clearInterval(otpCountdown);
            otpCountdown = null;
        }
        otpInput.value = '';
        mobileOtpInput.value = '';
        otpTimer.textContent = '';
        mobileOtpTimer.textContent = '';
    }
    
    function startOtpCountdown() {
        let timeLeft = 60; // 1 minute
        
        function updateTimer() {
            const minutes = Math.floor(timeLeft / 60);
            const seconds = timeLeft % 60;
            const timeString = `Code expires in ${seconds}s`;
            
            otpTimer.textContent = timeString;
            mobileOtpTimer.textContent = timeString;
            
            if (timeLeft <= 0) {
                clearInterval(otpCountdown);
                otpTimer.textContent = 'Code expired. Click "Resend Code" to get a new one.';
                mobileOtpTimer.textContent = 'Code expired. Click "Resend Code" to get a new one.';
                resendOtpBtn.style.display = 'block';
                mobileResendOtpBtn.style.display = 'block';
            }
            timeLeft--;
        }
        
        updateTimer();
        otpCountdown = setInterval(updateTimer, 1000);
    }
    
    async function requestOtp(employeeId, isMobile = false) {
        try {
            // Validate employee ID
            if (!employeeId || employeeId.trim() === '') {
                throw new Error('Employee ID is required');
            }

            // Get CSRF token
            const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                             document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                             getCookie('csrftoken');

            const response = await fetch('/api/otp/request/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken,
                },
                body: JSON.stringify({
                    employee_id: employeeId.trim()
                })
            });

            // Check if response is ok
            if (!response.ok) {
                let errorMessage = 'Request failed';
                try {
                    const errorData = await response.json();
                    errorMessage = errorData.message || errorMessage;
                } catch (e) {
                    // If JSON parsing fails, use status text
                    errorMessage = response.statusText || errorMessage;
                }
                throw new Error(errorMessage);
            }

            const data = await response.json();
            
            if (data.status === 'success') {
                // Show OTP field and hide employee ID field
                if (isMobile) {
                    const mobileEmployeeField = document.getElementById('mobile_employee_id_field');
                    const mobileOtpField = document.getElementById('mobile_otp_field');
                    
                    if (mobileEmployeeField) mobileEmployeeField.style.display = 'none';
                    if (mobileOtpField) {
                        mobileOtpField.style.display = 'block';
                        // Add required attribute to OTP digit inputs
                        for (let i = 1; i <= 6; i++) {
                            const input = document.getElementById(`mobile-code-${i}`);
                            if (input) input.setAttribute('required', '');
                        }
                        // Focus first OTP input
                        const firstInput = document.getElementById('mobile-code-1');
                        if (firstInput) firstInput.focus();
                    }
                } else {
                    const employeeField = document.getElementById('employee_id_field');
                    const otpField = document.getElementById('otp_field');
                    
                    if (employeeField) employeeField.style.display = 'none';
                    if (otpField) {
                        otpField.style.display = 'block';
                        // Add required attribute to OTP digit inputs
                        for (let i = 1; i <= 6; i++) {
                            const input = document.getElementById(`code-${i}`);
                            if (input) input.setAttribute('required', '');
                        }
                        // Focus first OTP input
                        const firstInput = document.getElementById('code-1');
                        if (firstInput) firstInput.focus();
                    }
                }

                // Update status message
                showMessage(data.message, 'success', isMobile);
                
                otpRequested = true;
                startOtpCountdown();

            } else {
                throw new Error(data.message || 'Failed to request OTP');
            }

        } catch (error) {
            console.error('Error requesting OTP:', error);
            showMessage(error.message || 'Failed to send verification code. Please try again.', 'error', isMobile);
            
            if (!otpRequested) {
                // Reset form state on error
                resetOtpState();
            }
        }
    }

    // Helper function to get CSRF cookie
    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Helper function to show messages
    function showMessage(message, type = 'info', isMobile = false) {
        // Create or update message element
        const messageId = isMobile ? 'mobile_message' : 'desktop_message';
        let messageEl = document.getElementById(messageId);
        
        if (!messageEl) {
            messageEl = document.createElement('div');
            messageEl.id = messageId;
            messageEl.className = 'mt-3 p-3 rounded text-sm';
            
            // Find container to append message
            const container = isMobile ? 
                document.querySelector('#mobile_auth_section') : 
                document.querySelector('#desktop_auth_section');
                
            if (container) {
                container.appendChild(messageEl);
            }
        }
        
        // Update message content and styling
        messageEl.textContent = message;
        messageEl.className = `mt-3 p-3 rounded text-sm ${
            type === 'success' ? 'bg-green-100 text-green-700 border border-green-300' :
            type === 'error' ? 'bg-red-100 text-red-700 border border-red-300' :
            'bg-blue-100 text-blue-700 border border-blue-300'
        }`;
        
        // Auto-hide success messages after 3 seconds
        if (type === 'success') {
            setTimeout(() => {
                if (messageEl && messageEl.parentNode) {
                    messageEl.parentNode.removeChild(messageEl);
                }
            }, 3000);
        }
    }

    // Desktop user type toggle
    userTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userType = this.getAttribute('data-type');
            
            // Remove active class from all buttons
            userTypeButtons.forEach(btn => btn.classList.remove('user-type-btn-active'));
            // Add active class to clicked button
            this.classList.add('user-type-btn-active');
            
            // Update form fields
            updateFormFields(userType);
        });
    });

    // Mobile user type toggle
    mobileUserTypeButtons.forEach(button => {
        button.addEventListener('click', function() {
            const userType = this.getAttribute('data-type');
            
            // Remove active class from all buttons
            mobileUserTypeButtons.forEach(btn => btn.classList.remove('mobile-user-type-btn-active'));
            // Add active class to clicked button
            this.classList.add('mobile-user-type-btn-active');
            
            // Update form fields
            updateFormFields(userType);
        });
    });
    
    // Form submission handlers
    document.querySelector('.fixed.lg\\:block form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const userType = userTypeInput.value;
        
        if (userType === 'internal') {
            const employeeId = loginIdentifierInput.value.trim();
            
            if (!otpRequested) {
                // First step: Request OTP
                if (!employeeId) {
                    showMessage('Please enter your employee ID.', 'error');
                    return;
                }
                await requestOtp(employeeId, false);
            } else {
                // Second step: Verify OTP
                const otpCode = getOtpCode(false);
                if (!otpCode) {
                    showMessage('Please enter the verification code.', 'error');
                    return;
                }
                if (otpCode.length !== 6) {
                    showMessage('Verification code must be 6 digits.', 'error');
                    return;
                }
                await verifyOtp(employeeId, otpCode, false);
            }
        } else {
            // External user - submit form normally
            this.submit();
        }
    });
    
    document.querySelector('#mobile-login form').addEventListener('submit', async function(e) {
        e.preventDefault();
        
        const userType = mobileUserTypeInput.value;
        
        if (userType === 'internal') {
            const employeeId = mobileLoginIdentifierInput.value.trim();
            
            if (!otpRequested) {
                // First step: Request OTP
                if (!employeeId) {
                    showMessage('Please enter your employee ID.', 'error', true);
                    return;
                }
                await requestOtp(employeeId, true);
            } else {
                // Second step: Verify OTP
                const otpCode = getOtpCode(true);
                if (!otpCode) {
                    showMessage('Please enter the verification code.', 'error', true);
                    return;
                }
                if (otpCode.length !== 6) {
                    showMessage('Verification code must be 6 digits.', 'error', true);
                    return;
                }
                await verifyOtp(employeeId, otpCode, true);
            }
        } else {
            // External user - submit form normally
            this.submit();
        }
    });
    
    // Resend OTP button handlers
    resendOtpBtn.addEventListener('click', function() {
        resendOtp(false);
    });
    
    mobileResendOtpBtn.addEventListener('click', function() {
        resendOtp(true);
    });
    
    // OTP inputs are now handled by setupOtpInputs function

    // Mobile-specific JavaScript
    const splashScreen = document.getElementById('splash-screen');
    const mobileLogin = document.getElementById('mobile-login');
    const mobileLoginButton = document.getElementById('mobile-login-button');
    const mobileLoginBack = document.getElementById('mobile-login-back');

    // Hide splash screen after 2 seconds
    setTimeout(() => {
        splashScreen.classList.add('hide');
    }, 2000);

    // Mobile login toggle
    mobileLoginButton.addEventListener('click', () => {
        mobileLogin.classList.add('show');
        document.body.classList.add('mobile-menu-open');
    });

    // Mobile login back button
    mobileLoginBack.addEventListener('click', () => {
        mobileLogin.classList.remove('show');
        document.body.classList.remove('mobile-menu-open');
    });

    // Add CSRF token to meta tag for easy access
    if (!document.querySelector('meta[name="csrf-token"]')) {
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || getCookie('csrftoken');
        if (csrfToken) {
            const meta = document.createElement('meta');
            meta.name = 'csrf-token';
            meta.content = csrfToken;
            document.head.appendChild(meta);
        }
    }

    // Setup OTP digit inputs
    function setupOtpInputs(isMobile = false) {
        const prefix = isMobile ? 'mobile-' : '';
        
        for (let i = 1; i <= 6; i++) {
            const input = document.getElementById(`${prefix}code-${i}`);
            if (!input) continue;
            
            input.addEventListener('input', function() {
                // Only allow numbers
                this.value = this.value.replace(/\D/g, '');
                
                // Auto-focus next input
                if (this.value.length === 1 && i < 6) {
                    const nextInput = document.getElementById(`${prefix}code-${i + 1}`);
                    if (nextInput) nextInput.focus();
                }
                
                // Update hidden input
                updateHiddenOtpInput(isMobile);
            });
            
            input.addEventListener('keydown', function(e) {
                // Handle backspace
                if (e.key === 'Backspace' && this.value === '' && i > 1) {
                    const prevInput = document.getElementById(`${prefix}code-${i - 1}`);
                    if (prevInput) {
                        prevInput.focus();
                        prevInput.value = '';
                    }
                }
            });
            
            input.addEventListener('paste', function(e) {
                e.preventDefault();
                const paste = (e.clipboardData || window.clipboardData).getData('text');
                const digits = paste.replace(/\D/g, '').substring(0, 6);
                
                // Fill inputs with pasted digits
                for (let j = 0; j < digits.length && j < 6; j++) {
                    const targetInput = document.getElementById(`${prefix}code-${j + 1}`);
                    if (targetInput) {
                        targetInput.value = digits[j];
                    }
                }
                
                // Focus the next empty input or last input
                const nextIndex = Math.min(digits.length + 1, 6);
                const nextInput = document.getElementById(`${prefix}code-${nextIndex}`);
                if (nextInput) nextInput.focus();
                
                // Update hidden input
                updateHiddenOtpInput(isMobile);
            });
        }
    }
    
    // Initialize OTP inputs
    setupOtpInputs(false); // Desktop
    setupOtpInputs(true);  // Mobile

});

async function verifyOtp(employeeId, otpCode, isMobile = false) {
    try {
        // Validate inputs
        if (!employeeId || !otpCode) {
            throw new Error('Employee ID and OTP code are required');
        }

        // Get CSRF token
        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         getCookie('csrftoken');

        const response = await fetch('/api/otp/verify/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
            },
            body: JSON.stringify({
                employee_id: employeeId.trim(),
                otp_code: otpCode.trim()
            })
        });

        if (!response.ok) {
            let errorMessage = 'Verification failed';
            try {
                const errorData = await response.json();
                errorMessage = errorData.message || errorMessage;
            } catch (e) {
                errorMessage = response.statusText || errorMessage;
            }
            throw new Error(errorMessage);
        }

        const data = await response.json();
        
        if (data.status === 'success') {
            showMessage(data.message, 'success', isMobile);
            // Redirect to dashboard
            setTimeout(() => {
                window.location.href = data.redirect_url || '/dashboard-redirect/';
            }, 1000);
        } else {
            showMessage(data.message, 'error', isMobile);
            
            if (data.max_attempts_reached) {
                // Show resend button
                const resendBtn = isMobile ? 
                    document.getElementById('mobile_resend_otp_btn') : 
                    document.getElementById('resend_otp_btn');
                if (resendBtn) resendBtn.style.display = 'block';
            }
            
            // Clear OTP inputs
            clearOtpInputs(isMobile);
        }
    } catch (error) {
        console.error('Error verifying OTP:', error);
        showMessage(error.message || 'Verification failed. Please try again.', 'error', isMobile);
    }
}

async function resendOtp(isMobile = false) {
    try {
        const employeeIdInput = isMobile ? 
            document.getElementById('mobile_employee_id') : 
            document.getElementById('employee_id');
        
        if (!employeeIdInput || !employeeIdInput.value) {
            throw new Error('Employee ID not found');
        }

        const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]')?.value || 
                         document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') ||
                         getCookie('csrftoken');

        const response = await fetch('/api/otp/resend/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken,
            },
            body: JSON.stringify({
                employee_id: employeeIdInput.value.trim()
            })
        });

        if (!response.ok) {
            let errorMessage = 'Failed to resend code';
            try {
                const errorData = await response.json();
                errorMessage = errorData.message || errorMessage;
            } catch (e) {
                errorMessage = response.statusText || errorMessage;
            }
            throw new Error(errorMessage);
        }

        const data = await response.json();
        
        if (data.status === 'success') {
            showMessage(`New code sent to ${data.email}`, 'success', isMobile);
            
            // Hide resend button
            const resendBtn = isMobile ? 
                document.getElementById('mobile_resend_otp_btn') : 
                document.getElementById('resend_otp_btn');
            if (resendBtn) resendBtn.style.display = 'none';
            
            // Restart countdown
            startOtpCountdown();
            
            // Clear and focus OTP inputs
            clearOtpInputs(isMobile);
        } else {
            throw new Error(data.message || 'Failed to resend code');
        }
    } catch (error) {
        console.error('Error resending OTP:', error);
        showMessage(error.message || 'Failed to resend code. Please try again.', 'error', isMobile);
    }
}
</script>
{% endblock %} 