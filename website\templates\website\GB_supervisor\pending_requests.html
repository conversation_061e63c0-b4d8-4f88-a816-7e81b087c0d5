{% extends "website/base.html" %}
{% load i18n %}
{% load static %}
{% load website_extras %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
            </svg>
            {{ page_title }}
        </h1>
        <a href="{% url 'website:supervisor_my_team' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"/>
            </svg>
            {% trans "My Team" %}
        </a>
    </div>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Pending Requests Section -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg mb-8">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-orange-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"/>
                </svg>
                {% trans "Pending Approval Requests" %}
                {% if pending_requests %}
                <span class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-orange-500 text-white">
                    {{ total_pending }}
                </span>
                {% endif %}
            </h2>
        </div>

        {% if pending_requests %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Employee" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Available Seats" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Request Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Message" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for request in pending_requests %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ request.employee.english_name|default:request.employee.full_name }}</div>
                            <div class="text-xs text-gray-400">{{ request.employee.employee_number }}</div>
                            <div class="text-xs text-gray-400">{{ request.user.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ request.course_instance.course.name_en }}</div>
                            <div class="text-xs text-gray-400">{{ request.course_instance.course.get_category_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.course_instance.start_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ request.course_instance.start_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.course_instance.available_seats }}</div>
                            {% if request.course_instance.available_seats <= 0 %}
                            <div class="text-xs text-red-400">{% trans "Full" %}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ request.created_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4">
                            {% if request.request_message %}
                            <div class="text-sm text-gray-300 max-w-xs truncate" title="{{ request.request_message }}">
                                {{ request.request_message }}
                            </div>
                            {% else %}
                            <div class="text-xs text-gray-500 italic">{% trans "No message" %}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button onclick="viewAttendance('{{ request.user.id }}', '{{ request.employee.english_name|default:request.employee.full_name|escapejs }}')" 
                                    class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 mr-2">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
                                </svg>
                                {% trans "Attendance" %}
                            </button>
                            <button onclick="openApprovalModal('{{ request.request_id }}', 'approve')" 
                                    class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md bg-green-600 text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 mr-2">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                {% trans "Approve" %}
                            </button>
                            <button onclick="openApprovalModal('{{ request.request_id }}', 'reject')" 
                                    class="inline-flex items-center px-3 py-1 text-xs font-medium rounded-md bg-red-600 text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
                                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                                </svg>
                                {% trans "Reject" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Requests" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% trans "You don't have any pending approval requests from your team members." %}</p>
        </div>
        {% endif %}
    </div>

    <!-- Recent Processed Requests Section -->
    {% if recent_processed %}
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                </svg>
                {% trans "Recent Decisions" %}
                <span class="ml-2 text-xs text-gray-400">({% trans "All processed requests" %})</span>
            </h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Employee" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Decision" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Processed Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Notes" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for request in recent_processed %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ request.employee.english_name|default:request.employee.full_name }}</div>
                            <div class="text-xs text-gray-400">{{ request.employee.employee_number }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ request.course_instance.course.name_en }}</div>
                            <div class="text-xs text-gray-400">{{ request.course_instance.start_date|date:"M d, Y" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if request.status == 'APPROVED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-900/30 text-green-400 border border-green-800/50">
                                {% trans "Approved" %}
                            </span>
                            {% elif request.status == 'REJECTED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-900/30 text-red-400 border border-red-800/50">
                                {% trans "Rejected" %}
                            </span>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ request.processed_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ request.processed_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4">
                            {% if request.notes %}
                            <div class="text-sm text-gray-300 max-w-xs truncate" title="{{ request.notes }}">
                                {{ request.notes }}
                            </div>
                            {% else %}
                            <div class="text-xs text-gray-500 italic">{% trans "No notes" %}</div>
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                            <button onclick="viewAttendance('{{ request.user.id }}', '{{ request.employee.english_name|default:request.employee.full_name|escapejs }}')" 
                                    class="inline-flex items-center px-2 py-1 text-xs font-medium rounded-md bg-blue-600 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
                                </svg>
                                {% trans "Attendance" %}
                            </button>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
    {% endif %}
</div>

<!-- Approval/Rejection Modal -->
<div id="approvalModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-modal="true" role="dialog">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-black/70 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-gray-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-gray-900 px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <h3 class="text-lg leading-6 font-medium text-white" id="approvalModalTitle">
                            {% trans "Confirm Action" %}
                        </h3>
                        <div class="mt-4" id="approvalModalContent">
                            <p class="text-sm text-gray-300" id="approvalModalMessage">
                                {% trans "Are you sure you want to perform this action?" %}
                            </p>
                        </div>
                        
                        <form id="approvalForm" method="POST" action="">
                            {% csrf_token %}
                            <input type="hidden" id="approval_action" name="action" value="">
                            
                            <div class="mt-4">
                                <label for="approval_notes" class="block text-sm font-medium text-gray-300">
                                    {% trans "Notes" %} <span class="text-gray-400">({% trans "optional" %})</span>
                                </label>
                                <textarea id="approval_notes" name="notes" rows="3" 
                                    class="mt-1 block w-full rounded-md bg-gray-800 border-gray-600 text-white shadow-sm focus:border-primary focus:ring-primary"
                                    placeholder="{% trans 'Add any comments about your decision...' %}"></textarea>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            <div class="bg-gray-800 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmApprovalBtn" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Confirm" %}
                </button>
                <button type="button" id="closeApprovalBtn" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-600 shadow-sm px-4 py-2 bg-gray-700 text-base font-medium text-white hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 bg-black bg-opacity-50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-blue-900/90 backdrop-blur-md rounded-lg border border-blue-800/30 w-full max-w-6xl max-h-[90vh] overflow-hidden">
            <!-- Modal Header -->
            <div class="px-6 py-4 border-b border-blue-800/30 flex items-center justify-between">
                <h3 id="attendanceModalTitle" class="text-xl font-semibold text-white">
                    {% trans "Team Member Attendance" %}
                </h3>
                <button onclick="closeAttendanceModal()" class="text-gray-400 hover:text-white focus:outline-none">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>
                    </svg>
                </button>
            </div>

            <!-- Modal Body -->
            <div class="p-6 overflow-y-auto max-h-[70vh]">
                <div id="attendanceContent" class="space-y-6">
                    <!-- Loading spinner -->
                    <div id="attendanceLoading" class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                        <span class="ml-3 text-white">{% trans "Loading attendance data..." %}</span>
                    </div>

                    <!-- Attendance data will be loaded here -->
                    <div id="attendanceData" class="hidden">
                        <!-- Course attendance sections will be dynamically added here -->
                    </div>

                    <!-- No data message -->
                    <div id="noAttendanceData" class="hidden text-center py-12">
                        <svg class="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                        <p class="text-gray-400">{% trans "No attendance records found for this team member." %}</p>
                    </div>
                </div>
            </div>

            <!-- Modal Footer -->
            <div class="px-6 py-4 border-t border-blue-800/30 bg-blue-900/50">
                <div class="flex items-center justify-between">
                    <div class="text-sm text-gray-400">
                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        {% trans "Read-only view - You can view but not modify attendance records" %}
                    </div>
                    <button onclick="closeAttendanceModal()" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md transition-colors">
                        {% trans "Close" %}
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    let currentUserId = null;
    
    document.addEventListener('DOMContentLoaded', function() {
        // Global functions for approval modal
        window.openApprovalModal = function(requestId, action) {
            const modal = document.getElementById('approvalModal');
            const form = document.getElementById('approvalForm');
            const actionInput = document.getElementById('approval_action');
            const titleElement = document.getElementById('approvalModalTitle');
            const messageElement = document.getElementById('approvalModalMessage');
            const confirmBtn = document.getElementById('confirmApprovalBtn');
            const notesTextarea = document.getElementById('approval_notes');
            
            // Set form action URL
            form.action = `{% url 'website:supervisor_process_request' request_id='PLACEHOLDER' %}`.replace('PLACEHOLDER', requestId);
            
            // Set action value
            actionInput.value = action;
            
            // Clear previous notes
            notesTextarea.value = '';
            
            // Update modal content based on action
            if (action === 'approve') {
                titleElement.textContent = '{% trans "Approve Request" %}';
                messageElement.textContent = '{% trans "Are you sure you want to approve this enrollment request?" %}';
                confirmBtn.textContent = '{% trans "Approve" %}';
                confirmBtn.className = 'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-green-600 text-base font-medium text-white hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 sm:ml-3 sm:w-auto sm:text-sm';
            } else if (action === 'reject') {
                titleElement.textContent = '{% trans "Reject Request" %}';
                messageElement.textContent = '{% trans "Are you sure you want to reject this enrollment request?" %}';
                confirmBtn.textContent = '{% trans "Reject" %}';
                confirmBtn.className = 'w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm';
            }
            
            // Show modal
            modal.classList.remove('hidden');
        };

        // Close approval modal
        document.getElementById('closeApprovalBtn').addEventListener('click', function() {
            document.getElementById('approvalModal').classList.add('hidden');
        });

        // Confirm approval action
        document.getElementById('confirmApprovalBtn').addEventListener('click', function() {
            document.getElementById('approvalForm').submit();
        });

        // Close modal when clicking outside
        document.getElementById('approvalModal').addEventListener('click', function(e) {
            if (e.target === this) {
                this.classList.add('hidden');
            }
        });
    });
    
    // Attendance viewing functions
    function viewAttendance(userId, userName) {
        currentUserId = userId;
        
        // Update modal title
        document.getElementById('attendanceModalTitle').innerHTML = 
            `<svg class="w-6 h-6 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-6 9l2 2 4-4"/>
            </svg>
            ${userName} - {% trans "Attendance Records" %}`;
        
        // Show modal
        document.getElementById('attendanceModal').classList.remove('hidden');
        
        // Reset content
        document.getElementById('attendanceLoading').classList.remove('hidden');
        document.getElementById('attendanceData').classList.add('hidden');
        document.getElementById('noAttendanceData').classList.add('hidden');
        
        // Load attendance data
        loadAttendanceData(userId);
    }
    
    function closeAttendanceModal() {
        document.getElementById('attendanceModal').classList.add('hidden');
        currentUserId = null;
    }
    
    async function loadAttendanceData(userId) {
        try {
            const response = await fetch(`{% url 'website:supervisor_team_attendance_api' %}?user_id=${userId}`);
            const data = await response.json();
            
            // Hide loading
            document.getElementById('attendanceLoading').classList.add('hidden');
            
            if (data.status === 'success' && data.courses && data.courses.length > 0) {
                displayAttendanceData(data.courses);
                document.getElementById('attendanceData').classList.remove('hidden');
            } else {
                document.getElementById('noAttendanceData').classList.remove('hidden');
            }
            
        } catch (error) {
            console.error('Error loading attendance data:', error);
            document.getElementById('attendanceLoading').classList.add('hidden');
            document.getElementById('noAttendanceData').innerHTML = `
                <div class="text-center py-12">
                    <svg class="mx-auto h-12 w-12 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"/>
                    </svg>
                    <p class="text-red-400">{% trans "Error loading attendance data. Please try again." %}</p>
                </div>
            `;
            document.getElementById('noAttendanceData').classList.remove('hidden');
        }
    }
    
    function displayAttendanceData(courses) {
        const container = document.getElementById('attendanceData');
        container.innerHTML = '';
        
        courses.forEach(course => {
            const courseSection = document.createElement('div');
            courseSection.className = 'bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden';
            
            courseSection.innerHTML = `
                <div class="px-6 py-4 border-b border-white/10">
                    <h4 class="text-lg font-semibold text-white">${course.course_name}</h4>
                    <p class="text-sm text-gray-400">${course.start_date} - ${course.end_date}</p>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">{% trans "Date" %}</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">{% trans "Time" %}</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">{% trans "Room" %}</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">{% trans "Status" %}</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-white uppercase tracking-wider">{% trans "First Half" %}</th>
                                <th class="px-6 py-3 text-center text-xs font-medium text-white uppercase tracking-wider">{% trans "Second Half" %}</th>
                            </tr>
                        </thead>
                        <tbody class="bg-transparent divide-y divide-white/10">
                            ${course.sessions.map(session => `
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">${session.date}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">${session.time}</td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">${session.room}</td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(session.status)} border ${getStatusBorderClass(session.status)}">
                                            ${session.status}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <span class="inline-flex items-center">
                                            <input type="checkbox" ${session.first_half ? 'checked' : ''} disabled class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm ${session.first_half ? 'text-green-400' : 'text-gray-400'}">${session.first_half ? 'Present' : 'Absent'}</span>
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-center">
                                        <span class="inline-flex items-center">
                                            <input type="checkbox" ${session.second_half ? 'checked' : ''} disabled class="rounded border-gray-300 text-blue-600 focus:ring-blue-500 focus:ring-opacity-50">
                                            <span class="ml-2 text-sm ${session.second_half ? 'text-green-400' : 'text-gray-400'}">${session.second_half ? 'Present' : 'Absent'}</span>
                                        </span>
                                    </td>
                                </tr>
                            `).join('')}
                        </tbody>
                    </table>
                </div>
            `;
            
            container.appendChild(courseSection);
        });
    }
    
    function getStatusBadgeClass(status) {
        switch(status.toLowerCase()) {
            case 'upcoming': return 'bg-blue-900/30 text-blue-400';
            case 'completed': return 'bg-green-900/30 text-green-400';
            case 'in progress': return 'bg-yellow-900/30 text-yellow-400';
            case 'cancelled': return 'bg-red-900/30 text-red-400';
            default: return 'bg-gray-900/30 text-gray-400';
        }
    }
    
    function getStatusBorderClass(status) {
        switch(status.toLowerCase()) {
            case 'upcoming': return 'border-blue-800/50';
            case 'completed': return 'border-green-800/50';
            case 'in progress': return 'border-yellow-800/50';
            case 'cancelled': return 'border-red-800/50';
            default: return 'border-gray-800/50';
        }
    }
    
    // Close modal when clicking outside
    document.getElementById('attendanceModal').addEventListener('click', function(e) {
        if (e.target === this) {
            closeAttendanceModal();
        }
    });
    
    // Close modal with Escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && !document.getElementById('attendanceModal').classList.contains('hidden')) {
            closeAttendanceModal();
        }
    });
</script>
{% endblock %} 