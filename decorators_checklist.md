- [ ] `@trainer_required` at `website/views_trainer.py:48`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:129`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:200`: Requires the user to be a trainer.
- [ ] `@require_GET` at `website/views_trainer.py:295`: Requires the request method to be GET.
- [ ] `@trainer_required` at `website/views_trainer.py:296`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:367`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:393`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:523`: Requires the user to be a trainer.
- [ ] `@require_http_methods(["POST"])` at `website/views_trainer.py:557`: Requires the request method to be POST.
- [ ] `@trainer_required` at `website/views_trainer.py:558`: Requires the user to be a trainer.
- [ ] `@require_GET` at `website/views_trainer.py:603`: Requires the request method to be GET.
- [ ] `@trainer_required` at `website/views_trainer.py:604`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:651`: Requires the user to be a trainer.
- [ ] `@trainer_required` at `website/views_trainer.py:803`: Requires the user to be a trainer.
- [ ] `@login_required` at `website/corporate_views.py:25`: Requires the user to be logged in.
- [ ] `@super_admin_or_staff` at `website/corporate_views.py:26`: Requires the user to be a super admin or staff member.
- [ ] `@login_required` at `website/corporate_views.py:56`: Requires the user to be logged in.
- [ ] `@super_admin_or_staff` at `website/corporate_views.py:57`: Requires the user to be a super admin or staff member.
- [ ] `@login_required` at `website/corporate_views.py:126`: Requires the user to be logged in.
- [ ] `@super_admin_or_staff` at `website/corporate_views.py:127`: Requires the user to be a super admin or staff member.
- [ ] `@super_admin_or_staff` at `website/corporate_views.py:156`: Requires the user to be a super admin or staff member.
- [ ] `@require_http_methods(["GET"])` at `website/corporate_views.py:157`: Requires the request method to be GET.
- [ ] `@staff_member_required` at `website/corporate_views.py:197`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["GET"])` at `website/corporate_views.py:198`: Requires the request method to be GET.
- [ ] `@staff_member_required` at `website/corporate_views.py:234`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["POST"])` at `website/corporate_views.py:235`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/corporate_views.py:236`: Ensures the view runs in a database transaction.
- [ ] `@staff_member_required` at `website/corporate_views.py:365`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["PUT"])` at `website/corporate_views.py:366`: Requires the request method to be PUT.
- [ ] `@transaction.atomic` at `website/corporate_views.py:367`: Ensures the view runs in a database transaction.
- [ ] `@staff_member_required` at `website/corporate_views.py:454`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["DELETE"])` at `website/corporate_views.py:455`: Requires the request method to be DELETE.
- [ ] `@transaction.atomic` at `website/corporate_views.py:456`: Ensures the view runs in a database transaction.
- [ ] `@staff_member_required` at `website/corporate_views.py:497`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["GET"])` at `website/corporate_views.py:498`: Requires the request method to be GET.
- [ ] `@staff_member_required` at `website/corporate_views.py:522`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["POST"])` at `website/corporate_views.py:523`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/corporate_views.py:524`: Ensures the view runs in a database transaction.
- [ ] `@staff_member_required` at `website/corporate_views.py:623`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["POST"])` at `website/corporate_views.py:624`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/corporate_views.py:625`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/corporate_views.py:729`: Requires the user to be logged in.
- [ ] `@login_required` at `website/corporate_views.py:806`: Requires the user to be logged in.
- [ ] `@staff_member_required` at `website/corporate_views.py:807`: Requires the user to be logged in and a staff member.
- [ ] `@login_required` at `website/corporate_views.py:832`: Requires the user to be logged in.
- [ ] `@staff_member_required` at `website/corporate_views.py:833`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["POST"])` at `website/corporate_views.py:834`: Requires the request method to be POST.
- [ ] `@login_required` at `website/958`: Requires the user to be logged in.
- [ ] `@staff_member_required` at `website/959`: Requires the user to be logged in and a staff member.
- [ ] `@require_http_methods(["GET"])` at `website/960`: Requires the request method to be GET.
- [ ] `@login_required` at `website/1021`: Requires the user to be logged in.
- [ ] `@login_required` at `website/1077`: Requires the user to be logged in.
- [ ] `@login_required` at `website/1233`: Requires the user to be logged in.
- [ ] `@login_required` at `website/1248`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/1249`: Requires the request method to be GET.
- [ ] `@login_required` at `website/1296`: Requires the user to be logged in.
- [ ] `@login_required` at `website/1316`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/1317`: Requires the request method to be GET.
- [ ] `@login_required` at `website/1352`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/1353`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/1354`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/1511`: Requires the user to be logged in.
- [ ] `@require_http_methods(["DELETE"])` at `website/1512`: Requires the request method to be DELETE.
- [ ] `@transaction.atomic` at `website/1513`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/1553`: Requires the user to be logged in.
- [ ] `@login_required` at `website/1648`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/1649`: Requires the request method to be GET.
- [ ] `@login_required` at `website/1694`: Requires the user to be logged in.
- [ ] `@login_required` at `website/1819`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/1820`: Requires the request method to be GET.
- [ ] `@login_required` at `website/1908`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET", "POST"])` at `website/1909`: Requires the request method to be GET or POST.
- [ ] `@login_required` at `website/2021`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/2022`: Requires the request method to be GET.
- [ ] `@login_required` at `website/2203`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/2204`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/2205`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/2303`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/2304`: Requires the request method to be GET.
- [ ] `@login_required` at `website/2386`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/2387`: Requires the request method to be GET.
- [ ] `@login_required` at `website/2423`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/2424`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/2425`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/2549`: Requires the user to be logged in.
- [ ] `@login_required` at `website/2604`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/2605`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/2606`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/2656`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/2657`: Requires the request method to be POST.
- [ ] `@transaction.atomic` at `website/2658`: Ensures the view runs in a database transaction.
- [ ] `@login_required` at `website/2716`: Requires the user to be logged in.
- [ ] `@login_required` at `website/2774`: Requires the user to be logged in.
- [ ] `@login_required` at `website/2879`: Requires the user to be logged in.
- [ ] `@login_required` at `website/2933`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/2934`: Requires the request method to be GET.
- [ ] `@login_required` at `website/3008`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/3009`: Requires the request method to be GET.
- [ ] `@login_required` at `website/3104`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/3105`: Requires the request method to be GET.
- [ ] `@login_required` at `website/3286`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/3287`: Requires the request method to be GET.
- [ ] `@login_required` at `website/3350`: Requires the user to be logged in.
- [ ] `@login_required` at `website/3379`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/3427`: Requires the request method to be POST.
- [ ] `@login_required` at `website/3495`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/3496`: Requires the request method to be GET.
- [ ] `@login_required` at `website/3563`: Requires the user to be logged in.
- [ ] `@login_required` at `website/3595`: Requires the user to be logged in.
- [ ] `@login_required` at `website/3659`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/3660`: Requires the request method to be POST.
- [ ] `@login_required` at `website/individual_views.py:17`: Requires the user to be logged in.
- [ ] `@login_required` at `website/individual_views.py:48`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/individual_views.py:49`: Requires the request method to be GET.
- [ ] `@login_required` at `website/individual_views.py:83`: Requires the user to be logged in.
- [ ] `@require_http_methods(["GET"])` at `website/individual_views.py:84`: Requires the request method to be GET.
- [ ] `@login_required` at `website/individual_views.py:117`: Requires the user to be logged in.
- [ ] `@login_required` at `website/individual_views.py:149`: Requires the user to be logged in.
- [ ] `@login_required` at `website/individual_views.py:306`: Requires the user to be logged in.
- [ ] `@require_http_methods(["POST"])` at `website/individual_views.py:307`: Requires the request method to be POST.
- [ ] `@login_required` at `website/individual_views.py:428`: Requires the user to be logged in.


website/views_trainer.py:
- @trainer_required (line 48): Requires the user to be a trainer.
- @trainer_required (line 129): Requires the user to be a trainer.
- @trainer_required (line 200): Requires the user to be a trainer.
- @require_GET (line 295): Requires the request method to be GET.
- @trainer_required (line 296): Requires the user to be a trainer.
- @trainer_required (line 367): Requires the user to be a trainer.
- @trainer_required (line 393): Requires the user to be a trainer.
- @trainer_required (line 523): Requires the user to be a trainer.
- @require_http_methods(["POST"]) (line 557): Requires the request method to be POST.
- @trainer_required (line 558): Requires the user to be a trainer.
- @require_GET (line 603): Requires the request method to be GET.
- @trainer_required (line 604): Requires the user to be a trainer.
- @trainer_required (line 651): Requires the user to be a trainer.
- @trainer_required (line 803): Requires the user to be a trainer.

website/corporate_views.py:
- @login_required (line 25): Requires the user to be logged in.
- @super_admin_or_staff (line 26): Requires the user to be a super admin or staff member.
- @login_required (line 56): Requires the user to be logged in.
- @super_admin_or_staff (line 57): Requires the user to be a super admin or staff member.
- @login_required (line 126): Requires the user to be logged in.
- @super_admin_or_staff (line 127): Requires the user to be a super admin or staff member.
- @super_admin_or_staff (line 156): Requires the user to be a super admin or staff member.
- @require_http_methods(["GET"]) (line 157): Requires the request method to be GET.
- @staff_member_required (line 197): Requires the user to be logged in and a staff member.
- @require_http_methods(["GET"]) (line 198): Requires the request method to be GET.
- @staff_member_required (line 234): Requires the user to be logged in and a staff member.
- @require_http_methods(["POST"]) (line 235): Requires the request method to be POST.
- @transaction.atomic (line 236): Ensures the view runs in a database transaction.
- @staff_member_required (line 365): Requires the user to be logged in and a staff member.
- @require_http_methods(["PUT"]) (line 366): Requires the request method to be PUT.
- @transaction.atomic (line 367): Ensures the view runs in a database transaction.
- @staff_member_required (line 454): Requires the user to be logged in and a staff member.
- @require_http_methods(["DELETE"]) (line 455): Requires the request method to be DELETE.
- @transaction.atomic (line 456): Ensures the view runs in a database transaction.
- @staff_member_required (line 497): Requires the user to be logged in and a staff member.
- @require_http_methods(["GET"]) (line 498): Requires the request method to be GET.
- @staff_member_required (line 522): Requires the user to be logged in and a staff member.
- @require_http_methods(["POST"]) (line 523): Requires the request method to be POST.
- @transaction.atomic (line 524): Ensures the view runs in a database transaction.
- @staff_member_required (line 623): Requires the user to be logged in and a staff member.
- @require_http_methods(["POST"]) (line 624): Requires the request method to be POST.
- @transaction.atomic (line 625): Ensures the view runs in a database transaction.
- @login_required (line 729): Requires the user to be logged in.
- @login_required (line 806): Requires the user to be logged in.
- @staff_member_required (line 807): Requires the user to be logged in and a staff member.
- @login_required (line 832): Requires the user to be logged in.
- @staff_member_required (line 833): Requires the user to be logged in and a staff member.
- @require_http_methods(["POST"]) (line 834): Requires the request method to be POST.
- @login_required (line 958): Requires the user to be logged in.
- @staff_member_required (line 959): Requires the user to be logged in and a staff member.
- @require_http_methods(["GET"]) (line 960): Requires the request method to be GET.
- @login_required (line 1021): Requires the user to be logged in.
- @login_required (line 1077): Requires the user to be logged in.
- @login_required (line 1233): Requires the user to be logged in.
- @login_required (line 1248): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 1249): Requires the request method to be GET.
- @login_required (line 1296): Requires the user to be logged in.
- @login_required (line 1316): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 1317): Requires the request method to be GET.
- @login_required (line 1352): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 1353): Requires the request method to be POST.
- @transaction.atomic (line 1354): Ensures the view runs in a database transaction.
- @login_required (line 1511): Requires the user to be logged in.
- @require_http_methods(["DELETE"]) (line 1512): Requires the request method to be DELETE.
- @transaction.atomic (line 1513): Ensures the view runs in a database transaction.
- @login_required (line 1553): Requires the user to be logged in.
- @login_required (line 1648): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 1649): Requires the request method to be GET.
- @login_required (line 1694): Requires the user to be logged in.
- @login_required (line 1819): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 1820): Requires the request method to be GET.
- @login_required (line 1908): Requires the user to be logged in.
- @require_http_methods(["GET", "POST"]) (line 1909): Requires the request method to be GET or POST.
- @login_required (line 2021): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 2022): Requires the request method to be GET.
- @login_required (line 2203): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 2204): Requires the request method to be POST.
- @transaction.atomic (line 2205): Ensures the view runs in a database transaction.
- @login_required (line 2303): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 2304): Requires the request method to be GET.
- @login_required (line 2386): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 2387): Requires the request method to be GET.
- @login_required (line 2423): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 2424): Requires the request method to be POST.
- @transaction.atomic (line 2425): Ensures the view runs in a database transaction.
- @login_required (line 2549): Requires the user to be logged in.
- @login_required (line 2604): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 2605): Requires the request method to be POST.
- @transaction.atomic (line 2606): Ensures the view runs in a database transaction.
- @login_required (line 2656): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 2657): Requires the request method to be POST.
- @transaction.atomic (line 2658): Ensures the view runs in a database transaction.
- @login_required (line 2716): Requires the user to be logged in.
- @login_required (line 2774): Requires the user to be logged in.
- @login_required (line 2879): Requires the user to be logged in.
- @login_required (line 2933): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 2934): Requires the request method to be GET.
- @login_required (line 3008): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 3009): Requires the request method to be GET.
- @login_required (line 3104): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 3105): Requires the request method to be GET.
- @login_required (line 3286): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 3287): Requires the request method to be GET.
- @login_required (line 3350): Requires the user to be logged in.
- @login_required (line 3379): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 3427): Requires the request method to be POST.
- @login_required (line 3495): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 3496): Requires the request method to be GET.
- @login_required (line 3563): Requires the user to be logged in.
- @login_required (line 3595): Requires the user to be logged in.
- @login_required (line 3659): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 3660): Requires the request method to be POST.

website/individual_views.py:
- @login_required (line 17): Requires the user to be logged in.
- @login_required (line 48): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 49): Requires the request method to be GET.
- @login_required (line 83): Requires the user to be logged in.
- @require_http_methods(["GET"]) (line 84): Requires the request method to be GET.
- @login_required (line 117): Requires the user to be logged in.
- @login_required (line 149): Requires the user to be logged in.
- @login_required (line 306): Requires the user to be logged in.
- @require_http_methods(["POST"]) (line 307): Requires the request method to be POST.
- @login_required (line 428): Requires the user to be logged in.