from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db import models
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.core.exceptions import ValidationError
import logging
import uuid
from django.apps import apps
from django.db.models import Q

# Create your models here.

class UserType(models.Model):
    code = models.CharField(_('Code'), max_length=50, unique=True)
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('User Type')
        verbose_name_plural = _('User Types')

class CustomUserManager(BaseUserManager):
    def create_user(self, email, username, password=None, **extra_fields):
        if not email:
            raise ValueError(_('The Email field must be set'))
        if not username:
            raise ValueError(_('The Username field must be set'))
        
        email = self.normalize_email(email)
        user = self.model(email=email, username=username, **extra_fields)
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, username, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        extra_fields.setdefault('is_active', True)
        return self.create_user(email, username, password, **extra_fields)

class CustomUser(AbstractUser):
    email = models.EmailField(_('Email'), unique=True)
    username = models.CharField(_('Username'), max_length=150, unique=True)
    user_type = models.ForeignKey(
        UserType,
        verbose_name=_('User Type'),
        on_delete=models.PROTECT,
        null=True
    )
    
    # Common fields for all user types
    phone_number = models.CharField(_('Phone Number'), max_length=20, blank=True)
    nationality = models.CharField(_('Nationality'), max_length=100, blank=True)
    passport_number = models.CharField(_('Passport Number'), max_length=50, blank=True)
    national_id = models.CharField(_('National ID'), max_length=50, blank=True)
    address = models.TextField(_('Address'), blank=True)
    date_of_birth = models.DateField(_('Date of Birth'), null=True, blank=True)
    
    # External Corporate specific fields
    company_name = models.CharField(_('Company Name'), max_length=200, blank=True)
    commercial_registration_number = models.CharField(_('Commercial Registration Number'), max_length=100, blank=True)
    tax_registration_number = models.CharField(_('Tax Registration Number'), max_length=100, blank=True)
    key_person_name = models.CharField(_('Key Person Name'), max_length=200, blank=True)
    key_person_phone = models.CharField(_('Key Person Phone'), max_length=20, blank=True)
    key_person_email = models.EmailField(_('Key Person Email'), blank=True)
    
    # Internal GB Corp specific fields
    employee_id = models.CharField(_('Employee ID'), max_length=50, blank=True)
    gb_employee = models.OneToOneField(
        'GB_Employee',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='user_profile',
        verbose_name=_('GB Employee Profile'),
        help_text=_('Link to Oracle employee data for internal GB employees')
    )
    
    # Oracle integration fields (cached from GB_Employee for faster access)
    oracle_department = models.CharField(_('Oracle Department'), max_length=200, blank=True)
    oracle_position = models.CharField(_('Oracle Position'), max_length=300, blank=True)
    oracle_supervisor_id = models.CharField(_('Oracle Supervisor ID'), max_length=50, blank=True)
    oracle_hire_date = models.DateTimeField(_('Oracle Hire Date'), null=True, blank=True)
    oracle_grade_name = models.CharField(_('Oracle Grade'), max_length=100, blank=True)
    
    # Profile status
    is_profile_complete = models.BooleanField(_('Is Profile Complete'), default=False)
    force_password_reset = models.BooleanField(_('Force Password Reset'), default=False)
    account_status = models.CharField(
        _('Account Status'),
        max_length=20,
        choices=[
            ('ACTIVE', _('Active')),
            ('INACTIVE', _('Inactive')),
            ('PENDING', _('Pending Approval'))
        ],
        default='PENDING'
    )

    objects = CustomUserManager()

    groups = models.ManyToManyField(
        'auth.Group',
        related_name='custom_user_set',
        blank=True,
        verbose_name=_('Groups'),
        help_text=_('The groups this user belongs to. A user will get all permissions granted to each of their groups.'),
    )
    user_permissions = models.ManyToManyField(
        'auth.Permission',
        related_name='custom_user_set',
        blank=True,
        verbose_name=_('User Permissions'),
        help_text=_('Specific permissions for this user.'),
    )

    class Meta:
        verbose_name = _('User')
        verbose_name_plural = _('Users')

    def sync_oracle_data(self):
        """
        Sync Oracle employee data to CustomUser fields for faster access
        """
        if self.gb_employee:
            self.oracle_department = self.gb_employee.department
            self.oracle_position = self.gb_employee.position_name
            self.oracle_supervisor_id = self.gb_employee.supervisor_id
            self.oracle_hire_date = self.gb_employee.hire_date
            self.oracle_grade_name = self.gb_employee.grade_name
            
            # Update employee_id if not set
            if not self.employee_id:
                self.employee_id = self.gb_employee.employee_number
                
            # Update email if not set or if Oracle email is different
            if self.gb_employee.oracle_email_address and not self.email:
                self.email = self.gb_employee.oracle_email_address
                
            # Update full name fields if available
            if self.gb_employee.english_name:
                name_parts = self.gb_employee.english_name.split(' ', 1)
                if len(name_parts) >= 2:
                    self.first_name = name_parts[0]
                    self.last_name = name_parts[1]
                elif len(name_parts) == 1:
                    self.first_name = name_parts[0]
            
            self.save(update_fields=[
                'oracle_department', 'oracle_position', 'oracle_supervisor_id',
                'oracle_hire_date', 'oracle_grade_name', 'employee_id',
                'email', 'first_name', 'last_name'
            ])

    def __str__(self):
        return self.username

class GB_Employee(models.Model):
    """
    Model to store employee data from Oracle integration for GB internal employees
    """
    employee_number = models.CharField(_('Employee Number'), max_length=50, unique=True, primary_key=True)
    full_name = models.CharField(_('Full Name'), max_length=500, blank=True)
    arabic_name = models.CharField(_('Arabic Name'), max_length=250, blank=True)
    english_name = models.CharField(_('English Name'), max_length=250, blank=True)
    oracle_username = models.CharField(_('Oracle Username'), max_length=100, blank=True)
    department = models.CharField(_('Department'), max_length=200, blank=True)
    position_name = models.CharField(_('Position Name'), max_length=300, blank=True)
    organization_id = models.CharField(_('Organization ID'), max_length=50, blank=True)
    supervisor_id = models.CharField(_('Supervisor ID'), max_length=50, blank=True)
    supervisor_user_name = models.CharField(_('Supervisor User Name'), max_length=500, blank=True)
    hire_date = models.DateTimeField(_('Hire Date'), null=True, blank=True)
    payroll_id = models.CharField(_('Payroll ID'), max_length=50, blank=True)
    grade_name = models.CharField(_('Grade Name'), max_length=100, blank=True)
    grade_id = models.CharField(_('Grade ID'), max_length=50, blank=True)
    oracle_phone_number = models.CharField(_('Oracle Phone Number'), max_length=20, blank=True, null=True)
    oracle_email_address = models.EmailField(_('Oracle Email Address'), blank=True)
    
    # Additional optional fields
    head_department = models.CharField(_('Head Department Employee ID'), max_length=50, blank=True, null=True)
    cost_center = models.IntegerField(_('Cost Center'), blank=True, null=True)
    
    # Sync metadata
    last_sync_date = models.DateTimeField(_('Last Sync Date'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    @classmethod
    def create_or_update_from_oracle(cls, oracle_data):
        """
        Create or update GB_Employee record from Oracle integration data
        
        Args:
            oracle_data (dict): Dictionary containing Oracle employee data
            
        Returns:
            GB_Employee: The created or updated employee record
        """
        employee_number = oracle_data.get('EMPLOYEE_NUMBER')
        if not employee_number:
            raise ValueError("Employee number is required")
            
        # Parse hire date if provided
        hire_date = None
        if oracle_data.get('HIRE_DATE'):
            from django.utils.dateparse import parse_datetime
            hire_date = parse_datetime(oracle_data['HIRE_DATE'])
            
        defaults = {
            'full_name': oracle_data.get('FULL_NAME', ''),
            'arabic_name': oracle_data.get('ARABIC_NAME', ''),
            'english_name': oracle_data.get('ENGLISH_NAME', ''),
            'oracle_username': oracle_data.get('USER_NAME', ''),
            'department': oracle_data.get('DEPARTMENT', ''),
            'position_name': oracle_data.get('POSITION_NAME', ''),
            'organization_id': oracle_data.get('ORGANIZATION_ID', ''),
            'supervisor_id': oracle_data.get('SUPERVISOR_ID', ''),
            'supervisor_user_name': oracle_data.get('SUPERVISOR_USER_NAME', ''),
            'hire_date': hire_date,
            'payroll_id': oracle_data.get('PAYROLL_ID', ''),
            'grade_name': oracle_data.get('GRADE_NAME', ''),
            'grade_id': oracle_data.get('GRADE_ID', ''),
            'oracle_phone_number': oracle_data.get('PHONE_NUMBER'),
            'oracle_email_address': oracle_data.get('EMAIL_ADDRESS', ''),
            'head_department': oracle_data.get('HEAD_DEPARTMENT'),
            'cost_center': oracle_data.get('COST_CENTER'),
            'is_active': True
        }
        
        employee, created = cls.objects.update_or_create(
            employee_number=employee_number,
            defaults=defaults
        )
        
        return employee
        
    def get_supervisor(self):
        """
        Get the supervisor GB_Employee record if available
        """
        if self.supervisor_id:
            try:
                return GB_Employee.objects.get(employee_number=self.supervisor_id)
            except GB_Employee.DoesNotExist:
                return None
        return None
        
    def get_subordinates(self):
        """
        Get all employees who report to this employee
        """
        return GB_Employee.objects.filter(supervisor_id=self.employee_number, is_active=True)

    def __str__(self):
        return f"{self.employee_number} - {self.english_name or self.full_name}"

    class Meta:
        verbose_name = _('GB Employee')
        verbose_name_plural = _('GB Employees')
        ordering = ['employee_number']
        indexes = [
            models.Index(fields=['employee_number']),
            models.Index(fields=['oracle_email_address']),
            models.Index(fields=['oracle_username']),
            models.Index(fields=['supervisor_id']),
            models.Index(fields=['organization_id']),
        ]

class GBEmployeeRequest(models.Model):
    """
    Model to track course enrollment requests from GB employees that need supervisor approval
    """
    STATUS_CHOICES = [
        ('PENDING', _('Pending Approval')),
        ('APPROVED', _('Approved')),
        ('REJECTED', _('Rejected')),
        ('CANCELLED', _('Cancelled')),
    ]
    
    request_id = models.AutoField(primary_key=True)
    employee = models.ForeignKey(
        GB_Employee,
        on_delete=models.CASCADE,
        related_name='enrollment_requests',
        verbose_name=_('Employee')
    )
    supervisor = models.ForeignKey(
        GB_Employee,
        on_delete=models.CASCADE,
        related_name='pending_approvals',
        verbose_name=_('Supervisor')
    )
    course_instance = models.ForeignKey(
        'CourseInstance',
        on_delete=models.CASCADE,
        verbose_name=_('Course Instance')
    )
    user = models.ForeignKey(
        CustomUser,
        on_delete=models.CASCADE,
        related_name='gb_enrollment_requests',
        verbose_name=_('User')
    )
    supervisor_user = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='supervised_enrollment_requests',
        verbose_name=_('Supervisor User Account')
    )
    status = models.CharField(
        _('Status'),
        max_length=20,
        choices=STATUS_CHOICES,
        default='PENDING'
    )
    request_message = models.TextField(
        _('Request Message'),
        blank=True,
        help_text=_('Optional message from employee to supervisor')
    )
    approval_notes = models.TextField(
        _('Approval Notes'),
        blank=True,
        help_text=_('Notes from supervisor regarding the decision')
    )
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    processed_at = models.DateTimeField(_('Processed At'), null=True, blank=True)
    processed_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='processed_gb_requests',
        verbose_name=_('Processed By')
    )
    supervisor_account_created = models.BooleanField(
        _('Supervisor Account Created'),
        default=False,
        help_text=_('Whether the supervisor account was created for this request')
    )
    
    def __str__(self):
        return f"{self.employee.employee_number} → {self.supervisor.employee_number} - {self.course_instance.course.name_en}"
    
    def approve(self, processed_by_user, notes=''):
        """
        Approve the enrollment request and create the reservation
        """
        from django.utils import timezone
        
        self.status = 'APPROVED'
        self.approval_notes = notes
        self.processed_at = timezone.now()
        self.processed_by = processed_by_user
        self.save()
        
        # Create the actual reservation
        reservation, created = Reservation.objects.get_or_create(
            user=self.user,
            course_instance=self.course_instance,
            defaults={'status': 'UPCOMING'}
        )
        
        # Create notification for the employee
        Notification.objects.create(
            user=self.user,
            message=_('Your enrollment request for course "{}" has been approved by your supervisor.').format(
                self.course_instance.course.name_en
            ),
            delivered_at=timezone.now()
        )
        
        return reservation
    
    def reject(self, processed_by_user, notes=''):
        """
        Reject the enrollment request
        """
        from django.utils import timezone
        
        self.status = 'REJECTED'
        self.approval_notes = notes
        self.processed_at = timezone.now()
        self.processed_by = processed_by_user
        self.save()
        
        # Create notification for the employee
        Notification.objects.create(
            user=self.user,
            message=_('Your enrollment request for course "{}" has been rejected by your supervisor. Reason: {}').format(
                self.course_instance.course.name_en,
                notes or _('No reason provided')
            ),
            delivered_at=timezone.now()
        )
    
    class Meta:
        verbose_name = _('GB Employee Request')
        verbose_name_plural = _('GB Employee Requests')
        ordering = ['-created_at']
        unique_together = ['employee', 'course_instance', 'status']  # Prevent duplicate pending requests

class Section(models.Model):
    code = models.CharField(_('Code'), max_length=50, unique=True)
    name_en = models.CharField(_('Name (English)'), max_length=100)
    name_ar = models.CharField(_('Name (Arabic)'), max_length=100)
    description_en = models.TextField(_('Description (English)'), blank=True)
    description_ar = models.TextField(_('Description (Arabic)'), blank=True)
    icon_svg = models.TextField(_('Icon SVG'), blank=True)
    order = models.IntegerField(_('Display Order'), default=0)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return self.name_en

    class Meta:
        verbose_name = _('Section')
        verbose_name_plural = _('Sections')
        ordering = ['order']

class Feature(models.Model):
    section = models.ForeignKey(Section, on_delete=models.CASCADE, related_name='features')
    title_en = models.CharField(_('Title (English)'), max_length=200)
    title_ar = models.CharField(_('Title (Arabic)'), max_length=200)
    description_en = models.TextField(_('Description (English)'), blank=True)
    description_ar = models.TextField(_('Description (Arabic)'), blank=True)
    icon_svg = models.TextField(_('Icon SVG'), blank=True)
    order = models.IntegerField(_('Display Order'), default=0)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return self.title_en

    class Meta:
        verbose_name = _('Feature')
        verbose_name_plural = _('Features')
        ordering = ['section', 'order']

def get_default_room_type():
    RoomType = apps.get_model('website', 'RoomType')
    return RoomType.objects.filter(is_active=True).first().room_type_id if RoomType.objects.filter(is_active=True).exists() else None

class Course(models.Model):
    STATUS_CHOICES = [
        ('ACTIVE', _('Active')),
        ('CANCELLED', _('Cancelled')),
        ('DRAFT', _('Draft')),
        ('ARCHIVED', _('Archived'))
    ]
    
    LOCATION_CHOICES = [
        ('ONLINE', _('Online')),
        ('PHYSICAL', _('Physical')),
        ('HYBRID', _('Hybrid'))
    ]
    
    CATEGORY_CHOICES = [
        ('AUTOMOTIVE', _('Automotive')),
        ('BUSINESS', _('Business Skills')),
        ('TECHNICAL', _('Technical')),
        ('PROFESSIONAL', _('Professional Development'))
    ]

    SESSION_TYPE_CHOICES = [
        ('FULL_DAY', _('Full Day')),
        ('HALF_DAY', _('Half Day')),
        ('CUSTOM', _('Custom'))
    ]
    
    course_id = models.AutoField(primary_key=True)
    name_en = models.CharField(_('Name (English)'), max_length=200)
    name_ar = models.CharField(_('Name (Arabic)'), max_length=200)
    description_en = models.TextField(_('Description (English)'), blank=True, default='')
    description_ar = models.TextField(_('Description (Arabic)'), blank=True, default='')
    
    # Course Details
    category = models.CharField(_('Category'), max_length=50, choices=CATEGORY_CHOICES, default='PROFESSIONAL')
    location = models.CharField(_('Location'), max_length=50, choices=LOCATION_CHOICES, default='PHYSICAL')
    session_type = models.CharField(_('Session Type'), max_length=20, choices=SESSION_TYPE_CHOICES, default='FULL_DAY')
    num_of_sessions = models.PositiveIntegerField(_('Number of Sessions'), default=1)
    capacity = models.PositiveIntegerField(_('Capacity'), default=20)
    prerequisite = models.TextField(_('Prerequisites'), blank=True, default='')
    # Removing room_types field as room types will now be selected per session
    # Store equipment category requirements
    equipment_categories = models.JSONField(
        _('Required Equipment Categories'),
        blank=True,
        null=True,
        default=dict,
        help_text=_('Categories of equipment required for this course with quantities')
    )
    # Store room type requirements for each session
    session_room_types = models.JSONField(
        _('Session Room Types'),
        blank=True,
        null=True,
        default=dict,
        help_text=_('Room type required for each session number (e.g. {"1": 1, "2": 2} where numbers are room_type_id)')
    )

    # Media and Display
    icon_svg = models.TextField(_('Icon SVG'), blank=True, default='')
    image = models.ImageField(_('Image'), upload_to='courses/', blank=True, null=True)
    order = models.IntegerField(_('Display Order'), default=1)
    
    # Status and Timestamps
    status = models.CharField(
        _('Status'), 
        max_length=20, 
        choices=STATUS_CHOICES, 
        default='ACTIVE'
    )
    cancellation_reason = models.TextField(_('Cancellation Reason'), blank=True)
    cancelled_at = models.DateTimeField(_('Cancelled At'), null=True, blank=True)
    cancelled_by = models.ForeignKey(
        CustomUser,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='cancelled_courses'
    )
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
     
    def __str__(self):
        return f"{self.course_id} - {self.name_en}"
        
    class Meta:
        verbose_name = _('Course')
        verbose_name_plural = _('Courses')
        ordering = ['category', 'order']
        
class RoomType(models.Model):
    room_type_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Name'), max_length=100)
    description = models.TextField(_('Description'), blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('Room Type')
        verbose_name_plural = _('Room Types')
        ordering = ['name']

class Room(models.Model):
    room_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Name'), max_length=100, default='Room')
    capacity = models.IntegerField(_('Capacity'))
    room_type = models.ForeignKey(
        RoomType,
        on_delete=models.PROTECT,  # Prevent deletion of room type if rooms are using it
        verbose_name=_('Room Type'),
        related_name='rooms'
    )
    fixed_equipment = models.ManyToManyField(
        'Equipment',
        verbose_name=_('Fixed Equipment'),
        related_name='fixed_in_rooms',
        blank=True,
        help_text=_('Equipment permanently assigned to this room')
    )
    is_active = models.BooleanField(_('Is Active'), default=True)
    description = models.TextField(_('Description'), blank=True)
    
    # Add status field from RoomAvailability
    STATUS_CHOICES = [
        ('BUSY', _('Busy')),
        ('BOOKED', _('Booked')),
        ('READY', _('Ready'))
    ]
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='READY')

    @property
    def availability(self):
        """
        Backward compatibility property to emulate the old availability relationship.
        Templates can continue to use room.availability.status and room.availability.get_status_display
        """
        class AvailabilityCompatWrapper:
            def __init__(self, room):
                self.status = room.status
                self._room = room
            
            def get_status_display(self):
                return dict(Room.STATUS_CHOICES).get(self._room.status, self._room.status)
        
        return AvailabilityCompatWrapper(self)

    def save(self, *args, **kwargs):
        # If this is a new room, create availability record
        is_new = not self.pk
        super().save(*args, **kwargs)
        
        if is_new:
            # Create availability record for new rooms
            RoomAvailability.objects.create(room=self)

    def delete(self, *args, **kwargs):
        # Reset fixed equipment status before deletion
        for equipment in self.fixed_equipment.all():
            try:
                availability = equipment.availability
                availability.status = 'READY'
                availability.save()
            except Exception as e:
                # Log error but continue with deletion
                logger = logging.getLogger(__name__)
                logger.error(f"Error resetting equipment status for {equipment}: {e}")
        
        super().delete(*args, **kwargs)

    def __str__(self):
        return f"{self.name} (ID: {self.room_id})"

    class Meta:
        verbose_name = _('Room')
        verbose_name_plural = _('Rooms')

class RoomAvailability(models.Model):
    BOOKING_TYPE_CHOICES = [
        ('SESSION', _('Session')),
        ('EVENT', _('Event'))
    ]
    
    room_availability_id = models.AutoField(primary_key=True)
    room = models.ForeignKey(
        Room, 
        on_delete=models.CASCADE, 
        related_name='availability_slots'
    )
    start_date = models.DateTimeField(_('Start Date'), null=True, blank=True)
    end_date = models.DateTimeField(_('End Date'), null=True, blank=True)
    booking_id = models.CharField(_('Booking ID'), max_length=50, null=True, blank=True)
    booking_type = models.CharField(_('Booking Type'), max_length=10, choices=BOOKING_TYPE_CHOICES, null=True, blank=True)
    session = models.ForeignKey('Session', on_delete=models.SET_NULL, null=True, blank=True)
    event = models.ForeignKey('Event', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        if self.start_date and self.end_date:
            return f"{self.room.name} - {self.start_date.strftime('%Y-%m-%d %H:%M')} to {self.end_date.strftime('%Y-%m-%d %H:%M')}"
        return f"{self.room.name} - Availability"

    def clean(self):
        # Validate that either session or event is set, not both
        if self.session and self.event:
            raise ValidationError(_('A room availability slot cannot be associated with both a session and an event'))
            
        # Validate that booking_type matches the actual booking
        if self.session and self.booking_type != 'SESSION':
            self.booking_type = 'SESSION'
        elif self.event and self.booking_type != 'EVENT':
            self.booking_type = 'EVENT'
            
        # Validate dates
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValidationError(_('End date must be after start date'))

    def is_available(self, start_time, end_time):
        """Check if room is available for the given time period"""
        # Make times naive for SQLite compatibility
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
            
        # Check for overlapping slots
        overlapping = RoomAvailability.objects.filter(
            room=self.room,
            start_date__lt=end_time,
            end_date__gt=start_time
        ).exists()
        
        return not overlapping

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _('Room Availability')
        verbose_name_plural = _('Room Availabilities')
        indexes = [
            models.Index(fields=['room', 'start_date', 'end_date']),
            models.Index(fields=['booking_type', 'booking_id'])
        ]

class Trainer(models.Model):
    STATUS_CHOICES = [
        ('BUSY', _('Busy')),
        ('BOOKED', _('Booked')),
        ('READY', _('Ready'))
    ]
    
    trainer_id = models.AutoField(primary_key=True)
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE)
    courses = models.ManyToManyField(Course)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='READY')
    is_supervisor = models.BooleanField(_('Is Supervisor'), default=False, help_text=_('Supervisors can assign courses to trainers'))

    def is_available(self, start_time, end_time, exclude_session_id=None):
        """Check if trainer is available for the given time period"""
        # If trainer status is not READY, they're not available
        # Temporarily allowing BUSY trainers to be available for rescheduling
        if self.status not in ['READY', 'BUSY']:
            return False
            
        # Handle timezone awareness for SQLite compatibility
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
            
        # Check if there are any overlapping availability slots
        overlapping_slots_query = self.availability_slots.filter(
            start_date__lt=end_time,
            end_date__gt=start_time
        )
        
        # Exclude the current session if its ID is provided
        if exclude_session_id:
            overlapping_slots_query = overlapping_slots_query.exclude(session__session_id=exclude_session_id)
            
        overlapping_slots_exist = overlapping_slots_query.exists()
        
        return not overlapping_slots_exist

    def __str__(self):
        return f"{self.trainer_id} - {self.user.get_full_name()}"
    
    class Meta:
        verbose_name = _('Trainer')
        verbose_name_plural = _('Trainers')

class TrainerAvailability(models.Model):
    BOOKING_TYPE_CHOICES = [
        ('SESSION', _('Session')),
        ('EVENT', _('Event'))
    ]
    
    trainer_availability_id = models.AutoField(primary_key=True)
    trainer = models.ForeignKey(
        'Trainer', 
        on_delete=models.CASCADE,
        related_name='availability_slots'
    )
    start_date = models.DateTimeField(_('Start Date'), null=True, blank=True)
    end_date = models.DateTimeField(_('End Date'), null=True, blank=True)
    booking_id = models.CharField(_('Booking ID'), max_length=50, null=True, blank=True)
    booking_type = models.CharField(_('Booking Type'), max_length=10, choices=BOOKING_TYPE_CHOICES, null=True, blank=True)
    session = models.ForeignKey('Session', on_delete=models.SET_NULL, null=True, blank=True)
    event = models.ForeignKey('Event', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        return f"{self.trainer.user.get_full_name() if self.trainer else 'No Trainer'} - {self.start_date} to {self.end_date}"

    def clean(self):
        """Validate time slots"""
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValidationError(_('End date must be after start date'))

    @classmethod
    def add_busy_slot(cls, trainer, start_time, end_time, session=None, event=None):
        """Add a new busy time slot for the trainer"""
        logger = logging.getLogger(__name__)
        
        # Ensure naive datetimes for SQLite compatibility
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
            
        # Validate times
        if end_time <= start_time:
            raise ValidationError(_('End time must be after start time'))
            
        # Determine booking type and ID
        booking_type = None
        booking_id = None
        
        if session:
            booking_type = 'SESSION'
            booking_id = session.session_id
        elif event:
            booking_type = 'EVENT'
            booking_id = str(event.event_id)
        
        # Remove any existing slots for this event/session to avoid duplicates
        if session:
            cls.objects.filter(trainer=trainer, session=session).delete()
        if event:
            cls.objects.filter(trainer=trainer, event=event).delete()
            
        # Create new availability record
        availability = cls.objects.create(
            trainer=trainer,
            start_date=start_time,
            end_date=end_time,
            booking_id=booking_id,
            booking_type=booking_type,
            session=session,
            event=event
        )
        
        # Update trainer status to BUSY
        trainer.status = 'BUSY'
        trainer.save(update_fields=['status'])
        logger.info(f"Set trainer {trainer.user.username} status to BUSY")
        
        return availability

    @classmethod
    def remove_busy_slots(cls, trainer=None, session_id=None, event_id=None):
        """Remove busy time slots for the trainer by session ID or event ID"""
        logger = logging.getLogger(__name__)
        
        # Log initial message
        if trainer:
            logger.info(f"Removing busy slots for trainer {trainer.user.username}: session_id={session_id}, event_id={event_id}")
        else:
            logger.info(f"Removing busy slots for ALL trainers: session_id={session_id}, event_id={event_id}")
        
        # Create base queryset
        queryset = cls.objects
        
        # If trainer is specified, filter by trainer
        if trainer:
            queryset = queryset.filter(trainer=trainer)
        
        # Delete matching availability records
        if session_id:
            # Find affected trainers first if trainer is None
            if not trainer:
                affected_trainers = [
                    ta.trainer for ta in queryset.filter(session__session_id=session_id).select_related('trainer')
                ]
                queryset.filter(session__session_id=session_id).delete()
                logger.info(f"Removed session slots for session_id={session_id} affecting {len(affected_trainers)} trainers")
                
                # Update status for each affected trainer
                for tr in affected_trainers:
                    remaining_slots = cls.objects.filter(trainer=tr).exists()
                    if not remaining_slots:
                        tr.status = 'READY'
                        tr.save(update_fields=['status'])
                        logger.info(f"Set trainer {tr.user.username} status to READY as no busy slots remain")
            else:
                # If trainer is specified, just delete their slots and update their status
                queryset.filter(session__session_id=session_id).delete()
                logger.info(f"Removed session slots for session_id={session_id}")
                
                # Update status for the specified trainer
                remaining_slots = cls.objects.filter(trainer=trainer).exists()
                if not remaining_slots:
                    trainer.status = 'READY'
                    trainer.save(update_fields=['status'])
                    logger.info(f"Set trainer {trainer.user.username} status to READY as no busy slots remain")
                
        if event_id:
            # Find affected trainers first if trainer is None
            if not trainer:
                affected_trainers = [
                    ta.trainer for ta in queryset.filter(event__event_id=event_id).select_related('trainer')
                ]
                queryset.filter(event__event_id=event_id).delete()
                logger.info(f"Removed event slots for event_id={event_id} affecting {len(affected_trainers)} trainers")
                
                # Update status for each affected trainer
                for tr in affected_trainers:
                    remaining_slots = cls.objects.filter(trainer=tr).exists()
                    if not remaining_slots:
                        tr.status = 'READY'
                        tr.save(update_fields=['status'])
                        logger.info(f"Set trainer {tr.user.username} status to READY as no busy slots remain")
            else:
                # If trainer is specified, just delete their slots and update their status
                queryset.filter(event__event_id=event_id).delete()
                logger.info(f"Removed event slots for event_id={event_id}")
                
                # Update status for the specified trainer
                remaining_slots = cls.objects.filter(trainer=trainer).exists()
                if not remaining_slots:
                    trainer.status = 'READY'
                    trainer.save(update_fields=['status'])
                    logger.info(f"Set trainer {trainer.user.username} status to READY as no busy slots remain")

    def save(self, *args, **kwargs):
        self.clean()  # Always validate before saving
        super().save(*args, **kwargs)
        
        # Update trainer status only if this is an actual booking (has booking_id)
        # Don't change status for newly created trainer with no booking data
        trainer = self.trainer
        if self.booking_id and trainer:
            trainer.status = 'BUSY'
            trainer.save(update_fields=['status'])

    class Meta:
        verbose_name = _('Trainer Availability')
        verbose_name_plural = _('Trainer Availabilities')

class Corporate(models.Model):
    CATEGORY_CHOICES = [
        ('A', _('Category A')),
        ('B', _('Category B')),
        ('C', _('Category C'))
    ]
    
    corporate_id = models.AutoField(primary_key=True)
    corporate_admin = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    address = models.TextField(_('Address'))
    tax_registration_number = models.CharField(_('Tax Registration Number'), max_length=100)
    legal_name = models.CharField(_('Legal Name'), max_length=200)
    commercial_registration_number = models.CharField(_('Commercial Registration Number'), max_length=100)
    capacity = models.IntegerField(_('Capacity'))
    phone_number = models.CharField(_('Phone Number'), max_length=20)
    category = models.CharField(_('Category'), max_length=1, choices=CATEGORY_CHOICES)

    def __str__(self):
        return self.legal_name

    class Meta:
        verbose_name = _('Corporate')
        verbose_name_plural = _('Corporates')
        

class CorporateAdmin(models.Model):
    """
    Model to specifically track corporate administrators
    """
    admin_id = models.AutoField(primary_key=True)
    user = models.OneToOneField(CustomUser, on_delete=models.CASCADE, related_name='corporate_admin_profile')
    corporate = models.ForeignKey(Corporate, on_delete=models.CASCADE, related_name='admin_details')
    position = models.CharField(_('Position'), max_length=100, blank=True)
    department = models.CharField(_('Department'), max_length=100, blank=True)
    direct_phone = models.CharField(_('Direct Phone'), max_length=20, blank=True)
    alternative_email = models.EmailField(_('Alternative Email'), blank=True)
    notes = models.TextField(_('Notes'), blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return f"{self.user.get_full_name()} - {self.corporate.legal_name}"

    class Meta:
        verbose_name = _('Corporate Admin')
        verbose_name_plural = _('Corporate Admins')
        ordering = ['corporate__legal_name', 'user__last_name']

class Session(models.Model):
    LOCATION_CHOICES = [
        ('PHYSICAL', _('Physical')),
        ('ONLINE', _('Online'))
    ]
    
    SLOT_CHOICES = [
        ('MORNING', _('Morning (9:00 AM - 12:00 PM)')),
        ('AFTERNOON', _('Afternoon (1:00 PM - 4:00 PM)')),
        ('FULL_DAY', _('Full Day (Both Slots)')),
        ('CUSTOM', _('Custom Time'))
    ]
    
    session_id = models.CharField(
        _('Session ID'), 
        max_length=50, 
        primary_key=True,
        editable=False
    )
    course = models.ForeignKey(
        Course, 
        on_delete=models.CASCADE, 
        verbose_name=_('Course')
    )
    trainers = models.ManyToManyField(
        Trainer, 
        verbose_name=_('Trainers'),
        related_name='sessions'
    )
    room = models.ForeignKey(
        Room, 
        on_delete=models.CASCADE,
        verbose_name=_('Room'),
        null=True,
        blank=True
    )
    required_equipment = models.ManyToManyField(
        'Equipment',
        verbose_name=_('Required Equipment'),
        blank=True,
        help_text=_('Equipment required for this session')
    )
    start_date = models.DateTimeField(_('Start Date'))
    end_date = models.DateTimeField(_('End Date'))
    slot_type = models.CharField(
        _('Slot Type'),
        max_length=20,
        choices=SLOT_CHOICES,
        default='FULL_DAY'
    )
    location = models.CharField(
        _('Location'), 
        max_length=10, 
        choices=LOCATION_CHOICES, 
        default='PHYSICAL'
    )
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), default=timezone.now)
    updated_at = models.DateTimeField(_('Updated At'), default=timezone.now)

    def clean(self):
        logger = logging.getLogger(__name__)
        
        if self.end_date and self.start_date:
            logger.debug(f"Cleaning session with dates - start: {self.start_date}, end: {self.end_date}")
            
            try:
                # Ensure dates are naive
                start_date = timezone.make_naive(self.start_date) if timezone.is_aware(self.start_date) else self.start_date
                end_date = timezone.make_naive(self.end_date) if timezone.is_aware(self.end_date) else self.end_date
                
                if end_date <= start_date:
                    raise ValidationError(_('End date must be after start date'))
                
                # Store naive times
                self.start_date = start_date
                self.end_date = end_date
                
            except Exception as e:
                logger.error(f"Error processing dates: {e}")
                raise ValidationError(_('Error processing dates. Please check your date/time values.'))
            
            # Check trainer availability
            try:
                logger.debug(f"Checking trainer availability for trainer: {self.trainers.all()}")
                
                # Check for existing sessions with this trainer at the same time
                # Exclude the current session if it's being updated
                existing_trainer_sessions = Session.objects.filter(
                    trainers__in=self.trainers.all(),
                    start_date__lt=self.end_date,
                    end_date__gt=self.start_date
                )
                
                if self.pk:  # If this is an existing session being updated
                    existing_trainer_sessions = existing_trainer_sessions.exclude(session_id=self.session_id)
                
                if existing_trainer_sessions.exists():
                    conflicting_session = existing_trainer_sessions.first()
                    raise ValidationError(_(
                        'Trainer is already booked for another session from {} to {}'
                    ).format(
                        conflicting_session.start_date.strftime('%Y-%m-%d %H:%M'),
                        conflicting_session.end_date.strftime('%Y-%m-%d %H:%M')
                    ))
                
                # Check for overlapping trainer availability slots
                overlapping_slots = TrainerAvailability.objects.filter(
                    trainer__in=self.trainers.all(),
                    start_date__lt=self.end_date,
                    end_date__gt=self.start_date
                )
                
                if self.pk:  # If this is an existing session being updated
                    overlapping_slots = overlapping_slots.exclude(session=self)
                
                if overlapping_slots.exists():
                    conflicting_slot = overlapping_slots.first()
                    raise ValidationError(_(
                        'Trainer is not available during this time period (booked from {} to {})'
                    ).format(
                        conflicting_slot.start_date.strftime('%Y-%m-%d %H:%M'),
                        conflicting_slot.end_date.strftime('%Y-%m-%d %H:%M')
                    ))
                    
            except ValidationError:
                # Re-raise validation errors
                raise
            except Exception as e:
                logger.error(f"Error checking trainer availability: {e}", exc_info=True)
                raise ValidationError(_('Error checking trainer availability: {}').format(str(e)))
            
            # Check room availability for physical sessions
            if self.location == 'PHYSICAL':
                if not self.room:
                    raise ValidationError(_('Room is required for physical sessions'))
                try:
                    logger.debug(f"Checking room availability for room: {self.room}")
                    
                    # Check for existing sessions with this room at the same time
                    # Exclude the current session if it's being updated
                    existing_room_sessions = Session.objects.filter(
                        room=self.room,
                        start_date__lt=self.end_date,
                        end_date__gt=self.start_date
                    )
                    
                    if self.pk:  # If this is an existing session being updated
                        existing_room_sessions = existing_room_sessions.exclude(session_id=self.session_id)
                    
                    if existing_room_sessions.exists():
                        conflicting_session = existing_room_sessions.first()
                        raise ValidationError(_(
                            'Room is already booked for another session from {} to {}'
                        ).format(
                            conflicting_session.start_date.strftime('%Y-%m-%d %H:%M'),
                            conflicting_session.end_date.strftime('%Y-%m-%d %H:%M')
                        ))
                    
                    # Check room availability using the availability model
                    overlapping_slots = RoomAvailability.objects.filter(
                        room=self.room,
                        start_date__lt=self.end_date,
                        end_date__gt=self.start_date
                    )
                    
                    if self.pk:  # If this is an existing session being updated
                        overlapping_slots = overlapping_slots.exclude(session=self)
                    
                    if overlapping_slots.exists():
                        conflicting_slot = overlapping_slots.first()
                        raise ValidationError(_(
                            'Room is not available during this time period (booked from {} to {})'
                        ).format(
                            conflicting_slot.start_date.strftime('%Y-%m-%d %H:%M'),
                            conflicting_slot.end_date.strftime('%Y-%m-%d %H:%M')
                        ))
                    
                except ValidationError:
                    # Re-raise validation errors
                    raise
                except Exception as e:
                    logger.error(f"Error checking room availability: {e}", exc_info=True)
                    raise ValidationError(_('Error checking room availability: {}').format(str(e)))

    def save(self, *args, **kwargs):
        logger = logging.getLogger(__name__)
        
        try:
            logger.debug("Running clean() before save")
            self.clean()  # This will ensure naive datetimes
        except Exception as e:
            logger.error(f"Error in clean(): {e}", exc_info=True)
            raise
        
        if not self.session_id:
            date_str = timezone.now().strftime('%Y%m%d')
            last_session = Session.objects.filter(session_id__startswith=f'S-{date_str}').order_by('-session_id').first()
            
            if last_session:
                last_number = int(last_session.session_id.split('-')[-1])
                new_number = last_number + 1
            else:
                new_number = 1
                
            self.session_id = f'S-{date_str}-{new_number:04d}'
            logger.debug(f"Generated new session_id: {self.session_id}")
        
        # Handle timezone awareness for dates
        start_date = self.start_date
        end_date = self.end_date
        if timezone.is_aware(start_date):
            start_date = timezone.make_naive(start_date)
        if timezone.is_aware(end_date):
            end_date = timezone.make_naive(end_date)
        
        # Handle date changes for existing sessions
        date_changed = False
        if self.pk:  # If this is an existing session being updated
            try:
                # Get original session
                original = Session.objects.get(pk=self.pk)
                
                # Check if dates changed
                if original.start_date != self.start_date or original.end_date != self.end_date:
                    date_changed = True
                    logger.debug(f"Session dates changed for {self.session_id}")
            except Session.DoesNotExist:
                # Must be a new session
                pass
        
        # Save the session first to ensure we have an ID
        is_new = not self.pk
        if not self.pk:
            self.created_at = timezone.make_naive(timezone.now()) if timezone.is_aware(timezone.now()) else timezone.now()
        self.updated_at = timezone.make_naive(timezone.now()) if timezone.is_aware(timezone.now()) else timezone.now()
        
        try:
            logger.debug("Calling super().save()")
            super().save(*args, **kwargs)
            logger.debug("Session saved successfully")
        except Exception as e:
            logger.error(f"Error in super().save(): {e}", exc_info=True)
            raise
        
        # Update trainer availability
        try:
            logger.debug(f"Updating trainer availability for trainer: {self.trainers.all()}")
            
            # Update trainer status based on availability
            for trainer in self.trainers.all():
                trainer.status = 'BUSY'
                trainer.save()
            
            # Create or update trainer availability records
            for trainer in self.trainers.all():
                trainer_availability, created = TrainerAvailability.objects.get_or_create(
                    trainer=trainer,
                    session=self,
                    defaults={
                        'start_date': start_date,
                        'end_date': end_date,
                        'booking_id': self.session_id,
                        'booking_type': 'SESSION'
                    }
                )
                
                if not created:
                    # Update the dates if the record already exists
                    trainer_availability.start_date = start_date
                    trainer_availability.end_date = end_date
                    trainer_availability.save()
                    
                    logger.debug(f"Trainer availability updated successfully for {trainer.user.username}")
        except Exception as e:
            logger.error(f"Error updating trainer availability: {e}", exc_info=True)
            raise ValidationError(_('Error updating trainer availability: {}').format(str(e)))
        
        # Check if we should skip room availability creation
        # This flag is set during session rescheduling to prevent duplicate records
        should_skip_room_availability = hasattr(self, '_skip_room_availability') and self._skip_room_availability
        
        # Update room availability for physical sessions
        if self.location == 'PHYSICAL' and self.room and not should_skip_room_availability:
            try:
                logger.debug(f"Updating room availability for room: {self.room}")
                
                # Update room status
                self.room.status = 'BUSY'
                self.room.save()
                
                # First check if we already have a room availability record that might be a duplicate
                existing_records = RoomAvailability.objects.filter(
                    room=self.room,
                    session__session_id=self.session_id
                )
                
                if existing_records.count() > 1:
                    # If we have multiple records, keep only the first one and delete the rest
                    logger.warning(f"Found {existing_records.count()} room availability records for session {self.session_id}. Cleaning up duplicates.")
                    first_record = existing_records.first()
                    existing_records.exclude(pk=first_record.pk).delete()
                    logger.info(f"Deleted {existing_records.count() - 1} duplicate room availability records")
                    
                    # Update the remaining record
                    first_record.start_date = start_date
                    first_record.end_date = end_date
                    first_record.save()
                    logger.info(f"Updated remaining room availability record")
                else:
                    # Normal flow - create or update a single record
                    room_availability, created = RoomAvailability.objects.get_or_create(
                        room=self.room,
                        session=self,
                        defaults={
                            'start_date': start_date,
                            'end_date': end_date,
                            'booking_id': self.session_id,
                            'booking_type': 'SESSION'
                        }
                    )
                    
                    if not created:
                        # Update the dates if the record already exists
                        room_availability.start_date = start_date
                        room_availability.end_date = end_date
                        room_availability.save()
                        
                        logger.debug("Room availability updated successfully")
                    else:
                        logger.debug("New room availability record created")
                
            except Exception as e:
                logger.error(f"Error updating room availability: {e}", exc_info=True)
                raise ValidationError(_('Error updating room availability: {}').format(str(e)))
        elif should_skip_room_availability:
            logger.info(f"Skipping automatic room availability creation for session {self.session_id} during rescheduling")
            
        # Update CourseInstance dates if this session's dates changed
        if self.pk and date_changed:
            try:
                from django.db.models import Min, Max
                
                # Find all CourseInstances this session belongs to
                course_instances = CourseInstance.objects.filter(sessions=self)
                
                for instance in course_instances:
                    # Update the start and end dates based on all sessions
                    date_range = instance.sessions.aggregate(
                        earliest=Min('start_date'),
                        latest=Max('end_date')
                    )
                    
                    # Update instance dates if they've changed
                    if instance.start_date != date_range['earliest'] or instance.end_date != date_range['latest']:
                        instance.start_date = date_range['earliest']
                        instance.end_date = date_range['latest']
                        instance.save()
                        logger.debug(f"Updated CourseInstance {instance.instance_id} dates after session update")
            except Exception as e:
                logger.error(f"Error updating CourseInstance after session save: {e}", exc_info=True)
                # Continue without failing - the session is already saved

    def delete(self, *args, **kwargs):
        logger = logging.getLogger(__name__)
        
        # Reset trainer availability
        try:
            logger.debug(f"Removing trainer availability for session: {self.session_id}")
            
            # Delete trainer availability records associated with this session
            TrainerAvailability.objects.filter(session=self).delete()
            
            # Reset trainer status if no other active sessions
            trainer_has_sessions = TrainerAvailability.objects.filter(
                trainer__in=self.trainers.all()
            ).exclude(session=self).exists()
            
            if not trainer_has_sessions:
                for trainer in self.trainers.all():
                    trainer.status = 'READY'
                    trainer.save()
                logger.debug(f"Reset trainer status to READY as no other sessions exist")
            
            logger.debug("Trainer availability reset successfully")
        except Exception as e:
            logger.error(f"Error resetting trainer availability: {e}", exc_info=True)
        
        # Reset room availability
        if self.room:
            try:
                # Delete room availability records associated with this session
                RoomAvailability.objects.filter(session=self).delete()
                
                # Reset room status if no other active sessions
                room_has_sessions = RoomAvailability.objects.filter(
                    room=self.room
                ).exclude(session=self).exists()
                
                if not room_has_sessions:
                    self.room.status = 'READY'
                    self.room.save()
                    logger.debug(f"Reset room status to READY as no other sessions exist")
                
                logger.debug(f"Room availability reset for session: {self.session_id}")
            except Exception as e:
                logger.error(f"Error resetting room availability: {e}", exc_info=True)
        
        # Reset equipment availability
        for equipment in self.required_equipment.all():
            try:
                EquipmentAvailability.remove_busy_slots(
                    equipment=equipment,
                    session_id=self.session_id
                )
                logger.debug(f"Removed busy slot for equipment {equipment.code} from session {self.session_id}")
            except Exception as e:
                logger.error(f"Error removing busy slot for equipment {equipment.code}: {e}", exc_info=True)
        
        # Update CourseInstance dates if this session is part of any
        try:
            from django.db.models import Min, Max
            course_instances = CourseInstance.objects.filter(sessions=self)
            
            # Store instances to update after deleting this session
            instances_to_update = list(course_instances)
            
            # Proceed with session deletion
            super().delete(*args, **kwargs)
            
            # Now update each course instance's dates
            for instance in instances_to_update:
                remaining_sessions = instance.sessions.all()
                
                if remaining_sessions.exists():
                    # Update the start and end dates based on remaining sessions
                    date_range = remaining_sessions.aggregate(
                        earliest=Min('start_date'),
                        latest=Max('end_date')
                    )
                    
                    instance.start_date = date_range['earliest']
                    instance.end_date = date_range['latest']
                    instance.save()
                    logger.debug(f"Updated CourseInstance {instance.instance_id} dates after session deletion")
                else:
                    # If no sessions left, either delete the instance or mark as inactive
                    instance.is_active = False
                    instance.save()
                    logger.debug(f"Marked CourseInstance {instance.instance_id} as inactive (no sessions)")
            
            return  # Already called super().delete()
        except Exception as e:
            logger.error(f"Error updating CourseInstance after session deletion: {e}", exc_info=True)
        
        # If the CourseInstance update failed, still complete the session deletion
        super().delete(*args, **kwargs)

    def __str__(self):
        return f"{self.session_id} - {self.course.name_en} - {self.trainers.all()}"

    class Meta:
        verbose_name = _('Session')
        verbose_name_plural = _('Sessions')
        ordering = ['-created_at']

class Attendance(models.Model):
    attendance_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    session = models.ForeignKey(Session, on_delete=models.CASCADE)
    trainer = models.ForeignKey(Trainer, on_delete=models.SET_NULL, null=True, blank=True, related_name='recorded_attendances')
    first_half = models.BooleanField(_('First Half'))
    second_half = models.BooleanField(_('Second Half'))
    
    def __str__(self):
        return f"{self.user.username} - {self.session.course.name_en} - {self.session.trainers.all()} - {self.session.room.room_id}"

    class Meta:
        verbose_name = _('Attendance')
        verbose_name_plural = _('Attendances')

class Role(models.Model):
    role_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Role Name'), max_length=100)

    def __str__(self):
        return self.name
    
    class Meta:
        verbose_name = _('Role')
        verbose_name_plural = _('Roles')


class Form(models.Model):
    FORM_TYPE_CHOICES = [
        ('EXAM', _('Exam')),
        ('QUIZ', _('Quiz')),
        ('REVIEW', _('Review')),
        ('ASSESSMENT', _('Assessment'))
    ]
    
    form_id = models.AutoField(primary_key=True)
    type = models.CharField(_('Type'), max_length=20, choices=FORM_TYPE_CHOICES)
    content = models.ForeignKey('FormContent', on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE)

    def __str__(self):
        return f"{self.course.name_en} - {self.type}"

    class Meta:
        verbose_name = _('Form')
        verbose_name_plural = _('Forms')


class FormContent(models.Model):
    CONTENT_TYPE_CHOICES = [
        ('TEXT', _('Text')),
        ('FILE', _('File')),
        ('LINK', _('Link'))
    ]
    
    content_id = models.AutoField(primary_key=True)
    content = models.TextField(_('Content'))
    content_type = models.CharField(_('Content Type'), max_length=20, choices=CONTENT_TYPE_CHOICES)

    def __str__(self):
        return f"Content {self.content_id}: {self.content_type}"

    class Meta:
        verbose_name = _('Form Content')
        verbose_name_plural = _('Form Contents')


class EventType(models.Model):
    event_type_id = models.AutoField(primary_key=True)
    type = models.CharField(_('Type'), max_length=100)

    def __str__(self):
        return self.type

    class Meta:
        verbose_name = _('Event Type')
        verbose_name_plural = _('Event Types')

class Event(models.Model):
    event_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(_('Name'), max_length=200, default='Untitled Event')
    description = models.TextField(_('Description'), blank=True)
    capacity = models.PositiveIntegerField(_('Capacity'), default=0)
    event_type = models.ForeignKey(
        EventType,
        on_delete=models.PROTECT,
        verbose_name=_('Event Type'),
        null=True  # Temporarily allow null for existing records
    )
    room = models.ForeignKey(
        'Room',
        on_delete=models.PROTECT,
        verbose_name=_('Room'),
        null=True,
        blank=True,
        related_name='events'
    )
    # room_type field is kept for backward compatibility but no longer used in the UI
    # room_type = models.ForeignKey(
    #     'RoomType',
    #     on_delete=models.PROTECT,
    #     verbose_name=_('Required Room Type'),
    #     help_text=_('The type of room required for this event'),
    #     null=True,
    #     blank=True
    # )
    required_equipment = models.ManyToManyField(
        'Equipment',
        verbose_name=_('Required Equipment'),
        blank=True,
        help_text=_('Equipment required for this event')
    )
    # Date and time fields
    start_time = models.DateTimeField(_('Start Time'), null=True, blank=True)
    end_time = models.DateTimeField(_('End Time'), null=True, blank=True)
    created_at = models.DateTimeField(_('Created At'), default=timezone.now)
    updated_at = models.DateTimeField(_('Updated At'), default=timezone.now)
    is_active = models.BooleanField(_('Is Active'), default=True)
    
    def clean(self):
        """Validate the event data"""
        logger = logging.getLogger(__name__)
        
        if self.start_time and self.end_time and self.start_time >= self.end_time:
            raise ValidationError(_("End time must be after start time"))
        
        if self.room and self.start_time and self.end_time:
            logger.debug(f"Checking room availability for room: {self.room}")
            
            try:
                # Handle timezone awareness for dates
                start_time = self.start_time
                end_time = self.end_time
                if timezone.is_aware(start_time):
                    start_time = timezone.make_naive(start_time)
                if timezone.is_aware(end_time):
                    end_time = timezone.make_naive(end_time)
                
                # Check for overlapping room availability records
                overlapping_slots = RoomAvailability.objects.filter(
                    room=self.room,
                    start_date__lt=end_time,
                    end_date__gt=start_time
                )
                
                # Exclude this event's own availability record if it exists
                if self.pk:
                    overlapping_slots = overlapping_slots.exclude(event=self)
                
                if overlapping_slots.exists():
                    conflict = overlapping_slots.first()
                    conflict_type = "session" if conflict.session else "event"
                    conflict_name = conflict.session.course.name_en if conflict.session else conflict.event.name
                    conflict_start = conflict.start_date.strftime("%Y-%m-%d %H:%M")
                    conflict_end = conflict.end_date.strftime("%Y-%m-%d %H:%M")
                    
                    raise ValidationError(_(
                        "Room is already booked for another {0} ({1}) from {2} to {3}"
                    ).format(conflict_type, conflict_name, conflict_start, conflict_end))
                
                logger.debug("Room availability check passed")
                
            except ValidationError:
                raise
            except Exception as e:
                logger.error(f"Error checking room availability: {e}", exc_info=True)
                raise ValidationError(_("Error checking room availability: {0}").format(str(e)))

    def save(self, *args, **kwargs):
        logger = logging.getLogger(__name__)
        
        # Ensure clean validation is run
        self.clean()
        
        # Set timestamps
        if not self.pk:  # Only set created_at for new instances
            self.created_at = timezone.now()
        self.updated_at = timezone.now()
        
        # Handle timezone awareness for dates
        start_time = self.start_time
        end_time = self.end_time
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
        
        # First save the event
        super().save(*args, **kwargs)
        
        # Check if we should skip room availability creation
        # This flag is set during event rescheduling to prevent duplicate records
        should_skip_room_availability = hasattr(self, '_skip_room_availability') and self._skip_room_availability
        
        # Update room availability if there's a room assigned
        if self.room and not should_skip_room_availability:
            try:
                logger.debug(f"Updating room availability for room: {self.room}")
                
                # Update room status
                self.room.status = 'BUSY'
                self.room.save()
                
                # Create or update room availability record
                room_availability, created = RoomAvailability.objects.get_or_create(
                    room=self.room,
                    event=self,
                    defaults={
                        'start_date': start_time,
                        'end_date': end_time,
                        'booking_id': str(self.event_id),
                        'booking_type': 'EVENT'
                    }
                )
                
                if not created:
                    # Update the dates if the record already exists
                    room_availability.start_date = start_time
                    room_availability.end_date = end_time
                    room_availability.save()
                    logger.debug("Updated existing room availability record")
                else:
                    logger.debug("Created new room availability record")
                
                logger.debug("Room availability updated successfully")
            except Exception as e:
                logger.error(f"Error updating room availability: {e}", exc_info=True)
                raise ValidationError(_('Error updating room availability: {}').format(str(e)))
        elif should_skip_room_availability:
            logger.info(f"Skipping automatic room availability creation for event {self.event_id} during rescheduling")
        
        # Handle equipment availability after saving the event
        if self.pk:  # Only if we have a valid pk
            try:
                for equipment in self.required_equipment.all():
                    try:
                        # Create or update equipment availability record
                        equipment_availability, created = EquipmentAvailability.objects.get_or_create(
                            equipment=equipment,
                            event=self,
                            defaults={
                                'start_date': start_time,
                                'end_date': end_time,
                                'booking_id': str(self.event_id),
                                'booking_type': 'EVENT'
                            }
                        )
                        
                        if not created:
                            # Update the dates if the record already exists
                            equipment_availability.start_date = start_time
                            equipment_availability.end_date = end_time
                            equipment_availability.save()
                        
                        # Update equipment status to BUSY
                        if equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                            equipment.status = 'BUSY'
                            equipment.save(update_fields=['status'])
                            logger.debug(f"Set equipment {equipment.code} status to BUSY")
                        
                        logger.debug(f"Equipment availability updated for {equipment.code}")
                    except Exception as e:
                        logger.error(f"Error updating equipment availability for {equipment.code}: {e}", exc_info=True)
                        # Continue with other equipment even if one fails
                        continue
            except Exception as e:
                logger.error(f"Error updating equipment availabilities: {e}", exc_info=True)

    def delete(self, *args, **kwargs):
        logger = logging.getLogger(__name__)
        
        try:
            # Get the related room availability records
            room_availabilities = RoomAvailability.objects.filter(event=self)
            
            # Clean up room availability records
            for room_availability in room_availabilities:
                room = room_availability.room
                logger.info(f"Deleting room availability for {room.name} and event {self.name}")
                room_availability.delete()
                
                # Check if the room has any other active bookings
                has_other_bookings = RoomAvailability.objects.filter(
                    room=room
                ).exists()
                
                if not has_other_bookings:
                    # If no other bookings, set the room status back to READY
                    room.status = 'READY'
                    room.save()
                    logger.info(f"Room {room.name} status set to READY")
            
            # Handle equipment availability
            for equipment in self.required_equipment.all():
                try:
                    # Delete equipment availability records for this event
                    EquipmentAvailability.objects.filter(
                        equipment=equipment,
                        event=self
                    ).delete()
                    
                    # Check if equipment has any other bookings
                    has_other_bookings = EquipmentAvailability.objects.filter(
                        equipment=equipment
                    ).exists()
                    
                    # If no other bookings and not in special status, set to READY
                    if not has_other_bookings and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                        equipment.status = 'READY'
                        equipment.save(update_fields=['status'])
                        logger.debug(f"Set equipment {equipment.code} status to READY")
                except Exception as e:
                    logger.error(f"Error cleaning up equipment {equipment.code} availability: {e}", exc_info=True)
                    continue
        except Exception as e:
            logger.error(f"Error cleaning up room or equipment availability: {e}", exc_info=True)
        
        # Call the original delete method
        super().delete(*args, **kwargs)

    def __str__(self):
        return f"{self.name} ({self.event_id})"

    class Meta:
        verbose_name = _('Event')
        verbose_name_plural = _('Events')
        ordering = ['-created_at']

    def full_clean(self, exclude=None, validate_unique=True):
        """Custom full_clean method for validating event data"""
        # Run Django's standard validation
        super().full_clean(exclude, validate_unique)
        
        # Apply additional validation from clean method
        logger = logging.getLogger(__name__)
        try:
            self.clean()
        except ValidationError as e:
            logger.error(f"Validation error in Event.full_clean: {e}")
            raise

class Questionnaire(models.Model):
    questionnaire_id = models.AutoField(primary_key=True)
    title = models.CharField(_('Title'), max_length=200 , default='Untitled Questionnaire')
    course = models.ForeignKey(Course, on_delete=models.CASCADE)
    session_number = models.PositiveIntegerField(_('Session Number'), default=1)
    questions = models.JSONField(_('Questions'), blank=True, null=True, default=list)
    comments = models.TextField(_('Comments'), blank=True)
    total_score = models.PositiveIntegerField(_('Total Score'), default=0, help_text=_('Automatically calculated from the sum of all question scores'))
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), default=timezone.now)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    # Temporary field to match database schema
    user_id = models.IntegerField(_('User ID'), null=True, blank=True)
    created_by = models.ForeignKey(
        CustomUser, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_questionnaires',
        verbose_name=_('Created By')
    )

    def __str__(self):
        return f"{self.title} - {self.course.name_en}"

    class Meta:
        verbose_name = _('Questionnaire')
        verbose_name_plural = _('Questionnaires')


class Notification(models.Model):
    notification_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    message = models.TextField(_('Message'))
    status = models.BooleanField(_('Read Status'), default=False)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    delivered_at = models.DateTimeField(_('Delivered At'), null=True, blank=True)

    def __str__(self):
        return f"{self.user.username} - {self.message}"

    class Meta:
        verbose_name = _('Notification')
        verbose_name_plural = _('Notifications')


class Payment(models.Model):
    PAYMENT_METHOD_CHOICES = [
        ('CASH', _('Cash')),
        ('CREDIT_CARD', _('Credit Card')),
        ('BANK_TRANSFER', _('Bank Transfer'))
    ]
    
    PAYMENT_SOURCE_CHOICES = [
        ('INDIVIDUAL', _('Individual')),
        ('EXTERNAL_COMPANY', _('External Company')),
        ('INTERNAL_GB', _('Internal GB'))
    ]
    
    STATUS_CHOICES = [
        ('PENDING', _('Pending')),
        ('COMPLETED', _('Completed')),
        ('FAILED', _('Failed')),
        ('REFUNDED', _('Refunded'))
    ]
    
    payment_id = models.AutoField(primary_key=True)
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    amount = models.DecimalField(_('Amount'), max_digits=10, decimal_places=2)
    payment_method = models.CharField(_('Payment Method'), max_length=20, choices=PAYMENT_METHOD_CHOICES)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES)
    payment_source = models.CharField(_('Payment Source'), max_length=20, choices=PAYMENT_SOURCE_CHOICES)

    def __str__(self):
        return f"{self.user.username} - {self.amount}"

    class Meta:
        verbose_name = _('Payment')
        verbose_name_plural = _('Payments')

class ContactInfo(models.Model):
    INFO_TYPE_CHOICES = [
        ('ADDRESS', _('Address')),
        ('EMAIL', _('Email')),
        ('PHONE', _('Phone')),
        ('HOTLINE', _('Hotline'))
    ]
    
    info_type = models.CharField(_('Information Type'), max_length=50, choices=INFO_TYPE_CHOICES, default='ADDRESS')
    value_en = models.CharField(_('Value (English)'), max_length=500)
    value_ar = models.CharField(_('Value (Arabic)'), max_length=500)
    icon_svg = models.TextField(_('Icon SVG'), blank=True, default='')
    order = models.IntegerField(_('Display Order'), default=0)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return f"{self.get_info_type_display()}: {self.value_en}"

    class Meta:
        verbose_name = _('Contact Information')
        verbose_name_plural = _('Contact Information')
        ordering = ['order']

class Slide(models.Model):
    title_en = models.CharField(_('Title (English)'), max_length=200)
    title_ar = models.CharField(_('Title (Arabic)'), max_length=200)
    description_en = models.TextField(_('Description (English)'), blank=True, default='')
    description_ar = models.TextField(_('Description (Arabic)'), blank=True, default='')
    image = models.ImageField(_('Image'), upload_to='slides/')
    order = models.IntegerField(_('Display Order'), default=0)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return self.title_en

    class Meta:
        verbose_name = _('Slide')
        verbose_name_plural = _('Slides')
        ordering = ['order']

class Category(models.Model):
    category_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Name'), max_length=100, unique=True)
    stock = models.PositiveIntegerField(_('Stock'), default=0)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True)

    def fix_stock(self):
        """Update stock count based on actual number of equipment items"""
        from django.db.models import Count
        actual_count = Equipment.objects.filter(category=self, is_active=True).count()
        if self.stock != actual_count:
            self.stock = actual_count
            self.save(update_fields=['stock'])
            return True
        return False

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('Category')
        verbose_name_plural = _('Categories')
        ordering = ['name']

class Brand(models.Model):
    brand_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Name'), max_length=100, unique=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True)

    def __str__(self):
        return self.name

    class Meta:
        verbose_name = _('Brand')
        verbose_name_plural = _('Brands')
        ordering = ['name']

class Model(models.Model):
    model_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Name'), max_length=100)
    brand = models.ForeignKey(Brand, on_delete=models.CASCADE, related_name='models')
    stock = models.PositiveIntegerField(_('Stock Quantity'), default=0)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True)

    def __str__(self):
        return f"{self.brand.name} - {self.name}"

    def decrease_stock(self):
        if self.stock > 0:
            self.stock -= 1
            self.save()
            return True
        return False

    class Meta:
        verbose_name = _('Model')
        verbose_name_plural = _('Models')
        ordering = ['brand__name', 'name']
        unique_together = ['brand', 'name']  # Model name must be unique per brand

class Equipment(models.Model):
    STATUS_CHOICES = [
        ('AVAILABLE', _('Available')),
        ('BUSY', _('Busy')),
        ('BOOKED', _('Booked')),
        ('READY', _('Ready')),
        ('FIXED', _('Fixed in Room')),
        ('MAINTENANCE', _('Maintenance')),
        ('SCRAP', _('Scrap'))
    ]
    
    equipment_id = models.AutoField(primary_key=True)
    code = models.CharField(_('Equipment Code'), max_length=50, unique=True)
    category = models.ForeignKey(Category, on_delete=models.PROTECT, verbose_name=_('Category'))
    brand = models.ForeignKey(Brand, on_delete=models.PROTECT, verbose_name=_('Brand'))
    model = models.ForeignKey(Model, on_delete=models.PROTECT, verbose_name=_('Model'))
    name = models.CharField(_('Name'), max_length=200)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='READY')
    description = models.TextField(_('Description'), blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True)

    def clean(self):
        # Validate code format
        if self.code and not self.code.replace('-', '').isalnum():
            raise ValidationError({
                'code': _('Equipment code can only contain letters, numbers, and hyphens')
            })
        
        # Check code uniqueness
        if self.code:
            exists = Equipment.objects.filter(code=self.code)
            if self.pk:  # If updating existing equipment
                exists = exists.exclude(pk=self.pk)
            if exists.exists():
                raise ValidationError({
                    'code': _('This equipment code is already in use')
                })

        # Validate that the model belongs to the selected brand
        if self.model and self.brand and self.model.brand != self.brand:
            raise ValidationError({
                'model': _('Selected model does not belong to the selected brand')
            })

    def save(self, *args, **kwargs):
        # Check if this is a new equipment (to update category stock)
        is_new = self.pk is None
        
        # Generate code before saving if it's missing
        if not self.code and is_new:
            # Get the next ID from the sequence
            from django.db import connection
            with connection.cursor() as cursor:
                cursor.execute("SELECT MAX(equipment_id) FROM website_equipment")
                max_id = cursor.fetchone()[0] or 0
                next_id = max_id + 1
            
            # Generate code using predicted next ID
            category_prefix = self.category.name[:4].upper()
            self.code = f"{category_prefix}-{str(next_id).zfill(4)}"
        
        # Run validation
        self.full_clean()
        
        # Save only once
        super().save(*args, **kwargs)
        
        # Update category stock count (ensure accurate count)
        self.category.fix_stock()

    def delete(self, *args, **kwargs):
        # Get category reference before deletion
        category = self.category
        
        # Delete the equipment
        super().delete(*args, **kwargs)
        
        # Update category stock with accurate count
        category.fix_stock()

    def __str__(self):
        return f"{self.code} - {self.name}"

    class Meta:
        verbose_name = _('Equipment')
        verbose_name_plural = _('Equipment')
        ordering = ['code']

class EquipmentAvailability(models.Model):
    BOOKING_TYPE_CHOICES = [
        ('SESSION', _('Session')),
        ('EVENT', _('Event'))
    ]
    
    equipment_availability_id = models.AutoField(primary_key=True)
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, related_name='availability_slots')
    start_date = models.DateTimeField(_('Start Date'), null=True, blank=True)
    end_date = models.DateTimeField(_('End Date'), null=True, blank=True)
    booking_id = models.CharField(_('Booking ID'), max_length=50, null=True, blank=True)
    booking_type = models.CharField(_('Booking Type'), max_length=10, choices=BOOKING_TYPE_CHOICES, null=True, blank=True)
    session = models.ForeignKey('Session', on_delete=models.SET_NULL, null=True, blank=True)
    event = models.ForeignKey('Event', on_delete=models.SET_NULL, null=True, blank=True)

    def __str__(self):
        if self.start_date and self.end_date:
            return f"{self.equipment.code} - {self.start_date.strftime('%Y-%m-%d %H:%M')} to {self.end_date.strftime('%Y-%m-%d %H:%M')}"
        return f"{self.equipment.code} - Availability"

    def clean(self):
        """Validate that either session or event is set, not both, and that dates are valid"""
        # Validate that either session or event is set, not both
        if self.session and self.event:
            raise ValidationError(_('An equipment availability slot cannot be associated with both a session and an event'))
            
        # Validate that booking_type matches the actual booking
        if self.session and self.booking_type != 'SESSION':
            self.booking_type = 'SESSION'
        elif self.event and self.booking_type != 'EVENT':
            self.booking_type = 'EVENT'
            
        # Validate dates
        if self.start_date and self.end_date and self.end_date <= self.start_date:
            raise ValidationError(_('End date must be after start date'))

    @classmethod
    def add_busy_slot(cls, equipment, start_time, end_time, session=None, event=None):
        """Add a new busy time slot for the equipment"""
        logger = logging.getLogger(__name__)
        
        # Ensure naive datetimes for SQLite compatibility
        if timezone.is_aware(start_time):
            start_time = timezone.make_naive(start_time)
        if timezone.is_aware(end_time):
            end_time = timezone.make_naive(end_time)
            
        # Validate times
        if end_time <= start_time:
            raise ValidationError(_('End time must be after start time'))
            
        # Determine booking type and ID
        booking_type = None
        booking_id = None
        
        if session:
            booking_type = 'SESSION'
            booking_id = session.session_id
        elif event:
            booking_type = 'EVENT'
            booking_id = str(event.event_id)
        
        # Remove any existing slots for this event/session to avoid duplicates
        if session:
            cls.objects.filter(equipment=equipment, session=session).delete()
        if event:
            cls.objects.filter(equipment=equipment, event=event).delete()
            
        # Create new availability record
        availability = cls.objects.create(
            equipment=equipment,
            start_date=start_time,
            end_date=end_time,
            booking_id=booking_id,
            booking_type=booking_type,
            session=session,
            event=event
        )
        
        # Update equipment status to BUSY
        if equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
            equipment.status = 'BUSY'
            equipment.save(update_fields=['status'])
            logger.info(f"Set equipment {equipment.code} status to BUSY")
        
        return availability

    @classmethod
    def remove_busy_slots(cls, equipment, session_id=None, event_id=None):
        """Remove busy time slots for the equipment by session ID or event ID"""
        logger = logging.getLogger(__name__)
        
        # Log initial message
        if equipment:
            logger.info(f"Removing busy slots for equipment {equipment.code}: session_id={session_id}, event_id={event_id}")
        else:
            logger.info(f"Removing busy slots for ALL equipment: session_id={session_id}, event_id={event_id}")
        
        # Create base queryset
        queryset = cls.objects
        
        # If equipment is specified, filter by equipment
        if equipment:
            queryset = queryset.filter(equipment=equipment)
        
        # Delete matching availability records
        if session_id:
            # Find affected equipment first if equipment is None
            if not equipment:
                affected_equipment = [
                    ea.equipment for ea in queryset.filter(session__session_id=session_id).select_related('equipment')
                ]
                queryset.filter(session__session_id=session_id).delete()
                logger.info(f"Removed session slots for session_id={session_id} affecting {len(affected_equipment)} equipment")
                
                # Update status for each affected equipment
                for eq in affected_equipment:
                    remaining_slots = cls.objects.filter(equipment=eq).exists()
                    if not remaining_slots and eq.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                        eq.status = 'READY'
                        eq.save(update_fields=['status'])
                        logger.info(f"Set equipment {eq.code} status to READY as no busy slots remain")
            else:
                # If equipment is specified, just delete its slots and update its status
                queryset.filter(session__session_id=session_id).delete()
                logger.info(f"Removed session slots for session_id={session_id}")
                
                # Update status for the specified equipment
                remaining_slots = cls.objects.filter(equipment=equipment).exists()
                if not remaining_slots and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                    equipment.status = 'READY'
                    equipment.save(update_fields=['status'])
                    logger.info(f"Set equipment {equipment.code} status to READY as no busy slots remain")
                
        if event_id:
            # Make sure event_id is a string (in case it's a UUID object)
            event_id_str = str(event_id)
            
            # Find affected equipment first if equipment is None
            if not equipment:
                affected_equipment = [
                    ea.equipment for ea in queryset.filter(event__event_id=event_id_str).select_related('equipment')
                ]
                queryset.filter(event__event_id=event_id_str).delete()
                logger.info(f"Removed event slots for event_id={event_id_str} affecting {len(affected_equipment)} equipment")
                
                # Update status for each affected equipment
                for eq in affected_equipment:
                    remaining_slots = cls.objects.filter(equipment=eq).exists()
                    if not remaining_slots and eq.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                        eq.status = 'READY'
                        eq.save(update_fields=['status'])
                        logger.info(f"Set equipment {eq.code} status to READY as no busy slots remain")
            else:
                # If equipment is specified, just delete its slots and update its status
                queryset.filter(event__event_id=event_id_str).delete()
                logger.info(f"Removed event slots for event_id={event_id_str}")
                
                # Update status for the specified equipment
                remaining_slots = cls.objects.filter(equipment=equipment).exists()
                if not remaining_slots and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
                    equipment.status = 'READY'
                    equipment.save(update_fields=['status'])
                    logger.info(f"Set equipment {equipment.code} status to READY as no busy slots remain")

    def save(self, *args, **kwargs):
        self.clean()  # Always validate before saving
        super().save(*args, **kwargs)
        
        # Update equipment status only if this is an actual booking (has booking_id)
        # Don't change status for newly created equipment with no booking data
        equipment = self.equipment
        if self.booking_id and equipment.status not in ['FIXED', 'MAINTENANCE', 'SCRAP']:
            equipment.status = 'BUSY'
            equipment.save(update_fields=['status'])

    class Meta:
        verbose_name = _('Equipment Availability')
        verbose_name_plural = _('Equipment Availabilities')
        indexes = [
            models.Index(fields=['equipment', 'start_date', 'end_date']),
            models.Index(fields=['booking_type', 'booking_id'])
        ]

class EquipmentMaintenance(models.Model):
    maintenance_id = models.AutoField(primary_key=True)
    equipment = models.ForeignKey(Equipment, on_delete=models.CASCADE, related_name='maintenance_records')
    maintenance_date = models.DateTimeField(_('Maintenance Date'))
    description = models.TextField(_('Maintenance Description'))
    performed_by = models.CharField(_('Performed By'), max_length=200)
    cost = models.DecimalField(_('Cost'), max_digits=10, decimal_places=2, default=0)
    next_maintenance_date = models.DateTimeField(_('Next Maintenance Date'), null=True, blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return f"Maintenance for {self.equipment.code} on {self.maintenance_date}"

    class Meta:
        verbose_name = _('Equipment Maintenance')
        verbose_name_plural = _('Equipment Maintenance Records')
        ordering = ['-maintenance_date']

class CourseInstance(models.Model):
    COURSE_TYPE_CHOICES = [
        ('INDIVIDUAL', _('Individual')),
        ('CORPORATE', _('Corporate')),
        ('GB', _('GB'))
    ]
    
    instance_id = models.AutoField(primary_key=True)
    course = models.ForeignKey(
        Course, 
        on_delete=models.CASCADE, 
        verbose_name=_('Course'),
        related_name='instances'
    )
    sessions = models.ManyToManyField(
        Session,
        verbose_name=_('Sessions'),
        related_name='course_instance'
    )
    batch_id = models.CharField(_('Batch ID'), max_length=100, blank=True, null=True, db_index=True, 
                               help_text=_('Identifier for grouping sessions created in the same batch'))
    start_date = models.DateTimeField(_('Start Date'))
    end_date = models.DateTimeField(_('End Date'))
    capacity = models.PositiveIntegerField(_('Capacity'), default=0, 
                                          help_text=_('Maximum number of students that can enroll in this course instance'))
    course_type = models.CharField(
        _('Course Type'),
        max_length=20,
        choices=COURSE_TYPE_CHOICES,
        default='INDIVIDUAL'
    )
    published = models.BooleanField(
        _('Published'),
        default=False,
        help_text=_('When published, the course instance is visible to users for enrollment. Unpublished instances still reserve time slots.')
    )
    corporate = models.ForeignKey(
        Corporate,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name=_('Corporate'),
        help_text=_('Only required when course type is Corporate')
    )
    cancellation_deadline_hours = models.PositiveIntegerField(
        _('Cancellation Deadline (hours)'),
        default=72,
        help_text=_('Number of hours before course start when students can no longer cancel their reservations. Default is 72 hours (3 days).')
    )
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    deactivation_reason = models.TextField(_('Deactivation Reason'), blank=True, null=True)
    deactivated_at = models.DateTimeField(_('Deactivated At'), blank=True, null=True)
    deactivated_by = models.CharField(_('Deactivated By'), max_length=255, blank=True, null=True)

    @property
    def available_seats(self):
        from django.db.models import Count
        # Count the number of reservations for this instance
        reservation_count = self.reservation_set.filter(
            status__in=['UPCOMING', 'IN_PROGRESS', 'WAITING_LIST']
        ).exclude(
            status__in=['CANCELLED', 'WAITING_TO_PAY']
        ).count()
        # Calculate available seats
        return max(0, self.capacity - reservation_count)
    
    @property
    def waiting_list_count(self):
        """Count the number of users on the waiting list for this instance"""
        return self.reservation_set.filter(status='WAITING_LIST').count()
    
    def get_waiting_list(self):
        """Return the waiting list reservations in order of creation (first come, first served)"""
        return self.reservation_set.filter(status='WAITING_LIST').order_by('created_at')
    
    def process_waiting_list(self):
        """
        Process the waiting list when a spot becomes available.
        When a seat becomes available, ALL users on the waiting list will be moved to WAITING_TO_PAY status.
        Whoever completes payment first will secure the available seat(s).
        Returns True if at least one reservation was promoted, False otherwise.
        """
        if self.available_seats > 0:
            # Get all reservations on the waiting list
            waiting_list = self.get_waiting_list()
            
            if waiting_list.exists():
                # Log information about the process
                logger = logging.getLogger(__name__)
                logger.info(f"Processing waiting list for course instance {self.instance_id}. Available seats: {self.available_seats}, Waiting list count: {waiting_list.count()}")
                
                # Move all waiting list reservations to WAITING_TO_PAY status
                count = 0
                for reservation in waiting_list:
                    reservation.status = 'WAITING_TO_PAY'
                    reservation.save(update_fields=['status'])
                    count += 1
                    
                    # Create a notification for each user
                    Notification.objects.create(
                        user=reservation.user,
                        message=_('A spot has become available in the course: {} ({}). You and others from the waiting list can now pay to secure your spot. The available seat(s) will be assigned to whoever pays first.').format(
                            self.course.name_en,
                            self.start_date.strftime('%Y-%m-%d')
                        ),
                        delivered_at=timezone.now()
                    )
                    
                    logger.info(f"User {reservation.user.username} promoted from waiting list to WAITING_TO_PAY for course instance {self.instance_id}")
                
                # Return True if at least one reservation was promoted
                return count > 0
        
        return False
    
    def __str__(self):
        return f"{self.course.name_en} ({self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')})"

    def clean(self):
        if self.end_date and self.start_date and self.end_date < self.start_date:
            raise ValidationError(_('End date must be after start date'))

    def save(self, *args, **kwargs):
        self.clean()
        # If capacity is not set but course is available, copy from course
        if self.capacity == 0 and hasattr(self, 'course') and self.course:
            self.capacity = self.course.capacity
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = _('Course Instance')
        verbose_name_plural = _('Course Instances')
        ordering = ['-start_date']

    @property
    def has_waiting_list(self):
        """Check if this course instance has any users on the waiting list"""
        return self.reservation_set.filter(status='WAITING_LIST').exists()
    
    @property
    def waiting_list_count(self):
        """Count the number of users on the waiting list for this instance"""
        return self.reservation_set.filter(status='WAITING_LIST').count()

class QuestionnaireResponse(models.Model):
    STATUS_CHOICES = [
        ('PENDING_SUBMISSION', _('Pending Submission')),
        ('COMPLETED_SUBMITTED', _('Completed and Submitted')),
        ('PENDING_TRAINER_REVIEW', _('Pending Trainer Review')),
        ('COMPLETED_TRAINER_REVIEWED', _('Completed and Trainer Reviewed')),
    ]
    
    response_id = models.AutoField(primary_key=True)
    questionnaire = models.ForeignKey(Questionnaire, on_delete=models.CASCADE, related_name='responses')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    course = models.ForeignKey(Course, on_delete=models.CASCADE, related_name='questionnaire_responses')
    session_number = models.PositiveIntegerField(_('Session Number'), default=1)
    # Add the new fields
    course_instance = models.ForeignKey(CourseInstance, on_delete=models.SET_NULL, null=True, blank=True, related_name='questionnaire_responses')
    session = models.ForeignKey(Session, on_delete=models.SET_NULL, null=True, blank=True, related_name='questionnaire_responses')
    question_responses = models.JSONField(_('Question Responses'), default=dict)
    comment = models.TextField(_('Comment'), blank=True, null=True)
    max_total_score = models.PositiveIntegerField(_('Maximum Total Score'), default=0, help_text=_('Maximum possible score for this questionnaire'))
    total_score = models.PositiveIntegerField(_('User Total Score'), default=0, help_text=_('Total score achieved by the user'))
    status = models.CharField(_('Status'), max_length=30, choices=STATUS_CHOICES, default='PENDING_SUBMISSION')
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    completed_at = models.DateTimeField(_('Completed At'), null=True, blank=True)

    def __str__(self):
        return f"Response {self.response_id} from {self.user.username} for {self.questionnaire.title}"

    class Meta:
        verbose_name = _('Questionnaire Response')
        verbose_name_plural = _('Questionnaire Responses')
        unique_together = ['questionnaire', 'user', 'course', 'session_number']

class Reservation(models.Model):
    STATUS_CHOICES = [
        ('WAITING_TO_PAY', _('Waiting to Pay')),
        ('WAITING_LIST', _('Waiting List')),
        ('UPCOMING', _('Upcoming')),
        ('IN_PROGRESS', _('In Progress')),
        ('COMPLETED', _('Completed')),
        ('CANCELLED', _('Cancelled')),
    ]
    
    reservation_id = models.AutoField(primary_key=True)
    course_instance = models.ForeignKey(CourseInstance, on_delete=models.CASCADE, verbose_name=_('Course Instance'), null=True, blank=True)
    session = models.ForeignKey(Session, on_delete=models.CASCADE, null=True, blank=True)  # Keep temporarily for migration
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    event = models.ForeignKey(Event, on_delete=models.SET_NULL, null=True, blank=True)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='WAITING_TO_PAY')
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    cancellation_reason = models.TextField(_('Cancellation Reason'), blank=True, null=True)
    cancelled_at = models.DateTimeField(_('Cancelled At'), null=True, blank=True)
    cancelled_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, related_name='cancelled_reservations')
    completed_at = models.DateTimeField(_('Completed At'), null=True, blank=True)

    def __str__(self):
        if self.course_instance:
            return f"{self.course_instance.course.name_en} - {self.user.username}"
        elif self.session:
            return f"{self.session.course.name_en} - {self.user.username}"
        return f"Reservation {self.reservation_id} - {self.user.username}"
        

    class Meta:
        unique_together = ['course_instance', 'user']

class EmergencyCancellationRequest(models.Model):
    STATUS_CHOICES = [
        ('PENDING', _('Pending Review')),
        ('APPROVED', _('Approved')),
        ('REJECTED', _('Rejected'))
    ]
    
    request_id = models.AutoField(primary_key=True)
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='emergency_cancellations')
    reason = models.TextField(_('Cancellation Reason'), help_text=_('Please explain why you need to cancel after the deadline'))
    attachment = models.FileField(_('Supporting Document'), upload_to='cancellation_requests/', null=True, blank=True,
                                help_text=_('Upload any supporting documents (e.g., medical certificate)'))
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='PENDING')
    admin_notes = models.TextField(_('Admin Notes'), blank=True, help_text=_('Notes for administrative use only'))
    created_at = models.DateTimeField(_('Requested At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Last Updated'), auto_now=True)
    processed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, 
                                    related_name='processed_cancellations')
    
    def __str__(self):
        return f"Emergency Cancellation Request #{self.request_id} - {self.reservation}"
    
    class Meta:
        verbose_name = _('Emergency Cancellation Request')
        verbose_name_plural = _('Emergency Cancellation Requests')
        ordering = ['-created_at']

class CorporateUserCancellationRequest(models.Model):
    STATUS_CHOICES = [
        ('PENDING', _('Pending Review')),
        ('APPROVED', _('Approved')),
        ('REJECTED', _('Rejected'))
    ]
    
    request_id = models.AutoField(primary_key=True)
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='corporate_cancellations')
    reason = models.TextField(_('Cancellation Reason'), help_text=_('Please explain why you need to cancel this reservation'))
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='PENDING')
    admin_notes = models.TextField(_('Admin Notes'), blank=True, help_text=_('Notes from corporate admin'))
    created_at = models.DateTimeField(_('Requested At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Last Updated'), auto_now=True)
    processed_by = models.ForeignKey(CustomUser, on_delete=models.SET_NULL, null=True, blank=True, 
                                    related_name='processed_corporate_cancellations')
    
    def __str__(self):
        return f"Corporate Cancellation Request #{self.request_id} - {self.reservation}"
    
    class Meta:
        verbose_name = _('Corporate User Cancellation Request')
        verbose_name_plural = _('Corporate User Cancellation Requests')
        ordering = ['-created_at']

class Holiday(models.Model):
    holiday_id = models.AutoField(primary_key=True)
    name = models.CharField(_('Holiday Name'), max_length=200)
    description = models.TextField(_('Description'), blank=True)
    date = models.DateField(_('Holiday Date'))
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    is_active = models.BooleanField(_('Is Active'), default=True, null=True, blank=True)
    
    def __str__(self):
        return f"{self.name} ({self.date.strftime('%Y-%m-%d')})"
    
    class Meta:
        verbose_name = _('Holiday')
        verbose_name_plural = _('Holidays')
        ordering = ['-date']

class CourseContent(models.Model):
    CONTENT_TYPE_CHOICES = [
        ('PRESENTATION', _('Presentation')),
        ('PDF', _('PDF Document')),
        ('IMAGE', _('Image')),
        ('VIDEO', _('Video')),
        ('DOCUMENT', _('Document')),
        ('OTHER', _('Other')),
    ]
    
    content_id = models.AutoField(primary_key=True)
    course = models.ForeignKey(
        Course, 
        on_delete=models.CASCADE, 
        related_name='contents',
        verbose_name=_('Course')
    )
    title = models.CharField(_('Title'), max_length=200)
    description = models.TextField(_('Description'), blank=True)
    file = models.FileField(_('File'), upload_to='course_contents/')
    content_type = models.CharField(
        _('Content Type'), 
        max_length=20, 
        choices=CONTENT_TYPE_CHOICES, 
        default='DOCUMENT'
    )
    created_by = models.ForeignKey(
        CustomUser, 
        on_delete=models.SET_NULL, 
        null=True, 
        related_name='created_contents',
        verbose_name=_('Created By')
    )
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    
    def __str__(self):
        return f"{self.title} ({self.get_content_type_display()}) - {self.course.name_en}"
    
    class Meta:
        verbose_name = _('Course Content')
        verbose_name_plural = _('Course Contents')
        ordering = ['-created_at']
        
    def delete(self, *args, **kwargs):
        # Soft delete - set is_active to False instead of actually deleting
        self.is_active = False
        self.save()

class CorporateAdminRequest(models.Model):
    COURSE_TYPE_CHOICES = [
        ('ONLINE', _('Online')),
        ('OFFLINE', _('Offline'))
    ]
    
    STATUS_CHOICES = [
        ('PENDING', _('Pending')),
        ('APPROVED', _('Approved')),
        ('REJECTED', _('Rejected')),
        ('CANCELLED', _('Cancelled')),
        ('PENDING_CANCEL', _('Pending Cancellation')),
    ]
    
    request_id = models.AutoField(primary_key=True)
    corporate = models.ForeignKey(
        Corporate, 
        on_delete=models.CASCADE,
        related_name='course_requests',
        verbose_name=_('Corporate')
    )
    corporate_admin = models.ForeignKey(
        CorporateAdmin, 
        on_delete=models.CASCADE,
        related_name='course_requests',
        verbose_name=_('Corporate Admin')
    )
    course = models.ForeignKey(
        Course,
        on_delete=models.SET_NULL,
        related_name='corporate_requests',
        verbose_name=_('Course'),
        null=True,
        blank=True
    )
    course_type = models.CharField(
        _('Course Type'), 
        max_length=10, 
        choices=COURSE_TYPE_CHOICES, 
        default='OFFLINE'
    )
    capacity = models.PositiveIntegerField(_('Capacity'), default=20)
    notes = models.TextField(_('Notes'), blank=True)
    status = models.CharField(
        _('Status'), 
        max_length=14, 
        choices=STATUS_CHOICES, 
        default='PENDING'
    )
    admin_notes = models.TextField(_('Admin Notes'), blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    
    def __str__(self):
        return f"{self.corporate.legal_name} - {self.get_course_type_display()} - {self.status}"
    
    class Meta:
        verbose_name = _('Corporate Admin Request')
        verbose_name_plural = _('Corporate Admin Requests')
        ordering = ['-created_at']

class CorporateAdminNewCourseRequest(models.Model):
    COURSE_TYPE_CHOICES = [
        ('ONLINE', _('Online')),
        ('OFFLINE', _('Offline'))
    ]
    
    STATUS_CHOICES = [
        ('PENDING', _('Pending')),
        ('APPROVED', _('Approved')),
        ('REJECTED', _('Rejected')),
        ('CANCELLED', _('Cancelled')),
        ('PENDING_CANCEL', _('Pending Cancellation')),
    ]
    
    request_id = models.AutoField(primary_key=True)
    corporate = models.ForeignKey(
        Corporate, 
        on_delete=models.CASCADE,
        related_name='new_course_requests',
        verbose_name=_('Corporate')
    )
    corporate_admin = models.ForeignKey(
        CorporateAdmin, 
        on_delete=models.CASCADE,
        related_name='new_course_requests',
        verbose_name=_('Corporate Admin')
    )
    course_name = models.CharField(_('Course Name'), max_length=200)
    course_description = models.TextField(_('Course Description'), blank=True)
    course_type = models.CharField(
        _('Course Type'), 
        max_length=10, 
        choices=COURSE_TYPE_CHOICES, 
        default='OFFLINE'
    )
    capacity = models.PositiveIntegerField(_('Capacity'), default=20)
    notes = models.TextField(_('Additional Notes'), blank=True)
    status = models.CharField(
        _('Status'), 
        max_length=14, 
        choices=STATUS_CHOICES, 
        default='PENDING'
    )
    admin_notes = models.TextField(_('Admin Notes'), blank=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)
    
    def __str__(self):
        return f"{self.corporate.legal_name} - {self.course_name} - {self.get_status_display()}"
    
    class Meta:
        verbose_name = _('Corporate Admin New Course Request')
        verbose_name_plural = _('Corporate Admin New Course Requests')
        ordering = ['-created_at']

class Survey(models.Model):
    survey_id = models.AutoField(primary_key=True)
    title = models.CharField(_('Title'), max_length=200, help_text=_('The main title of the survey.'))
    course_instance = models.ForeignKey(
        CourseInstance,
        verbose_name=_('Course Instance'),
        on_delete=models.CASCADE,
        related_name='surveys'
    )
    questions = models.JSONField(
        _('Questions'),
        blank=True,
        null=True,
        default=list,
        help_text=_('Survey questions in JSON format')
    )
    start_date = models.DateTimeField(_('Start Date'), null=True, blank=True)
    is_active = models.BooleanField(_('Is Active'), default=True)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    updated_at = models.DateTimeField(_('Updated At'), auto_now=True)

    def __str__(self):
        return self.title

    class Meta:
        verbose_name = _('Survey')
        verbose_name_plural = _('Surveys')


class SurveyResponse(models.Model):
    STATUS_CHOICES = [
        ('PENDING', _('Pending')),
        ('COMPLETED', _('Completed')),
    ]
    
    response_id = models.AutoField(primary_key=True)
    survey = models.ForeignKey(Survey, on_delete=models.CASCADE, related_name='responses')
    user = models.ForeignKey(CustomUser, on_delete=models.CASCADE)
    reservation = models.ForeignKey(Reservation, on_delete=models.CASCADE, related_name='survey_responses')
    question_responses = models.JSONField(_('Question Responses'), default=dict)
    comment = models.TextField(_('Comment'), blank=True, null=True)
    status = models.CharField(_('Status'), max_length=20, choices=STATUS_CHOICES, default='PENDING')
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    completed_at = models.DateTimeField(_('Completed At'), null=True, blank=True)

    def __str__(self):
        return f"Response {self.response_id} from {self.user.username} for {self.survey.title}"

    class Meta:
        verbose_name = _('Survey Response')
        verbose_name_plural = _('Survey Responses')
        unique_together = ['survey', 'user', 'reservation']

# Add this class to the end of the file, after the last existing model

class EmailLog(models.Model):
    """
    Model to track email sending activities for debugging and monitoring.
    This helps identify email delivery issues and track email history.
    """
    recipient = models.EmailField(_('Recipient'))
    subject = models.CharField(_('Subject'), max_length=255)
    cc_emails = models.TextField(_('CC'), blank=True)
    status = models.CharField(_('Status'), max_length=20)  # SUCCESS, FAILED, ERROR
    transaction_id = models.CharField(_('Transaction ID'), max_length=20, blank=True)
    error_message = models.TextField(_('Error Message'), blank=True)
    sent_at = models.DateTimeField(_('Sent At'), auto_now_add=True)
    
    class Meta:
        verbose_name = _('Email Log')
        verbose_name_plural = _('Email Logs')
        ordering = ['-sent_at']
    
    def __str__(self):
        return f"{self.status}: {self.subject} to {self.recipient}"

class OTPVerification(models.Model):
    """
    Model to handle OTP verification for internal GB employees during login.
    OTP expires after 1 minute and allows 3 attempts.
    """
    otp_id = models.AutoField(primary_key=True)
    employee_id = models.CharField(_('Employee ID'), max_length=50)
    email = models.EmailField(_('Email'))
    otp_code = models.CharField(_('OTP Code'), max_length=6)
    attempts = models.PositiveIntegerField(_('Attempts'), default=0)
    max_attempts = models.PositiveIntegerField(_('Max Attempts'), default=3)
    is_used = models.BooleanField(_('Is Used'), default=False)
    is_expired = models.BooleanField(_('Is Expired'), default=False)
    created_at = models.DateTimeField(_('Created At'), auto_now_add=True)
    expires_at = models.DateTimeField(_('Expires At'))
    used_at = models.DateTimeField(_('Used At'), null=True, blank=True)
    
    class Meta:
        verbose_name = _('OTP Verification')
        verbose_name_plural = _('OTP Verifications')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['employee_id', 'created_at']),
            models.Index(fields=['email', 'otp_code']),
        ]
    
    def __str__(self):
        return f"OTP for {self.employee_id} ({self.email}) - {self.get_status_display()}"
    
    def get_status_display(self):
        if self.is_used:
            return _('Used')
        elif self.is_expired or self.is_expired_by_time():
            return _('Expired')
        elif self.attempts >= self.max_attempts:
            return _('Max Attempts Reached')
        else:
            return _('Active')
    
    def is_expired_by_time(self):
        """Check if OTP is expired by time (1 minute)"""
        return timezone.now() > self.expires_at
    
    def is_valid(self):
        """Check if OTP is still valid for use"""
        return (
            not self.is_used and 
            not self.is_expired and 
            not self.is_expired_by_time() and 
            self.attempts < self.max_attempts
        )
    
    def verify(self, provided_otp):
        """
        Verify the provided OTP against this record.
        Returns tuple (success: bool, message: str)
        """
        # Increment attempts
        self.attempts += 1
        
        # Check if max attempts reached
        if self.attempts >= self.max_attempts:
            self.is_expired = True
            self.save()
            return False, _('Maximum attempts reached. Please request a new OTP.')
        
        # Check if OTP is expired by time
        if self.is_expired_by_time():
            self.is_expired = True
            self.save()
            return False, _('OTP has expired. Please request a new one.')
        
        # Check if OTP is already used or manually expired
        if self.is_used or self.is_expired:
            self.save()  # Save the attempt count
            return False, _('OTP is no longer valid. Please request a new one.')
        
        # Verify the OTP code
        if self.otp_code == provided_otp:
            self.is_used = True
            self.used_at = timezone.now()
            self.save()
            return True, _('OTP verified successfully.')
        else:
            self.save()  # Save the attempt count
            remaining_attempts = self.max_attempts - self.attempts
            if remaining_attempts > 0:
                return False, _('Invalid OTP. {} attempts remaining.').format(remaining_attempts)
            else:
                self.is_expired = True
                self.save()
                return False, _('Invalid OTP. Maximum attempts reached. Please request a new OTP.')
    
    def save(self, *args, **kwargs):
        # Set expiration time (1 minute from creation) if not set
        if not self.expires_at:
            self.expires_at = timezone.now() + timezone.timedelta(minutes=1)
        super().save(*args, **kwargs)
    
    @classmethod
    def cleanup_expired(cls):
        """Clean up expired OTP records older than 1 hour"""
        cutoff_time = timezone.now() - timezone.timedelta(hours=1)
        cls.objects.filter(created_at__lt=cutoff_time).delete()
    
    @classmethod
    def create_otp(cls, employee_id, email):
        """
        Create a new OTP for the given employee ID and email.
        Expires any existing active OTPs for this employee.
        """
        import random
        
        # Expire any existing active OTPs for this employee
        cls.objects.filter(
            employee_id=employee_id,
            is_used=False,
            is_expired=False
        ).update(is_expired=True)
        
        # Generate 6-digit OTP
        otp_code = f"{random.randint(100000, 999999)}"
        
        # Create new OTP record
        otp_record = cls.objects.create(
            employee_id=employee_id,
            email=email,
            otp_code=otp_code
        )
        
        return otp_record
