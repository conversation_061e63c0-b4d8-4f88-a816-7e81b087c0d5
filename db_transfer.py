import sqlite3
import os
import sys

def transfer_data():
    # Database paths
    source_db_path = os.path.join('..', 'db.sqlite3')
    target_db_path = 'db.sqlite3'
    
    print(f"Source DB: {source_db_path}")
    print(f"Target DB: {target_db_path}")
    
    # Connect to both databases
    try:
        source_conn = sqlite3.connect(source_db_path)
        source_cursor = source_conn.cursor()
        
        target_conn = sqlite3.connect(target_db_path)
        target_cursor = target_conn.cursor()
        
        # Get all tables from source database
        source_cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in source_cursor.fetchall()]
        
        # Exclude the survey table
        if 'website_survey' in tables:
            tables.remove('website_survey')
        
        # Tables that should be skipped (internal SQLite tables)
        skip_tables = ['sqlite_sequence', 'sqlite_stat1', 'sqlite_stat2', 'sqlite_stat3', 'sqlite_stat4']
        tables = [table for table in tables if table not in skip_tables]
        
        print(f"Found {len(tables)} tables to transfer")
        
        # For each table, transfer data
        for table in tables:
            try:
                print(f"Transferring table: {table}")
                
                # Get table schema
                source_cursor.execute(f"PRAGMA table_info({table})")
                columns = source_cursor.fetchall()
                column_names = [column[1] for column in columns]
                
                # Get data from source table
                source_cursor.execute(f"SELECT * FROM {table}")
                rows = source_cursor.fetchall()
                
                if not rows:
                    print(f"  Table {table} is empty, skipping")
                    continue
                
                print(f"  Found {len(rows)} rows to transfer")
                
                # Check if target table exists
                target_cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
                if not target_cursor.fetchone():
                    print(f"  Table {table} doesn't exist in target database, skipping")
                    continue
                
                # Clear target table first
                target_cursor.execute(f"DELETE FROM {table}")
                
                # Insert data into target table
                placeholders = ', '.join(['?' for _ in column_names])
                columns_str = ', '.join(column_names)
                
                for row in rows:
                    try:
                        target_cursor.execute(f"INSERT INTO {table} ({columns_str}) VALUES ({placeholders})", row)
                    except sqlite3.Error as e:
                        print(f"  Error inserting row into {table}: {e}")
                        continue
                
                target_conn.commit()
                print(f"  Successfully transferred table: {table}")
                
            except sqlite3.Error as e:
                print(f"Error processing table {table}: {e}")
                continue
        
        print("Data transfer completed")
        
    except sqlite3.Error as e:
        print(f"Database error: {e}")
    finally:
        if 'source_conn' in locals():
            source_conn.close()
        if 'target_conn' in locals():
            target_conn.close()

if __name__ == "__main__":
    transfer_data() 