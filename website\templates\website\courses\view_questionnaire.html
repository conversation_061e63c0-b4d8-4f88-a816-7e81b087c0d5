{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "View Questionnaire" %} - {{ questionnaire.title }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-3xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-3">
                <a href="{% url 'website:course_content_list' course.course_id %}" class="text-white/70 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <h1 class="text-2xl font-bold text-white">{% trans "View Questionnaire" %}: {{ questionnaire.title }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'website:edit_questionnaire' course.course_id questionnaire.questionnaire_id %}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                    </svg>
                    {% trans "Edit" %}
                </a>
                <a href="{% url 'website:delete_questionnaire' course.course_id questionnaire.questionnaire_id %}" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    {% trans "Delete" %}
                </a>
            </div>
        </div>

        <!-- Questionnaire Details -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <div class="p-6 border-b border-white/10">
                <h2 class="text-xl font-semibold text-white">{% trans "Questionnaire Details" %}</h2>
                <p class="text-white/70 text-sm mt-1">
                    {% trans "Created by" %}: {{ questionnaire.created_by.get_full_name|default:"-" }} | {% trans "Created on" %}: {{ questionnaire.created_at|date:"M d, Y" }}
                </p>
            </div>

            <div class="p-6 space-y-6">
                <div>
                    <h3 class="text-white font-medium">{% trans "Title" %}</h3>
                    <p class="text-white mt-1 py-2 px-3 bg-white/5 border border-white/10 rounded-md">{{ questionnaire.title }}</p>
                </div>
                
                <div>
                    <h3 class="text-white font-medium">{% trans "Session Number" %}</h3>
                    <p class="text-white mt-1 py-2 px-3 bg-white/5 border border-white/10 rounded-md">{% trans "Session" %} {{ questionnaire.session_number }}</p>
                </div>

                {% if questionnaire.comments %}
                <div>
                    <h3 class="text-white font-medium">{% trans "Comments" %}</h3>
                    <p class="text-white mt-1 py-2 px-3 bg-white/5 border border-white/10 rounded-md">{{ questionnaire.comments }}</p>
                </div>
                {% endif %}
                
                <!-- Questions Section -->
                <div>
                    <h3 class="text-white font-medium mb-4">{% trans "Questions" %}</h3>
                    
                    <div id="questionsContainer" class="space-y-4">
                        {% for question in questionnaire.questions %}
                        <div class="question-item border border-white/10 rounded-md p-4 bg-white/5">
                            <div class="mb-3">
                                <h4 class="text-white font-medium">{% trans "Question Text" %}</h4>
                                <p class="text-white mt-1 py-2 px-3 bg-white/5 border border-white/10 rounded-md">{{ question.text }}</p>
                            </div>
                            
                            <div class="mb-3">
                                <h4 class="text-white font-medium">{% trans "Question Type" %}</h4>
                                <p class="text-white mt-1 py-2 px-3 bg-white/5 border border-white/10 rounded-md">
                                    {% if question.type == 'rating' %}
                                        {% trans "Rating (1-5)" %}
                                    {% elif question.type == 'multiple_choice' or question.type == 'multiplechoice' %}
                                        {% trans "Multiple Choice" %}
                                    {% elif question.type == 'checkbox' %}
                                        {% trans "Checkbox" %}
                                    {% elif question.type == 'text' %}
                                        {% trans "Text" %}
                                    {% else %}
                                        {{ question.type }}
                                    {% endif %}
                                </p>
                            </div>
                            
                            {% if question.type == 'multiple_choice' or question.type == 'multiplechoice' or question.type == 'checkbox' %}
                                {% if question.options %}
                                <div>
                                    <h4 class="text-white font-medium">{% trans "Options" %}</h4>
                                    <ul class="mt-2 space-y-2">
                                        {% for option in question.options %}
                                        <li class="text-white py-2 px-3 bg-white/5 border border-white/10 rounded-md">{{ option.text|default:option }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}
                            {% endif %}
                        </div>
                        {% endfor %}
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4 border-t border-white/10">
                    <a href="{% url 'website:course_content_list' course.course_id %}" class="bg-white/10 hover:bg-white/20 text-white px-4 py-2 rounded-md">
                        {% trans "Back" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 