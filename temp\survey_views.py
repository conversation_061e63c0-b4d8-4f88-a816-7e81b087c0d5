@login_required
@staff_member_required
def surveys_view(request):
    """
    View for displaying all surveys
    """
    return render(request, 'website/surveys.html')

@login_required
@staff_member_required
@require_http_methods(["GET"])
def surveys_api(request):
    """
    API endpoint for retrieving surveys with filtering and pagination
    """
    try:
        # Get query parameters
        search_query = request.GET.get('search', '')
        is_active = request.GET.get('is_active', None)
        start_date_order = request.GET.get('start_date_order', None)
        name_order = request.GET.get('name_order', None)
        page = int(request.GET.get('page', 1))
        per_page = int(request.GET.get('per_page', 10))

        # Start with all surveys
        surveys = Survey.objects.all()

        # Apply filters
        if search_query:
            surveys = surveys.filter(title__icontains=search_query)
        
        if is_active is not None:
            is_active_bool = is_active.lower() == 'true'
            surveys = surveys.filter(is_active=is_active_bool)

        # Apply ordering
        if start_date_order:
            order_param = '-start_date' if start_date_order == 'desc' else 'start_date'
            surveys = surveys.order_by(order_param)
        
        if name_order:
            order_param = '-title' if name_order == 'desc' else 'title'
            surveys = surveys.order_by(order_param)
        
        # Default ordering if no ordering specified
        if not start_date_order and not name_order:
            surveys = surveys.order_by('-created_at')

        # Count total before pagination
        total_count = surveys.count()
        total_pages = (total_count + per_page - 1) // per_page  # Ceiling division

        # Apply pagination
        start_idx = (page - 1) * per_page
        end_idx = page * per_page
        surveys_page = surveys[start_idx:end_idx]

        # Prepare response data
        survey_data = []
        for survey in surveys_page:
            course_instance_name = None
            if survey.course_instance:
                course_name = survey.course_instance.course.name_en
                course_instance_name = f"{course_name} ({survey.course_instance.start_date.strftime('%b %d, %Y')})"
            
            survey_data.append({
                'survey_id': survey.survey_id,
                'title': survey.title,
                'course_instance_name': course_instance_name,
                'start_date': survey.start_date.isoformat() if survey.start_date else None,
                'is_active': survey.is_active,
                'created_at': survey.created_at.isoformat(),
                'updated_at': survey.updated_at.isoformat()
            })

        return JsonResponse({
            'status': 'success',
            'surveys': survey_data,
            'page': page,
            'per_page': per_page,
            'total_count': total_count,
            'total_pages': total_pages
        })
    except Exception as e:
        logger.error(f"Error fetching surveys: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["DELETE"])
def survey_detail_api(request, survey_id):
    """
    API endpoint for deleting a specific survey
    """
    try:
        survey = get_object_or_404(Survey, survey_id=survey_id)
        survey.delete()
        return JsonResponse({
            'status': 'success',
            'message': 'Survey deleted successfully'
        })
    except Exception as e:
        logger.error(f"Error deleting survey: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
@staff_member_required
@require_http_methods(["POST"])
def survey_bulk_action_api(request, action):
    """
    API endpoint for performing bulk actions on surveys
    """
    try:
        data = json.loads(request.body)
        survey_ids = data.get('ids', [])
        
        if not survey_ids:
            return JsonResponse({
                'status': 'error',
                'message': 'No surveys selected'
            }, status=400)
        
        surveys = Survey.objects.filter(survey_id__in=survey_ids)
        
        if action == 'activate':
            surveys.update(is_active=True)
            message = 'Surveys activated successfully'
        elif action == 'deactivate':
            surveys.update(is_active=False)
            message = 'Surveys deactivated successfully'
        elif action == 'delete':
            surveys.delete()
            message = 'Surveys deleted successfully'
        else:
            return JsonResponse({
                'status': 'error',
                'message': 'Invalid action'
            }, status=400)
        
        return JsonResponse({
            'status': 'success',
            'message': message
        })
    except Exception as e:
        logger.error(f"Error performing bulk action on surveys: {e}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500) 