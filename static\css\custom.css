/* Fade In Animation */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.6s ease-out;
}

/* Smooth Hover Transitions */
.hover-transition {
    transition: all 0.3s ease-in-out;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #2a51a3;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1a3b82;
}

/* Base responsive settings */
:root {
    --slide-transition: 1.5s;
    --content-width: min(1200px, 90vw);
    --header-height: clamp(60px, 8vh, 80px);
}

/* Modal positioning */
.modal-top-positioning {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    z-index: 9999 !important;
    margin-top: 0 !important;
}

/* Notification styles */
.notification-container {
    position: fixed;
    top: calc(var(--header-height) + 10px);
    right: 1rem;
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    max-width: 400px;
}

/* Add style for yellow notification badges that might be under header */
.absolute.top-0.right-0.inline-flex.bg-red-500,
.absolute.top-0.right-0.inline-flex.bg-yellow-500 {
    z-index: 1001 !important;
    position: absolute !important;
}

/* Additional notification styles for warning messages */
.bg-yellow-100,
.border-yellow-500,
.bg-yellow-500, 
.text-yellow-800,
[class*="bg-yellow-"],
div[class*="yellow"] {
    z-index: 9999 !important;
    position: relative !important;
}

/* Style for message containers that might be hidden */
#messageContainer,
.message-container,
div[id*="message"],
div[class*="message"] {
    position: fixed !important;
    top: calc(var(--header-height) + 10px) !important;
    z-index: 9999 !important;
}

/* Override for any alert elements */
.alert,
.alert-warning,
.alert-danger,
.alert-info,
.alert-success {
    z-index: 9999 !important;
    position: relative !important;
}

/* Responsive container */
.container {
    width: var(--content-width);
    margin: 0 auto;
    padding: 0 clamp(1rem, 3vw, 2rem);
}

/* Slideshow styles */
.slideshow-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

.slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.5s ease-in-out;
}

.slide-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide.active .slide-image {
    transform: scale(1.1);
}

.overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

/* Background Gradients */
.bg-gradient-1 {
    background: linear-gradient(
        135deg,
        #2a51a3 0%,
        #1a3b82 50%,
        #102754 100%
    );
    animation: gradientShift1 15s infinite alternate;
}

.bg-gradient-2 {
    background: linear-gradient(
        135deg,
        #1e3c7b 0%,
        #2a51a3 50%,
        #0f2447 100%
    );
    animation: gradientShift2 15s infinite alternate;
}

.bg-gradient-3 {
    background: linear-gradient(
        135deg,
        #102754 0%,
        #1a3b82 50%,
        #0a1c3d 100%
    );
    animation: gradientShift3 15s infinite alternate;
}

/* Gradient Animations */
@keyframes gradientShift1 {
    0% {
        background-position: 0% 0%;
    }
    100% {
        background-position: 100% 100%;
    }
}

@keyframes gradientShift2 {
    0% {
        background-position: 100% 0%;
    }
    100% {
        background-position: 0% 100%;
    }
}

@keyframes gradientShift3 {
    0% {
        background-position: 50% 0%;
    }
    100% {
        background-position: 50% 100%;
    }
}

/* Overlay animations */
.slide::before {
    content: '';
    position: absolute;
    inset: 0;
    background: radial-gradient(
        circle at center,
        transparent 0%,
        rgba(0, 0, 0, 0.7) 100%
    );
    opacity: 0;
    transition: opacity 1s ease-in-out;
    z-index: 1;
}

.slide.active::before {
    opacity: 1;
}

/* Animated background patterns */
.slide::after {
    content: '';
    position: absolute;
    inset: 0;
    background-image: 
        linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, rgba(255, 255, 255, 0.05) 75%),
        linear-gradient(-45deg, transparent 75%, rgba(255, 255, 255, 0.05) 75%);
    background-size: 30px 30px;
    background-position: 0 0, 0 15px, 15px -15px, -15px 0px;
    opacity: 0.05;
    z-index: 2;
}

/* Ensure proper stacking */
.slide-content {
    position: relative;
    z-index: 3;
}

.min-h-screen {
    position: relative;
    z-index: 4;
}

/* Performance optimizations */
.slide, .overlay, .slide-bg {
    will-change: transform, opacity;
    backface-visibility: hidden;
}

/* Animated vignette */
.slideshow-container::after {
    display: none;
}

/* Responsive content layout */
.content-wrapper {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(min(100%, 300px), 1fr));
    gap: clamp(1rem, 4vw, 2rem);
    width: 100%;
    max-width: var(--content-width);
    margin: 0 auto;
    padding: clamp(1rem, 3vw, 2rem);
}

/* Responsive feature cards */
.feature-card {
    background: linear-gradient(
        to bottom right,
        rgba(42, 81, 163, 0.1),
        rgba(0, 0, 0, 0.3)
    );
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    padding: clamp(1rem, 3vw, 2rem);
    border-radius: clamp(0.5rem, 1vw, 1rem);
    transition: transform 0.3s ease, background 0.3s ease;
}

.feature-card:hover {
    transform: translateY(-5px);
    background: linear-gradient(
        to bottom right,
        rgba(42, 81, 163, 0.2),
        rgba(0, 0, 0, 0.4)
    );
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Responsive typography */
h1 {
    font-size: clamp(2rem, 5vw, 4rem);
    line-height: 1.2;
}

h2 {
    font-size: clamp(1.5rem, 4vw, 3rem);
}

h3 {
    font-size: clamp(1.25rem, 3vw, 2rem);
}

p {
    font-size: clamp(1rem, 2vw, 1.25rem);
}

/* Responsive form elements */
.form-container {
    width: min(100%, 400px);
    margin: 0 auto;
}

.input-field {
    width: 100%;
    padding: clamp(0.5rem, 2vw, 1rem);
    font-size: clamp(0.875rem, 2vw, 1rem);
}

/* Media queries for larger screens */
@media (min-width: 1024px) {
    .content-grid {
        grid-template-columns: 3fr 2fr;
    }
    
    .feature-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Add floating animation to content */
@keyframes float {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Stagger feature card animations */
.feature-card:nth-child(2) {
    animation-delay: 2s;
}

/* Add shimmer effect */
.slide.active .slide-bg::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    animation: shimmer 3s infinite;
    z-index: 1;
}

@keyframes shimmer {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Update text colors for dark text on light background */
.text-white {
    color: #ffffff !important;
}

.text-white\/90 {
    color: rgba(255, 255, 255, 0.9) !important;
}

/* Modal styles for fixed size and scrolling */
.modal-dialog {
    max-height: 80vh !important;
    margin-top: 15vh !important;
    margin-bottom: 5vh !important;
    overflow: hidden !important;
    max-width: 700px !important;
    width: 90% !important;
}

.modal-content {
    max-height: 80vh !important;
    display: flex !important;
    flex-direction: column !important;
    width: 100% !important;
}

.modal-body {
    overflow-y: auto !important;
    max-height: 60vh !important;
    padding-right: 0.5rem !important;
}

/* Make scrollbars more visible on dark backgrounds */
.modal-body::-webkit-scrollbar {
    width: 8px;
    background-color: rgba(255, 255, 255, 0.05);
}

.modal-body::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 4px;
}

.modal-body::-webkit-scrollbar-thumb:hover {
    background-color: rgba(255, 255, 255, 0.3);
} 