{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-900 to-gray-900">
    {% include 'website/header.html' %}

    <div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10">
            <div class="p-8">
                <h1 class="text-2xl font-bold text-white mb-6">{% trans "Password Reset Required" %}</h1>
                <p class="text-white/80 mb-6">{% trans "Your account administrator requires you to change your password and complete your profile before continuing. All fields are required." %}</p>

                {% include 'website/message.html' %}
                
                <form method="post" class="space-y-6">
                    {% csrf_token %}
                    
                    <div class="border-b border-white/10 pb-6 mb-6">
                        <h2 class="text-lg font-semibold text-white mb-4">{% trans "Reset Your Password" %}</h2>
                        <div class="space-y-4">
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "Current Password" %} *</label>
                                <div class="relative mt-1">
                                    <input type="password" name="current_password" id="current_password" value="" required class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10">
                                    <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-white/70 hover:text-white focus:outline-none toggle-password" data-target="current_password">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                                    </button>
                                </div>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "New Password" %} *</label>
                                <div class="relative mt-1">
                                    <input type="password" name="new_password" id="new_password" required class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10">
                                     <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-white/70 hover:text-white focus:outline-none toggle-password" data-target="new_password">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                                    </button>
                                </div>
                                <p class="mt-1 text-sm text-white/60">{% trans "Make sure your password is at least 8 characters and contains letters, numbers, and special characters." %}</p>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "Confirm New Password" %} *</label>
                                <div class="relative mt-1">
                                    <input type="password" name="confirm_password" id="confirm_password" required class="block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent pr-10">
                                    <button type="button" class="absolute inset-y-0 right-0 px-3 flex items-center text-white/70 hover:text-white focus:outline-none toggle-password" data-target="confirm_password">
                                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" /></svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h2 class="text-lg font-semibold text-white mb-4">{% trans "Complete Your Profile" %}</h2>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "First name" %} *</label>
                                <input type="text" name="first_name" value="" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "Last name" %} *</label>
                                <input type="text" name="last_name" value="" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "Phone number" %} *</label>
                                <input type="text" name="phone_number" value="{{ request.user.phone_number }}" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "Nationality" %} *</label>
                                <input type="text" name="nationality" value="{{ request.user.nationality }}" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "National id" %} *</label>
                                <input type="text" name="national_id" value="" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-white/70">{% trans "Date of birth" %} *</label>
                                <input type="date" name="date_of_birth" value="{% if request.user.date_of_birth %}{{ request.user.date_of_birth|date:'Y-m-d' }}{% endif %}" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-white/70">{% trans "Address" %} *</label>
                            <textarea name="address" rows="4" required class="mt-1 block w-full bg-white/5 border border-white/10 rounded-md py-2 px-3 text-white placeholder-white/30 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent">{{ request.user.address }}</textarea>
                        </div>
                    </div>
                    
                    <div>
                        <button type="submit" class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            {% trans "Update Profile & Password" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const togglePasswordButtons = document.querySelectorAll('.toggle-password');

        togglePasswordButtons.forEach(button => {
            button.addEventListener('click', function () {
                const targetInputId = this.dataset.target;
                const targetInput = document.getElementById(targetInputId);
                const icon = this.querySelector('svg');
                
                if (targetInput.getAttribute('type') === 'password') {
                    targetInput.setAttribute('type', 'text');
                    // Change to eye-slash icon when password is visible
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.542-7 .524-1.661 1.34-3.123 2.374-4.325m.924-1.013A10.013 10.013 0 0112 5c4.478 0 8.268 2.943 9.542 7a10.05 10.05 0 01-1.875 3.825M9 12a3 3 0 116 0 3 3 0 01-6 0zm-4.243 4.243L17.757 6.243" />';
                } else {
                    targetInput.setAttribute('type', 'password');
                    // Change back to eye icon when password is hidden
                    icon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
                }
            });
        });
    });
</script>

{% endblock %} 