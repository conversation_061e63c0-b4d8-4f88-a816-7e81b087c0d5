{% load static %}
{% load i18n %}

{% get_available_languages as LANGUAGES %}
{% get_language_info_list for LANGUAGES as languages %}
{% get_current_language as LANGUAGE_CODE %}

<nav class="bg-white shadow-md fixed top-0 left-0 right-0 z-50" dir="{{ LANGUAGE_CODE|default:'ltr' }}">
    <div class="max-w-7xl mx-auto px-2 sm:px-4 md:px-6 lg:px-8">
        <div class="flex justify-between min-h-20 py-2">
            <!-- Logo and Primary Nav -->
            <div class="flex flex-1 overflow-x-auto hide-scrollbar">
                <div class="absolute top-0 left-0 flex items-start">
                    <img class="h-12 sm:h-16 w-auto" src="{% static 'images/logo.png' %}" alt="GB Academy">
                </div>
                
                <div class="w-full flex justify-center">

                    <div class="hidden sm:flex sm:items-center max-w-7xl mx-auto px-2" id="nav-container">
                        <div class="flex items-center justify-center space-x-1 w-full">
                            <!-- Dashboard Link -->
                            {% if request.user.is_superuser %}
                            <a href="{% url 'website:super_admin_dashboard' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'super_admin_dashboard' %}active{% endif %}" title="{% trans 'Dashboard' %}">
                            {% elif request.user.is_staff %}
                            <a href="{% url 'website:system_admin_dashboard' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'system_admin_dashboard' %}active{% endif %}" title="{% trans 'Dashboard' %}">
                            {% elif request.user.user_type.code == 'TRAINER' %}
                            <a href="{% url 'website:trainer_dashboard' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'trainer_dashboard' %}active{% endif %}" title="{% trans 'Dashboard' %}">
                            {% elif request.user.user_type.code == 'EXTERNAL_INDIVIDUAL' %}
                            <a href="{% url 'website:individual_user_dashboard' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'individual_user_dashboard' %}active{% endif %}" title="{% trans 'Dashboard' %}">
                            {% elif request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE' %}
                            <a href="{% url 'website:corporate_user_dashboard' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'corporate_user_dashboard' %}active{% endif %}" title="{% trans 'Dashboard' %}">
                            {% else %}
                            <a href="{% url 'website:main' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'main' %}active{% endif %}" title="{% trans 'Dashboard' %}">
                            {% endif %}
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                                </svg>
                                <span class="nav-label">{% trans "Dashboard" %}</span>
                            </a>

                            <!-- Courses Link -->
                            {% if request.user.user_type.code != 'EXTERNAL_CORPORATE_TRAINEE' %}
                            <a href="{% url 'website:courses' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'courses' %}active{% endif %}" title="{% trans 'Courses' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                                <span class="nav-label">{% trans "Courses" %}</span>
                            </a>
                            {% endif %}
                            <!-- Calendar Link -->
                            {% if request.user.user_type.code == 'TRAINER' %}
                            <a href="{% url 'website:trainer_calendar' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'trainer_calendar' %}active{% endif %}" title="{% trans 'My Calendar' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="nav-label">{% trans "My Calendar" %}</span>
                            </a>

                            <!-- Sessions Link -->
                            <a href="{% url 'website:trainer_sessions_view' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'trainer_sessions_view' %}active{% endif %}" title="{% trans 'My Sessions' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                                </svg>
                                <span class="nav-label">{% trans "My Sessions" %}</span>
                            </a>
                            {% elif request.user.user_type.code == 'EXTERNAL_INDIVIDUAL' %}
                            <a href="{% url 'website:user_calendar' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'user_calendar' %}active{% endif %}" title="{% trans 'My Course Calendar' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="nav-label">{% trans "My Course Calendar" %}</span>
                            </a>
                            <a href="{% url 'website:user_reservations' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'user_reservations' %}active{% endif %}" title="{% trans 'My Reservations' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                <span class="nav-label">{% trans "My Reservations" %}</span>
                            </a>
                            {% elif request.user.user_type.code == 'INTERNAL_GB' %}
                            <a href="{% url 'website:gb_user_calendar' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'gb_user_calendar' %}active{% endif %}" title="{% trans 'My GB Calendar' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="nav-label">{% trans "My GB Calendar" %}</span>
                            </a>
                            <a href="{% url 'website:gb_user_reservations_view' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'gb_user_reservations_view' %}active{% endif %}" title="{% trans 'My Reservations' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                <span class="nav-label">{% trans "My Reservations" %}</span>
                            </a>
                            {% elif request.user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN' %}
                            <!-- Calendar Link -->
                            <a href="{% url 'website:corporate_admin_calendar' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'corporate_admin_calendar' %}active{% endif %}" title="{% trans 'Corporate Calendar' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="nav-label">{% trans "Corporate Calendar" %}</span>
                            </a>

                            <!-- Requests Link -->
                            <a href="{% url 'website:my_corporate_requests' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'my_corporate_requests' %}active{% endif %}" title="{% trans 'My Requests' %}">
                                <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
                                </svg>
                                <span class="nav-label">{% trans "My Requests" %}</span>
                            </a>

                            <!-- Users Link -->
                            <a href="{% url 'website:my_corporate_users' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'my_corporate_users' %}active{% endif %}" title="{% trans 'My Users' %}">
                                <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"/>
                                </svg>
                                <span class="nav-label">{% trans "My Users" %}</span>
                            </a>

                            <!-- Reservations Link -->
                            <a href="{% url 'website:my_corporate_reservations' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'my_corporate_reservations' %}active{% endif %}" title="{% trans 'User Reservations' %}">
                                <svg class="nav-icon" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
                                </svg>
                                <span class="nav-label">{% trans "User Reservations" %}</span>
                            </a>
                            {% elif request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE' %}
                            <!-- Corporate Courses Link -->
                            <a href="{% url 'website:corporate_user_courses_view' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'corporate_user_courses_view' %}active{% endif %}" title="{% trans 'My Corporate Courses' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                                </svg>
                                <span class="nav-label">{% trans "My Corporate Courses" %}</span>
                            </a>

                            <!-- Corporate Calendar Link -->
                            <a href="{% url 'website:corporate_user_calendar_view' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'corporate_user_calendar_view' %}active{% endif %}" title="{% trans 'My Corporate Calendar' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="nav-label">{% trans "My Corporate Calendar" %}</span>
                            </a>

                            <!-- Corporate Reservations Link -->
                            <a href="{% url 'website:corporate_user_reservations' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'corporate_user_reservations' %}active{% endif %}" title="{% trans 'My Reservations' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                                </svg>
                                <span class="nav-label">{% trans "My Reservations" %}</span>
                            </a>
                            {% else %}
                            <!-- Default Calendar Link -->
                            <a href="{% url 'website:calendar' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'calendar' %}active{% endif %}" title="{% trans 'Calendar' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                                </svg>
                                <span class="nav-label">{% trans "Calendar" %}</span>
                            </a>
                            {% endif %}
                            <!-- Staff-only Links -->
                            {% if request.user.is_staff %}
                            <!-- Resources Link -->
                            <a href="{% url 'website:rooms' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'rooms' %}active{% endif %}" title="{% trans 'Resources' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                                <span class="nav-label">{% trans "Resources" %}</span>
                            </a>

                            <!-- Trainers Link -->
                            <a href="{% url 'website:trainers' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'trainers' %}active{% endif %}" title="{% trans 'Trainers' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                                </svg>
                                <span class="nav-label">{% trans "Trainers" %}</span>
                            </a>

                            <!-- Corporate Link -->
                            <a href="{% url 'website:corporate_admins_view' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'corporate_admins_view' %}active{% endif %}" title="{% trans 'Corporate' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                                </svg>
                                <span class="nav-label">{% trans "Corporate" %}</span>
                            </a>

                            <!-- Admin Reservations Link -->
                            <a href="{% url 'website:admin_reservations' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'admin_reservations' %}active{% endif %}" title="{% trans 'Reservations' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                                </svg>
                                <span class="nav-label">{% trans "Reservations" %}</span>
                            </a>

                            <!-- Surveys Link -->
                            <a href="{% url 'website:surveys' %}" class="nav-icon-link {% if request.resolver_match.url_name == 'surveys' %}active{% endif %}" title="{% trans 'Surveys' %}">
                                <svg class="nav-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                                </svg>
                                <span class="nav-label">{% trans "Surveys" %}</span>
                            </a>
                            {% endif %}
                            {% comment %} {% if request.user.is_staff %}
                            <a href="{% url 'website:sessions' %}" class="{% if request.resolver_match.url_name == 'sessions' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'sessions' %}border-primary{% else %}border-transparent{% endif %}">
                                <div class="flex items-center">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                                    </svg>
                                    {% trans "Sessions" %}
                                </div>
                            </a>
                            {% endif %} {% endcomment %}
                        </div>
                    </div>
                </div>
            </div>

            <!-- Mobile menu button -->
            <div class="flex items-center sm:hidden">
                <button type="button" 
                        class="inline-flex items-center justify-center p-2 rounded-md text-gray-500 hover:text-primary hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
                        onclick="document.getElementById('mobile-menu').classList.toggle('hidden')"
                        aria-expanded="false">
                    <span class="sr-only">Open main menu</span>
                    <!-- Icon when menu is closed -->
                    <svg class="block h-6 w-6" fill="none" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                    </svg>
                    <!-- Icon when menu is open -->
                    <svg class="hidden h-6 w-6" fill="none" stroke="currentColor" aria-hidden="true">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>

            <!-- Right side navigation -->
            <div class="absolute top-2 right-5 hidden sm:flex sm:items-center sm:justify-end sm:flex-shrink-0 p-2">
                <div class="flex items-center space-x-2">
                    <!-- Add Session Button -->
                    {% if request.user.is_staff %}
                    <a href="{% url 'website:sessions' %}" class="p-2 text-gray-500 hover:text-primary transition-colors duration-200 relative z-10" title="{% trans 'Add Session' %}">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                    </a>
                    {% endif %}

                    <!-- Notifications -->
                    <div class="relative">
                        <button class="p-2 text-gray-500 hover:text-primary transition-colors duration-200" title="{% trans 'Notifications' %}">
                            <span class="sr-only">View notifications</span>
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <!-- Notification badge -->
                            <span class="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full z-[1001]">2</span>
                        </button>
                    </div>

                    {% if request.user.is_superuser %}
                    <!-- Admin Settings for Superusers -->
                    <a href="{% url 'admin:index' %}" class="p-2 text-gray-500 hover:text-primary transition-colors duration-200" title="{% trans 'Admin Panel' %}">
                        <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </a>
                    {% endif %}

                    <!-- Profile dropdown -->
                    <div class="relative">
                        <button type="button"
                                class="p-2 text-gray-500 hover:text-primary transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary rounded-full"
                                id="user-menu-button"
                                onclick="document.getElementById('user-dropdown').classList.toggle('hidden')"
                                title="{% trans 'Profile Menu' %}">
                            <span class="sr-only">Open user menu</span>
                            <div class="h-6 w-6 rounded-full bg-primary flex items-center justify-center text-white text-sm font-medium">
                                {{ request.user.username|first|upper }}
                            </div>
                        </button>
                            <!-- Dropdown menu -->
                            <div id="user-dropdown" class="hidden origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 z-50">
                                <div class="py-1">
                                    <a href="{% url 'website:profile' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                                            </svg>
                                            <span class="profile-menu-text">{% trans "Profile" %}</span>
                                        </div>
                                    </a>
                                    {% comment %} <a href="{% url 'website:settings' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            </svg>
                                            <span class="profile-menu-text">{% trans "Settings" %}</span>
                                        </div>
                                    </a> {% endcomment %}
                                    <a href="{% url 'website:settings' %}" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                                            </svg>
                                            <span class="profile-menu-text">{% trans "Settings" %}</span>
                                        </div>
                                    </a>
                                    <hr class="my-1 border-gray-200">
                                    <a href="{% url 'website:logout' %}" class="block px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                                        <div class="flex items-center">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                                            </svg>
                                            <span class="profile-menu-text">{% trans "Logout" %}</span>
                                        </div>
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile menu, show/hide based on menu state -->
        <div class="hidden sm:hidden" id="mobile-menu">
            <div class="pt-2 pb-3 space-y-1">
                {% if request.user.is_superuser %}
                <a href="{% url 'website:super_admin_dashboard' %}" 
                   class="{% if request.resolver_match.url_name == 'super_admin_dashboard' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'super_admin_dashboard' %}border-primary{% else %}border-transparent{% endif %}">
                {% elif request.user.is_staff %}
                <a href="{% url 'website:system_admin_dashboard' %}" 
                   class="{% if request.resolver_match.url_name == 'system_admin_dashboard' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'system_admin_dashboard' %}border-primary{% else %}border-transparent{% endif %}">
                {% elif request.user.user_type.code == 'TRAINER' %}
                <a href="{% url 'website:trainer_dashboard' %}" 
                   class="{% if request.resolver_match.url_name == 'trainer_dashboard' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'trainer_dashboard' %}border-primary{% else %}border-transparent{% endif %}">
                {% elif request.user.user_type.code == 'EXTERNAL_INDIVIDUAL' %}
                <a href="{% url 'website:individual_user_dashboard' %}" class="{% if request.resolver_match.url_name == 'individual_user_dashboard' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                {% elif request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE' %}
                <a href="{% url 'website:corporate_user_dashboard' %}" class="{% if request.resolver_match.url_name == 'corporate_user_dashboard' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                {% else %}
                <a href="{% url 'website:main' %}" 
                   class="{% if request.resolver_match.url_name == 'main' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'main' %}border-primary{% else %}border-transparent{% endif %}">
                {% endif %}
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"/>
                        </svg>
                        {% trans "Dashboard" %}
                    </div>
                </a>
                {% if request.user.user_type.code == 'INTERNAL_GB' %}
                <a href="{% url 'website:gb_user_calendar' %}" class="{% if request.resolver_match.url_name == 'gb_user_calendar' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'gb_user_calendar' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        {% trans "My GB Calendar" %}
                    </div>
                </a>
                <a href="{% url 'website:gb_user_reservations_view' %}" class="{% if request.resolver_match.url_name == 'gb_user_reservations_view' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'gb_user_reservations_view' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                        {% trans "My Reservations" %}
                    </div>
                </a>
                {% endif %}
                {% if request.user.user_type.code != 'EXTERNAL_CORPORATE_TRAINEE' %}
                <a href="{% url 'website:courses' %}" 
                   class="{% if request.resolver_match.url_name == 'courses' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'courses' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                        {% trans "Courses" %}
                    </div>
                </a>
                {% endif %}
                {% if request.user.user_type.code == 'TRAINER' %}
                <a href="{% url 'website:trainer_calendar' %}" 
                   class="{% if request.resolver_match.url_name == 'trainer_calendar' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'trainer_calendar' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        {% trans "My Calendar" %}
                    </div>
                </a>
                <a href="{% url 'website:trainer_sessions_view' %}" 
                   class="{% if request.resolver_match.url_name == 'trainer_sessions_view' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'trainer_sessions_view' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                        {% trans "My Sessions" %}
                    </div>
                </a>
                {% elif request.user.user_type.code == 'EXTERNAL_INDIVIDUAL' %}
                <a href="{% url 'website:user_calendar' %}" 
                   class="{% if request.resolver_match.url_name == 'user_calendar' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'user_calendar' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        {% trans "My Course Calendar" %}
                    </div>
                </a>
                <a href="{% url 'website:user_reservations' %}" class="{% if request.resolver_match.url_name == 'user_reservations' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'user_reservations' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                        </svg>
                        {% trans "My Reservations" %}
                    </div>
                </a>
                {% elif request.user.user_type.code == 'EXTERNAL_CORPORATE_ADMIN' %}
                <a href="{% url 'website:corporate_admin_calendar' %}" 
                   class="{% if request.resolver_match.url_name == 'corporate_admin_calendar' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'corporate_admin_calendar' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                        </svg>
                        {% trans "Corporate Calendar" %}
                    </div>
                </a>
                <a href="{% url 'website:my_corporate_requests' %}" class="{% if request.resolver_match.url_name == 'my_corporate_requests' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'my_corporate_requests' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
                        </svg>
                        {% trans "My Requests" %}
                    </div>
                </a>
                <a href="{% url 'website:my_corporate_users' %}" 
                   class="{% if request.resolver_match.url_name == 'my_corporate_users' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'my_corporate_users' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z"/>
                        </svg>
                        {% trans "My Users" %}
                    </div>
                </a>
                <a href="{% url 'website:my_corporate_reservations' %}" 
                   class="{% if request.resolver_match.url_name == 'my_corporate_reservations' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'my_corporate_reservations' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25zM6.75 12h.008v.008H6.75V12zm0 3h.008v.008H6.75V15zm0 3h.008v.008H6.75V18z"/>
                        </svg>
                        {% trans "User Reservations" %}
                    </div>
                </a>
                {% comment %} <a href="{% url 'website:user_reservations' %}" class="{% if request.resolver_match.url_name == 'user_reservations' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    {% trans "My Reservations" %}
                </a> {% endcomment %}
                {% elif request.user.user_type.code == 'EXTERNAL_CORPORATE_TRAINEE' %}
                <a href="{% url 'website:corporate_user_courses_view' %}" class="{% if request.resolver_match.url_name == 'corporate_user_courses_view' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                    </svg>
                    {% trans "My Corporate Courses" %}
                </a>
                <a href="{% url 'website:corporate_user_calendar_view' %}" class="{% if request.resolver_match.url_name == 'corporate_user_calendar_view' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {% trans "My Corporate Calendar" %}
                </a>
                <a href="{% url 'website:corporate_user_reservations' %}" class="{% if request.resolver_match.url_name == 'corporate_user_reservations' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
                    </svg>
                    {% trans "My Reservations" %}
                </a>
                {% else %}
                <a href="{% url 'website:calendar' %}" class="{% if request.resolver_match.url_name == 'calendar' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
                    </svg>
                    {% trans "Calendar" %}
                </a>
                {% endif %}
                {% if request.user.is_staff %}
                <a href="{% url 'website:rooms' %}" class="{% if request.resolver_match.url_name == 'rooms' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    {% trans "Resources" %}
                </a>
                <a href="{% url 'website:trainers' %}" class="{% if request.resolver_match.url_name == 'trainers' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"/>
                    </svg>
                    {% trans "Trainers" %}
                </a>
                <a href="{% url 'website:corporate_admins_view' %}" class="{% if request.resolver_match.url_name == 'corporate_admins_view' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
                    </svg>
                    {% trans "Corporate" %}
                </a>
                <a href="{% url 'website:admin_reservations' %}" class="nav-link {% if request.resolver_match.url_name == 'admin_reservations' %}active text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 inline-flex items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                    </svg>
                    <span class="nav-text">{% trans "Reservations" %}</span>
                </a>
                <a href="{% url 'website:surveys' %}" class="{% if request.resolver_match.url_name == 'surveys' %}text-primary border-primary{% else %}text-gray-500 border-transparent{% endif %} hover:text-primary hover:border-primary px-2 md:px-3 py-2 flex flex-col items-center text-sm font-medium border-b-2 whitespace-nowrap">
                    <svg class="w-5 h-5 mr-1 md:mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
                    </svg>
                    {% trans "Surveys" %}
                </a>
                {% endif %}
                {% comment %} {% if request.user.is_staff %}
                <a href="{% url 'website:sessions' %}" class="{% if request.resolver_match.url_name == 'sessions' %}bg-primary text-white{% else %}text-gray-500 hover:bg-gray-100 hover:text-primary{% endif %} block pl-3 pr-4 py-2 text-base font-medium border-l-4 {% if request.resolver_match.url_name == 'sessions' %}border-primary{% else %}border-transparent{% endif %}">
                    <div class="flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                        </svg>
                        {% trans "Sessions" %}
                    </div>
                </a>
                {% endif %} {% endcomment %}
            </div>
            <!-- Mobile menu profile section -->
            <div class="pt-4 pb-3 border-t border-gray-200">
                <div class="flex items-center px-4">
                    <div class="flex-shrink-0">
                        <div class="h-10 w-10 rounded-full bg-primary flex items-center justify-center text-white text-lg">
                            {{ request.user.username|first|upper }}
                        </div>
                    </div>
                    <div class="ml-3">
                        <div class="text-base font-medium text-gray-800">{{ request.user.get_full_name }}</div>
                        <div class="text-sm font-medium text-gray-500">{{ request.user.email }}</div>
                    </div>
                    {% if request.user.is_superuser %}
                    <div class="ml-auto">
                        <a href="{% url 'admin:index' %}" class="p-2 text-gray-500 hover:text-primary" title="Admin Panel">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            </svg>
                        </a>
                    </div>
                    {% endif %}
                </div>
                <div class="mt-3 space-y-1">
                    <a href="{% url 'website:profile' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"/>
                        </svg>
                        <span class="profile-menu-text">{% trans "Profile" %}</span>
                    </a>
                    <a href="{% url 'website:settings' %}" class="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                        <span class="profile-menu-text">{% trans "Settings" %}</span>
                    </a>
                    <a href="{% url 'website:logout' %}" class="flex items-center px-4 py-2 text-sm text-red-600 hover:bg-gray-100">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"/>
                        </svg>
                        <span class="profile-menu-text">{% trans "Logout" %}</span>
                    </a>
                </div>
            </div>
        </div>
    </div>
</nav>

<!-- Notification Container -->
<div id="notification-container" class="notification-container"></div>

<!-- Spacer to prevent content from hiding under fixed header -->
<div class="h-20"></div>

<style>
/* RTL Support */
.rtl-mode {
    direction: rtl;
}

.rtl-mode .sm\:ml-8 {
    margin-left: 0;
    margin-right: 2rem;
}

.rtl-mode .space-x-8 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
}

.rtl-mode .space-x-4 > :not([hidden]) ~ :not([hidden]) {
    --tw-space-x-reverse: 1;
}

/* Language button active state */
.lang-btn-active {
    @apply bg-white/20 text-white;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.3);
}

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

/* Vertical separator between nav items */
.flex.space-x-2.md\:space-x-4.lg\:space-x-8.items-center a {
  position: relative;
}

.flex.space-x-2.md\:space-x-4.lg\:space-x-8.items-center a:not(:last-child)::after {
  content: "";
  position: absolute;
  right: -8px;
  top: 50%;
  transform: translateY(-50%);
  height: 60%;
  width: 1px;
  background-color: rgba(107, 114, 128, 0.3);
}

@media (min-width: 768px) {
  .flex.space-x-2.md\:space-x-4.lg\:space-x-8.items-center a:not(:last-child)::after {
    right: -12px;
  }
}

@media (min-width: 1024px) {
  .flex.space-x-2.md\:space-x-4.lg\:space-x-8.items-center a:not(:last-child)::after {
    right: -24px;
  }
}

/* Reset navigation link styles to show labels */
.nav-link {
  position: relative;
  display: flex;
  align-items: center;
  width: auto;
  padding-right: 0.75rem;
}

.nav-link .nav-text,
.nav-text {
  position: static;
  opacity: 1;
  transform: none;
  white-space: nowrap;
  pointer-events: auto;
  transition: none;
  background-color: transparent;
  padding: 0;
  box-shadow: none;
  margin-left: 0.25rem;
}

@media (min-width: 768px) {
  .nav-link .nav-text,
  .nav-text {
    margin-left: 0.5rem;
  }
}

/* Center navigation and handle spacing */
.flex.space-x-2.md\:space-x-4.lg\:space-x-8.items-center {
  justify-content: center;
}

/* Optimize navigation space usage - reduce right padding to utilize available space */
.w-full.flex.justify-center {
  padding-left: 120px;
  padding-right: 60px; /* Reduced from 120px to use more space */
}

@media (min-width: 768px) {
  .w-full.flex.justify-center {
    padding-left: 150px;
    padding-right: 80px; /* Reduced from 150px to use more space */
  }
}

/* Make navigation scrollable */
.overflow-x-auto.hide-scrollbar {
  -webkit-overflow-scrolling: touch;
  scroll-behavior: smooth;
}

/* Ensure the navigation menu is properly centered and scrollable */
.max-w-3xl.mx-auto {
  width: 100%;
  overflow-x: auto;
}

/* Fix for surveys link to display horizontally */
a[href*="surveys"] {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
}

/* 8-Link Navigation System */
#nav-container {
  max-height: none;
  width: 100%;
}

#nav-container .flex {
  min-height: 3rem;
  gap: 0.5rem;
  width: 100%;
  justify-content: center;
  align-items: center;
}

/* Dynamic spacing based on number of links */
#nav-container .flex:has(.nav-icon-link:nth-child(8)) {
  gap: 0.125rem; /* Reduced gap when 8 links present */
}

#nav-container .flex:has(.nav-icon-link:nth-child(7):last-child) {
  gap: 0.25rem; /* Medium gap for 7 links */
}

#nav-container .flex:has(.nav-icon-link:nth-child(6):last-child) {
  gap: 0.375rem; /* Larger gap for 6 links */
}

#nav-container .flex:has(.nav-icon-link:nth-child(5):last-child) {
  gap: 0.5rem; /* Even larger gap for 5 links */
}

#nav-container .flex:has(.nav-icon-link:nth-child(4):last-child) {
  gap: 0.75rem; /* Large gap for 4 links */
}

#nav-container .flex:has(.nav-icon-link:nth-child(3):last-child) {
  gap: 1rem; /* Very large gap for 3 links */
}

/* Navigation link styling - utilizing full available space */
.nav-icon-link {
  transition: all 0.3s ease-in-out;
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex: 1;
  min-width: 160px;
  max-width: 240px;
  height: 2.5rem;
  padding: 0.5rem 0.875rem;
  border-radius: 0.5rem;
  background: transparent;
  border: 1px solid transparent;
  margin: 0;
  box-sizing: border-box;
  text-decoration: none;
  color: #6b7280;
}

/* Optimized width for 8 links - using available space */
#nav-container .flex:has(.nav-icon-link:nth-child(8)) .nav-icon-link {
  min-width: 130px;
  max-width: 170px;
  padding: 0.5rem 0.75rem;
}

/* More generous width for fewer links */
#nav-container .flex:has(.nav-icon-link:nth-child(6):last-child) .nav-icon-link {
  min-width: 180px;
  max-width: 260px;
}

#nav-container .flex:has(.nav-icon-link:nth-child(5):last-child) .nav-icon-link {
  min-width: 200px;
  max-width: 300px;
}

#nav-container .flex:has(.nav-icon-link:nth-child(4):last-child) .nav-icon-link {
  min-width: 220px;
  max-width: 340px;
}

/* Icon styling - positioned on the left */
.nav-icon {
  width: 1.125rem;
  height: 1.125rem;
  margin-right: 0.5rem;
  margin-bottom: 0;
  flex-shrink: 0;
  transition: all 0.3s ease-in-out;
}

/* Label styling - full text visible */
.nav-label {
  font-size: 0.875rem;
  line-height: 1.2;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: 500;
  text-align: left;
  flex: 1;
  transition: all 0.3s ease-in-out;
}

/* Hover effects */
.nav-icon-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 0.9);
  border-color: rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.nav-icon-link:hover .nav-icon {
  transform: scale(1.05);
}

/* Active link styling */
.nav-icon-link.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15), rgba(147, 51, 234, 0.15));
  border-color: rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.2);
  color: #3b82f6;
}

.nav-icon-link.active .nav-icon {
  color: #3b82f6;
}

/* Responsive adjustments for space-optimized navigation */
@media (max-width: 1200px) {
  .nav-icon-link {
    min-width: 110px;
    max-width: 150px;
    height: 2.25rem;
    padding: 0.4rem 0.6rem;
  }

  /* 8 links - optimized for available space */
  #nav-container .flex:has(.nav-icon-link:nth-child(8)) .nav-icon-link {
    min-width: 100px;
    max-width: 130px;
    padding: 0.4rem 0.5rem;
  }

  /* Fewer links - more generous spacing */
  #nav-container .flex:has(.nav-icon-link:nth-child(6):last-child) .nav-icon-link {
    min-width: 140px;
    max-width: 190px;
  }

  #nav-container .flex:has(.nav-icon-link:nth-child(4):last-child) .nav-icon-link {
    min-width: 170px;
    max-width: 240px;
  }

  .nav-icon {
    width: 1rem;
    height: 1rem;
    margin-right: 0.375rem;
  }

  .nav-label {
    font-size: 0.8rem;
  }
}

@media (max-width: 768px) {
  #nav-container .flex {
    gap: 0.25rem;
  }

  #nav-container .flex:has(.nav-icon-link:nth-child(8)) {
    gap: 0.1rem;
  }

  .nav-icon-link {
    min-width: 75px;
    max-width: 95px;
    height: 2rem;
    padding: 0.3rem 0.4rem;
  }

  #nav-container .flex:has(.nav-icon-link:nth-child(8)) .nav-icon-link {
    min-width: 70px;
    max-width: 85px;
    padding: 0.3rem 0.3rem;
  }

  #nav-container .flex:has(.nav-icon-link:nth-child(6):last-child) .nav-icon-link {
    min-width: 90px;
    max-width: 120px;
  }

  .nav-icon {
    width: 0.875rem;
    height: 0.875rem;
    margin-right: 0.25rem;
  }

  .nav-label {
    font-size: 0.75rem;
  }
}

@media (max-width: 640px) {
  #nav-container .flex {
    gap: 0.2rem;
  }

  #nav-container .flex:has(.nav-icon-link:nth-child(8)) {
    gap: 0.05rem;
  }

  .nav-icon-link {
    min-width: 65px;
    max-width: 80px;
    height: 1.875rem;
    padding: 0.25rem 0.3rem;
  }

  #nav-container .flex:has(.nav-icon-link:nth-child(8)) .nav-icon-link {
    min-width: 60px;
    max-width: 75px;
    padding: 0.25rem 0.25rem;
  }

  .nav-icon {
    width: 0.75rem;
    height: 0.75rem;
    margin-right: 0.2rem;
  }

  .nav-label {
    font-size: 0.7rem;
  }
}

/* Add subtle background for better visibility */
nav {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}
</style>

<!-- Replace the existing script that adds the hover behavior -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Handle dropdown toggles and other necessary functionality
        document.addEventListener('click', function(event) {
            const userDropdown = document.getElementById('user-dropdown');
            const userMenuButton = document.getElementById('user-menu-button');
            
            if (userDropdown && !userDropdown.contains(event.target) && 
                userMenuButton && !userMenuButton.contains(event.target)) {
                userDropdown.classList.add('hidden');
            }
        });
        
        // Enhanced navigation functionality
        const container = document.getElementById('nav-container');

        if (container) {
            // Add smooth transitions and hover effects
            const navLinks = container.querySelectorAll('a');
            navLinks.forEach(link => {
                link.addEventListener('mouseenter', function() {
                    this.style.transform = 'translateY(-1px)';
                });

                link.addEventListener('mouseleave', function() {
                    this.style.transform = 'translateY(0)';
                });
            });
        }
    });
</script>


