{% extends "website/login_base.html" %}
{% load static %}

{% block content %}
<!-- Background Slideshow -->
<div class="fixed inset-0 z-0">
    <div class="slideshow-container">
        <div class="slide">
            <img src="{% static 'images/slides/slide1.jpg' %}" class="slide-image" alt="Background 1">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide2.jpg' %}" class="slide-image" alt="Background 2">
            <div class="overlay"></div>
        </div>
        <div class="slide">
            <img src="{% static 'images/slides/slide3.jpg' %}" class="slide-image" alt="Background 3">
            <div class="overlay"></div>
        </div>
    </div>
</div>

<!-- Main Content -->
<div class="min-h-screen flex items-center justify-center relative z-10">
    <div class="container px-4 py-8">
        <div class="max-w-5xl mx-auto">
            <div class="w-full space-y-3 bg-black/20 backdrop-blur-md p-6 rounded-lg shadow-xl border border-white/10">
                <div class="text-center">
                    <div class="bg-white inline-block p-4 rounded-lg mb-4">
                        <img class="mx-auto h-20 w-auto" src="{% static 'images/logo.png' %}" alt="GB Academy Logo">
                    </div>
                    <h2 class="text-3xl font-bold tracking-tight text-white mb-1">
                        Create Account
                    </h2>
                    <p class="text-base text-white/80">
                        Step 2 of 4: Account Details
                    </p>
                </div>

                {% include "website/registration/timeline.html" with current_step=current_step show_corporate=show_corporate %}
                
                <form method="post" class="mt-6 space-y-4">
                    {% csrf_token %}
                    
                    {% if form.errors %}
                    <div class="bg-red-500/20 backdrop-blur-md border border-red-500/30 text-white/90 px-4 py-2 rounded-md" role="alert">
                        <strong class="font-bold">Please fix the following errors:</strong>
                        <ul class="mt-1">
                            {% for field in form %}
                                {% for error in field.errors %}
                                    <li>{{ error }}</li>
                                {% endfor %}
                            {% endfor %}
                        </ul>
                    </div>
                    {% endif %}
                    
                    <!-- Account Information Section -->
                    <div class="space-y-2">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                            <!-- Email and Username fields -->
                            {% for field in form %}
                                {% if field.name in 'email,username' %}
                                <div class="space-y-2">
                                    <label for="{{ field.id_for_label }}" 
                                           class="block text-sm font-medium text-white/90">
                                        {{ field.label }}
                                    </label>
                                    
                                    <input type="{{ field.field.widget.input_type }}"
                                           name="{{ field.name }}"
                                           id="{{ field.id_for_label }}"
                                           class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                           {% if field.field.required %}required{% endif %}>
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>

                        <!-- Password Section -->
                        <div class="mt-1 grid grid-cols-1 md:grid-cols-2 gap-x-4">
                            <div class="space-y-0.5">
                                <label for="{{ form.password1.id_for_label }}" 
                                       class="block text-sm font-medium text-white/90">
                                    {{ form.password1.label }}
                                </label>
                                
                                <input type="password"
                                       name="{{ form.password1.name }}"
                                       id="{{ form.password1.id_for_label }}"
                                       class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                       required
                                       {{ form.password1.field.widget.attrs|safe }}>
                                
                                {% if form.password1.help_text %}
                                <div class="mt-1">{{ form.password1.help_text|safe }}</div>
                                {% endif %}
                            </div>

                            <div class="space-y-0.5">
                                <label for="{{ form.password2.id_for_label }}" 
                                       class="block text-sm font-medium text-white/90">
                                    {{ form.password2.label }}
                                </label>
                                
                                <input type="password"
                                       name="{{ form.password2.name }}"
                                       id="{{ form.password2.id_for_label }}"
                                       class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                       required
                                       {{ form.password2.field.widget.attrs|safe }}>
                                
                                {% if form.password2.help_text %}
                                <div class="mt-1">{{ form.password2.help_text|safe }}</div>
                                {% endif %}
                            </div>
                        </div>
                    </div>

                    <!-- Personal Information Fields (without section header) -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        {% for field in form %}
                            {% if field.name in 'first_name,last_name,phone_number,nationality,national_id,date_of_birth,address' %}
                            <div class="space-y-2 {% if field.name == 'address' %}md:col-span-3{% endif %}">
                                <label for="{{ field.id_for_label }}" 
                                       class="block text-sm font-medium text-white/90">
                                    {{ field.label }}
                                </label>
                                
                                {% if field.field.widget.template_name == 'django/forms/widgets/textarea.html' %}
                                    <textarea name="{{ field.name }}"
                                              id="{{ field.id_for_label }}"
                                              class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                              rows="3"
                                              {% if field.field.required %}required{% endif %}></textarea>
                                {% else %}
                                    <input type="{% if field.name == 'date_of_birth' %}date{% else %}text{% endif %}"
                                           name="{{ field.name }}"
                                           id="{{ field.id_for_label }}"
                                           class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                           {% if field.field.required %}required{% endif %}>
                                {% endif %}
                                
                                {% if field.help_text %}
                                <div class="mt-2">{{ field.help_text|safe }}</div>
                                {% endif %}
                            </div>
                            {% endif %}
                        {% endfor %}
                    </div>

                    <!-- Corporate Information Section (Only for Corporate Registration) -->
                    {% if form.company_name %}
                    <div class="space-y-4">
                        <h3 class="text-xl font-semibold text-white/90 border-b border-white/10 pb-2">Corporate Information</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            {% for field in form %}
                                {% if field.name in 'company_name,commercial_registration_number,tax_registration_number,key_person_name,key_person_phone,key_person_email' %}
                                <div class="space-y-2 {% if field.name == 'company_name' %}md:col-span-2{% endif %}">
                                    <label for="{{ field.id_for_label }}" 
                                           class="block text-sm font-medium text-white/90">
                                        {{ field.label }}
                                    </label>
                                    
                                    <input type="{% if field.name == 'key_person_email' %}email{% else %}text{% endif %}"
                                           name="{{ field.name }}"
                                           id="{{ field.id_for_label }}"
                                           class="mt-1 block w-full bg-white/10 border border-white/20 text-white rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-white/50 focus:border-transparent py-2 px-3"
                                           {% if field.field.required %}required{% endif %}>
                                    
                                    {% if field.help_text %}
                                    <div class="mt-2">{{ field.help_text|safe }}</div>
                                    {% endif %}
                                </div>
                                {% endif %}
                            {% endfor %}
                        </div>
                    </div>
                    {% endif %}
                    
                    <div class="text-center max-w-2xl mx-auto -mt-24">
                        <button type="submit"
                                class="w-full bg-white/20 hover:bg-white/30 text-white text-base rounded-md py-2 px-4 focus:outline-none focus:ring-2 focus:ring-white/50 focus:ring-offset-2 focus:ring-offset-transparent transition-all duration-300 transform hover:scale-[1.02]">
                                Continue
                        </button>
                        <a href="{% url 'website:register_step1' %}" class="inline-block mt-2 text-sm text-white/80 hover:text-white transition-colors duration-300">
                            ← Back to Registration Type
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Password Validation Script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const passwordInput = document.querySelector('.password-input');
    const confirmPasswordInput = document.querySelector('.confirm-password-input');
    const requirements = {
        length: password => password.length >= 8,
        alphanumeric: password => /[a-zA-Z]/.test(password) && /[0-9]/.test(password),
        'non-numeric': password => !/^\d+$/.test(password),
        uncommon: password => !['password', '12345678', 'qwerty'].includes(password.toLowerCase()),
        personal: password => true // This will be handled by the backend
    };

    function updateRequirement(requirement, valid) {
        const element = document.querySelector(`[data-requirement="${requirement}"]`);
        if (element) {
            const icon = element.querySelector('svg');
            const text = element.querySelector('span');
            
            if (valid) {
                element.classList.add('text-green-500');
                element.classList.remove('text-white/30');
                icon.classList.add('text-green-500');
                icon.classList.remove('text-white/30');
                text.classList.add('text-green-500');
                text.classList.remove('text-white/30');
            } else {
                element.classList.remove('text-green-500');
                element.classList.add('text-white/30');
                icon.classList.remove('text-green-500');
                icon.classList.add('text-white/30');
                text.classList.remove('text-green-500');
                text.classList.add('text-white/30');
            }
        }
    }

    function validatePassword() {
        const password = passwordInput.value;
        const requirements = document.querySelector('.password-requirements');
        
        if (password) {
            requirements.classList.remove('hidden');
            Object.entries(requirements).forEach(([requirement, validator]) => {
                updateRequirement(requirement, validator(password));
            });
        } else {
            requirements.classList.add('hidden');
        }
        validatePasswordMatch();
    }

    function validatePasswordMatch() {
        const password = passwordInput.value;
        const confirmPassword = confirmPasswordInput.value;
        const matchMessage = document.querySelector('.passwords-match-message');
        
        if (confirmPassword) {
            matchMessage.classList.remove('hidden');
            if (password === confirmPassword) {
                matchMessage.textContent = '✓ Passwords match';
                matchMessage.classList.add('text-green-500');
                matchMessage.classList.remove('text-red-500');
            } else {
                matchMessage.textContent = '✗ Passwords do not match';
                matchMessage.classList.add('text-red-500');
                matchMessage.classList.remove('text-green-500');
            }
        } else {
            matchMessage.classList.add('hidden');
        }
    }

    if (passwordInput) {
        passwordInput.addEventListener('input', validatePassword);
    }
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', validatePasswordMatch);
    }
});
</script>
{% endblock %} 