{% extends "admin/base_site.html" %}
{% load i18n admin_urls static admin_modify %}

{% block extrahead %}{{ block.super }}
<script src="{% static 'admin/js/admin/RelatedObjectLookups.js' %}"></script>
{{ media }}
{% endblock %}

{% block extrastyle %}{{ block.super }}
<link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}">
<style>
    .trainer-item {
        padding: 10px;
        margin-bottom: 5px;
        border: 1px solid #ddd;
        border-radius: 5px;
        display: flex;
        align-items: center;
    }
    .trainer-item.selected {
        background-color: #e5f3ff;
        border-color: #79aec8;
    }
    .trainer-status {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        color: white;
        margin-left: 10px;
        font-size: 12px;
    }
    .status-READY { background-color: #28a745; }
    .status-BUSY { background-color: #dc3545; }
    .status-BOOKED { background-color: #fd7e14; }
</style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
<a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
&rsaquo; <a href="{% url 'admin:app_list' app_label=opts.app_label %}">{{ opts.app_config.verbose_name }}</a>
&rsaquo; <a href="{% url 'admin:website_course_changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
&rsaquo; <a href="{% url 'admin:website_course_change' object_id=course.course_id %}">{{ course.name_en }}</a>
&rsaquo; {% trans 'Assign Trainers' %}
</div>
{% endblock %}

{% block content %}
<div id="content-main">
    <form method="post" id="assign-trainers-form">
        {% csrf_token %}
        <div class="module aligned">
            <h2>{{ title }}</h2>
            
            <div class="form-row">
                <div class="fieldBox">
                    <label class="required">{% trans 'Course' %}:</label>
                    <strong>{{ course.name_en }}</strong> (ID: {{ course.course_id }})
                </div>
            </div>
            
            <div class="form-row">
                <div class="fieldBox">
                    <label class="required">{% trans 'Currently assigned trainers' %}:</label>
                    {% for trainer in course.trainer_set.all %}
                        <div>{{ trainer.user.get_full_name }} ({{ trainer.user.username }})</div>
                    {% empty %}
                        <div><em>{% trans 'No trainers currently assigned' %}</em></div>
                    {% endfor %}
                </div>
            </div>
            
            <div class="form-row">
                <div class="fieldBox">
                    <label class="required">{% trans 'Available trainers' %}:</label>
                    <div style="margin-top: 10px;">
                        {% for trainer in trainers %}
                            <div class="trainer-item {% if trainer.trainer_id in current_trainers %}selected{% endif %}">
                                <input type="checkbox" name="trainers" value="{{ trainer.trainer_id }}" id="trainer_{{ trainer.trainer_id }}"
                                       {% if trainer.trainer_id in current_trainers %}checked{% endif %}>
                                <label for="trainer_{{ trainer.trainer_id }}" style="margin-left: 10px;">
                                    {{ trainer.user.get_full_name }} ({{ trainer.user.username }})
                                </label>
                                <span class="trainer-status status-{{ trainer.status }}">{{ trainer.status }}</span>
                            </div>
                        {% empty %}
                            <div><em>{% trans 'No trainers available' %}</em></div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </div>
        
        <div class="submit-row">
            <input type="submit" value="{% trans 'Save' %}" class="default" name="_save">
            <a href="{% url 'admin:website_course_change' object_id=course.course_id %}" class="closelink">{% trans 'Cancel' %}</a>
        </div>
    </form>
</div>
{% endblock %} 