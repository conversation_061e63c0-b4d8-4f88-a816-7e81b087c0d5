{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Join Waiting List" %} | GB Academy{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="max-w-lg mx-auto bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10">
            <h2 class="text-xl font-semibold text-white">{% trans "No Available Seats" %}</h2>
        </div>
        
        <div class="p-6">
            <div class="mb-6">
                <div class="bg-yellow-900/20 backdrop-blur-md rounded-lg border border-yellow-800/30 p-4 mb-6">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            <svg class="h-5 w-5 text-yellow-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                            </svg>
                        </div>
                        <div class="ml-3">
                            <p class="text-yellow-300 font-semibold">{% trans "Sorry, no seats available!" %}</p>
                            <p class="text-yellow-200 text-sm mt-1">
                                {% trans "Unfortunately, all seats for this course have been filled while you were waiting to pay." %}
                            </p>
                        </div>
                    </div>
                </div>
                
                <h3 class="text-lg font-medium text-white mb-3">{% trans "Course Information" %}</h3>
                <div class="bg-white/5 rounded-lg p-4 mb-6">
                    <p class="text-white font-medium">{{ course_instance.course.name_en }}</p>
                    <p class="text-sm text-gray-400 mt-1">{% trans "Category" %}: {{ course_instance.course.get_category_display }}</p>
                    <p class="text-sm text-gray-400">{% trans "Dates" %}: {{ course_instance.start_date|date:"M d, Y" }} - {{ course_instance.end_date|date:"M d, Y" }}</p>
                    <p class="text-sm text-gray-400">{% trans "Sessions" %}: {{ course_instance.sessions.count }}</p>
                    <p class="text-sm text-gray-400">{% trans "Waiting List" %}: {{ course_instance.waiting_list_count }} {% trans "people" %}</p>
                </div>
                
                <h3 class="text-lg font-medium text-white mb-3">{% trans "Would you like to join the waiting list?" %}</h3>
                <p class="text-gray-300 mb-6">
                    {% trans "You can join the waiting list for this course. If a spot becomes available, you will be automatically notified." %}
                </p>
                
                <div class="flex flex-col space-y-4">
                    <form method="POST" action="{% url 'website:confirm_payment' reservation.reservation_id %}">
                        {% csrf_token %}
                        <input type="hidden" name="join_waiting_list" value="yes">
                        <button type="submit" class="w-full inline-flex justify-center py-3 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                            {% trans "Yes, Join Waiting List" %}
                        </button>
                    </form>
                    
                    <form method="POST" action="{% url 'website:confirm_payment' reservation.reservation_id %}">
                        {% csrf_token %}
                        <input type="hidden" name="join_waiting_list" value="no">
                        <button type="submit" class="w-full inline-flex justify-center py-3 px-4 border border-gray-600 shadow-sm text-sm font-medium rounded-md text-white bg-transparent hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500">
                            {% trans "No, Cancel My Reservation" %}
                        </button>
                    </form>
                    
                    <a href="{% url 'website:user_reservations' %}" class="text-center text-sm text-gray-400 hover:text-white">
                        {% trans "Return to My Reservations" %}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 