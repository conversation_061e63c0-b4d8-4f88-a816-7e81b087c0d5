{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "My Surveys" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    .survey-card {
        transition: all 0.3s ease;
        border-left: 4px solid transparent;
    }
    
    .survey-card:hover {
        border-left-color: #4f46e5;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.2);
        transform: translateY(-2px);
    }
    
    .tab-button {
        transition: all 0.2s ease;
    }
    
    .tab-button.active {
        background-color: rgba(79, 70, 229, 0.9) !important;
        border-color: rgba(129, 140, 248, 1) !important;
        color: white !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            {% trans "My Surveys" %}
        </h1>
        <p class="text-indigo-300 mt-2">{% trans "View and complete your course feedback surveys" %}</p>
    </div>

    <!-- Tab Navigation -->
    <div class="flex border-b border-indigo-600/30 mb-6">
        <button id="pending-tab" class="tab-button active px-4 py-2 bg-indigo-800/50 text-white font-medium rounded-t-lg mr-2">
            {% trans "Pending Surveys" %}
            <span id="pending-count" class="ml-2 px-2 py-1 bg-indigo-600 text-white text-xs rounded-full">{{ pending_responses.count }}</span>
        </button>
        <button id="completed-tab" class="tab-button px-4 py-2 bg-indigo-900/40 text-gray-300 font-medium rounded-t-lg">
            {% trans "Completed Surveys" %}
            <span id="completed-count" class="ml-2 px-2 py-1 bg-gray-600 text-white text-xs rounded-full">{{ completed_responses.count }}</span>
        </button>
    </div>

    <!-- Pending Surveys Section -->
    <div id="pending-surveys" class="space-y-4">
        {% if pending_responses %}
            {% for response in pending_responses %}
                <div class="survey-card bg-indigo-900/50 backdrop-blur-md rounded-lg border border-indigo-600/40 p-5 shadow-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-white">{{ response.survey.title }}</h3>
                            <p class="text-indigo-300 mt-1">
                                {% trans "Course" %}: {{ response.reservation.course_instance.course.name_en }}
                            </p>
                            <p class="text-gray-400 text-sm mt-1">
                                {% trans "Completed on" %}:
                                {{ response.reservation.completed_at|date:"F j, Y" }}
                            </p>
                            <div class="mt-3 flex items-center text-sm">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/20 text-yellow-300">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    {% trans "Pending Response" %}
                                </span>
                                <span class="ml-2 text-gray-400">
                                    {{ response.survey.questions|length }} {% trans "questions" %}
                                </span>
                            </div>
                        </div>
                        <a href="{% url 'website:gb_take_survey' survey_id=response.survey.survey_id reservation_id=response.reservation.reservation_id %}" 
                           class="bg-indigo-600 hover:bg-indigo-700 text-white font-medium py-2 px-4 rounded-lg shadow-md transition-all transform hover:scale-105">
                            {% trans "Complete Survey" %}
                        </a>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-10 bg-indigo-900/30 backdrop-blur-sm rounded-lg border border-indigo-600/20 shadow-lg">
                <svg class="mx-auto h-12 w-12 text-indigo-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-white">{% trans "All Caught Up!" %}</h3>
                <p class="mt-2 text-gray-300">{% trans "You have no pending surveys to complete." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Completed Surveys Section (Hidden by Default) -->
    <div id="completed-surveys" class="space-y-4 hidden">
        {% if completed_responses %}
            {% for response in completed_responses %}
                <div class="survey-card bg-gray-800/70 backdrop-blur-md rounded-lg border border-gray-700 p-5 shadow-lg">
                    <div class="flex justify-between items-start">
                        <div>
                            <h3 class="text-lg font-semibold text-white">{{ response.survey.title }}</h3>
                            <p class="text-blue-300 mt-1">
                                {% trans "Course" %}: {{ response.reservation.course_instance.course.name_en }}
                            </p>
                            <div class="mt-3 flex items-center text-sm">
                                <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/20 text-green-300">
                                    <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                    </svg>
                                    {% trans "Completed" %}
                                </span>
                                <span class="ml-4 text-gray-400">
                                    {% trans "Completed on" %}: {{ response.completed_at|date:"F j, Y" }}
                                </span>
                            </div>
                        </div>
                        <span class="text-gray-400 text-sm">
                            {{ response.survey.questions|length }} {% trans "questions answered" %}
                        </span>
                    </div>
                </div>
            {% endfor %}
        {% else %}
            <div class="text-center py-10 bg-gray-800/50 backdrop-blur-sm rounded-lg border border-gray-700 shadow-lg">
                <svg class="mx-auto h-12 w-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                </svg>
                <h3 class="mt-4 text-lg font-medium text-white">{% trans "No Completed Surveys" %}</h3>
                <p class="mt-2 text-gray-300">{% trans "You haven't completed any surveys yet." %}</p>
            </div>
        {% endif %}
    </div>

    <!-- Return Button -->
    <div class="mt-8 flex justify-center">
        <a href="{% url 'website:gb_user_reservations_view' %}" class="inline-flex items-center px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-md shadow transition-colors">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"></path>
            </svg>
            {% trans "Back to My Reservations" %}
        </a>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tab switching functionality
        const pendingTab = document.getElementById('pending-tab');
        const completedTab = document.getElementById('completed-tab');
        const pendingSurveys = document.getElementById('pending-surveys');
        const completedSurveys = document.getElementById('completed-surveys');
        
        pendingTab.addEventListener('click', function() {
            pendingTab.classList.add('active', 'bg-indigo-800/50');
            pendingTab.classList.remove('bg-indigo-900/40', 'text-gray-300');
            completedTab.classList.remove('active', 'bg-indigo-800/50');
            completedTab.classList.add('bg-indigo-900/40', 'text-gray-300');
            
            pendingSurveys.classList.remove('hidden');
            completedSurveys.classList.add('hidden');
        });
        
        completedTab.addEventListener('click', function() {
            completedTab.classList.add('active', 'bg-indigo-800/50');
            completedTab.classList.remove('bg-indigo-900/40', 'text-gray-300');
            pendingTab.classList.remove('active', 'bg-indigo-800/50');
            pendingTab.classList.add('bg-indigo-900/40', 'text-gray-300');
            
            completedSurveys.classList.remove('hidden');
            pendingSurveys.classList.add('hidden');
        });
    });
</script>
{% endblock %} 