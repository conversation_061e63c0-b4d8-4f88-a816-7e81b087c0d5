{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-5">
    <!-- <PERSON> Header -->
    <div class="flex justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" d="M15 19.128a9.38 9.38 0 002.625.372 9.337 9.337 0 004.121-.952 4.125 4.125 0 00-7.533-2.493M15 19.128v-.003c0-1.113-.285-2.16-.786-3.07M15 19.128v.106A12.318 12.318 0 018.624 21c-2.331 0-4.512-.645-6.374-1.766l-.001-.109a6.375 6.375 0 0111.964-3.07M12 6.375a3.375 3.375 0 11-6.75 0 3.375 3.375 0 016.75 0zm8.25 2.25a2.625 2.625 0 11-5.25 0 2.625 2.625 0 015.25 0z" />
            </svg>
            {{ page_title }}
        </h1>
        <button id="addUserBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
            </svg>
            {% trans "Add Users to Course" %}
        </button>
    </div>

    <!-- Corporate Info Card -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 p-6 mb-4">
        <h2 class="text-lg font-semibold text-white mb-4">{% trans "Corporate Information" %}</h2>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
                <p class="text-sm font-medium text-white/70">{% trans "Corporate Name" %}</p>
                <p class="text-base text-white">{{ corporate.legal_name }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-white/70">{% trans "Phone Number" %}</p>
                <p class="text-base text-white">{{ corporate.phone_number }}</p>
            </div>
            <div>
                <p class="text-sm font-medium text-white/70">{% trans "Corporate Capacity" %}</p>
                <p class="text-base text-white">{{ corporate.capacity }}</p>
            </div>
        </div>
    </div>

    <!-- Users Table -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
        <div class="p-6 border-b border-white/10">
            <h2 class="text-lg font-semibold text-white">{% trans "Corporate Users" %}</h2>
            <p class="mt-1 text-sm text-white/70">{% trans "Manage users who belong to your corporate account." %}</p>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                            {% trans "Name" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                            {% trans "Username" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                            {% trans "Email" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                            {% trans "Type" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                            {% trans "Joined" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white/70 uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody id="usersTableBody" class="bg-white/5 divide-y divide-white/10">
                    <!-- Users will be loaded here by JavaScript -->
                    <tr>
                        <td colspan="6" class="px-6 py-4 text-center text-sm text-white/70">
                            {% trans "Loading users..." %}
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add User Modal -->
<div id="addUserModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-[#2a3f6e] rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full border border-white/20">
            <form id="addUserForm" class="p-6">
                <div class="mb-6">
                    <h3 class="text-lg font-medium text-white">{% trans "Add New Corporate Users" %}</h3>
                    <p class="mt-1 text-sm text-white/70">{% trans "Add multiple users at once. Each user will be added as a corporate trainee with default name John Doe and a username derived from their email. You can also enroll them in a corporate course." %}</p>
                </div>
                
                <!-- Email input with add button -->
                <div class="mb-4 flex space-x-2">
                    <div class="flex-grow">
                        <label for="email" class="block text-sm font-medium text-white/80">{% trans "Email" %} *</label>
                        <input type="email" name="email" id="email" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-white/20 rounded-md bg-[#172b56] text-white">
                        <p class="text-xs text-white/50 mt-1">{% trans "Username will be extracted from email address." %}</p>
                    </div>
                    <div class="flex items-end">
                        <button type="button" id="addEmailBtn" class="mb-1 px-4 py-2 border border-transparent rounded-md text-white bg-primary hover:bg-primary/90 focus:outline-none">
                            <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                            </svg>
                        </button>
                    </div>
                </div>
                
                <!-- Course Instance Dropdown -->
                <div class="mb-4">
                    <label for="courseInstance" class="block text-sm font-medium text-white/80">{% trans "Enroll in Course" %}</label>
                    <select id="courseInstance" name="courseInstance" class="mt-1 focus:ring-primary focus:border-primary block w-full shadow-sm sm:text-sm border-white/20 rounded-md bg-[#172b56] text-white">
                        <option value="">{% trans "-- Select a course (optional) --" %}</option>
                        <!-- Course instances will be loaded here via JavaScript -->
                    </select>
                    <p class="text-xs text-white/50 mt-1">{% trans "If selected, users will be automatically enrolled in this course." %}</p>
                </div>
                
                <!-- Email list section -->
                <div class="mb-6">
                    <label class="block text-sm font-medium text-white/80 mb-2">{% trans "Emails to Add" %}</label>
                    <div id="emailList" class="bg-white/5 rounded-md border border-white/10 p-3 min-h-[100px] max-h-[200px] overflow-y-auto">
                        <p id="emptyEmailList" class="text-white/50 text-sm text-center py-2">{% trans "No emails added yet" %}</p>
                        <ul id="emailListItems" class="space-y-2"></ul>
                    </div>
                </div>
                
                <div>
                    <p class="text-sm text-red-400 hidden" id="formError"></p>
                </div>
                <div class="mt-5 sm:mt-6 sm:grid sm:grid-cols-2 sm:gap-3 sm:grid-flow-row-dense">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:col-start-2 sm:text-sm">
                        {% trans "Add Users" %}
                    </button>
                    <button type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-white/30 shadow-sm px-4 py-2 bg-white/10 text-base font-medium text-white hover:bg-white/20 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:col-start-1 sm:text-sm" id="cancelAddUser">
                        {% trans "Cancel" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Delete User Confirmation Modal -->
<div id="deleteUserModal" class="fixed inset-0 z-50 overflow-y-auto hidden" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 bg-gray-900 bg-opacity-75 transition-opacity" aria-hidden="true"></div>
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="sm:flex sm:items-start">
                    <div class="mx-auto flex-shrink-0 flex items-center justify-center h-12 w-12 rounded-full bg-red-100 sm:mx-0 sm:h-10 sm:w-10">
                        <svg class="h-6 w-6 text-red-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                        </svg>
                    </div>
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                        <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                            {% trans "Deactivate User" %}
                        </h3>
                        <div class="mt-2">
                            <p class="text-sm text-gray-500">
                                {% trans "Are you sure you want to deactivate this user? This user will no longer be able to access the system. This action cannot be undone." %}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                <button type="button" id="confirmDeleteUser" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-red-600 text-base font-medium text-white hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Deactivate" %}
                </button>
                <button type="button" id="cancelDeleteUser" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                    {% trans "Cancel" %}
                </button>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Load the users
        loadUsers();

        // Add user modal functionality
        const addUserBtn = document.getElementById('addUserBtn');
        const addUserModal = document.getElementById('addUserModal');
        const cancelAddUserBtn = document.getElementById('cancelAddUser');
        const addUserForm = document.getElementById('addUserForm');
        const formError = document.getElementById('formError');
        
        // Multiple email input functionality
        const emailInput = document.getElementById('email');
        const addEmailBtn = document.getElementById('addEmailBtn');
        const emailList = document.getElementById('emailList');
        const emailListItems = document.getElementById('emailListItems');
        const emptyEmailList = document.getElementById('emptyEmailList');
        
        // Array to store emails
        let emailsToAdd = [];

        // Delete user modal functionality
        const deleteUserModal = document.getElementById('deleteUserModal');
        const cancelDeleteUserBtn = document.getElementById('cancelDeleteUser');
        const confirmDeleteUserBtn = document.getElementById('confirmDeleteUser');
        let userToDelete = null;

        // Show add user modal
        addUserBtn.addEventListener('click', function() {
            addUserModal.classList.remove('hidden');
            formError.classList.add('hidden');
            addUserForm.reset();
            
            // Clear email list
            emailsToAdd = [];
            updateEmailListDisplay();
            
            // Load course instances
            loadCourseInstances();
        });

        // Hide add user modal
        cancelAddUserBtn.addEventListener('click', function() {
            addUserModal.classList.add('hidden');
        });
        
        // Function to load course instances
        function loadCourseInstances() {
            fetch('{% url "website:get_corporate_course_instances" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const courseInstanceSelect = document.getElementById('courseInstance');
                        
                        // Clear existing options except the first one
                        while (courseInstanceSelect.options.length > 1) {
                            courseInstanceSelect.remove(1);
                        }
                        
                        // Add course instances to dropdown
                        if (data.course_instances && data.course_instances.length > 0) {
                            data.course_instances.forEach(instance => {
                                // Only add instances that have available seats
                                if (instance.available_seats > 0) {
                                    const option = document.createElement('option');
                                    option.value = instance.instance_id;
                                    option.textContent = `${instance.course_name} (${instance.start_date} - ${instance.end_date}) - ${instance.available_seats} seats available`;
                                    courseInstanceSelect.appendChild(option);
                                }
                            });
                        } else {
                            const option = document.createElement('option');
                            option.disabled = true;
                            option.textContent = '{% trans "No course instances available" %}';
                            courseInstanceSelect.appendChild(option);
                        }
                    } else {
                        console.error('Error loading course instances:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error loading course instances:', error);
                });
        }
        
        // Add email to the list
        addEmailBtn.addEventListener('click', function() {
            const email = emailInput.value.trim();
            
            if (!email) {
                formError.textContent = '{% trans "Please enter an email address" %}';
                formError.classList.remove('hidden');
                return;
            }
            
            // Basic email validation
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                formError.textContent = '{% trans "Please enter a valid email address" %}';
                formError.classList.remove('hidden');
                return;
            }
            
            // Check if email already exists in the list
            if (emailsToAdd.includes(email)) {
                formError.textContent = '{% trans "This email is already in the list" %}';
                formError.classList.remove('hidden');
                return;
            }
            
            // Add email to array
            emailsToAdd.push(email);
            
            // Update the display
            updateEmailListDisplay();
            
            // Clear input and hide error
            emailInput.value = '';
            formError.classList.add('hidden');
        });
        
        // Update the email list display
        function updateEmailListDisplay() {
            if (emailsToAdd.length === 0) {
                emptyEmailList.classList.remove('hidden');
                emailListItems.innerHTML = '';
                return;
            }
            
            emptyEmailList.classList.add('hidden');
            emailListItems.innerHTML = '';
            
            emailsToAdd.forEach((email, index) => {
                const li = document.createElement('li');
                li.className = 'flex justify-between items-center bg-white/10 rounded px-3 py-2';
                li.innerHTML = `
                    <span class="text-white text-sm">${email}</span>
                    <button type="button" class="text-white/70 hover:text-white" data-index="${index}">
                        <svg class="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                `;
                emailListItems.appendChild(li);
                
                // Add click event to remove button
                li.querySelector('button').addEventListener('click', function() {
                    const indexToRemove = parseInt(this.getAttribute('data-index'));
                    emailsToAdd.splice(indexToRemove, 1);
                    updateEmailListDisplay();
                });
            });
        }

        // Handle add user form submission
        addUserForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            if (emailsToAdd.length === 0) {
                formError.textContent = '{% trans "Please add at least one email address" %}';
                formError.classList.remove('hidden');
                return;
            }
            
            // Process all emails in the list sequentially
            processEmailsSequentially(0);
        });
        
        // Process emails one by one
        function processEmailsSequentially(index) {
            if (index >= emailsToAdd.length) {
                // All emails processed, close modal and reload users
                addUserModal.classList.add('hidden');
                loadUsers();
                return;
            }
            
            // Get selected course instance
            const courseInstanceSelect = document.getElementById('courseInstance');
            const selectedCourseInstanceId = courseInstanceSelect.value;
            
            const userData = {
                email: emailsToAdd[index],
                course_instance_id: selectedCourseInstanceId || null
            };
            
            // Add user with current email
            fetch('{% url "website:add_corporate_user" %}', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': '{{ csrf_token }}'
                },
                body: JSON.stringify(userData)
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Construct success message with password if available
                    let successMessage = `${data.message}: ${emailsToAdd[index]}`;
                    
                    // Add password if provided (for new users)
                    if (data.user && data.user.password) {
                        {% comment %} successMessage += ` - Password: ${data.user.password}`; {% endcomment %}
                    }
                    
                    // Add enrollment message if provided
                    if (data.enrollment_message) {
                        successMessage += ` - ${data.enrollment_message}`;
                    }
                    
                    showNotification(successMessage, 'success');
                    
                    // Process next email
                    processEmailsSequentially(index + 1);
                } else {
                    // Show error in form
                    formError.textContent = `Error adding ${emailsToAdd[index]}: ${data.message || '{% trans "Error adding user" %}'}`;
                    formError.classList.remove('hidden');
                    
                    // Continue with next email
                    processEmailsSequentially(index + 1);
                }
            })
            .catch(error => {
                console.error('Error adding user:', error);
                formError.textContent = `Error adding ${emailsToAdd[index]}: {% trans "An error occurred. Please try again." %}`;
                formError.classList.remove('hidden');
                
                // Continue with next email
                processEmailsSequentially(index + 1);
            });
        }

        // Show delete user modal
        function showDeleteUserModal(userId, userName) {
            deleteUserModal.classList.remove('hidden');
            userToDelete = userId;
        }

        // Hide delete user modal
        cancelDeleteUserBtn.addEventListener('click', function() {
            deleteUserModal.classList.add('hidden');
            userToDelete = null;
        });

        // Handle delete user confirmation
        confirmDeleteUserBtn.addEventListener('click', function() {
            if (userToDelete) {
                deleteUser(userToDelete);
            }
        });

        // API Functions
        function loadUsers() {
            fetch('{% url "website:corporate_users_api" %}')
                .then(response => response.json())
                .then(data => {
                    const usersTableBody = document.getElementById('usersTableBody');
                    
                    if (data.users && data.users.length > 0) {
                        usersTableBody.innerHTML = '';
                        
                        data.users.forEach(user => {
                            const row = document.createElement('tr');
                            row.className = 'hover:bg-white/10 transition-colors duration-150';
                            
                            row.innerHTML = `
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">${user.first_name} ${user.last_name}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white/80">${user.username}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white/80">${user.email}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                                        ${user.user_type_display || '{% trans "User" %}'}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white/80">
                                    ${user.date_joined}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <button class="text-red-600 hover:text-red-900 delete-user-btn" data-user-id="${user.user_id}" data-user-name="${user.first_name} ${user.last_name}">
                                        {% trans "Deactivate" %}
                                    </button>
                                </td>
                            `;
                            usersTableBody.appendChild(row);
                        });
                        
                        // Add event listeners to delete buttons
                        document.querySelectorAll('.delete-user-btn').forEach(btn => {
                            btn.addEventListener('click', function() {
                                const userId = this.getAttribute('data-user-id');
                                const userName = this.getAttribute('data-user-name');
                                showDeleteUserModal(userId, userName);
                            });
                        });
                    } else {
                        usersTableBody.innerHTML = `
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-sm text-white/70">
                                    {% trans "No users found" %}
                                </td>
                            </tr>
                        `;
                    }
                })
                .catch(error => {
                    console.error('Error loading users:', error);
                    const usersTableBody = document.getElementById('usersTableBody');
                    usersTableBody.innerHTML = `
                        <tr>
                            <td colspan="6" class="px-6 py-4 text-center text-sm text-red-400">
                                {% trans "Error loading users. Please try again." %}
                            </td>
                        </tr>
                    `;
                });
        }

        function deleteUser(userId) {
            fetch(`{% url "website:delete_corporate_user" 0 %}`.replace('0', userId), {
                method: 'DELETE',
                headers: {
                    'X-CSRFToken': '{{ csrf_token }}'
                }
            })
            .then(response => response.json())
            .then(data => {
                // Hide modal
                deleteUserModal.classList.add('hidden');
                userToDelete = null;
                
                if (data.status === 'success') {
                    // Reload users
                    loadUsers();
                    
                    // Show success message
                    showNotification('{% trans "User deactivated successfully" %}', 'success');
                } else {
                    // Show error message
                    showNotification(data.message || '{% trans "Error deactivating user" %}', 'error');
                }
            })
            .catch(error => {
                console.error('Error deleting user:', error);
                deleteUserModal.classList.add('hidden');
                userToDelete = null;
                showNotification('{% trans "An error occurred. Please try again." %}', 'error');
            });
        }

        // Helper function to show notifications
        function showNotification(message, type = 'info') {
            const container = document.getElementById('notification-container');
            const notification = document.createElement('div');
            
            notification.className = `notification ${type} animate-slide-in-right`;
            notification.innerHTML = `
                <div class="p-4 rounded-md shadow-md ${type === 'success' ? 'bg-green-100 border-green-400 text-green-800' : type === 'error' ? 'bg-red-100 border-red-400 text-red-800' : 'bg-blue-100 border-blue-400 text-blue-800'}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            ${type === 'success' 
                                ? '<svg class="h-5 w-5 text-green-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path></svg>'
                                : type === 'error'
                                ? '<svg class="h-5 w-5 text-red-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path></svg>'
                                : '<svg class="h-5 w-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path></svg>'
                            }
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">${message}</p>
                        </div>
                    </div>
                </div>
            `;
            
            container.appendChild(notification);
            
            // Check if this is a success message containing credentials and keep it visible longer
            const timeoutDuration = (type === 'success' && message.includes('Password')) ? 15000 : 5000;
            
            // Auto-remove after timeout duration
            setTimeout(() => {
                notification.classList.add('animate-slide-out-right');
                setTimeout(() => {
                    notification.remove();
                }, 500);
            }, timeoutDuration);
        }
    });
</script>

<style>
    #notification-container {
        position: fixed;
        top: 80px;
        right: 20px;
        z-index: 9999;
        max-width: 350px;
    }
    
    .notification {
        margin-bottom: 10px;
    }
    
    .animate-slide-in-right {
        animation: slideInRight 0.5s forwards;
    }
    
    .animate-slide-out-right {
        animation: slideOutRight 0.5s forwards;
    }
    
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
    
    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }
</style>
{% endblock %} 