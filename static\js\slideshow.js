document.addEventListener('DOMContentLoaded', function() {
    const slides = document.querySelectorAll('.slide');
    if (!slides || slides.length === 0) return;

    let currentSlide = 0;
    let isAnimating = false;

    function showSlide(index) {
        if (!slides[index] || !slides[currentSlide]) return;
        if (isAnimating) return;
        isAnimating = true;

        // Hide all slides except current and next
        slides.forEach((slide, i) => {
            if (slide && slide.style) {
                if (i !== currentSlide && i !== index) {
                    slide.style.visibility = 'hidden';
                    slide.style.opacity = '0';
                }
            }
        });

        // Make next slide visible but transparent
        if (slides[index].style) {
            slides[index].style.visibility = 'visible';
            slides[index].style.opacity = '0';
        }
        
        // Fade out current slide
        if (slides[currentSlide].style) {
            slides[currentSlide].style.opacity = '0';
        }
        
        // Fade in next slide
        setTimeout(() => {
            if (slides[index].style) {
                slides[index].style.opacity = '1';
            }
            currentSlide = index;
            isAnimating = false;
        }, 50);
    }

    function nextSlide() {
        const next = (currentSlide + 1) % slides.length;
        showSlide(next);
    }

    // Initialize first slide
    slides.forEach(slide => {
        if (slide && slide.style) {
            slide.style.visibility = 'hidden';
            slide.style.opacity = '0';
            slide.style.transition = 'opacity 0.5s ease-in-out';
        }
    });

    if (slides[0] && slides[0].style) {
        slides[0].style.visibility = 'visible';
        slides[0].style.opacity = '1';
    }

    // Start slideshow
    setInterval(nextSlide, 7000);
}); 