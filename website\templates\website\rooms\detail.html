{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ room.name }} - {% trans "Room Details" %}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Back Button -->
        <div class="mb-6">
            <a href="{% url 'website:rooms' %}" class="inline-flex items-center text-white/70 hover:text-white">
                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
                {% trans "Back to Rooms" %}
            </a>
        </div>

        <!-- Room Details Card -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <!-- Header -->
            <div class="px-6 py-4 border-b border-white/10">
                <div class="flex justify-between items-center">
                    <h1 class="text-2xl font-bold text-white">{{ room.name }}</h1>
                    <div class="flex space-x-3">
                        <button onclick="handleEditRoom({{ room.room_id }})" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-primary hover:bg-primary/90">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                            {% trans "Edit" %}
                        </button>
                        <button onclick="deleteRoom({{ room.room_id }})" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md text-sm font-medium text-white bg-red-600 hover:bg-red-700">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                            </svg>
                            {% trans "Delete" %}
                        </button>
                    </div>
                </div>
            </div>

            <!-- Content -->
            <div class="p-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Basic Information -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-white">{% trans "Basic Information" %}</h2>
                    <div class="bg-white/5 rounded-lg p-4 space-y-3">
                        <div>
                            <span class="text-white/70">{% trans "Room Type" %}</span>
                            <p class="text-white">{{ room.room_type.name }}</p>
                        </div>
                        <div>
                            <span class="text-white/70">{% trans "Capacity" %}</span>
                            <p class="text-white">{{ room.capacity }}</p>
                        </div>
                        <div>
                            <span class="text-white/70">{% trans "Status" %}</span>
                            <p class="text-white">{{ room.availability.get_status_display }}</p>
                        </div>
                    </div>
                </div>

                <!-- Fixed Equipment -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-white">{% trans "Fixed Equipment" %}</h2>
                    <div class="bg-white/5 rounded-lg p-4">
                        {% if room.fixed_equipment.exists %}
                            <div class="space-y-3">
                                {% for equipment in room.fixed_equipment.all %}
                                <div class="flex items-center justify-between p-2 bg-white/5 rounded">
                                    <div>
                                        <p class="text-white font-medium">{{ equipment.name }}</p>
                                        <p class="text-white/70 text-sm">{{ equipment.code }}</p>
                                    </div>
                                    <span class="px-2 py-1 text-xs font-medium rounded bg-primary/20 text-primary">
                                        {{ equipment.category.name }}
                                    </span>
                                </div>
                                {% endfor %}
                            </div>
                        {% else %}
                            <p class="text-white/70 text-center py-4">{% trans "No fixed equipment assigned" %}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Current Course Information -->
                <div class="space-y-4">
                    <h2 class="text-lg font-semibold text-white">{% trans "Current Course" %}</h2>
                    <div class="bg-white/5 rounded-lg p-4">
                        {% if room.current_course %}
                            <div class="space-y-3">
                                <div>
                                    <span class="text-white/70">{% trans "Course Name" %}</span>
                                    <p class="text-white">{{ room.current_course.name }}</p>
                                </div>
                                <div>
                                    <span class="text-white/70">{% trans "Time" %}</span>
                                    <p class="text-white">{{ room.current_course.start_time|time:"g:i A" }} - {{ room.current_course.end_time|time:"g:i A" }}</p>
                                </div>
                            </div>
                        {% else %}
                            <p class="text-white/70 text-center py-4">{% trans "No active course" %}</p>
                        {% endif %}
                    </div>
                </div>

                <!-- Description -->
                <div class="md:col-span-2 space-y-4">
                    <h2 class="text-lg font-semibold text-white">{% trans "Description" %}</h2>
                    <div class="bg-white/5 rounded-lg p-4">
                        <p class="text-white/90">{{ room.description|default:_("No description available") }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Include the room modal -->
{% include 'website/rooms/modal.html' %}

{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %}

{% block extra_js %}
<script>
    // Set these as window variables to be accessible from the modal
    window.isEditing = false;
    window.currentRoomId = {{ room.room_id }};

    async function handleEditRoom(roomId) {
        try {
            const response = await fetch(`{% url 'website:edit_room' room_id=1 %}`.replace('1', roomId), {
                headers: {
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });
            
            const data = await response.json();
            if (response.ok) {
                // Use the new function with room data
                openEditRoomModal(roomId, data.room);
            } else {
                throw new Error(data.message || '{% trans "Failed to load room details" %}');
            }
        } catch (error) {
            console.error('Error loading room:', error);
            alert('{% trans "An error occurred while loading the room" %}');
        }
    }

    async function deleteRoom(roomId) {
        if (!confirm('{% trans "Are you sure you want to delete this room?" %}')) {
            return;
        }

        try {
            const response = await fetch(`{% url 'website:delete_room' room_id=1 %}`.replace('1', roomId), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                }
            });

            const data = await response.json();
            if (response.ok) {
                window.location.href = '{% url "website:rooms" %}';
            } else {
                alert(data.message || '{% trans "Failed to delete room" %}');
            }
        } catch (error) {
            console.error('Error deleting room:', error);
            alert('{% trans "An error occurred while deleting the room" %}');
        }
    }

    function openRoomModal(editing = false) {
        window.isEditing = editing;
        const modal = document.getElementById('roomModal');
        if (modal) {
            modal.classList.remove('hidden');
        }
        
        // Toggle equipment section based on editing mode
        if (typeof toggleEquipmentSection === 'function') {
            toggleEquipmentSection(editing);
        } else {
            // Fallback if function not found
            const equipmentSection = document.getElementById('equipmentSection');
            const modalTitle = document.getElementById('modalTitle');
            
            if (editing) {
                if (equipmentSection) equipmentSection.classList.remove('hidden');
                if (modalTitle) modalTitle.textContent = '{% trans "Edit Room" %}';
            } else {
                if (equipmentSection) equipmentSection.classList.add('hidden');
                if (modalTitle) modalTitle.textContent = '{% trans "Create Room" %}';
            }
        }
    }

    function closeRoomModal() {
        document.getElementById('roomModal').classList.add('hidden');
        document.getElementById('roomForm').reset();
        window.isEditing = false;
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }
</script>
{% endblock %} 