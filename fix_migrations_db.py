#!/usr/bin/env python
"""Script to fix Django migration records in the database."""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'project.settings')
django.setup()

# Now we can import Django models
from django.db import connection

def fix_migrations():
    """Fix the migration records in the database."""
    with connection.cursor() as cursor:
        # Get all website migrations to see what we're dealing with
        cursor.execute("SELECT id, app, name, applied FROM django_migrations WHERE app = 'website' ORDER BY id;")
        migrations = cursor.fetchall()
        
        print("Current migration records:")
        for migration in migrations:
            print(f"ID: {migration[0]}, App: {migration[1]}, Name: {migration[2]}, Applied: {migration[3]}")
        
        # Delete all migrations from 0019 and onward (we'll reapply them)
        cursor.execute(
            "DELETE FROM django_migrations WHERE app = 'website' AND name >= '0019_' AND name <= '0040_';"
        )
        print("Cleared problematic migration records")
        
        # Add our fixed migration 0019
        cursor.execute(
            "INSERT INTO django_migrations (app, name, applied) VALUES ('website', '0019_fix_merge', datetime('now'));"
        )
        print("Added merged migration: 0019_fix_merge")
        
        # Add post-0019 migrations back to the database
        migrations_to_mark_as_applied = [
            '0021_alter_course_room_type',
            '0022_event_required_equipment_event_room_type',
            '0023_remove_course_num_of_days_and_more', 
            '0024_event_end_time_event_room_event_start_time',
            '0025_equipment_cleanup',
            '0026_category_brand_model',
            '0027_remove_event_room_type_alter_equipment_code',
            '0028_remove_equipment_stock_model_stock',
            '0029_session_required_equipment',
            '0030_room_fixed_equipment',
            '0031_alter_equipmentavailability_status',
            '0032_remove_traineravailability_busy_slots_and_more',
            '0033_room_availability_structure',
            '0034_remove_equipmentavailability_busy_slots_and_more',
            '0035_alter_equipment_status_alter_session_slot_type',
            '0036_remove_course_room_type_course_room_types',
            '0037_remove_session_trainer_session_trainers'
        ]
        
        for migration in migrations_to_mark_as_applied:
            cursor.execute(
                "INSERT INTO django_migrations (app, name, applied) VALUES ('website', %s, datetime('now'));",
                [migration]
            )
            print(f"Added migration record: {migration}")

if __name__ == "__main__":
    try:
        fix_migrations()
        print("Migration fix operation completed.")
        print("Now run 'python manage.py migrate' to check if it worked.")
    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1) 