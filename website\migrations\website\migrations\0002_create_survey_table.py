from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('website', 'PREVIOUS_MIGRATION_NAME'),  # Replace with your most recent migration name
    ]

    operations = [
        migrations.CreateModel(
            name='Survey',
            fields=[
                ('survey_id', models.AutoField(primary_key=True, serialize=False)),
                ('title', models.Char<PERSON>ield(help_text='The main title of the survey.', max_length=200, verbose_name='Title')),
                ('questions', models.JSONField(blank=True, default=list, help_text='Survey questions in JSON format', null=True, verbose_name='Questions')),
                ('start_date', models.DateTimeField(blank=True, null=True, verbose_name='Start Date')),
                ('is_active', models.BooleanField(default=True, verbose_name='Is Active')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='Created At')),
                ('updated_at', models.DateTime<PERSON>ield(auto_now=True, verbose_name='Updated At')),
                ('course_instance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='surveys', to='website.courseinstance', verbose_name='Course Instance')),
            ],
            options={
                'verbose_name': 'Survey',
                'verbose_name_plural': 'Surveys',
                'ordering': ['-created_at'],
            },
        ),
    ]