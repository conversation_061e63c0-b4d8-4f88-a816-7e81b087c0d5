"""project URL Configuration

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/3.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.conf.urls.i18n import i18n_patterns
from django.conf.urls.static import static
from django.contrib import admin
from django.contrib.auth import views as auth_views
from django.urls import path, include
from django.views.generic import RedirectView

from project import settings
from website import otp_views

urlpatterns = [
                  path('dzJAMvwB/', admin.site.urls),
                  path('i18n/', include('django.conf.urls.i18n')),
                  path(
                      'password_reset/',
                      auth_views.PasswordResetView.as_view(),
                      name='admin_password_reset',
                  ),
                  path(
                      'password_reset/done/',
                      auth_views.PasswordResetDoneView.as_view(),
                      name='password_reset_done',
                  ),
                  path(
                      'reset/<uidb64>/<token>/',
                      auth_views.PasswordResetConfirmView.as_view(),
                      name='password_reset_confirm',
                  ),
                  path(
                      'reset/done/',
                      auth_views.PasswordResetCompleteView.as_view(),
                      name='password_reset_complete',
                  ),
                  path('accounts/login/', RedirectView.as_view(url='/admin/', permanent=False)),
                  path('api/v1/', include('api.urls')),
                  # OTP API endpoints (no i18n prefix needed)
                  path('api/otp/request/', otp_views.request_otp_api, name='request_otp_api'),
                  path('api/otp/verify/', otp_views.verify_otp_api, name='verify_otp_api'),
                  path('api/otp/resend/', otp_views.resend_otp_api, name='resend_otp_api'),
              ] + static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

urlpatterns += i18n_patterns(
    path('', include('website.urls')),
)

# handler404 = 'website.views.page_not_found_view'

if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
