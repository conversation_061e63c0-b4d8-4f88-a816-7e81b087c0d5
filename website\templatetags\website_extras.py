from django import template

register = template.Library()

@register.filter
def get_item(dictionary, key):
    """
    Template filter to get a dictionary value by key.
    Usage: {{ dictionary|get_item:key }}
    """
    if dictionary is None:
        return None
    return dictionary.get(str(key))

@register.filter
def status_to_color_class(status):
    """
    Converts a status string to a corresponding color class for UI display.
    """
    status_colors = {
        'WAITING_TO_PAY': 'bg-yellow-200 text-yellow-800',
        'WAITING_LIST': 'bg-blue-200 text-blue-800',
        'UPCOMING': 'bg-green-200 text-green-800',
        'IN_PROGRESS': 'bg-indigo-200 text-indigo-800',
        'COMPLETED': 'bg-gray-400 text-gray-800',
        'CANCELLED': 'bg-red-200 text-red-800',
        'PENDING': 'bg-yellow-200 text-yellow-800',
        'APPROVED': 'bg-green-200 text-green-800',
        'REJECTED': 'bg-red-200 text-red-800',
    }
    return status_colors.get(status, 'bg-gray-200 text-gray-800') 