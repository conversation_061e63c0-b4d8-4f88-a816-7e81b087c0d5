#!/usr/bin/env python
"""Script to reset migration state in the database - direct approach."""
import os
import sqlite3
import sys

def reset_migrations():
    """Reset the migration state in the database."""
    # Connect directly to the SQLite database
    conn = sqlite3.connect('db.sqlite3')
    cursor = conn.cursor()
    
    try:
        print("Starting migration reset...")
        
        # Clear all website migrations from the database
        cursor.execute("DELETE FROM django_migrations WHERE app = 'website';")
        print("Deleted all website migration records from database.")
        
        # Insert all migrations as if they were applied successfully
        migrations = [
            '0001_initial',
            '0002_add_course_status',
            '0002_add_room_types',
            '0003_attendance_corporate_event_form_formcontent_and_more',
            '0004_alter_roomavailability_options_room_description_and_more',
            '0005_alter_session_options_session_created_at_and_more',
            '0006_alter_session_location',
            '0007_remove_course_id_alter_course_course_id',
            '0008_alter_traineravailability_options_and_more',
            '0009_alter_traineravailability_start_time_and_more',
            '0010_squashed_fix',
            '0011_eventtype_event_event_type',
            '0012_session_slot_type',
            '0013_remove_roomavailability_end_time_and_more',
            '0016_room_type_restructure',
            '0017_merge_0002_add_room_types_0016_room_type_restructure',
            '0018_alter_roomtype_room_type_id',
            '0019_fix_merge',
            '0021_alter_course_room_type',
            '0022_event_required_equipment_event_room_type',
            '0023_remove_course_num_of_days_and_more',
            '0024_event_end_time_event_room_event_start_time',
            '0025_equipment_cleanup',
            '0026_category_brand_model',
            '0027_remove_event_room_type_alter_equipment_code',
            '0028_remove_equipment_stock_model_stock',
            '0029_session_required_equipment',
            '0030_room_fixed_equipment',
            '0031_alter_equipmentavailability_status',
            '0032_remove_traineravailability_busy_slots_and_more',
            '0033_room_availability_structure',
            '0034_remove_equipmentavailability_busy_slots_and_more',
            '0035_alter_equipment_status_alter_session_slot_type',
            '0036_remove_course_room_type_course_room_types',
            '0037_remove_session_trainer_session_trainers',
        ]
        
        for migration in migrations:
            cursor.execute(
                "INSERT INTO django_migrations (app, name, applied) VALUES ('website', ?, datetime('now'));",
                (migration,)
            )
            print(f"Added migration record: {migration}")
        
        # Commit the changes to the database
        conn.commit()
        print("Migration reset completed successfully!")
        print("Now Django will see all migrations as already applied.")
        
    except Exception as e:
        conn.rollback()
        print(f"Error: {e}")
        sys.exit(1)
    finally:
        conn.close()

if __name__ == "__main__":
    reset_migrations() 