{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-[#172b56]">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto">
        <div class="flex justify-between items-center mb-4">
            <div>
                <h1 class="text-3xl font-bold text-white">{% trans "Corporate Admin Dashboard" %}</h1>
                <p class="text-gray-300">{% trans "Welcome back" %} {{ request.user.get_full_name }}!</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'website:my_corporate_users' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                    {% trans "Manage Users" %}
                </a>
                <a href="{% url 'website:corporate_admin_calendar' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary hover:bg-secondary/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {% trans "Calendar" %}
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20 mb-8">
            <h2 class="text-xl font-semibold text-white mb-4">{% trans "Quick Actions" %}</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <a href="{% url 'website:my_corporate_users' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-indigo-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Manage Users" %}</span>
                </a>
                
                <a href="{% url 'website:my_corporate_reservations' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-yellow-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "User Reservations" %}</span>
                </a>
                
                <a href="{% url 'website:corporate_admin_calendar' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-blue-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Calendar" %}</span>
                </a>
                
                <a href="{% url 'website:my_corporate_requests' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-pink-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h3.75M9 15h3.75M9 18h3.75m3 .75H18a2.25 2.25 0 002.25-2.25V6.108c0-1.135-.845-2.098-1.976-2.192a48.424 48.424 0 00-1.123-.08m-5.801 0c-.065.21-.1.433-.1.664 0 .414.336.75.75.75h4.5a.75.75 0 00.75-.75 2.25 2.25 0 00-.1-.664m-5.8 0A2.251 2.251 0 0113.5 2.25H15c1.012 0 1.867.668 2.15 1.586m-5.8 0c-.376.023-.75.05-1.124.08C9.095 4.01 8.25 4.973 8.25 6.108V8.25m0 0H4.875c-.621 0-1.125.504-1.125 1.125v11.25c0 .621.504 1.125 1.125 1.125h9.75c.621 0 1.125-.504 1.125-1.125V9.375c0-.621-.504-1.125-1.125-1.125H8.25z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Manage Requests" %}</span>
                </a>
                
                <a href="{% url 'website:profile' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-teal-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Profile Settings" %}</span>
                </a>
            </div>
        </div>

        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <!-- Users in Training -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Users in Training" %}</p>
                        <p class="text-xs text-white/50 mb-1">{% trans "Corporate users currently in active sessions" %}</p>
                        <p class="text-2xl font-bold text-white" id="users-in-training-count">
                            <span class="animate-pulse">•••</span>
                        </p>
                    </div>
                    <div class="p-3 bg-blue-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Total Corporate Users -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Total Corporate Users" %}</p>
                        <p class="text-xs text-white/50 mb-1">{% trans "All users in your corporate" %}</p>
                        <p class="text-2xl font-bold text-white" id="total-corporate-users-count">
                            <span class="animate-pulse">•••</span>
                        </p>
                    </div>
                    <div class="p-3 bg-yellow-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notification Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <!-- Corporate Info -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">{% trans "Corporate Info" %}</h2>
                    <div class="p-2 bg-indigo-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
                
                <!-- stacked layout -->
                <div class="space-y-4">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Company Name" %}</p>
                        <p class="text-xl font-bold text-white">{{ corporate }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Admin Email" %}</p>
                        <p class="text-md font-medium text-white/90">{{ corporate_admin.user.email }}</p>
                    </div>
                </div>
            </div>

            <!-- Pending Actions -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-purple-800/40 to-pink-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "Pending Actions" %}</h2>
                        </div>
                        <div class="p-2 bg-red-500/20 rounded-full">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="p-6">
                    <div class="grid grid-cols-2 gap-4">
                        <a href="{% url 'website:my_corporate_reservations' %}" class="col-span-2 block p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                            <p class="text-sm font-medium text-white/60">{% trans "User Cancellation Requests" %}</p>
                            <p class="text-2xl font-bold text-white flex items-center justify-between" id="user-cancellation-count">
                                {{ corporate_cancellation_requests }}
                                {% if corporate_cancellation_requests > 0 %}
                                <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-purple-500 rounded-full">{{ corporate_cancellation_requests }}</span>
                                {% endif %}
                            </p>
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sessions -->
        <div class="grid grid-cols-1 gap-4 mb-8">
            <!-- In Progress Sessions -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-blue-800/40 to-indigo-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "In Progress Sessions" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-green-500/30 text-green-300 border border-green-500/30">{{ in_progress_sessions.count }} {% trans "Active" %}</span>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Course" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Trainer" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Room" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Time" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white/5 divide-y divide-white/10">
                            {% if in_progress_sessions %}
                                {% for session in in_progress_sessions %}
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.course.name_en }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {% for trainer in session.trainers.all %}
                                                {{ trainer.user.first_name }} {{ trainer.user.last_name }}{% if not forloop.last %}, {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.room.name|default:"Online" }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.start_date|date:"H:i" }} - {{ session.end_date|date:"H:i" }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-white/70">
                                        {% trans "No sessions currently in progress" %}
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Upcoming Events Section -->
        <div class="grid grid-cols-1 gap-4 mb-8">
            <!-- Upcoming Events -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-indigo-800/40 to-blue-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "Upcoming Events" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-indigo-500/30 text-indigo-300 border border-indigo-500/30">{% trans "Next 5" %}</span>
                    </div>
                </div>
                <div id="dashboardUpcomingEvents" class="p-4 space-y-2 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                    <div class="flex items-center justify-center h-24">
                        <div class="text-white/70 flex items-center">
                            <div class="animate-spin mr-3 h-5 w-5 border-2 border-white/20 border-t-white rounded-full"></div>
                            {% trans "Loading events..." %}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Debug context variables
    console.log('Context variables:');
    console.log('corporate_admin:', '{{ corporate_admin }}');
    console.log('corporate:', '{{ corporate }}');
    console.log('corporate_name:', '{{ corporate.name }}');
    console.log('corporate_cancellation_requests:', {{ corporate_cancellation_requests|default:"'none'" }});
    console.log('emergency_cancellation_requests:', {{ emergency_cancellation_requests|default:"'none'" }});
    console.log('pending_new_course_requests:', {{ pending_new_course_requests|default:"'none'" }});
    console.log('total_corporate_users:', {{ total_corporate_users|default:"'none'" }});
    console.log('users_in_training:', {{ users_in_training|default:"'none'" }});
    console.log('in_progress_sessions count:', {{ in_progress_sessions.count|default:"'none'" }});
    console.log('upcoming_sessions count:', {{ upcoming_sessions.count|default:"'none'" }});
    
    document.addEventListener('DOMContentLoaded', function() {
        // Function to initialize dashboard stats with server-rendered data
        function initializeDashboardStats() {
            // Set initial values from server-rendered data
            document.getElementById('users-in-training-count').textContent = '{{ users_in_training }}';
            document.getElementById('total-corporate-users-count').textContent = '{{ total_corporate_users }}';
            
            // Update notification count for user cancellation requests
            const userCancellationCount = document.getElementById('user-cancellation-count');
            if (userCancellationCount) { // Check if element exists before manipulating it
                userCancellationCount.innerHTML = '{{ corporate_cancellation_requests }}';
                {% if corporate_cancellation_requests > 0 %}
                userCancellationCount.innerHTML += `<span class="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-purple-500 rounded-full">{{ corporate_cancellation_requests }}</span>`;
                {% endif %}
            }
            
            // Fetch the latest cancellation request count from API
            fetchCancellationRequestsCount();
        }
        
        // Function to fetch cancellation requests count from API
        function fetchCancellationRequestsCount() {
            fetch('{% url "website:get_cancellation_requests_count_api" %}')
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status === 'success') {
                        // Update the cancellation requests count
                        const userCancellationCount = document.getElementById('user-cancellation-count');
                        if (userCancellationCount) {
                            userCancellationCount.innerHTML = data.count;
                            if (data.count > 0) {
                                userCancellationCount.innerHTML += `<span class="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-purple-500 rounded-full">${data.count}</span>`;
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching cancellation requests count:', error);
                });
        }
        
        // Function to initialize in-progress sessions with server-rendered data
        function initializeInProgressSessions() {
            // Update active sessions count
            document.getElementById('active-sessions-count').textContent = 
                '{{ in_progress_sessions.count }} {% trans "Active" %}';
            
            // Get sessions container
            const container = document.getElementById('in-progress-sessions-container');
            
            // Create table HTML
            let tableHTML = `
                <table class="min-w-full divide-y divide-white/10">
                    <thead class="bg-white/5">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Course" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Trainer" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Room" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Time" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Trainees" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white/5 divide-y divide-white/10">
            `;
            
            {% if in_progress_sessions %}
                {% for session in in_progress_sessions %}
                tableHTML += `
                    <tr>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            {{ session.course.name_en }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            {% for trainer in session.trainers.all %}
                                {{ trainer.user.first_name }} {{ trainer.user.last_name }}{% if not forloop.last %}, {% endif %}
                            {% endfor %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            {{ session.room.name|default:"Online" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            {{ session.start_date|date:"H:i" }} - {{ session.end_date|date:"H:i" }}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                            {{ session.course_instance.reservation_set.count }}
                        </td>
                    </tr>
                `;
                {% endfor %}
            {% else %}
                tableHTML += `
                    <tr>
                        <td colspan="5" class="px-6 py-4 text-center text-sm text-white/70">
                            {% trans "No sessions currently in progress" %}
                        </td>
                    </tr>
                `;
            {% endif %}
            
            tableHTML += `
                    </tbody>
                </table>
            `;
            
            // Update container with table
            container.innerHTML = tableHTML;
        }
        
        // Function to fetch upcoming events
        function fetchUpcomingEvents() {
            // Get current date and end of week
            const today = new Date();
            const endOfWeek = new Date(today);
            endOfWeek.setDate(today.getDate() + 14); // Get events for next two weeks
            
            // Format dates for API
            const formatDate = (date) => {
                return date.toISOString().split('T')[0]; // YYYY-MM-DD format
            };
            
            // Build API URL
            const url = `{% url 'website:corporate_calendar_events_api' %}?start=${formatDate(today)}&end=${formatDate(endOfWeek)}&corporate={{ corporate.id }}`;
            
            // Show loading state
            const container = document.getElementById('dashboardUpcomingEvents');
            container.innerHTML = `
                <div class="flex items-center justify-center h-24">
                    <div class="text-white/70 flex flex-col items-center">
                        <div class="w-8 h-8 mb-2 relative">
                            <div class="absolute inset-0 rounded-full border-t-2 border-b-2 border-indigo-500/60 animate-spin"></div>
                            <div class="absolute inset-1 rounded-full border-r-2 border-l-2 border-indigo-300/40 animate-spin animation-delay-150"></div>
                            <div class="absolute inset-2 rounded-full border-t-2 border-indigo-400/50 animate-pulse"></div>
                        </div>
                        <p class="text-sm">{% trans "Loading events..." %}</p>
                    </div>
                </div>
            `;
            
            // Fetch events from API
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.events && Array.isArray(response.events)) {
                        // Display upcoming events
                        showUpcomingEvents(response.events);
                    } else {
                        throw new Error('No events found in response');
                    }
                })
                .catch(error => {
                    console.error('Error fetching events:', error);
                    document.getElementById('dashboardUpcomingEvents').innerHTML = `
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto mb-2 text-red-400/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-white/70">{% trans "Error loading events. Please try again later." %}</p>
                        </div>
                    `;
                });
        }
        
        // Function to display upcoming events
        function showUpcomingEvents(events) {
            const container = document.getElementById('dashboardUpcomingEvents');
            
            // Sort events by date
            const sortedEvents = [...events].sort((a, b) => {
                const dateA = new Date(a.start);
                const dateB = new Date(b.start);
                return dateA - dateB;
            });
            
            // Filter to only future events
            const today = new Date();
            const upcomingEvents = sortedEvents.filter(event => {
                const eventDate = new Date(event.start);
                return eventDate >= today;
            }).slice(0, 5); // Limit to 5 events
            
            // Clear container
            container.innerHTML = '';
            
            // Display upcoming events
            if (upcomingEvents.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-indigo-400/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p class="text-white/70">{% trans "No upcoming events" %}</p>
                    </div>
                `;
                return;
            }
            
            // Create elements for each event
            upcomingEvents.forEach(event => {
                const eventDate = new Date(event.start);
                const day = eventDate.getDate();
                const month = eventDate.toLocaleString('en-US', { month: 'short' });
                const time = eventDate.toLocaleString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                
                // Create event element
                const eventElement = document.createElement('div');
                eventElement.className = 'flex items-start space-x-4 p-3 rounded-lg hover:bg-white/10 transition-all duration-200 cursor-pointer border border-transparent hover:border-white/20';
                
                // Add click action
                eventElement.addEventListener('click', function() {
                    // Could add navigation to event details in the future
                    eventElement.classList.add('bg-white/5');
                    setTimeout(() => eventElement.classList.remove('bg-white/5'), 200);
                });
                
                // Date box
                const dateBox = document.createElement('div');
                dateBox.className = 'flex-shrink-0 w-12 text-center bg-white/5 rounded-lg py-1 border border-white/10';
                dateBox.innerHTML = `
                    <div class="text-sm font-semibold text-primary">${day}</div>
                    <div class="text-xs text-white/70">${month}</div>
                `;
                
                // Event details
                const eventDetails = document.createElement('div');
                eventDetails.className = 'flex-1 min-w-0';
                
                // Get icon based on event type
                let typeIcon, typeClass, typeBadgeClass;
                
                if (event.type === 'session') {
                    typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /></svg>';
                    typeClass = 'text-green-400';
                    typeBadgeClass = 'bg-green-400/20 text-green-400 border-green-400/20';
                } else if (event.type === 'holiday') {
                    typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>';
                    typeClass = 'text-red-400';
                    typeBadgeClass = 'bg-red-400/20 text-red-400 border-red-400/20';
                } else {
                    typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>';
                    typeClass = 'text-blue-400';
                    typeBadgeClass = 'bg-blue-400/20 text-blue-400 border-blue-400/20';
                }
                
                // Add holiday emoji for holidays
                const holidayPrefix = event.type === 'holiday' ? '🎉 ' : '';
                
                // For holidays, show "All day" instead of time
                const timeDisplay = event.type === 'holiday' ? 'All day' : time;
                
                // Location info if available
                const locationDisplay = event.room ? `· ${event.room}` : '';
                
                // Show title and info with improved styling
                eventDetails.innerHTML = `
                    <p class="text-sm font-medium text-white truncate">${holidayPrefix}${event.title || 'Untitled Event'}</p>
                    <div class="flex items-center mt-1">
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${typeBadgeClass}">
                            ${typeIcon}
                            ${event.type || 'Event'}
                        </span>
                        <span class="mx-1.5 text-white/40">•</span>
                        <span class="text-xs text-white/70">${timeDisplay} ${locationDisplay}</span>
                    </div>
                `;
                
                // Add elements to container
                eventElement.appendChild(dateBox);
                eventElement.appendChild(eventDetails);
                container.appendChild(eventElement);
            });
        }
        
        // Initialize data when page loads
        initializeDashboardStats();
        fetchUpcomingEvents();
        
        // Refresh events and cancellation requests count every 5 minutes
        setInterval(fetchUpcomingEvents, 300000);
        setInterval(fetchCancellationRequestsCount, 300000);
    });
</script>
{% endblock %} 