{% comment %} 

note 
it got handeld in trainer/partials/attendance.html

{% endcomment %}


{% comment %} {% extends 'website/base.html' %}
{% load i18n %}
{% load static %}

{% block title %}{% trans "Manage Attendance" %} | {% trans "Trainer Dashboard" %}{% endblock %}

{% block content %}
<div class="py-10">
    <header>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center">
                <h1 class="text-3xl font-bold leading-tight text-white flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 mr-3 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
                    </svg>
                    {% trans "Manage Attendance" %}
                </h1>
                <a href="{% url 'website:trainer_sessions_view' %}" class="inline-flex items-center px-4 py-2 border border-gray-600 rounded-md shadow-sm text-sm font-medium text-white bg-gray-700 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-150">
                    <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                    {% trans "Back to Sessions" %}
                </a>
            </div>
        </div>
    </header>
    <main>
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            {% if messages %}
                <div class="mt-6">
                    {% for message in messages %}
                        <div class="p-4 mb-4 {% if message.tags == 'success' %}bg-green-800/70 border border-green-600{% elif message.tags == 'error' %}bg-red-800/70 border border-red-600{% else %}bg-blue-800/70 border border-blue-600{% endif %} rounded-md shadow-lg transform transition-all animate-fade-in-down">
                            <div class="flex items-center">
                                <div class="flex-shrink-0">
                                    {% if message.tags == 'success' %}
                                        <svg class="h-5 w-5 text-green-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                        </svg>
                                    {% elif message.tags == 'error' %}
                                        <svg class="h-5 w-5 text-red-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                        </svg>
                                    {% else %}
                                        <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                        </svg>
                                    {% endif %}
                                </div>
                                <div class="ml-3">
                                    <p class="text-white font-medium">{{ message }}</p>
                                </div>
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% endif %}

            <div class="bg-gray-800 shadow-lg rounded-lg overflow-hidden mt-6 border border-gray-700">
                <div class="px-6 py-5 border-b border-gray-700 bg-gradient-to-r from-gray-800 to-gray-900">
                    <div class="flex flex-col md:flex-row md:justify-between md:items-center">
                        <div>
                            <h3 class="text-xl font-bold text-white flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                                {{ session.course.name_en }}
                            </h3>
                            
                            <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div class="flex items-center text-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 20l4-16m2 16l4-16M6 9h14M4 15h14" />
                                    </svg>
                                    <span class="font-medium mr-1">{% trans "Session ID:" %}</span> {{ session.session_id }}
                                </div>
                                
                                <div class="flex items-center text-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                                    </svg>
                                    <span class="font-medium mr-1">{% trans "Date:" %}</span> {{ session.start_date|date:"M d, Y" }}
                                </div>
                                
                                <div class="flex items-center text-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="font-medium mr-1">{% trans "Time:" %}</span> {{ session.start_date|time:"H:i" }} - {{ session.end_date|time:"H:i" }}
                                </div>
                                
                                <div class="flex items-center text-gray-300">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                                    </svg>
                                    <span class="font-medium mr-1">{% trans "Room:" %}</span>
                                    {% if session.room %}
                                        {{ session.room.name }}
                                    {% else %}
                                        {% trans "Online" %}
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 md:mt-0">
                            {% if current_time >= session.start_date and current_time <= session.end_date %}
                                <div class="inline-flex items-center px-4 py-2 bg-green-900/50 border border-green-700 rounded-lg shadow-sm">
                                    <span class="w-3 h-3 mr-2 rounded-full bg-green-500 animate-pulse"></span>
                                    <span class="text-green-300 font-semibold">{% trans "In Progress" %}</span>
                                </div>
                            {% elif current_time > session.end_date %}
                                <div class="inline-flex items-center px-4 py-2 bg-gray-700/50 border border-gray-600 rounded-lg shadow-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                    <span class="text-gray-300 font-semibold">{% trans "Completed" %}</span>
                                </div>
                            {% else %}
                                <div class="inline-flex items-center px-4 py-2 bg-blue-900/50 border border-blue-700 rounded-lg shadow-sm">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <span class="text-blue-300 font-semibold">{% trans "Upcoming" %}</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>

                {% if users_attendance %}
                    <div class="flex items-center justify-between px-6 py-3 bg-gray-700/30 border-t border-b border-gray-700">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                            </svg>
                            <span class="font-semibold text-white">{{ users_attendance|length }}</span>
                            <span class="ml-1 text-gray-300">{% trans "registered users" %}</span>
                        </div>

                        {% if session_started %}
                        <div class="text-sm text-gray-400">
                            <span class="inline-flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                                {% trans "Attendance can be marked" %}
                            </span>
                        </div>
                        {% endif %}
                    </div>
                    <form method="post" action="{% url 'website:trainer_session_attendance' session_id=session.session_id %}">
                        {% csrf_token %}
                        <div class="px-6 py-5">
                            <h4 class="font-semibold text-lg text-white mb-4 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                                </svg>
                                {% trans "Attendees" %} ({{ users_attendance|length }})
                            </h4>
                            
                            {% if not session_started %}
                                <div class="mb-6 bg-blue-900/30 border border-blue-900 rounded-md p-4">
                                    <div class="flex">
                                        <div class="flex-shrink-0">
                                            <svg class="h-5 w-5 text-blue-400" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2h-1V9a1 1 0 00-1-1z" clip-rule="evenodd" />
                                            </svg>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-sm font-medium text-blue-400">{% trans "Note" %}</h3>
                                            <div class="mt-2 text-sm text-blue-300">
                                                <p>{% trans "Attendance can only be marked once the session has started." %}</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            {% endif %}
                            
                            <div class="overflow-x-auto rounded-lg border border-gray-700">
                                <table class="min-w-full divide-y divide-gray-700">
                                    <thead class="bg-gray-700/50">
                                        <tr>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Name" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Email" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "First Half" %}
                                            </th>
                                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                                                {% trans "Second Half" %}
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody class="divide-y divide-gray-700 bg-gray-800">
                                        {% for user_data in users_attendance %}
                                            <tr class="hover:bg-gray-700 transition-colors duration-150">
                                                <td class="px-6 py-4 whitespace-nowrap">
                                                    <div class="flex items-center">
                                                        <div class="flex-shrink-0 h-10 w-10 rounded-full bg-gray-700 flex items-center justify-center">
                                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                            </svg>
                                                        </div>
                                                        <div class="ml-4">
                                                            <div class="text-sm font-medium text-white">
                                                                {{ user_data.user.get_full_name }}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-300">
                                                    <div class="flex items-center">
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                                                        </svg>
                                                        {{ user_data.user.email }}
                                                    </div>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                                    <label class="inline-flex items-center justify-center space-x-3 cursor-pointer {% if not session_started %}opacity-50 cursor-not-allowed{% endif %}">
                                                        <input type="checkbox" 
                                                               name="first_half_{{ user_data.user.id }}" 
                                                               id="first_half_{{ user_data.user.id }}"
                                                               class="h-5 w-5 rounded border-gray-500 bg-gray-700 text-blue-600 focus:ring-blue-600 focus:ring-offset-gray-800" 
                                                               {% if user_data.first_half %}checked{% endif %}
                                                               {% if not session_started %}disabled{% endif %}>
                                                        <span class="text-white font-medium">{% trans "Present" %}</span>
                                                    </label>
                                                </td>
                                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                                    <label class="inline-flex items-center justify-center space-x-3 cursor-pointer {% if not session_started %}opacity-50 cursor-not-allowed{% endif %}">
                                                        <input type="checkbox" 
                                                               name="second_half_{{ user_data.user.id }}" 
                                                               id="second_half_{{ user_data.user.id }}"
                                                               class="h-5 w-5 rounded border-gray-500 bg-gray-700 text-blue-600 focus:ring-blue-600 focus:ring-offset-gray-800" 
                                                               {% if user_data.second_half %}checked{% endif %}
                                                               {% if not session_started %}disabled{% endif %}>
                                                        <span class="text-white font-medium">{% trans "Present" %}</span>
                                                    </label>
                                                </td>
                                            </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            {% if session_started %}
                                <div class="mt-6 flex justify-end">
                                    <button type="submit" class="inline-flex items-center px-5 py-2.5 border border-blue-700 rounded-md shadow-sm text-sm font-medium text-white bg-blue-700 hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-all duration-150">
                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        {% trans "Save Attendance" %}
                                    </button>
                                </div>
                            {% endif %}
                        </div>
                    </form>
                {% else %}
                    <div class="px-6 py-12 text-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-gray-600 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                        <p class="text-gray-400 text-lg">{% trans "No attendees found for this session." %}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </main>
</div>
{% endblock %}  {% endcomment %}