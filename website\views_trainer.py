from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from functools import wraps
from django.db import transaction
from django.http import JsonResponse
from django.views.decorators.http import require_GET, require_http_methods

from .models import (
    Session, Trainer, Attendance, CourseInstance, Reservation, CustomUser,
    Course, Room, Equipment, Questionnaire, QuestionnaireResponse, SurveyResponse,
    Survey
)

# Import email utility functions
from trainer_email_utils import (
    send_attendance_update_email,
    send_questionnaire_review_notification,
    send_new_questionnaire_notification,
    send_session_management_notification,
    send_bulk_attendance_notification
)

import logging
import json
from .decorators import super_admin_or_staff
logger = logging.getLogger(__name__)

def trainer_required(view_func):
    """
    Decorator for views that checks that the user is a trainer.
    """
    @wraps(view_func)
    def _wrapped_view(request, *args, **kwargs):
        # Super admin bypass
        if request.user.is_authenticated and request.user.user_type and request.user.user_type.code == 'SUPER_ADMIN':
            return view_func(request, *args, **kwargs)
            
        if not request.user.is_authenticated:
            messages.error(request, _('Please log in first.'))
            return redirect('login')
        if not hasattr(request.user, 'trainer'):
            messages.error(request, _('You must be a trainer to access this page.'))
            return redirect('website:courses')
        return view_func(request, *args, **kwargs)
    return _wrapped_view

@trainer_required
def trainer_sessions_view(request):
    """
    View for trainers to see all their assigned course instances and sessions.
    Allows trainers to view and manage attendance for sessions.
    """
    trainer = request.user.trainer
    
    # Get all sessions assigned to this trainer
    trainer_sessions = Session.objects.filter(
        trainers=trainer,
        is_active=True
    ).select_related('course', 'room').prefetch_related('course_instance')
    
    # Group sessions by course instance
    instances_data = {}
    for session in trainer_sessions:
        # Get all course instances for this session
        for instance in session.course_instance.all():
            if instance.is_active:
                if instance.instance_id not in instances_data:
                    instances_data[instance.instance_id] = {
                        'instance': instance,
                        'sessions': []
                    }
                
                # Count registered users for this session/instance
                user_count = Reservation.objects.filter(
                    course_instance=instance,
                    status__in=['UPCOMING', 'IN_PROGRESS', 'COMPLETED']
                ).count()
                
                # Add session info including start/end time
                session_data = {
                    'session': session,
                    'in_progress': timezone.now() >= session.start_date and timezone.now() <= session.end_date,
                    'completed': timezone.now() > session.end_date,
                    'user_count': user_count
                }
                instances_data[instance.instance_id]['sessions'].append(session_data)
    
    # Convert the dictionary to a list for the template
    instances_list = list(instances_data.values())
    
    # Sort instances by start date
    instances_list.sort(key=lambda x: x['instance'].start_date)
    
    # For each instance, sort sessions by start date
    for instance_data in instances_list:
        instance_data['sessions'].sort(key=lambda x: x['session'].start_date)
    
    context = {
        'instances_list': instances_list,
        'trainer': trainer
    }
    
    return render(request, 'website/trainer/sessions.html', context)

@trainer_required
def trainer_session_attendance(request, session_id):
    """
    View for trainers to manage attendance for a specific session.
    """
    trainer = request.user.trainer
    session = get_object_or_404(Session, session_id=session_id, trainers=trainer)
    
    # Get all course instances for this session
    course_instances = session.course_instance.all()
    
    # Get all users who have reservations for these course instances
    reservations = Reservation.objects.filter(
        course_instance__in=course_instances,
        status__in=['UPCOMING', 'IN_PROGRESS', 'CONFIRMED']
    ).select_related('user', 'course_instance')
    
    # Get existing attendance records
    attendance_records = {
        (attendance.user_id, attendance.session_id): attendance
        for attendance in Attendance.objects.filter(session=session)
    }
    
    # Prepare user list with attendance status
    users_attendance = []
    for reservation in reservations:
        attendance_record = attendance_records.get((reservation.user.id, session.session_id))
        users_attendance.append({
            'user': reservation.user,
            'reservation': reservation,
            'attendance': attendance_record,
            'first_half': attendance_record.first_half if attendance_record else False,
            'second_half': attendance_record.second_half if attendance_record else False
        })
    
    # Sort by user's name
    users_attendance.sort(key=lambda x: x['user'].get_full_name())
    
    if request.method == 'POST':
        for user_data in users_attendance:
            user_id = user_data['user'].id
            first_half = f'first_half_{user_id}' in request.POST
            second_half = f'second_half_{user_id}' in request.POST
            
            attendance, created = Attendance.objects.update_or_create(
                user_id=user_id,
                session=session,
                defaults={
                    'first_half': first_half,
                    'second_half': second_half,
                    'trainer': trainer  # Adding trainer ID to attendance record
                }
            )
            
            # Send attendance update email to the user
            try:
                attendance_status = {
                    'first_half': first_half,
                    'second_half': second_half
                }
                course_name = session.course.name_en if session.course else "Training Session"
                session_date = session.start_date.strftime('%Y-%m-%d') if session.start_date else "scheduled date"
                
                send_attendance_update_email(
                    user=user_data['user'],
                    course_name=course_name,
                    session_date=session_date,
                    attendance_status=attendance_status
                )
            except Exception as e:
                logger.error(f"Failed to send attendance email to user {user_data['user'].email}: {str(e)}")
        
        messages.success(request, _('Attendance updated successfully.'))
        return redirect('website:trainer_session_attendance', session_id=session_id)
    
    context = {
        'session': session,
        'users_attendance': users_attendance,
        'current_time': timezone.now(),
        'session_started': timezone.now() >= session.start_date,
        'session_ended': timezone.now() > session.end_date,
        'trainer': trainer
    }
    
    return render(request, 'website/trainer/attendance.html', context)

@trainer_required
def add_questionnaire(request, course_id):
    """View to add a new questionnaire to a course"""
    logger = logging.getLogger(__name__)
    course = get_object_or_404(Course, course_id=course_id)
    is_supervisor = request.user.trainer.is_supervisor
    
    # Check if the trainer owns this course or is a supervisor
    if not is_supervisor and course not in request.user.trainer.courses.all():
        messages.error(request, _('You do not have permission to add questionnaires to this course.'))
        return redirect('website:courses')
    
    if request.method == 'POST':
        try:
            # Get form data
            title = request.POST.get('title')
            session_number = request.POST.get('session_number', 1)
            comments = request.POST.get('comments', '')
            
            # Validate title
            if not title:
                messages.error(request, _('Title is required.'))
                return render(request, 'website/courses/add_questionnaire.html', {'course': course})
            
            # Parse the questions JSON data from the form
            questions_data = []
            question_count = 0
            total_score = 0
            
            # Get all POST data for debugging
            logger.debug(f"POST data: {dict(request.POST)}")
            
            # Count how many questions we need to process
            for key in request.POST.keys():
                if key.startswith('questions[') and key.endswith('][text]'):
                    question_count += 1
            
            logger.debug(f"Found {question_count} questions to process")
            
            # Process each question
            for i in range(question_count):
                question_text = request.POST.get(f'questions[{i}][text]')
                question_type = request.POST.get(f'questions[{i}][type]', 'text')
                question_score = request.POST.get(f'questions[{i}][score]', '0')
                
                try:
                    question_score = int(question_score)
                    if question_score < 0:
                        question_score = 0
                except (ValueError, TypeError):
                    question_score = 0
                
                # Add to total score
                total_score += question_score
                
                if not question_text:
                    continue  # Skip empty questions
                
                question_data = {
                    'text': question_text,
                    'type': question_type,
                    'score': question_score
                }
                
                # If this is multiple choice or checkbox, get options
                if question_type in ['multiplechoice', 'checkbox']:
                    options = []
                    correct_answers = []
                    option_prefix = f'questions[{i}][options][]'
                    
                    # Get all options for this question from POST.getlist
                    option_values = request.POST.getlist(option_prefix)
                    logger.debug(f"Options for question {i}: {option_values}")
                    
                    # Get correct answers
                    correct_answer_keys = [k for k in request.POST.keys() if k.startswith(f'questions[{i}][correct_options]')]
                    
                    # Process each option
                    for j, option_value in enumerate(option_values):
                        if option_value.strip():
                            option_id = j + 1
                            # Create option object with proper structure
                            option_obj = {
                                "id": option_id,  # Generate sequential ID
                                "text": option_value.strip()
                            }
                            options.append(option_obj)
                            
                            # Check if this option is marked as correct
                            if f'questions[{i}][correct_options][{j}]' in request.POST:
                                correct_answers.append(option_id)
                    
                    question_data['options'] = options
                    question_data['correct_answers'] = correct_answers
                
                questions_data.append(question_data)
            
            logger.debug(f"Processed questions data: {questions_data}")
            
            # Create questionnaire object
            questionnaire = Questionnaire(
                title=title,
                course=course,
                session_number=session_number,
                questions=questions_data,
                comments=comments,
                created_by=request.user,
                user_id=request.user.id,  # Set user_id to match database schema
                total_score=total_score,
                is_active=True
            )
            questionnaire.save()
            
            # Send notifications to enrolled users
            try:
                # Get course instances for this course
                course_instances = CourseInstance.objects.filter(course=course, is_active=True)
                
                # Get users enrolled in these instances
                enrolled_users = CustomUser.objects.filter(
                    reservation__course_instance__in=course_instances,
                    reservation__status__in=['UPCOMING', 'IN_PROGRESS']
                ).distinct()
                
                # Notify each user
                for user in enrolled_users:
                    send_new_questionnaire_notification(
                        user=user,
                        course_name=course.name_en,
                        questionnaire_title=title
                    )
                
                logger.info(f"Sent new questionnaire notifications to {enrolled_users.count()} users")
            except Exception as e:
                logger.error(f"Failed to send new questionnaire notifications: {str(e)}")
            
            messages.success(request, _('Questionnaire added successfully!'))
            return redirect('website:course_content_list', course_id=course.course_id)
            
        except Exception as e:
            logger.error(f"Error creating questionnaire: {str(e)}")
            messages.error(request, _('An error occurred while saving the questionnaire.'))
    
    # Create a list of session numbers based on course.num_of_sessions
    session_numbers = range(1, course.num_of_sessions + 1)
    
    context = {
        'course': course,
        'session_numbers': session_numbers,
    }
    return render(request, 'website/courses/add_questionnaire.html', context)

@require_GET
@trainer_required
def get_course_questionnaires(request, course_id):
    """
    API endpoint to fetch all questionnaires for a specific course.
    Returns JSON response with questionnaire data.
    """
    logger.debug(f"Fetching questionnaires for course_id: {course_id}")
    
    try:
        # Get the course
        course = get_object_or_404(Course, course_id=course_id)
        logger.debug(f"Found course: {course.name_en}")
        
        # Check if the trainer has permission to view this course
        trainer = request.user.trainer
        is_supervisor = trainer.is_supervisor
        
        if not is_supervisor and course not in trainer.courses.all():
            logger.warning(f"User {request.user.username} does not have permission to view questionnaires for course {course_id}")
            return JsonResponse({
                'status': 'error',
                'message': _('You do not have permission to view questionnaires for this course.')
            }, status=403)
            
        # Get all questionnaires for this course
        questionnaires = Questionnaire.objects.filter(
            course=course,
            is_active=True
        ).order_by('-created_at')
        
        logger.debug(f"Found {questionnaires.count()} questionnaires for course {course_id}")
        
        # Prepare the response data
        questionnaires_data = []
        for q in questionnaires:
            questionnaires_data.append({
                'questionnaire_id': q.questionnaire_id,
                'title': q.title,
                'session_number': q.session_number,
                'questions_count': len(q.questions) if q.questions else 0,
                'comments': q.comments,
                'created_at': q.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                'created_by': q.created_by.get_full_name() if q.created_by else None,
            })
        
        response_data = {
            'status': 'success',
            'course': {
                'id': course.course_id,
                'name': course.name_en,
            },
            'questionnaires': questionnaires_data,
            'total_count': len(questionnaires_data)
        }
        
        logger.debug(f"Returning {len(questionnaires_data)} questionnaires for course {course_id}")
        return JsonResponse(response_data, safe=True)
        
    except Course.DoesNotExist:
        logger.error(f"Course with ID {course_id} not found")
        return JsonResponse({
            'status': 'error',
            'message': _('Course not found.')
        }, status=404)
    except Exception as e:
        logger.error(f"Error fetching questionnaires for course {course_id}: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching questionnaires.')
        }, status=500)

@trainer_required
def view_questionnaire(request, course_id, questionnaire_id):
    """View to display a specific questionnaire"""
    logger = logging.getLogger(__name__)
    
    try:
        course = get_object_or_404(Course, course_id=course_id)
        questionnaire = get_object_or_404(Questionnaire, questionnaire_id=questionnaire_id, course=course)
        
        # Check if the trainer owns this course or is a supervisor
        is_supervisor = request.user.trainer.is_supervisor
        
        if not is_supervisor and course not in request.user.trainer.courses.all():
            messages.error(request, _('You do not have permission to view this questionnaire.'))
            return redirect('website:courses')
            
        context = {
            'course': course,
            'questionnaire': questionnaire,
        }
        return render(request, 'website/courses/view_questionnaire.html', context)
    except Exception as e:
        logger.error(f"Error viewing questionnaire: {str(e)}")
        messages.error(request, _('An error occurred while viewing the questionnaire.'))
        return redirect('website:course_content_list', course_id=course_id)
        
@trainer_required
def edit_questionnaire(request, course_id, questionnaire_id):
    """View to edit an existing questionnaire"""
    logger = logging.getLogger(__name__)
    
    try:
        course = get_object_or_404(Course, course_id=course_id)
        questionnaire = get_object_or_404(Questionnaire, questionnaire_id=questionnaire_id, course=course)
        
        # Check if the trainer owns this course or is a supervisor
        is_supervisor = request.user.trainer.is_supervisor
        
        if not is_supervisor and course not in request.user.trainer.courses.all():
            messages.error(request, _('You do not have permission to edit this questionnaire.'))
            return redirect('website:courses')
            
        if request.method == 'POST':
            # Process the form data
            title = request.POST.get('title')
            session_number = request.POST.get('session_number', 1)
            comments = request.POST.get('comments', '')
            
            # Validate title
            if not title:
                messages.error(request, _('Title is required.'))
                return render(request, 'website/courses/edit_questionnaire.html', {
                    'course': course,
                    'questionnaire': questionnaire
                })
            
            # Get all POST data for debugging
            logger.debug(f"POST data: {dict(request.POST)}")
            
            # Process questions data from form
            questions_data = []
            question_count = 0
            total_score = 0
            
            # Count how many questions we need to process
            for key in request.POST.keys():
                if key.startswith('questions[') and key.endswith('][text]'):
                    question_count += 1
            
            logger.debug(f"Found {question_count} questions to process")
            
            # Process each question
            for i in range(question_count):
                question_text = request.POST.get(f'questions[{i}][text]')
                question_type = request.POST.get(f'questions[{i}][type]', 'text')
                question_score = request.POST.get(f'questions[{i}][score]', '0')
                
                try:
                    question_score = int(question_score)
                    if question_score < 0:
                        question_score = 0
                except (ValueError, TypeError):
                    question_score = 0
                
                # Add to total score
                total_score += question_score
                
                if not question_text:
                    continue  # Skip empty questions
                
                question_data = {
                    'text': question_text,
                    'type': question_type,
                    'score': question_score
                }
                
                # If this is multiple choice or checkbox, get options
                if question_type in ['multiplechoice', 'checkbox']:
                    options = []
                    correct_answers = []
                    option_prefix = f'questions[{i}][options][]'
                    
                    # Get all options for this question from POST.getlist
                    option_values = request.POST.getlist(option_prefix)
                    logger.debug(f"Options for question {i}: {option_values}")
                    
                    # Get correct answers
                    correct_answer_keys = [k for k in request.POST.keys() if k.startswith(f'questions[{i}][correct_options]')]
                    
                    # Process each option
                    for j, option_value in enumerate(option_values):
                        if option_value.strip():
                            option_id = j + 1
                            # Create option object with proper structure
                            option_obj = {
                                "id": option_id,  # Generate sequential ID
                                "text": option_value.strip()
                            }
                            options.append(option_obj)
                            
                            # Check if this option is marked as correct
                            if f'questions[{i}][correct_options][{j}]' in request.POST:
                                correct_answers.append(option_id)
                    
                    question_data['options'] = options
                    question_data['correct_answers'] = correct_answers
                
                questions_data.append(question_data)
            
            logger.debug(f"Processed questions data: {questions_data}")
            
            # Update questionnaire
            questionnaire.title = title
            questionnaire.session_number = session_number
            questionnaire.questions = questions_data
            questionnaire.comments = comments
            questionnaire.total_score = total_score
            questionnaire.save()
            
            messages.success(request, _('Questionnaire updated successfully!'))
            return redirect('website:course_content_list', course_id=course_id)
            
        # Create a list of session numbers based on course.num_of_sessions
        session_numbers = range(1, course.num_of_sessions + 1)
        
        context = {
            'course': course,
            'questionnaire': questionnaire,
            'session_numbers': session_numbers,
        }
        return render(request, 'website/courses/edit_questionnaire.html', context)
    except Exception as e:
        logger.error(f"Error editing questionnaire: {str(e)}")
        messages.error(request, _('An error occurred while editing the questionnaire.'))
        return redirect('website:course_content_list', course_id=course_id)

@trainer_required
def delete_questionnaire(request, course_id, questionnaire_id):
    """View to delete a questionnaire"""
    logger = logging.getLogger(__name__)
    
    try:
        course = get_object_or_404(Course, course_id=course_id)
        questionnaire = get_object_or_404(Questionnaire, questionnaire_id=questionnaire_id, course=course)
        
        # Check if the trainer owns this course or is a supervisor
        is_supervisor = request.user.trainer.is_supervisor
        
        if not is_supervisor and course not in request.user.trainer.courses.all():
            messages.error(request, _('You do not have permission to delete this questionnaire.'))
            return redirect('website:courses')
            
        if request.method == 'POST':
            # Soft delete the questionnaire
            questionnaire.is_active = False
            questionnaire.save()
            
            messages.success(request, _('Questionnaire deleted successfully!'))
            return redirect('website:course_content_list', course_id=course_id)
            
        context = {
            'course': course,
            'questionnaire': questionnaire,
        }
        return render(request, 'website/courses/delete_questionnaire.html', context)
    except Exception as e:
        logger.error(f"Error deleting questionnaire: {str(e)}")
        messages.error(request, _('An error occurred while deleting the questionnaire.'))
        return redirect('website:course_content_list', course_id=course_id)

@require_http_methods(["POST"])
@trainer_required
def delete_questionnaire_api(request, course_id, questionnaire_id):
    """API endpoint to delete a questionnaire"""
    logger = logging.getLogger(__name__)
    
    try:
        course = get_object_or_404(Course, course_id=course_id)
        questionnaire = get_object_or_404(Questionnaire, questionnaire_id=questionnaire_id, course=course)
        
        # Check if the trainer has permission to delete this questionnaire
        is_supervisor = request.user.trainer.is_supervisor
        
        if not is_supervisor and course not in request.user.trainer.courses.all():
            return JsonResponse({
                'status': 'error',
                'message': _('You do not have permission to delete this questionnaire.')
            }, status=403)
            
        # Soft delete the questionnaire
        questionnaire.is_active = False
        questionnaire.save()
        
        return JsonResponse({
            'status': 'success',
            'message': _('Questionnaire deleted successfully!')
        })
    except Course.DoesNotExist:
        logger.error(f"Course with ID {course_id} not found")
        return JsonResponse({
            'status': 'error',
            'message': _('Course not found.')
        }, status=404)
    except Questionnaire.DoesNotExist:
        logger.error(f"Questionnaire with ID {questionnaire_id} not found")
        return JsonResponse({
            'status': 'error',
            'message': _('Questionnaire not found.')
        }, status=404)
    except Exception as e:
        logger.error(f"Error deleting questionnaire via API: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while deleting the questionnaire.')
        }, status=500)

@require_GET
@trainer_required
def get_questionnaire_details(request, course_id, questionnaire_id):
    """
    API endpoint to fetch a specific questionnaire details.
    Returns JSON with full questionnaire data including questions.
    """
    logger.debug(f"Fetching questionnaire details for id: {questionnaire_id}")
    
    try:
        # Get the course and questionnaire
        course = get_object_or_404(Course, course_id=course_id)
        questionnaire = get_object_or_404(Questionnaire, questionnaire_id=questionnaire_id, course=course)
        
        # Check if the trainer has permission to view this course
        trainer = request.user.trainer
        is_supervisor = trainer.is_supervisor
        
        if not is_supervisor and course not in trainer.courses.all():
            return JsonResponse({
                'status': 'error',
                'message': _('You do not have permission to view this questionnaire.')
            }, status=403)
        
        # Prepare the questionnaire data including all questions
        questionnaire_data = {
            'questionnaire_id': questionnaire.questionnaire_id,
            'title': questionnaire.title,
            'session_number': questionnaire.session_number,
            'comments': questionnaire.comments,
            'questions': questionnaire.questions,
            'created_at': questionnaire.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'created_by': questionnaire.created_by.get_full_name() if questionnaire.created_by else None,
            'is_active': questionnaire.is_active
        }
        
        return JsonResponse({
            'status': 'success',
            'questionnaire': questionnaire_data
        })
        
    except Exception as e:
        logger.error(f"Error fetching questionnaire details: {str(e)}", exc_info=True)
        return JsonResponse({
            'status': 'error',
            'message': _('An error occurred while fetching questionnaire details.')
        }, status=500)

@trainer_required
def trainer_manage_session(request, session_id):
    """
    View for trainers to manage a specific session with tabs for attendance, surveys, and questionnaires.
    """
    trainer = request.user.trainer
    session = get_object_or_404(Session, session_id=session_id, trainers=trainer)
    
    # Get all course instances for this session
    course_instances = session.course_instance.all()
    
    # Get all users who have reservations for these course instances
    reservations = Reservation.objects.filter(
        course_instance__in=course_instances,
        status__in=['UPCOMING', 'IN_PROGRESS', 'COMPLETED']
    ).select_related('user', 'course_instance')
    
    # Get existing attendance records
    attendance_records = {
        (attendance.user_id, attendance.session_id): attendance
        for attendance in Attendance.objects.filter(session=session)
    }
    
    # Prepare user list with attendance status
    users_attendance = []
    for reservation in reservations:
        attendance_record = attendance_records.get((reservation.user.id, session.session_id))
        users_attendance.append({
            'user': reservation.user,
            'reservation': reservation,
            'attendance': attendance_record,
            'first_half': attendance_record.first_half if attendance_record else False,
            'second_half': attendance_record.second_half if attendance_record else False
        })
    
    # Sort by user's name
    users_attendance.sort(key=lambda x: x['user'].get_full_name())
    
    # Handle attendance form submission
    if request.method == 'POST' and 'attendance_submit' in request.POST:
        for user_data in users_attendance:
            user_id = user_data['user'].id
            first_half = f'first_half_{user_id}' in request.POST
            second_half = f'second_half_{user_id}' in request.POST
            
            attendance, created = Attendance.objects.update_or_create(
                user_id=user_id,
                session=session,
                defaults={
                    'first_half': first_half,
                    'second_half': second_half,
                    'trainer': trainer  # Adding trainer ID to attendance record
                }
            )
        
            # Send attendance update email to the user
            try:
                attendance_status = {
                    'first_half': first_half,
                    'second_half': second_half
                }
                course_name = session.course.name_en if session.course else "Training Session"
                session_date = session.start_date.strftime('%Y-%m-%d') if session.start_date else "scheduled date"
                
                send_attendance_update_email(
                    user=user_data['user'],
                    course_name=course_name,
                    session_date=session_date,
                    attendance_status=attendance_status
                )
            except Exception as e:
                logger.error(f"Failed to send attendance email to user {user_data['user'].email}: {str(e)}")
        
        messages.success(request, _('Attendance updated successfully.'))
        return redirect('website:trainer_manage_session', session_id=session_id)
    
    # Get questionnaires for this course
    questionnaires = Questionnaire.objects.filter(
        course=session.course,
        session_number=1,  # Assuming session number is 1 for simplicity
        is_active=True
    ).order_by('-created_at')
    
    # Get pending questionnaire responses for this session
    pending_responses = QuestionnaireResponse.objects.filter(
        session=session,
        status='PENDING_TRAINER_REVIEW'
    ).select_related('user', 'questionnaire')
    
    # Get survey data for all reservations in this session
    from django.db.models import Q
    
    # Get all reservations for these course instances
    all_reservations_ids = [r.reservation_id for r in reservations]
    
    # First get all survey responses for these reservations
    all_survey_responses = []
    if all_reservations_ids:
        all_survey_responses = list(SurveyResponse.objects.filter(
            reservation_id__in=all_reservations_ids
        ).select_related('survey', 'user', 'reservation'))
    
    # Get unique survey IDs from responses
    survey_ids = set([response.survey_id for response in all_survey_responses])
    
    # Only get surveys that have responses
    surveys = {}
    if survey_ids:
        for survey in Survey.objects.filter(survey_id__in=survey_ids).select_related('course_instance'):
            surveys[survey.survey_id] = survey
    
    # Create a lookup dictionary for survey responses
    survey_responses = {}
    for response in all_survey_responses:
        survey_responses[(response.survey_id, response.user_id)] = response
    
    # Create user survey status list
    users_survey_status = []
    for reservation in reservations:
        user_surveys = []
        for survey_id, survey in surveys.items():
            # Check if user has completed this survey
            response = survey_responses.get((survey_id, reservation.user.id))
            user_surveys.append({
                'survey': survey,
                'response': response,
                'completed': response and response.status == 'COMPLETED'
            })
        
        users_survey_status.append({
            'user': reservation.user,
            'reservation': reservation,
            'surveys': user_surveys
        })
    
    # Calculate survey submission stats
    survey_stats = {}
    for survey_id, survey in surveys.items():
        completed_count = 0
        for response in all_survey_responses:
            if response.survey_id == survey_id and response.status == 'COMPLETED':
                completed_count += 1
        
        survey_stats[survey_id] = {
            'survey': survey,
            'total_users': len(reservations),
            'completed_count': completed_count,
            'completion_percentage': int(completed_count / len(reservations) * 100) if reservations else 0
        }
    
    # Determine which tab is active
    active_tab = request.GET.get('tab', 'attendance')
    
    context = {
        'session': session,
        'users_attendance': users_attendance,
        'questionnaires': questionnaires,
        'pending_responses': pending_responses,
        'current_time': timezone.now(),
        'session_started': timezone.now() >= session.start_date,
        'session_ended': timezone.now() > session.end_date,
        'trainer': trainer,
        'active_tab': active_tab,
        'surveys': surveys.values(),
        'survey_stats': survey_stats,
        'users_survey_status': users_survey_status
    }
    
    return render(request, 'website/trainer/manage_session.html', context)

@trainer_required
def review_questionnaire_response(request, response_id):
    """
    View for trainers to review a questionnaire response with PENDING_TRAINER_REVIEW status.
    Allows them to view user answers, adjust scoring, and mark as reviewed.
    """
    trainer = request.user.trainer
    response = get_object_or_404(QuestionnaireResponse, response_id=response_id, status='PENDING_TRAINER_REVIEW')
    
    # Verify the trainer has access to this session/course
    if response.session:
        if not response.session.trainers.filter(pk=trainer.pk).exists():
            messages.error(request, _('You are not assigned to this session.'))
            return redirect('website:trainer_sessions')
    else:
        # If no session, check if trainer is authorized for this course
        if not trainer.courses.filter(pk=response.course.pk).exists() and not trainer.is_supervisor:
            messages.error(request, _('You are not authorized to review this questionnaire response.'))
            return redirect('website:trainer_sessions')
    
    # Get the questionnaire and user response data
    questionnaire = response.questionnaire
    user = response.user
    
    if request.method == 'POST':
        # Update the review
        adjusted_score = request.POST.get('adjusted_score', '')
        trainer_comment = request.POST.get('trainer_comment', '')
        
        try:
            # Update the score if provided
            if adjusted_score:
                adjusted_score = int(adjusted_score)
                if adjusted_score < 0:
                    adjusted_score = 0
                elif adjusted_score > response.max_total_score:
                    adjusted_score = response.max_total_score
                response.total_score = adjusted_score
            
            # Update trainer comment
            if 'trainer_comment' in request.POST:
                # Store trainer comment in the response data
                question_responses = response.question_responses.copy()
                question_responses['_trainer_comment'] = trainer_comment
                response.question_responses = question_responses
            
            # Mark as reviewed
            response.status = 'COMPLETED_TRAINER_REVIEWED'
            response.save()
            
            # Send notification to the user
            try:
                send_questionnaire_review_notification(
                    user=user,
                    questionnaire_title=questionnaire.title,
                    score=response.total_score,
                    max_score=response.max_total_score,
                    trainer_comment=trainer_comment
                )
                logger.info(f"Sent questionnaire review notification to {user.email}")
            except Exception as e:
                logger.error(f"Failed to send questionnaire review email to user {user.email}: {str(e)}")
            
            messages.success(request, _('Questionnaire response has been reviewed successfully.'))
            
            # Redirect back to the session management page
            if response.session:
                return redirect('website:trainer_manage_session', session_id=response.session.session_id)
            else:
                return redirect('website:trainer_sessions')
            
        except ValueError:
            messages.error(request, _('Invalid score value. Please enter a number.'))
    
    # Prepare context for template
    context = {
        'response': response,
        'questionnaire': questionnaire,
        'user': user,
        'questions': questionnaire.questions,
        'trainer': trainer,
    }
    
    return render(request, 'website/trainer/review_questionnaire.html', context) 