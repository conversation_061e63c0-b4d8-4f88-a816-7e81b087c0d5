import os

from project.settings import DEBUG
from dotenv import load_dotenv

load_dotenv()

if DEBUG:
    EBS_USER = os.getenv('EBS_USER')
    EBS_PASSWORD = os.getenv('EBS_PASSWORD')
    EBS_OTP_URL = f"""http://172.16.170.153:7003/soa-infra/services/default/CX_PLSQL_RGI_CX_OTP_PKG/RGI_CX_OTP_PKG_Service"""
    EBS_CAR_INFO_URL = f"""http://172.16.170.153:7003/soa-infra/services/default/CX_PLSQL_RGI_CAR_INFO/RGI_CAR_INFO_Service"""
    EBS_CAR_HISTORY_URL = f"""http://172.16.170.153:7003/soa-infra/services/default/CX_PLSQL_RGI_CAR_HISTORY/RGI_CAR_HISTORY_Service"""

    VODAFONE_URL = f"""https://e3len.vodafone.com.eg/web2sms/sms/submit/"""
    VODAFONE_ACCOUNT_ID = os.getenv('VODAFONE_ACCOUNT_ID')
    VODAFONE_PASSWORD = os.getenv('VODAFONE_PASSWORD')
    VODAFONE_SENDER_NAME = 'GB-Auto'
    VODAFONE_SECRET_KEY = b'888D4B0C4D6A467FA0046FC50A897BCB'

    RN_USER = os.getenv('RN_USER')
    RN_PASSWORD = os.getenv('RN_PASSWORD')
    RN_BRAND = 'HYN'
    RN_BRAND_ID = 1
    RN_WORKSHOP_MECHANICAL_TYPE_ID = 3
    RN_BRAND_LOOKUP_NAME = 'Hyundai'
    RN_SHOWROOM_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/package.Showroom"""
    RN_BRANCH_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.Branch"""
    RN_OPERATION_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.Operation"""
    RN_APPOINTMENTS_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/CO.Appointments"""
    RN_VEHICLE_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/package.Vehicle"""
    RN_QUERY_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/queryResults"""
    RN_MODEL_OPERATION_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.ModelOperation"""
    RN_MODEL_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.Model"""
    RN_CONTACTS_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/contacts"""
    RN_OPERATION_CATEGORY_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.OperationCategory"""
    RN_EVENTS_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/CO.events"""
    RN_APP_CANCEL_REASONS_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.AppCancellationReas"""
    RN_INCIDENTS_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/incidents"""
    RN_APPOINTMENT_PARTS_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.appointmentParts"""
    RN_APPOINTMENT_CONSUMBLE_URL = f"""https://gb-rightnow--tst1.custhelp.com/services/rest/connect/v1.3/Config.appointmentConsumble"""

    PAYMOB_API_KEY = f"""{os.getenv('PAYMOB_API_KEY')}"""
    PAYMOB_HMAC_KEY = bytes(os.getenv('PAYMOB_HMAC_KEY'), 'utf-8')
    PAYMOB_TOKENS_URL = f"""https://accept.paymob.com/api/auth/tokens"""
    PAYMOB_ORDERS_URL = f"""https://accept.paymob.com/api/ecommerce/orders"""
    PAYMOB_PAYMENT_KEYS_URL = f"""https://accept.paymob.com/api/acceptance/payment_keys"""

    CX_USER = os.getenv('CX_USER')
    CX_PASSWORD = os.getenv('CX_PASSWORD')
    CX_CONTACTS_URL = f"""https://ccmf-test.fa.em3.oraclecloud.com/crmRestApi/resources/latest/contacts"""
    CX_SHOWROOM_URL = f"""https://ccmf-test.fa.em3.oraclecloud.com/crmRestApi/resources/latest/Showroom_c"""

    EXPO_URL = f"""https://exp.host/--/api/v2/push/send"""

    IT_EMAILS = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
    ]

    RSA_EMAILS = [
                     '<EMAIL>',
                 ] + IT_EMAILS
else:
    EBS_USER =  os.getenv('EBS_USER')
    EBS_PASSWORD = os.getenv('EBS_PASSWORD')
    EBS_OTP_URL = ""
    EBS_CAR_INFO_URL = f"""http://**************:7003/soa-infra/services/default/PROD_PLSQL_RGI_CAR_INFO/RGI_CAR_INFO_Service"""
    EBS_CAR_HISTORY_URL = f"""http://**************:7003/soa-infra/services/default/PROD_PLSQL_RGI_CAR_HISTORY/RGI_CAR_HISTORY_Service"""

    VODAFONE_URL = f"""https://e3len.vodafone.com.eg/web2sms/sms/submit/"""
    VODAFONE_ACCOUNT_ID = os.getenv('VODAFONE_ACCOUNT_ID')
    VODAFONE_PASSWORD = os.getenv('VODAFONE_PASSWORD')
    VODAFONE_SENDER_NAME = 'GB-Auto'
    VODAFONE_SECRET_KEY = b'888D4B0C4D6A467FA0046FC50A897BCB'

    RN_USER = os.getenv('RN_USER')
    RN_PASSWORD = os.getenv('RN_PASSWORD')
    RN_BRAND = 'HYN'
    RN_BRAND_ID = 1
    RN_WORKSHOP_MECHANICAL_TYPE_ID = 3
    RN_BRAND_LOOKUP_NAME = 'Hyundai'
    RN_SHOWROOM_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/package.Showroom"""
    RN_BRANCH_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.Branch"""
    RN_OPERATION_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.Operation"""
    RN_APPOINTMENTS_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/CO.Appointments"""
    RN_VEHICLE_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/package.Vehicle"""
    RN_QUERY_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/queryResults"""
    RN_MODEL_OPERATION_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.ModelOperation"""
    RN_MODEL_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.Model"""
    RN_CONTACTS_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/contacts"""
    RN_OPERATION_CATEGORY_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.OperationCategory"""
    RN_EVENTS_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/CO.events"""
    RN_APP_CANCEL_REASONS_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.AppCancellationReas"""
    RN_INCIDENTS_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/incidents"""
    RN_APPOINTMENT_PARTS_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.appointmentParts"""
    RN_APPOINTMENT_CONSUMBLE_URL = f"""https://gb-rightnow.custhelp.com/services/rest/connect/v1.3/Config.appointmentConsumble"""

    PAYMOB_API_KEY = f"""{os.getenv('PAYMOB_API_KEY')}"""
    PAYMOB_HMAC_KEY = bytes(os.getenv('PAYMOB_HMAC_KEY'), 'utf-8')
    PAYMOB_TOKENS_URL = f"""https://accept.paymob.com/api/auth/tokens"""
    PAYMOB_ORDERS_URL = f"""https://accept.paymob.com/api/ecommerce/orders"""
    PAYMOB_PAYMENT_KEYS_URL = f"""https://accept.paymob.com/api/acceptance/payment_keys"""

    CX_USER = os.getenv('CX_USER')
    CX_PASSWORD = os.getenv('CX_PASSWORD')
    CX_CONTACTS_URL = f"""https://ccmf.fa.em3.oraclecloud.com/crmRestApi/resources/latest/contacts"""
    CX_SHOWROOM_URL = f"""https://ccmf.fa.em3.oraclecloud.com/crmRestApi/resources/latest/Showroom_c"""

    EXPO_URL = f"""https://exp.host/--/api/v2/push/send"""

    IT_EMAILS = [
        '<EMAIL>',
        '<EMAIL>',
    ]

    RSA_EMAILS = [
        '<EMAIL>',
    ]
