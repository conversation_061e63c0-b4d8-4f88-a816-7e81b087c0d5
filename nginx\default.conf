server_tokens off; # prevent version information disclosure for security reasons

upstream app {
    server app:8000;
}

server {
    server_name project.com;

    location / {
        proxy_pass http://app;

        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";

        proxy_redirect off;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Host $http_host;
        proxy_set_header X-Forwarded-Proto $scheme;

        client_max_body_size 64M;
    }

    location /static/ {
        alias /static/;
    }

    location /media/ {
        alias /media/;
    }

    listen 443 ssl; # managed by Certbot
    ssl_certificate /etc/letsencrypt/live/project.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/project.com/privkey.pem; # managed by Certbot
    ssl_password_file /etc/letsencrypt/live/project.com/Password.txt;
#     include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
#     ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot
}

server {
    if ($host = project.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    server_name project.com;
    listen 80;
    return 404; # managed by Certbot
}
