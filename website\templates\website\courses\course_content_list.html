{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Course Content" %} - {{ course.name_en }}{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center justify-between mb-8">
            <div class="flex items-center space-x-3">
                <a href="{% url 'website:course_detail' course.course_id %}" class="text-white/70 hover:text-white">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                    </svg>
                </a>
                <h1 class="text-2xl font-bold text-white">{% trans "Course Content" %}: {{ course.name_en }}</h1>
            </div>
            <div class="flex space-x-3">
                <a href="{% url 'website:add_course_content' course.course_id %}" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    {% trans "Add Content" %}
                </a>
                <a href="{% url 'website:add_questionnaire' course.course_id %}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md flex items-center">
                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    {% trans "Add Questionnaire" %}
                </a>
            </div>
        </div>

        <!-- Content List -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden mb-8">
            <div class="p-6 border-b border-white/10">
                <h2 class="text-xl font-semibold text-white">{% trans "Available Content" %}</h2>
                <p class="text-white/70 text-sm mt-1">
                    {% trans "Manage your course material including presentations, documents, images, and more." %}
                </p>
            </div>

            {% if course_contents %}
            <div class="divide-y divide-white/10">
                {% for content in course_contents %}
                <div class="p-4 flex items-center justify-between hover:bg-white/5">
                    <div class="flex-1">
                        <h3 class="text-white font-medium">{{ content.title }}</h3>
                        <div class="flex space-x-4 mt-1">
                            <span class="text-white/70 text-sm">{{ content.get_content_type_display }}</span>
                            <span class="text-white/70 text-sm">{{ content.created_at|date:"M d, Y" }}</span>
                        </div>
                        {% if content.description %}
                        <p class="text-white/70 text-sm mt-1">{{ content.description }}</p>
                        {% endif %}
                    </div>
                    <div class="flex space-x-2">
                        <a href="{% url 'website:download_course_content' course.course_id content.content_id %}" class="bg-primary/80 hover:bg-primary text-white px-3 py-1 rounded-md text-sm inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
                            </svg>
                            {% trans "Download" %}
                        </a>
                        <a href="{% url 'website:edit_course_content' course.course_id content.content_id %}" class="bg-indigo-600/80 hover:bg-indigo-600 text-white px-3 py-1 rounded-md text-sm inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            {% trans "Edit" %}
                        </a>
                        <a href="{% url 'website:delete_course_content' course.course_id content.content_id %}" class="bg-red-600/80 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            {% trans "Delete" %}
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="p-8 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 text-white/20 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <h3 class="text-lg font-medium text-white mb-1">{% trans "No content added yet" %}</h3>
                    <p class="text-white/70 mb-4">{% trans "Add your first document, presentation, image or other content." %}</p>
                    <a href="{% url 'website:add_course_content' course.course_id %}" class="bg-primary hover:bg-primary/90 text-white px-4 py-2 rounded-md inline-flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        {% trans "Add First Content" %}
                    </a>
                </div>
            </div>
            {% endif %}
        </div>

        <!-- Questionnaires List -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <div class="p-6 border-b border-white/10">
                <h2 class="text-xl font-semibold text-white">{% trans "Course Questionnaires" %}</h2>
                <p class="text-white/70 text-sm mt-1">
                    {% trans "Manage questionnaires and surveys for each session of this course." %}
                </p>
            </div>

            {% if questionnaires %}
            <div class="divide-y divide-white/10">
                {% for questionnaire in questionnaires %}
                <div class="p-4 flex items-center justify-between hover:bg-white/5">
                    <div class="flex-1">
                        <h3 class="text-white font-medium">{{ questionnaire.title }}</h3>
                        <div class="flex space-x-4 mt-1">
                            <span class="text-white/70 text-sm">{% trans "Session" %} {{ questionnaire.session_number }}</span>
                            <span class="text-white/70 text-sm">{{ questionnaire.created_at|date:"M d, Y" }}</span>
                            <span class="text-white/70 text-sm">{{ questionnaire.questions|length }} {% trans "questions" %}</span>
                        </div>
                        {% if questionnaire.comments %}
                        <p class="text-white/70 text-sm mt-1">{{ questionnaire.comments }}</p>
                        {% endif %}
                    </div>
                    <div class="flex space-x-2">
                        <a href="{% url 'website:view_questionnaire' course.course_id questionnaire.questionnaire_id %}" class="bg-primary/80 hover:bg-primary text-white px-3 py-1 rounded-md text-sm inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                            {% trans "View" %}
                        </a>
                        <a href="{% url 'website:edit_questionnaire' course.course_id questionnaire.questionnaire_id %}" class="bg-indigo-600/80 hover:bg-indigo-600 text-white px-3 py-1 rounded-md text-sm inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                            </svg>
                            {% trans "Edit" %}
                        </a>
                        <a href="{% url 'website:delete_questionnaire' course.course_id questionnaire.questionnaire_id %}" class="bg-red-600/80 hover:bg-red-600 text-white px-3 py-1 rounded-md text-sm inline-flex items-center">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                            </svg>
                            {% trans "Delete" %}
                        </a>
                    </div>
                </div>
                {% endfor %}
            </div>
            {% else %}
            <div class="p-8 text-center">
                <div class="flex flex-col items-center">
                    <svg class="w-16 h-16 text-white/20 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <h3 class="text-lg font-medium text-white mb-1">{% trans "No questionnaires added yet" %}</h3>
                    <p class="text-white/70 mb-4">{% trans "Add your first questionnaire to gather feedback from students." %}</p>
                    <a href="{% url 'website:add_questionnaire' course.course_id %}" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-md inline-flex items-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                        </svg>
                        {% trans "Add First Questionnaire" %}
                    </a>
                </div>
            </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 