{% comment %} {% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="min-h-screen bg-gradient-to-br from-blue-900 to-gray-900">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Welcome Section -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 mb-8 border border-white/10">
            <h1 class="text-2xl font-bold text-white mb-2">{% trans "Welcome" %}, {{ request.user.username }}!</h1>
            <p class="text-white/80">
                {% if request.user.is_staff %}
                    {% trans "System Administrator Dashboard" %}
                {% elif request.user.groups.all.0.name == 'trainer' %}
                    {% trans "Trainer Dashboard" %}
                {% elif request.user.groups.all.0.name == 'corporate' %}
                    {% trans "Corporate User Dashboard" %}
                {% else %}
                    {% trans "Student Dashboard" %}
                {% endif %}
            </p>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                <h3 class="text-lg font-semibold text-white mb-2">{% trans "Enrolled Courses" %}</h3>
                <p class="text-3xl font-bold text-white">0</p>
            </div>
            <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                <h3 class="text-lg font-semibold text-white mb-2">{% trans "Upcoming Sessions" %}</h3>
                <p class="text-3xl font-bold text-white">0</p>
            </div>
            <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                <h3 class="text-lg font-semibold text-white mb-2">{% trans "Certificates" %}</h3>
                <p class="text-3xl font-bold text-white">0</p>
            </div>
        </div>

        <!-- Main Content Area -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- Left Column -->
            <div class="lg:col-span-2 space-y-8">
                <!-- Upcoming Courses -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                    <h2 class="text-xl font-bold text-white mb-4">{% trans "Upcoming Courses" %}</h2>
                    <div class="space-y-4">
                        <!-- Empty state -->
                        <div class="text-center py-8">
                            <p class="text-white/70">{% trans "No upcoming courses" %}</p>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                    <h2 class="text-xl font-bold text-white mb-4">{% trans "Recent Activities" %}</h2>
                    <div class="space-y-4">
                        <!-- Empty state -->
                        <div class="text-center py-8">
                            <p class="text-white/70">{% trans "No recent activities" %}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Column -->
            <div class="space-y-8">
                <!-- Calendar -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                    <h2 class="text-xl font-bold text-white mb-4">{% trans "Calendar" %}</h2>
                    <!-- Mini calendar here -->
                </div>

                <!-- Notifications -->
                <div class="bg-white/5 backdrop-blur-md rounded-lg p-6 border border-white/10">
                    <h2 class="text-xl font-bold text-white mb-4">{% trans "Notifications" %}</h2>
                    <div class="space-y-4">
                        <!-- Empty state -->
                        <div class="text-center py-8">
                            <p class="text-white/70">{% trans "No new notifications" %}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block footer %}
    {% include 'website/footer.html' %}
{% endblock %} {% endcomment %}