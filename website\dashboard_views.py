import datetime
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.utils import timezone
from django.db.models import Count, Q
from django.conf import settings
import logging

from .models import (
    CustomUser, Course, Session, Trainer, Corporate, CorporateAdmin, Reservation,
    CourseInstance, CorporateAdminRequest, CorporateUserCancellationRequest, 
    EmergencyCancellationRequest, CorporateAdminNewCourseRequest, Notification,
    UserType, Room, Equipment, Questionnaire, QuestionnaireResponse, SurveyResponse,
    GBEmployeeRequest, GB_Employee
)

logger = logging.getLogger(__name__)

@login_required
@staff_member_required
def system_admin_dashboard(request):
    """
    Dashboard view for system administrators
    Displays system-wide statistics and information
    """
    # Current date for calculations
    today = timezone.now().date()
    end_of_week = today + datetime.timedelta(days=(6 - today.weekday()))
    
    # Get all in-progress sessions
    in_progress_sessions = Session.objects.filter(
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now(),
        is_active=True
    ).select_related('course', 'room').prefetch_related('trainers')
    
    # Get upcoming sessions - use dates without time component for more reliable comparison
    # Strip time component from today for consistent comparison
    today_start = datetime.datetime.combine(today, datetime.time.min)
    if settings.USE_TZ:
        today_start = timezone.make_aware(today_start)
        end_of_week_dt = timezone.make_aware(datetime.datetime.combine(end_of_week, datetime.time.max))
    else:
        end_of_week_dt = datetime.datetime.combine(end_of_week, datetime.time.max)
    
    # Log the date ranges for debugging
    logger.info(f"Finding upcoming sessions from {today_start} to {end_of_week_dt}")
    
    # Modified query to use date-based comparison rather than exact datetime
    upcoming_sessions = Session.objects.filter(
        start_date__gte=today_start,
        start_date__lte=end_of_week_dt,
        is_active=True
    ).select_related('course', 'room').prefetch_related('trainers').order_by('start_date')[:10]
    
    # Log found sessions
    logger.info(f"Found {upcoming_sessions.count()} upcoming sessions")
    for session in upcoming_sessions:
        logger.info(f"Upcoming session: {session.session_id} - {session.course.name_en} - {session.start_date}")
    
    # Get counts for notifications
    pending_corporate_requests = CorporateAdminRequest.objects.filter(status='PENDING').count()
    pending_new_course_requests = CorporateAdminNewCourseRequest.objects.filter(status='PENDING').count()
    emergency_cancellation_requests = EmergencyCancellationRequest.objects.filter(status='PENDING').count()
    
    # Get count of external corporate admins
    external_corporate_admins = CorporateAdmin.objects.filter(is_active=True).count()
    
    # Get count of online course instances
    online_course_instances = CourseInstance.objects.filter(
        course__location='ONLINE',
        is_active=True
    ).count()
    
    # Get count of active trainers
    active_trainers = Trainer.objects.filter(user__is_active=True).count()
    
    # Get count of active rooms
    active_rooms = Room.objects.filter(is_active=True).count()
    
    # Get count of active trainees (students)
    trainee_type = UserType.objects.filter(code__in=['EXTERNAL_CORPORATE_TRAINEE', 'EXTERNAL_INDIVIDUAL']).first()
    active_trainees = CustomUser.objects.filter(
        user_type=trainee_type,
        is_active=True
    ).count() if trainee_type else 0
    
    # Get equipment maintenance data
    equipment_maintenance = Equipment.objects.filter(status='MAINTENANCE').count()
    
    context = {
        'page_title': 'System Admin Dashboard',
        'in_progress_sessions': in_progress_sessions,
        'upcoming_sessions': upcoming_sessions,
        'pending_corporate_requests': pending_corporate_requests,
        'pending_new_course_requests': pending_new_course_requests,
        'emergency_cancellation_requests': emergency_cancellation_requests,
        'external_corporate_admins': external_corporate_admins,
        'online_course_instances': online_course_instances,
        'active_trainers': active_trainers,
        'active_rooms': active_rooms,
        'active_trainees': active_trainees,
        'equipment_maintenance': equipment_maintenance,
    }
    
    return render(request, 'website/dashboard/system_admin_dashboard.html', context)

@login_required
@staff_member_required
def super_admin_dashboard(request):
    """
    Dashboard view for super administrators
    Similar to system admin but with additional data and controls
    """
    # Current date for calculations
    today = timezone.now().date()
    end_of_week = today + datetime.timedelta(days=(6 - today.weekday()))
    
    # Get all in-progress sessions
    in_progress_sessions = Session.objects.filter(
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now(),
        is_active=True
    ).select_related('course', 'room').prefetch_related('trainers')
    
    # Get upcoming sessions - use dates without time component for more reliable comparison
    # Strip time component from today for consistent comparison
    today_start = datetime.datetime.combine(today, datetime.time.min)
    if settings.USE_TZ:
        today_start = timezone.make_aware(today_start)
        end_of_week_dt = timezone.make_aware(datetime.datetime.combine(end_of_week, datetime.time.max))
    else:
        end_of_week_dt = datetime.datetime.combine(end_of_week, datetime.time.max)
    
    # Log the date ranges for debugging
    logger.info(f"Finding upcoming sessions from {today_start} to {end_of_week_dt}")
    
    # Modified query to use date-based comparison rather than exact datetime
    upcoming_sessions = Session.objects.filter(
        start_date__gte=today_start,
        start_date__lte=end_of_week_dt,
        is_active=True
    ).select_related('course', 'room').prefetch_related('trainers').order_by('start_date')[:10]
    
    # Log found sessions
    logger.info(f"Found {upcoming_sessions.count()} upcoming sessions")
    for session in upcoming_sessions:
        logger.info(f"Upcoming session: {session.session_id} - {session.course.name_en} - {session.start_date}")
    
    # Get counts for notifications
    pending_corporate_requests = CorporateAdminRequest.objects.filter(status='PENDING').count()
    pending_new_course_requests = CorporateAdminNewCourseRequest.objects.filter(status='PENDING').count()
    emergency_cancellation_requests = EmergencyCancellationRequest.objects.filter(status='PENDING').count()
    
    # Get count of external corporate admins
    external_corporate_admins = CorporateAdmin.objects.filter(is_active=True).count()
    
    # Get count of online course instances
    online_course_instances = CourseInstance.objects.filter(
        course__location='ONLINE',
        is_active=True
    ).count()
    
    # Get count of active trainers
    active_trainers = Trainer.objects.filter(user__is_active=True).count()
    
    # Get count of active rooms
    active_rooms = Room.objects.filter(is_active=True).count()
    
    # Get count of active trainees (students)
    trainee_type = UserType.objects.filter(code__in=['EXTERNAL_CORPORATE_TRAINEE', 'EXTERNAL_INDIVIDUAL']).first()
    active_trainees = CustomUser.objects.filter(
        user_type=trainee_type,
        is_active=True
    ).count() if trainee_type else 0
    
    # Get equipment maintenance data
    equipment_maintenance = Equipment.objects.filter(status='MAINTENANCE').count()
    
    # Get financial data (for super admin only)
    completed_courses = CourseInstance.objects.filter(
        end_date__lt=timezone.now(),
        is_active=True
    ).count()
    
    # Course distribution by category
    course_categories = Course.objects.filter(is_active=True).values('category').annotate(
        count=Count('category')
    ).order_by('category')
    
    context = {
        'page_title': 'Super Admin Dashboard',
        'in_progress_sessions': in_progress_sessions,
        'upcoming_sessions': upcoming_sessions,
        'pending_corporate_requests': pending_corporate_requests,
        'pending_new_course_requests': pending_new_course_requests,
        'emergency_cancellation_requests': emergency_cancellation_requests,
        'external_corporate_admins': external_corporate_admins,
        'online_course_instances': online_course_instances,
        'active_trainers': active_trainers,
        'active_rooms': active_rooms,
        'active_trainees': active_trainees,
        'equipment_maintenance': equipment_maintenance,
        'completed_courses': completed_courses,
        'course_categories': course_categories,
    }
    
    return render(request, 'website/dashboard/super_admin_dashboard.html', context)

@login_required
def trainer_dashboard(request):
    """
    Dashboard view for trainers
    Shows active sessions, upcoming sessions, and pending questionnaires for review
    """
    # Get the trainer object for the current user
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        # Redirect to home if user is not a trainer
        logger.warning(f"User {request.user.username} tried to access trainer dashboard but is not a trainer")
        return redirect('website:home')
    
    # Current date and time for calculations
    now = timezone.now()
    today = now.date()
    end_of_week = today + datetime.timedelta(days=(6 - today.weekday()))
    
    # Get date ranges for queries
    today_start = datetime.datetime.combine(today, datetime.time.min)
    if settings.USE_TZ:
        today_start = timezone.make_aware(today_start)
        end_of_week_dt = timezone.make_aware(datetime.datetime.combine(end_of_week, datetime.time.max))
    else:
        end_of_week_dt = datetime.datetime.combine(end_of_week, datetime.time.max)
    
    # Get active sessions (currently in progress)
    active_sessions = Session.objects.filter(
        trainers=trainer,
        start_date__lte=now,
        end_date__gte=now,
        is_active=True
    ).select_related('course', 'room')
    
    logger.info(f"Found {active_sessions.count()} active sessions for trainer {trainer.user.username}")
    
    # Get upcoming sessions for this week (not including active ones)
    upcoming_sessions = Session.objects.filter(
        trainers=trainer,
        start_date__gt=now,
        start_date__lte=end_of_week_dt,
        is_active=True
    ).select_related('course', 'room').order_by('start_date')
    
    logger.info(f"Found {upcoming_sessions.count()} upcoming sessions for trainer {trainer.user.username}")
    
    # Get pending questionnaires that need trainer review
    # Assuming there's a relationship between questionnaires and trainers
    # This query will need to be adjusted based on your actual model relationships
    pending_questionnaires = QuestionnaireResponse.objects.filter(
        status='PENDING_TRAINER_REVIEW',
        session__trainers=trainer
    ).select_related('questionnaire', 'course')
    
    logger.info(f"Found {pending_questionnaires.count()} pending questionnaires for trainer {trainer.user.username}")
    
    context = {
        'page_title': 'Trainer Dashboard',
        'trainer': trainer,
        'active_sessions': active_sessions,
        'upcoming_sessions': upcoming_sessions,
        'pending_questionnaires': pending_questionnaires
    }
    
    return render(request, 'website/dashboard/trainer_dashboard.html', context)

@login_required
def corporate_admin_dashboard(request):
    """
    Dashboard view for corporate administrators
    Shows in-progress sessions, upcoming sessions, corporate users, and pending requests
    """
    # Get the corporate admin object for the current user
    try:
        corporate_admin = CorporateAdmin.objects.get(user=request.user)
    except CorporateAdmin.DoesNotExist:
        # Redirect to home if user is not a corporate admin
        logger.warning(f"User {request.user.username} tried to access corporate admin dashboard but is not a corporate admin")
        return redirect('website:home')
    
    # Get the corporate entity this admin belongs to
    corporate = corporate_admin.corporate
    
    
    # Current date for calculations
    today = timezone.now().date()
    end_of_week = today + datetime.timedelta(days=(6 - today.weekday()))
    
    # Get all in-progress sessions
    in_progress_sessions = Session.objects.filter(
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now(),
        is_active=True
    ).select_related('course', 'room').prefetch_related('trainers')
    
    # Get upcoming sessions - use dates without time component for more reliable comparison
    # Strip time component from today for consistent comparison
    today_start = datetime.datetime.combine(today, datetime.time.min)
    if settings.USE_TZ:
        today_start = timezone.make_aware(today_start)
        end_of_week_dt = timezone.make_aware(datetime.datetime.combine(end_of_week, datetime.time.max))
    else:
        end_of_week_dt = datetime.datetime.combine(end_of_week, datetime.time.max)
    
    # Log the date ranges for debugging
    logger.info(f"Finding upcoming sessions from {today_start} to {end_of_week_dt}")
    
    # Modified query to use date-based comparison rather than exact datetime
    upcoming_sessions = Session.objects.filter(
        start_date__gte=today_start,
        start_date__lte=end_of_week_dt,
        is_active=True
    ).select_related('course', 'room').prefetch_related('trainers').order_by('start_date')[:10]
    
    # Log found sessions
    logger.info(f"Found {upcoming_sessions.count()} upcoming sessions")
    for session in upcoming_sessions:
        logger.info(f"Upcoming session: {session.session_id} - {session.course.name_en} - {session.start_date}")
    
    # Get counts for notifications and requests
    corporate_cancellation_requests = CorporateUserCancellationRequest.objects.filter(
        reservation__user__corporate=corporate,
        status='PENDING'
    ).count()
    
    emergency_cancellation_requests = CorporateUserCancellationRequest.objects.filter(
        reservation__user__corporate=corporate,
        status='PENDING'
    ).count()
    
    # Additional attempt to get cancellation requests using company_name field
    # This is needed because corporate_views.py queries by company_name instead of corporate relation
    try:
        corporate_cancellation_requests_alt = CorporateUserCancellationRequest.objects.filter(
            reservation__user__company_name=corporate.legal_name,
            status='PENDING'
        ).count()
        # Use the higher count to ensure we're not missing any requests
        if corporate_cancellation_requests_alt > corporate_cancellation_requests:
            corporate_cancellation_requests = corporate_cancellation_requests_alt
    except Exception as e:
        logger.warning(f"Error getting alternative corporate cancellation count: {str(e)}")
    
    pending_new_course_requests = CorporateAdminNewCourseRequest.objects.filter(
        corporate_admin__corporate=corporate,
        status='PENDING'
    ).count()
    
    # Get user statistics for this corporate entity
    corporate_user_type = UserType.objects.filter(code='EXTERNAL_CORPORATE_TRAINEE').first()
    
    # Total users in this corporate (all users with the same corporate name, regardless of user type)
    total_corporate_users = CustomUser.objects.filter(
        company_name=corporate.legal_name,
        is_active=True
    ).count()
    
    # Users currently in training (external corporate users with the same corporate name)
    users_in_training = CustomUser.objects.filter(
        corporate=corporate,
        user_type=corporate_user_type,
        is_active=True
    ).distinct().count()
    
    # # Get active trainers for current in-progress sessions
    # active_trainers = Trainer.objects.filter(
    #     sessions__course_instance__reservation__user__corporate=corporate,
    #     sessions__start_date__lte=now(),
    #     sessions__end_date__gte=now,
    #     sessions__is_active=True,
    #     user__is_active=True
    # ).distinct().count()
    
    # # Get active rooms being used by this corporate
    # active_rooms = Room.objects.filter(
    #     session__course_instance__reservation__user__corporate=corporate,
    #     session__start_date__lte=now,
    #     session__end_date__gte=now,
    #     session__is_active=True,
    #     is_active=True
    # ).distinct().count()
    
    context = {
        'page_title': 'Corporate Admin Dashboard',
        'corporate_admin': corporate_admin,
        'corporate': corporate,
        'in_progress_sessions': in_progress_sessions,
        'upcoming_sessions': upcoming_sessions,
        'corporate_cancellation_requests': corporate_cancellation_requests,
        'emergency_cancellation_requests': emergency_cancellation_requests,
        'pending_new_course_requests': pending_new_course_requests,
        'total_corporate_users': total_corporate_users,
        'users_in_training': users_in_training,
        # 'active_trainers': active_trainers,
        # 'active_rooms': active_rooms,
    }
    
    return render(request, 'website/dashboard/corporate_admin_dashboard.html', context)

@login_required
def trainer_sessions(request):
    """
    View for trainer to see all their sessions (past, current, and future)
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        return redirect('website:home')
    
    # Get filter parameters
    status_filter = request.GET.get('status', 'all')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    # Base query for sessions assigned to this trainer
    sessions = Session.objects.filter(trainers=trainer).select_related('course', 'room')
    
    # Apply status filter
    now = timezone.now()
    if status_filter == 'active':
        sessions = sessions.filter(start_date__lte=now, end_date__gte=now, is_active=True)
    elif status_filter == 'upcoming':
        sessions = sessions.filter(start_date__gt=now, is_active=True)
    elif status_filter == 'past':
        sessions = sessions.filter(end_date__lt=now)
    
    # Apply date filters if provided
    if date_from:
        try:
            date_from_obj = datetime.datetime.strptime(date_from, '%Y-%m-%d').date()
            date_from_dt = datetime.datetime.combine(date_from_obj, datetime.time.min)
            if settings.USE_TZ:
                date_from_dt = timezone.make_aware(date_from_dt)
            sessions = sessions.filter(start_date__gte=date_from_dt)
        except ValueError:
            logger.warning(f"Invalid date_from format: {date_from}")
    
    if date_to:
        try:
            date_to_obj = datetime.datetime.strptime(date_to, '%Y-%m-%d').date()
            date_to_dt = datetime.datetime.combine(date_to_obj, datetime.time.max)
            if settings.USE_TZ:
                date_to_dt = timezone.make_aware(date_to_dt)
            sessions = sessions.filter(end_date__lte=date_to_dt)
        except ValueError:
            logger.warning(f"Invalid date_to format: {date_to}")
    
    # Order by start date (most recent first for past, earliest first for upcoming)
    if status_filter == 'past':
        sessions = sessions.order_by('-start_date')
    else:
        sessions = sessions.order_by('start_date')
    
    context = {
        'trainer': trainer,
        'sessions': sessions,
        'status_filter': status_filter,
        'date_from': date_from,
        'date_to': date_to,
    }
    
    return render(request, 'website/trainer/sessions.html', context)

@login_required
def manage_session(request, session_id):
    """
    View for trainer to manage a specific session
    Allows attendance tracking and accessing session materials
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
        session = Session.objects.get(session_id=session_id, trainers=trainer)
    except (Trainer.DoesNotExist, Session.DoesNotExist):
        return redirect('website:trainer_dashboard')
    
    # Get reservations/attendees for this session
    # We're accessing through the CourseInstance that this session belongs to
    reservations = Reservation.objects.filter(
        course_instance__sessions=session,
        status__in=['UPCOMING', 'IN_PROGRESS']
    ).select_related('user')
    
    # Get attendance records for this session
    attendance_records = {}
    from .models import Attendance
    
    attendances = Attendance.objects.filter(session=session)
    for attendance in attendances:
        attendance_records[attendance.user.id] = {
            'first_half': attendance.first_half,
            'second_half': attendance.second_half
        }
    
    # Get course materials for this session
    from .models import CourseContent
    course_materials = CourseContent.objects.filter(
        course=session.course,
        is_active=True
    )
    
    context = {
        'trainer': trainer,
        'session': session,
        'reservations': reservations,
        'attendance_records': attendance_records,
        'course_materials': course_materials,
    }
    
    return render(request, 'website/trainer/manage_session.html', context)

from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
import json

@login_required
@csrf_exempt
@require_POST
def record_attendance(request, session_id):
    """
    API to record attendance for a session
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
        session = Session.objects.get(session_id=session_id, trainers=trainer)
    except (Trainer.DoesNotExist, Session.DoesNotExist):
        return JsonResponse({'status': 'error', 'message': 'Session not found or access denied'}, status=404)
    
    try:
        data = json.loads(request.body)
        user_id = data.get('user_id')
        first_half = data.get('first_half', False)
        second_half = data.get('second_half', False)
        
        if not user_id:
            return JsonResponse({'status': 'error', 'message': 'User ID is required'}, status=400)
        
        user = CustomUser.objects.get(id=user_id)
        
        # Check if user is enrolled in this session
        is_enrolled = Reservation.objects.filter(
            course_instance__sessions=session,
            user=user,
            status__in=['UPCOMING', 'IN_PROGRESS']
        ).exists()
        
        if not is_enrolled:
            return JsonResponse({'status': 'error', 'message': 'User is not enrolled in this session'}, status=400)
        
        # Get or create attendance record
        from .models import Attendance
        attendance, created = Attendance.objects.get_or_create(
            session=session,
            user=user,
            defaults={
                'trainer': trainer,
                'first_half': first_half,
                'second_half': second_half
            }
        )
        
        if not created:
            # Update existing record
            attendance.first_half = first_half
            attendance.second_half = second_half
            attendance.trainer = trainer
            attendance.save()
        
        return JsonResponse({
            'status': 'success',
            'message': 'Attendance recorded successfully',
            'attendance': {
                'user_id': user.id,
                'first_half': attendance.first_half,
                'second_half': attendance.second_half
            }
        })
        
    except CustomUser.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': 'User not found'}, status=404)
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': 'Invalid JSON data'}, status=400)
    except Exception as e:
        logger.error(f"Error recording attendance: {str(e)}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@login_required
def trainer_questionnaires(request):
    """
    View for trainer to manage questionnaires
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        return redirect('website:home')
    
    # Get filter parameters
    status_filter = request.GET.get('status', 'pending')
    
    # Get all questionnaires for courses this trainer teaches
    if status_filter == 'pending':
        # Use Q objects to combine conditions with OR
        from django.db.models import Q
        questionnaire_responses = QuestionnaireResponse.objects.filter(
            Q(status='PENDING_TRAINER_REVIEW') &
            (Q(session__trainers=trainer) | Q(course__in=trainer.courses.all()))
        ).select_related('questionnaire', 'course', 'user').distinct()
    elif status_filter == 'completed':
        questionnaire_responses = QuestionnaireResponse.objects.filter(
            status='COMPLETED_TRAINER_REVIEWED',
            session__trainers=trainer
        ).select_related('questionnaire', 'course', 'user')
    else:
        # For 'all' status, include everything the trainer should see
        from django.db.models import Q
        questionnaire_responses = QuestionnaireResponse.objects.filter(
            Q(session__trainers=trainer) | Q(course__in=trainer.courses.all())
        ).select_related('questionnaire', 'course', 'user').distinct()
    
    # Order by creation date (newest first)
    questionnaire_responses = questionnaire_responses.order_by('-created_at')
    
    # Log for debugging
    logger.info(f"Found {questionnaire_responses.count()} questionnaire responses with filter '{status_filter}' for trainer {trainer.user.username}")
    
    context = {
        'trainer': trainer,
        'questionnaire_responses': questionnaire_responses,
        'status_filter': status_filter
    }
    
    return render(request, 'website/trainer/questionnaires.html', context)

@login_required
def review_questionnaire(request, questionnaire_id):
    """
    View for trainer to review a specific questionnaire response
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
        questionnaire_response = QuestionnaireResponse.objects.get(
            response_id=questionnaire_id,
            session__trainers=trainer
        )
    except (Trainer.DoesNotExist, QuestionnaireResponse.DoesNotExist):
        return redirect('website:trainer_questionnaires')
    
    if request.method == 'POST':
        # Update questionnaire review
        feedback = request.POST.get('feedback', '')
        score_adjustments = {}
        
        # Process score adjustments
        for key, value in request.POST.items():
            if key.startswith('score_'):
                question_id = key.replace('score_', '')
                try:
                    score_adjustments[question_id] = int(value)
                except ValueError:
                    pass
        
        # Update response data with trainer feedback
        response_data = questionnaire_response.question_responses
        for question_id, score in score_adjustments.items():
            if question_id in response_data:
                response_data[question_id]['trainer_score'] = score
                response_data[question_id]['trainer_feedback'] = feedback
        
        questionnaire_response.question_responses = response_data
        questionnaire_response.status = 'COMPLETED_TRAINER_REVIEWED'
        questionnaire_response.save()
        
        return redirect('website:trainer_questionnaires')
    
    context = {
        'trainer': trainer,
        'questionnaire_response': questionnaire_response,
    }
    
    return render(request, 'website/trainer/review_questionnaire.html', context)

@login_required
def session_report(request, session_id):
    """
    Generate a report for a specific session including:
    - Attendance statistics
    - Questionnaire completion status
    - Overall student performance
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
        session = Session.objects.get(session_id=session_id, trainers=trainer)
    except (Trainer.DoesNotExist, Session.DoesNotExist):
        return redirect('website:trainer_dashboard')
    
    # Get attendance data
    from .models import Attendance
    attendances = Attendance.objects.filter(session=session)
    
    # Calculate attendance statistics
    total_reservations = Reservation.objects.filter(
        course_instance__sessions=session,
        status__in=['UPCOMING', 'IN_PROGRESS', 'COMPLETED']
    ).count()
    
    attended_first_half = attendances.filter(first_half=True).count()
    attended_second_half = attendances.filter(second_half=True).count()
    attended_both = attendances.filter(first_half=True, second_half=True).count()
    
    attendance_rate = 0
    if total_reservations > 0:
        # Average of first and second half attendance
        attendance_rate = ((attended_first_half / total_reservations) + 
                          (attended_second_half / total_reservations)) / 2 * 100
    
    # Get questionnaire data for this session
    questionnaire_responses = QuestionnaireResponse.objects.filter(
        session=session
    ).select_related('user')
    
    total_questionnaires = questionnaire_responses.count()
    completed_questionnaires = questionnaire_responses.filter(
        status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED']
    ).count()
    
    questionnaire_completion_rate = 0
    if total_questionnaires > 0:
        questionnaire_completion_rate = (completed_questionnaires / total_questionnaires) * 100
    
    # Calculate average scores
    avg_score = 0
    if completed_questionnaires > 0:
        total_score = 0
        for response in questionnaire_responses.filter(
            status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED']
        ):
            total_score += response.total_score
        avg_score = total_score / completed_questionnaires
    
    context = {
        'trainer': trainer,
        'session': session,
        'total_reservations': total_reservations,
        'attended_first_half': attended_first_half,
        'attended_second_half': attended_second_half,
        'attended_both': attended_both,
        'attendance_rate': round(attendance_rate, 1),
        'total_questionnaires': total_questionnaires,
        'completed_questionnaires': completed_questionnaires,
        'questionnaire_completion_rate': round(questionnaire_completion_rate, 1),
        'avg_score': round(avg_score, 1),
        'attendances': attendances.select_related('user'),
    }
    
    return render(request, 'website/trainer/session_report.html', context)

@login_required
def trainer_course_materials(request, course_id=None):
    """
    View for trainer to manage course materials
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        return redirect('website:home')
    
    # Get the courses this trainer teaches
    trainer_courses = Course.objects.filter(
        sessions__trainers=trainer
    ).distinct()
    
    selected_course = None
    course_materials = []
    
    if course_id:
        try:
            selected_course = Course.objects.get(course_id=course_id)
            # Verify trainer is assigned to this course
            if not trainer_courses.filter(course_id=course_id).exists():
                return redirect('website:trainer_course_materials')
            
            # Get course materials
            from .models import CourseContent
            course_materials = CourseContent.objects.filter(
                course=selected_course,
                is_active=True
            )
        except Course.DoesNotExist:
            pass
    
    context = {
        'trainer': trainer,
        'trainer_courses': trainer_courses,
        'selected_course': selected_course,
        'course_materials': course_materials,
    }
    
    return render(request, 'website/trainer/course_materials.html', context)

@login_required
@require_POST
def upload_course_material(request, course_id):
    """
    API to upload a new course material
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
        course = Course.objects.get(course_id=course_id)
        
        # Verify trainer is assigned to this course
        if not Session.objects.filter(course=course, trainers=trainer).exists():
            return JsonResponse({'status': 'error', 'message': 'Access denied'}, status=403)
        
        # Process the upload
        title = request.POST.get('title', '')
        description = request.POST.get('description', '')
        content_type = request.POST.get('content_type', 'DOCUMENT')
        file = request.FILES.get('file')
        
        if not title or not file:
            return JsonResponse({'status': 'error', 'message': 'Title and file are required'}, status=400)
        
        # Create new course content
        from .models import CourseContent
        course_content = CourseContent.objects.create(
            course=course,
            title=title,
            description=description,
            file=file,
            content_type=content_type,
            created_by=request.user,
            is_active=True
        )
        
        return JsonResponse({
            'status': 'success',
            'message': 'Course material uploaded successfully',
            'content': {
                'id': course_content.content_id,
                'title': course_content.title,
                'description': course_content.description,
                'file_url': course_content.file.url,
                'content_type': course_content.get_content_type_display()
            }
        })
        
    except (Trainer.DoesNotExist, Course.DoesNotExist):
        return JsonResponse({'status': 'error', 'message': 'Course or trainer not found'}, status=404)
    except Exception as e:
        logger.error(f"Error uploading course material: {str(e)}")
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@login_required
def trainer_profile(request):
    """
    View for trainer to manage their profile
    """
    try:
        trainer = Trainer.objects.get(user=request.user)
    except Trainer.DoesNotExist:
        return redirect('website:home')
    
    # Get statistics for this trainer
    total_sessions = Session.objects.filter(trainers=trainer).count()
    active_sessions = Session.objects.filter(
        trainers=trainer,
        start_date__lte=timezone.now(),
        end_date__gte=timezone.now(),
        is_active=True
    ).count()
    
    # Get courses this trainer is qualified to teach
    qualified_courses = trainer.courses.all()
    
    if request.method == 'POST':
        # Update trainer profile
        pass  # Implementation would depend on what fields can be updated
    
    context = {
        'trainer': trainer,
        'total_sessions': total_sessions,
        'active_sessions': active_sessions,
        'qualified_courses': qualified_courses,
    }
    
    return render(request, 'website/trainer/profile.html', context)

@login_required
def individual_user_dashboard(request):
    """
    Dashboard view for individual users
    Shows active sessions, upcoming sessions, and pending questionnaires/surveys
    """
    # Check if user is an individual user type
    user_types = ['EXTERNAL_INDIVIDUAL']
    if not request.user.user_type or request.user.user_type.code not in user_types:
        return redirect('website:home')
    
    # Current date and time for calculations
    now = timezone.now()
    today = now.date()
    
    # Get active sessions (currently in progress)
    active_sessions = Session.objects.filter(
        course_instance__reservation__user=request.user,
        start_date__lte=now,
        end_date__gte=now,
        is_active=True
    ).select_related('course', 'room').distinct()
    
    # Get upcoming sessions (not yet started)
    upcoming_sessions = Session.objects.filter(
        course_instance__reservation__user=request.user,
        start_date__gt=now,
        is_active=True
    ).select_related('course', 'room').order_by('start_date').distinct()
    
    # Get pending questionnaires
    pending_questionnaires = QuestionnaireResponse.objects.filter(
        user=request.user,
        status='PENDING_SUBMISSION'
    ).select_related('questionnaire', 'course')
    
    # Get pending surveys
    pending_surveys = SurveyResponse.objects.filter(
        user=request.user,
        status='PENDING'
    ).select_related('survey', 'reservation')
    
    context = {
        'page_title': 'My Dashboard',
        'active_sessions': active_sessions,
        'upcoming_sessions': upcoming_sessions,
        'pending_questionnaires': pending_questionnaires,
        'pending_surveys': pending_surveys
    }
    
    return render(request, 'website/dashboard/individual_user_dashboard.html', context)

@login_required
def corporate_user_dashboard(request):
    """
    Dashboard view for corporate users
    Shows active sessions, upcoming sessions, and pending questionnaires/surveys
    """
    # Check if user is a corporate user type
    user_types = ['EXTERNAL_CORPORATE_TRAINEE']
    if not request.user.user_type or request.user.user_type.code not in user_types:
        return redirect('website:home')
    
    # Current date and time for calculations
    now = timezone.now()
    today = now.date()
    
    # Get active sessions (currently in progress)
    active_sessions = Session.objects.filter(
        course_instance__reservation__user=request.user,
        start_date__lte=now,
        end_date__gte=now,
        is_active=True
    ).select_related('course', 'room').distinct()
    
    # Get upcoming sessions (not yet started)
    upcoming_sessions = Session.objects.filter(
        course_instance__reservation__user=request.user,
        start_date__gt=now,
        is_active=True
    ).select_related('course', 'room').order_by('start_date').distinct()
    
    # Get pending questionnaires
    pending_questionnaires = QuestionnaireResponse.objects.filter(
        user=request.user,
        status='PENDING_SUBMISSION'
    ).select_related('questionnaire', 'course')
    
    # Get pending surveys
    pending_surveys = SurveyResponse.objects.filter(
        user=request.user,
        status='PENDING'
    ).select_related('survey', 'reservation')
    
    context = {
        'page_title': 'My Corporate Dashboard',
        'active_sessions': active_sessions,
        'upcoming_sessions': upcoming_sessions,
        'pending_questionnaires': pending_questionnaires,
        'pending_surveys': pending_surveys
    }
    
    return render(request, 'website/dashboard/corporate_user_dashboard.html', context)

@login_required
def internal_gb_dashboard(request):
    """
    Dashboard view for internal GB users
    Shows active sessions, upcoming sessions, and pending questionnaires/surveys
    """
    # Check if user is an internal GB user type
    user_types = ['INTERNAL_GB']
    if not request.user.user_type or request.user.user_type.code not in user_types:
        return redirect('website:home')
    
    # Current date and time for calculations
    now = timezone.now()
    today = now.date()
    
    # Get active sessions (currently in progress) for this user
    active_sessions = Session.objects.filter(
        course_instance__reservation__user=request.user,
        start_date__lte=now,
        end_date__gte=now,
        is_active=True
    ).select_related('course', 'room').distinct()
    
    # Get upcoming sessions (not yet started) for this user
    upcoming_sessions = Session.objects.filter(
        course_instance__reservation__user=request.user,
        start_date__gt=now,
        is_active=True
    ).select_related('course', 'room').order_by('start_date').distinct()
    
    # Get pending questionnaires for this user
    pending_questionnaires = QuestionnaireResponse.objects.filter(
        user=request.user,
        status='PENDING_SUBMISSION'
    ).select_related('questionnaire', 'course')
    
    # Get pending surveys for this user
    pending_surveys = SurveyResponse.objects.filter(
        user=request.user,
        status='PENDING'
    ).select_related('survey', 'reservation')
    
    # Get pending team approval requests if user is a GB supervisor
    pending_approval_requests = []
    try:
        # Check if user has a linked GB_Employee profile via gb_employee field
        if hasattr(request.user, 'gb_employee') and request.user.gb_employee:
            # Check if user is a supervisor and get pending requests for their team
            pending_approval_requests = GBEmployeeRequest.objects.filter(
                supervisor=request.user.gb_employee,
                status='PENDING'
            ).select_related('employee', 'course_instance__course')
        else:
            logger.info(f"User {request.user.username} does not have a linked GB_Employee profile")
            pending_approval_requests = []
    except Exception as e:
        logger.warning(f"Error fetching pending approval requests: {str(e)}")
        pending_approval_requests = []
    
    context = {
        'page_title': 'My Dashboard',
        'active_sessions': active_sessions,
        'upcoming_sessions': upcoming_sessions,
        'pending_questionnaires': pending_questionnaires,
        'pending_surveys': pending_surveys,
        'pending_approval_requests': pending_approval_requests,
    }
    
    return render(request, 'website/dashboard/internal_gb_dashboard.html', context) 