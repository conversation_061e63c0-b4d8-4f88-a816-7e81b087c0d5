{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "All User Reservations" %} | GB Academy{% endblock %}

{% block extra_css %}
<style>
    /* Ensure modals stack properly above all content */
    #editReservationModal {
        z-index: 9999 !important; /* Force highest z-index */
    }
    
    /* Ensure modal content is above backdrop */
    #editReservationModal > div > div:last-child {
        position: relative;
        z-index: 10000 !important; /* Even higher z-index */
    }
    
    /* Attendance modal styling */
    #attendanceModal {
        z-index: 9999 !important; /* Force highest z-index */
    }
    
    /* Ensure attendance modal content is above backdrop */
    #attendanceModal > div > div:last-child {
        position: relative;
        z-index: 10000 !important; /* Even higher z-index */
    }
    
    /* Make sure notification panels have lower z-index */
    .mb-8.bg-yellow-900\/20 { /* Escaped / for CSS selector */
        z-index: 10;
        position: relative;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex flex-wrap justify-between items-center mb-4">
        <h1 class="text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
            </svg>
            {% trans "All User Reservations" %}
        </h1>
        <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {% trans "Courses" %}
        </a>
    </div>

    {% if messages %}
    <div class="mb-8">
        {% for message in messages %}
        <div class="p-4 mb-4 text-sm rounded-lg {% if message.tags == 'success' %}bg-green-800/30 backdrop-blur-md text-green-400 border border-green-400/20{% elif message.tags == 'error' %}bg-red-800/30 backdrop-blur-md text-red-400 border border-red-400/20{% elif message.tags == 'warning' %}bg-yellow-800/30 backdrop-blur-md text-yellow-400 border border-yellow-400/20{% else %}bg-blue-800/30 backdrop-blur-md text-blue-400 border border-blue-400/20{% endif %}">
            {{ message }}
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <!-- Filter Form - Match page background -->
    <div class="rounded-lg border border-blue-800 overflow-hidden shadow-lg mb-8">
        <div class="p-4">            
            <form method="GET" action="{% url 'website:admin_reservations' %}">
                <div class="flex items-center">
                    <div class="flex-1 grid grid-cols-6 gap-x-2">
                        <div>
                        <label for="course_id" class="block text-sm font-medium text-white mb-1">{% trans "Course" %}</label>
                            <select id="course_id" name="course_id" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                            <option value="">{% trans "All Courses" %}</option>
                            {% for course in courses %}
                            <option value="{{ course.course_id }}" {% if filters.course_id == course.course_id|slugify %}selected{% endif %}>{{ course.name_en }}</option>
                            {% endfor %}
                        </select>
                    </div>
                        <div>
                            <label for="instance_id" class="block text-sm font-medium text-white mb-1">{% trans "Course Instance" %}</label>
                            <select id="instance_id" name="instance_id" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                                <option value="">{% trans "All Instances" %}</option>
                                {% for instance in course_instances %}
                                <option value="{{ instance.instance_id }}" {% if filters.instance_id == instance.instance_id|slugify %}selected{% endif %}>{{ instance.course.name_en }} ({{ instance.start_date|date:"M d, Y" }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div>
                        <label for="user_id" class="block text-sm font-medium text-white mb-1">{% trans "User" %}</label>
                            <select id="user_id" name="user_id" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                            <option value="">{% trans "All Users" %}</option>
                            {% for user in users %}
                            <option value="{{ user.id }}" {% if filters.user_id == user.id|slugify %}selected{% endif %}>{{ user.username }}</option>
                            {% endfor %}
                        </select>
                    </div>
                        <div>
                        <label for="status" class="block text-sm font-medium text-white mb-1">{% trans "Status" %}</label>
                            <select id="status" name="status" class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="UPCOMING" {% if filters.status == 'UPCOMING' %}selected{% endif %}>{% trans "Upcoming" %}</option>
                            <option value="IN_PROGRESS" {% if filters.status == 'IN_PROGRESS' %}selected{% endif %}>{% trans "In Progress" %}</option>
                            <option value="COMPLETED" {% if filters.status == 'COMPLETED' %}selected{% endif %}>{% trans "Completed" %}</option>
                            <option value="WAITING_LIST" {% if filters.status == 'WAITING_LIST' %}selected{% endif %}>{% trans "Waiting List" %}</option>
                            <option value="CANCELLED" {% if filters.status == 'CANCELLED' %}selected{% endif %}>{% trans "Cancelled" %}</option>
                            <option value="WAITING_TO_PAY" {% if filters.status == 'WAITING_TO_PAY' %}selected{% endif %}>{% trans "Waiting to Pay" %}</option>
                        </select>
                    </div>
                        <div>
                        <label for="start_date" class="block text-sm font-medium text-white mb-1">{% trans "Start Date (From)" %}</label>
                        <input type="date" id="start_date" name="start_date" value="{{ filters.start_date|default:'' }}" placeholder="mm/dd/yyyy"
                                   class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                    </div>
                        <div>
                        <label for="end_date" class="block text-sm font-medium text-white mb-1">{% trans "End Date (To)" %}</label>
                        <input type="date" id="end_date" name="end_date" value="{{ filters.end_date|default:'' }}" placeholder="mm/dd/yyyy"
                                   class="w-full px-2 py-2 bg-blue-800/50 border border-blue-700 rounded-md text-white text-sm focus:ring-primary focus:border-primary">
                        </div>
                    </div>
                    <div class="flex space-x-2 items-end pl-3">
                        <a href="{% url 'website:admin_reservations' %}" class="px-4 py-2 text-sm font-medium text-white hover:text-gray-200 border border-blue-700 rounded-md">
                            {% trans "Reset" %}
                        </a>
                        <button type="submit" class="px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                            {% trans "Apply Filters" %}
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Emergency Cancellation Requests Section -->
    <div class="mb-8 bg-yellow-900/20 backdrop-blur-md rounded-lg border border-yellow-800/30 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-yellow-800/30 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white flex items-center">
                <svg class="w-6 h-6 mr-2 text-yellow-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                </svg>
                {% trans "Emergency Cancellation Requests" %}
                <span id="pending-count" class="ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none rounded-full bg-yellow-500 text-white">
                    Loading...
                </span>
            </h2>
            <a href="{% url 'website:admin_cancellation_requests' %}" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-yellow-600 text-white hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500">
                {% trans "View All Requests" %}
            </a>
        </div>

        <div id="emergency-requests-container" class="p-4">
            <div class="flex justify-center">
                <svg class="animate-spin h-8 w-8 text-yellow-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </div>
        </div>
    </div>

    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% trans "All Course Reservations" %}</h2>
        </div>

        {% if all_reservations %}
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "User" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Start Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "End Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Sessions" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Reserved On" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Status" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-transparent divide-y divide-white/10">
                    {% for reservation in all_reservations %}
                    <tr class="hover:bg-white/5">
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ reservation.user.username }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.user.email }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm font-medium text-white">{{ reservation.course_instance.course.name_en }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.course.get_category_display }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.start_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.start_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.end_date|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.course_instance.end_date|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.course_instance.sessions.count }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="text-sm text-white">{{ reservation.created_at|date:"M d, Y" }}</div>
                            <div class="text-xs text-gray-400">{{ reservation.created_at|date:"h:i A" }}</div>
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            {% if reservation.status == 'UPCOMING' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'COMPLETED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-800/30 text-gray-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'IN_PROGRESS' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'WAITING_LIST' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-yellow-800/30 text-yellow-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'CANCELLED' %}
                            <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-800/30 text-red-400">
                                {{ reservation.get_status_display }}
                            </span>
                            {% elif reservation.status == 'WAITING_TO_PAY' %}
                            <div class="badge badge-warning badge-sm">{% trans "Waiting to Pay" %}</div>
                            {% else %}
                                {# Fallback to date-based status if status field is not available #}
                                {% if reservation.course_instance.start_date > now %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-800/30 text-green-400">
                                    {% trans "Upcoming" %}
                                </span>
                                {% elif reservation.course_instance.end_date < now %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-gray-800/30 text-gray-400">
                                    {% trans "Completed" %}
                                </span>
                                {% else %}
                                <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-blue-800/30 text-blue-400">
                                    {% trans "In Progress" %}
                                </span>
                                {% endif %}
                            {% endif %}
                        </td>
                        <td class="px-6 py-4 whitespace-nowrap">
                            <div class="flex justify-end items-center space-x-4">
                                <a href="{% url 'website:course_detail' course_id=reservation.course_instance.course.course_id %}?ref=admin-reservations" class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "View Course" %}
                                </a>
                                <button type="button" 
                                        onclick="openEditModal('{{ reservation.reservation_id }}', '{{ reservation.user.username }}', '{{ reservation.course_instance.course.name_en }}', '{{ reservation.status }}', '{{ reservation.course_instance.instance_id }}')"
                                        class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "Edit" %}
                                </button>
                                <button type="button"
                                        onclick="viewAttendance('{{ reservation.course_instance.instance_id }}', '{{ reservation.user.id }}', '{{ reservation.course_instance.course.name_en|escapejs }}', '{{ reservation.user.username|escapejs }}')"
                                        class="text-blue-400 hover:text-blue-300 text-sm">
                                    {% trans "Attendance" %}
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="px-6 py-12 text-center">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
            </svg>
            <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Reservations" %}</h3>
            <p class="mt-1 text-sm text-gray-400">{% trans "There are no course reservations in the system yet." %}</p>
            <div class="mt-6">
                <a href="{% url 'website:courses' %}" class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-primary hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
                    </svg>
                    {% trans "Manage Courses" %}
                </a>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- Edit Reservation Modal -->
<div id="editReservationModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <div class="inline-block align-bottom bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
            <form id="updateReservationForm" method="POST" action="{% url 'website:update_reservation' 0 %}">
                {% csrf_token %}
                <input type="hidden" id="reservation_id" name="reservation_id" value="">
                <input type="hidden" id="course_instance_id" name="course_instance_id" value="">
                
                <div class="px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                    <div class="sm:flex sm:items-start">
                        <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                            <h3 class="text-lg leading-6 font-medium text-white" id="modalTitle">
                                {% trans "Edit Reservation" %}
                            </h3>
                            <div class="mt-6 space-y-4">
                                <div>
                                    <label for="edit_user" class="block text-sm font-medium text-gray-300">{% trans "User" %}</label>
                                    <input type="text" id="edit_user" class="mt-1 block w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2" disabled>
                                </div>
                                <div>
                                    <label for="edit_course" class="block text-sm font-medium text-gray-300">{% trans "Course" %}</label>
                                    <input type="text" id="edit_course" class="mt-1 block w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2" disabled>
                                </div>
                                
                                <!-- Course capacity and waiting list information -->
                                <div class="grid grid-cols-2 gap-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">{% trans "Available Seats" %}</label>
                                        <div class="flex items-center mt-1">
                                            <span id="available_seats_count" class="text-lg font-semibold text-white">-</span>
                                            <span class="text-sm text-gray-400 ml-2">/ <span id="total_capacity">-</span></span>
                                        </div>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-300">{% trans "Waiting List" %}</label>
                                        <div class="flex items-center mt-1">
                                            <span id="waiting_list_count" class="text-lg font-semibold text-white">-</span>
                                            <span class="text-sm text-gray-400 ml-2">{% trans "users waiting" %}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <label for="edit_status" class="block text-sm font-medium text-gray-300">{% trans "Status" %}</label>
                                    <select id="edit_status" name="status" class="mt-1 block w-full bg-blue-800/50 border border-blue-700 rounded-md text-white px-3 py-2">
                                        <option value="PENDING">{% trans "Pending" %}</option>
                                        <option value="WAITING_LIST">{% trans "Waiting List" %}</option>
                                        <option value="UPCOMING">{% trans "Upcoming" %}</option>
                                        <option value="IN_PROGRESS">{% trans "In Progress" %}</option>
                                        <option value="COMPLETED">{% trans "Completed" %}</option>
                                        <option value="CANCELLED">{% trans "Cancelled" %}</option>
                                        <option value="WAITING_TO_PAY">{% trans "Waiting to Pay" %}</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="bg-blue-800/50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse">
                    <button type="submit" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-primary text-base font-medium text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary sm:ml-3 sm:w-auto sm:text-sm">
                        {% trans "Update" %}
                    </button>
                    <button type="button" onclick="closeEditModal()" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-500 shadow-sm px-4 py-2 bg-transparent text-base font-medium text-gray-300 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                        {% trans "Cancel" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Attendance Modal -->
<div id="attendanceModal" class="fixed inset-0 z-50 hidden overflow-y-auto">
    <div class="flex items-center justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <div class="fixed inset-0 transition-opacity" aria-hidden="true">
            <div class="absolute inset-0 bg-black opacity-75"></div>
        </div>

        <!-- Modal panel -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
        <div class="inline-block align-bottom bg-blue-900 rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-5xl sm:w-full">
            <div class="bg-blue-900 px-6 pt-6 pb-5 sm:p-8 sm:pb-6">
                <div class="sm:flex sm:items-start">
                    <div class="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left w-full">
                        <div class="flex justify-between items-center mb-6">
                            <h3 class="text-xl font-semibold text-white" id="attendanceModalTitle">
                                {% trans "Course Attendance" %}
                            </h3>
                            <button type="button" id="closeAttendanceModal" class="text-gray-400 hover:text-white" onclick="closeAttendanceModal()">
                                <svg class="h-7 w-7" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </button>
                        </div>
                        
                        <div id="attendanceModalContent">
                            <div class="flex items-center justify-center h-32">
                                <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Fetch emergency cancellation requests
        fetchEmergencyCancellationRequests();
        
        // Edit Reservation Modal Functions
        function openEditModal(reservationId, username, courseName, status, courseInstanceId) {
            // Update form action URL with the correct reservation ID
            document.getElementById('updateReservationForm').action = "{% url 'website:update_reservation' 0 %}".replace('0', reservationId);
            
            // Set values in the form
            document.getElementById('reservation_id').value = reservationId;
            document.getElementById('edit_user').value = username;
            document.getElementById('edit_course').value = courseName;
            document.getElementById('edit_status').value = status;
            document.getElementById('course_instance_id').value = courseInstanceId;
            
            // Fetch course instance capacity and waiting list information
            fetchCourseInstanceDetails(courseInstanceId);
            
            // Show the modal
            document.getElementById('editReservationModal').classList.remove('hidden');
        }
        
        function fetchCourseInstanceDetails(courseInstanceId) {
            // Set loading state
            document.getElementById('available_seats_count').textContent = "...";
            document.getElementById('total_capacity').textContent = "...";
            document.getElementById('waiting_list_count').textContent = "...";
            
            // Make API request to get course instance details
            fetch(`/api/course-instances/${courseInstanceId}/details/`)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    // Update the UI with the retrieved information
                    document.getElementById('available_seats_count').textContent = data.available_seats;
                    document.getElementById('total_capacity').textContent = data.capacity;
                    document.getElementById('waiting_list_count').textContent = data.waiting_list_count;
                })
                .catch(error => {
                    console.error('Error fetching course instance details:', error);
                    document.getElementById('available_seats_count').textContent = "-";
                    document.getElementById('total_capacity').textContent = "-";
                    document.getElementById('waiting_list_count').textContent = "-";
                });
        }
        
        function closeEditModal() {
            // Hide the modal
            document.getElementById('editReservationModal').classList.add('hidden');
        }
        
        // Make these functions available to the window
        window.openEditModal = openEditModal;
        window.closeEditModal = closeEditModal;
        
        // Function to fetch and display emergency cancellation requests
        function fetchEmergencyCancellationRequests() {
            fetch('{% url "website:emergency_cancellation_api" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.status !== 'success') {
                        throw new Error(data.message || 'Failed to fetch emergency cancellation requests');
                    }
                    
                    const pendingCount = data.count;
                    const pendingRequests = data.requests;
                    
                    // Update count badge
                    document.getElementById('pending-count').textContent = pendingCount;
                    
                    // Get container
                    const container = document.getElementById('emergency-requests-container');
                    
                    // Clear loading indicator
                    container.innerHTML = '';
                    
                    if (pendingCount === 0) {
                        // Show no requests message
                        container.innerHTML = `
                            <div class="text-center py-4">
                                <svg class="mx-auto h-10 w-10 text-yellow-500/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Requests" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "There are no emergency cancellation requests waiting for review." %}</p>
                            </div>
                        `;
                    } else {
                        // Create a table to display the requests
                        const table = document.createElement('div');
                        table.className = 'overflow-x-auto';
                        table.innerHTML = `
                            <table class="min-w-full divide-y divide-yellow-800/30">
                                <thead class="bg-yellow-900/30">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "User" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Course" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Start Date" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Reason" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                                            {% trans "Actions" %}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-transparent divide-y divide-yellow-800/30" id="requests-table-body">
                                </tbody>
                            </table>
                        `;
                        
                        container.appendChild(table);
                        const tableBody = document.getElementById('requests-table-body');
                        
                        // Add up to 3 most recent requests
                        const requestsToShow = pendingRequests.slice(0, 3);
                        requestsToShow.forEach(request => {
                            // Format dates
                            const startDate = new Date(request.course_instance.start_date);
                            const createdAt = new Date(request.created_at);
                            
                            const row = document.createElement('tr');
                            row.className = 'hover:bg-yellow-900/20';
                            row.innerHTML = `
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm font-medium text-white">${request.user.full_name}</div>
                                    <div class="text-xs text-gray-400">${request.user.email}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">${request.course.name}</div>
                                    <div class="text-xs text-gray-400">${request.course.category}</div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div class="text-sm text-white">${startDate.toLocaleDateString()}</div>
                                    <div class="text-xs text-gray-400">${startDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                                </td>
                                <td class="px-6 py-4">
                                    <div class="text-sm text-white max-h-12 overflow-hidden text-ellipsis">${request.reason.length > 50 ? request.reason.substring(0, 50) + '...' : request.reason}</div>
                                    ${request.has_attachment ? `
                                    <div class="text-xs text-yellow-500 mt-1">{% trans "Has attachment" %}</div>
                                    ` : ''}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right">
                                    <a href="{% url 'website:admin_cancellation_requests' %}" class="text-yellow-500 hover:text-yellow-400">
                                        {% trans "Review" %}
                                    </a>
                                </td>
                            `;
                            
                            tableBody.appendChild(row);
                        });
                        
                        // If there are more requests than shown, add a note
                        if (pendingCount > 3) {
                            const moreMsg = document.createElement('div');
                            moreMsg.className = 'text-center py-2 text-yellow-500 text-sm';
                            moreMsg.textContent = `{% trans "and" %} ${pendingCount - 3} {% trans "more pending requests" %}`;
                            container.appendChild(moreMsg);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching emergency cancellation requests:', error);
                    const container = document.getElementById('emergency-requests-container');
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <svg class="mx-auto h-10 w-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error Loading Requests" %}</h3>
                            <p class="mt-1 text-sm text-red-400">{% trans "Unable to load emergency cancellation requests." %}</p>
                        </div>
                    `;
                    document.getElementById('pending-count').textContent = "!";
                });
        }
        
        // Course Instance Filtering
        const courseSelect = document.getElementById('course_id');
        const instanceSelect = document.getElementById('instance_id');
        
        // Store all course instances for filtering
        const allInstances = [];
        {% for instance in course_instances %}
        allInstances.push({
            id: "{{ instance.instance_id }}",
            courseId: "{{ instance.course.course_id }}",
            name: "{{ instance.course.name_en }} ({{ instance.start_date|date:'M d, Y' }})"
        });
        {% endfor %}
        
        // Filter course instances when course selection changes
        courseSelect.addEventListener('change', function() {
            const selectedCourseId = this.value;
            
            // Clear the instance select
            instanceSelect.innerHTML = '<option value="">{% trans "All Instances" %}</option>';
            
            // If no course is selected, we're done
            if (!selectedCourseId) {
                // Add all instances back
                allInstances.forEach(function(instance) {
                    const option = document.createElement('option');
                    option.value = instance.id;
                    option.textContent = instance.name;
                    instanceSelect.appendChild(option);
                });
                return;
            }
            
            // Filter instances for the selected course
            const filteredInstances = allInstances.filter(function(instance) {
                return instance.courseId === selectedCourseId;
            });
            
            // Add filtered instances to the select
            filteredInstances.forEach(function(instance) {
                const option = document.createElement('option');
                option.value = instance.id;
                option.textContent = instance.name;
                instanceSelect.appendChild(option);
            });
        });

        // Attendance Modal Functions
        function viewAttendance(courseInstanceId, userId, courseName, userName) {
            // Get modal elements
            const modal = document.getElementById('attendanceModal');
            const modalTitle = document.getElementById('attendanceModalTitle');
            const modalContent = document.getElementById('attendanceModalContent');
            
            if (!modal || !modalTitle || !modalContent) {
                console.error('Attendance modal elements not found');
                return;
            }
            
            // Update modal title with course and user name
            if (userName) {
                modalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - ${userName} - {% trans "Attendance" %}`;
            } else {
                modalTitle.innerHTML = `<span class="text-blue-400">${courseName}</span> - {% trans "Attendance" %}`;
            }
            
            // Show loading spinner
            modalContent.innerHTML = `
                <div class="flex items-center justify-center h-32">
                    <div class="animate-spin rounded-full h-10 w-10 border-b-2 border-white"></div>
                </div>
            `;
            
            // Show the modal
            modal.classList.remove('hidden');
            
            // Build URL with parameters for fetching attendance data
            let url = `/api/attendance?course_instance_id=${courseInstanceId}`;
            if (userId) {
                url += `&user_id=${userId}`;
            }
            
            // Fetch attendance data
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(data => {
                    if (data.status !== 'success') {
                        throw new Error(data.message || 'Failed to fetch attendance data');
                    }
                    
                    const sessions = data.sessions;
                    
                    if (sessions.length === 0) {
                        modalContent.innerHTML = `
                            <div class="text-center py-8">
                                <svg class="mx-auto h-12 w-12 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4"/>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Sessions Found" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "There are no sessions for this course instance yet." %}</p>
                            </div>
                        `;
                        return;
                    }
                    
                // Construct the form HTML
                let formHtml = `
                    <form id="admin-attendance-form" method="post" action="{% url 'website:admin_update_attendance' %}">
                        {% csrf_token %}
                        <input type="hidden" name="course_instance_id" value="${courseInstanceId}">
                        <input type="hidden" name="user_id" value="${userId}">
                        
                        <div class="overflow-x-auto rounded-lg border border-gray-700">
                            <table class="min-w-full divide-y divide-gray-700">
                                <thead class="bg-gray-800">
                                    <tr>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            {% trans "Date" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            {% trans "Time" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            {% trans "Room" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            {% trans "Status" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            {% trans "First Half" %}
                                        </th>
                                        <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                                            {% trans "Second Half" %}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-gray-900 divide-y divide-gray-800">
                    `;
                    
                    sessions.forEach(session => {
                        const attendance = session.attendance || {};
                        
                        // Set status classes and text
                        let statusClass = 'text-blue-400';
                        let statusText = '{% trans "Upcoming" %}';
                        
                        if (session.status === 'in_progress') {
                            statusClass = 'text-yellow-400';
                            statusText = '{% trans "In Progress" %}';
                        } else if (session.status === 'completed') {
                            statusClass = 'text-gray-400';
                            statusText = '{% trans "Completed" %}';
                        }
                        
                    // Debug session ID
                    console.log(`Session data:`, session);
                    
                    formHtml += `
                            <tr class="hover:bg-gray-800">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                    ${session.date}
                                <input type="hidden" name="session_id_${session.session_id}" value="${session.session_id}">
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                    ${session.time}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                    ${session.room}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span class="${statusClass} text-sm">${statusText}</span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                <label class="inline-flex items-center justify-center space-x-3 cursor-pointer">
                                    <input type="checkbox" 
                                           name="first_half_${session.session_id}" 
                                           class="h-5 w-5 rounded border-gray-500 bg-gray-700 text-blue-600 focus:ring-blue-600 focus:ring-offset-gray-800" 
                                           ${attendance.first_half ? 'checked' : ''}>
                                    <span class="text-white font-medium">{% trans "Present" %}</span>
                                </label>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-center">
                                <label class="inline-flex items-center justify-center space-x-3 cursor-pointer">
                                    <input type="checkbox" 
                                           name="second_half_${session.session_id}" 
                                           class="h-5 w-5 rounded border-gray-500 bg-gray-700 text-blue-600 focus:ring-blue-600 focus:ring-offset-gray-800" 
                                           ${attendance.second_half ? 'checked' : ''}>
                                    <span class="text-white font-medium">{% trans "Present" %}</span>
                                </label>
                                </td>
                            </tr>
                        `;
                    });
                    
                formHtml += `
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-5 bg-blue-900/20 border border-blue-800/50 rounded-md p-4 text-sm text-blue-300">
                            <div class="flex">
                                <div class="flex-shrink-0">
                                    <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <p class="font-medium text-blue-400 mb-1">{% trans "Attendance Information" %}</p>
                                <p>{% trans "As an administrator, you can update attendance records for any user and session." %}</p>
                                </div>
                            </div>
                        </div>
                    {% if user.user_type.code == 'SUPER_ADMIN' %}
                    <div class="mt-6 flex justify-end">
                        <button type="submit" id="saveAttendanceBtn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <svg class="-ml-1 mr-2 h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />
                            </svg>
                            {% trans "Save Attendance" %}
                        </button>
                    </div>
                    {% endif %}
                    </form>
                `;
                
                modalContent.innerHTML = formHtml;
                
                // Add event listener to the form to handle submission
                const attendanceForm = document.getElementById('admin-attendance-form');
                if (attendanceForm) {
                    attendanceForm.addEventListener('submit', function(e) {
                        e.preventDefault();
                        
                        // Log form data for debugging
                        const formData = new FormData(attendanceForm);
                        console.log("Submitting attendance form with data:");
                        for (let [key, value] of formData.entries()) {
                            console.log(key + ": " + value);
                        }
                        
                        // Submit the form via fetch API to better handle response
                        fetch(attendanceForm.action, {
                            method: 'POST',
                            body: formData,
                            headers: {
                                'X-Requested-With': 'XMLHttpRequest',
                                'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                            }
                        })
                        .then(response => {
                            if (!response.ok) {
                                throw new Error('Network response was not ok');
                            }
                            // Try to parse as JSON, fall back to text if not JSON
                            return response.json().catch(() => response.text());
                        })
                        .then(data => {
                            console.log("Server response:", data);
                            
                            // Close the modal
                            closeAttendanceModal();
                            
                            // Determine notification type based on status
                            let notificationClass = 'bg-green-500';
                            let message = '{% trans "Attendance saved successfully" %}';
                            
                            if (typeof data === 'object') {
                                if (data.status === 'error') {
                                    notificationClass = 'bg-red-500';
                                } else if (data.status === 'warning') {
                                    notificationClass = 'bg-yellow-500';
                                }
                                
                                if (data.message) {
                                    message = data.message;
                                }
                            }
                            
                            // Show notification
                            const notification = document.createElement('div');
                            notification.className = `fixed top-16 right-4 ${notificationClass} text-white px-4 py-2 rounded shadow-lg z-50`;
                            notification.textContent = message;
                            document.body.appendChild(notification);
                            
                            // Remove notification after 3 seconds
                            setTimeout(() => {
                                notification.remove();
                            }, 3000);
                        })
                        .catch(error => {
                            console.error('Error saving attendance:', error);
                            
                            // Show error notification
                            const notification = document.createElement('div');
                            notification.className = 'fixed top-16 right-4 bg-red-500 text-white px-4 py-2 rounded shadow-lg z-50';
                            notification.textContent = '{% trans "Error saving attendance" %}';
                            document.body.appendChild(notification);
                            
                            // Remove notification after 3 seconds
                            setTimeout(() => {
                                notification.remove();
                            }, 3000);
                        });
                    });
                }
                })
                .catch(error => {
                    console.error('Error fetching attendance data:', error);
                    modalContent.innerHTML = `
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error Loading Attendance" %}</h3>
                            <p class="mt-1 text-sm text-red-400">${error.message || '{% trans "Failed to load attendance data" %}'}</p>
                        </div>
                    `;
                });
        }
        
        // Make the function available to the window
        window.viewAttendance = viewAttendance;
        
        // Function to close the attendance modal
        function closeAttendanceModal() {
            const modal = document.getElementById('attendanceModal');
            if (modal) {
                modal.classList.add('hidden');
            }
        }
        
        // Make closeAttendanceModal available to the window
        window.closeAttendanceModal = closeAttendanceModal;
        
        // Add event listener to close button
        const closeAttendanceModalBtn = document.getElementById('closeAttendanceModal');
        if (closeAttendanceModalBtn) {
            closeAttendanceModalBtn.addEventListener('click', closeAttendanceModal);
        }
    });
</script>
{% endblock %} 