from django.contrib.auth.backends import ModelBackend
from django.db.models import Q
from .models import CustomUser

class EmailOrUsernameModelBackend(ModelBackend):
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None or password is None:
            return None
            
        try:
            # Try to fetch the user by email or username
            # Only authenticate external users (those with passwords)
            user = CustomUser.objects.get(
                Q(username=username) | Q(email=username)
            )
            # Ensure this is an external user and verify password
            if (user.check_password(password) and 
                hasattr(user, 'user_type') and 
                user.user_type and 
                user.user_type.code.startswith('EXTERNAL')):
                return user
        except CustomUser.DoesNotExist:
            return None
        return None

class OracleEmployeeBackend(ModelBackend):
    """
    Authentication backend for Oracle employees (no password required)
    """
    def authenticate(self, request, employee_id=None, **kwargs):
        if employee_id is None:
            return None
            
        try:
            # Authenticate Oracle employees by employee_id only
            # Only authenticate internal GB users
            user = CustomUser.objects.get(
                Q(employee_id=employee_id) | Q(gb_employee__employee_number=employee_id),
                user_type__code='INTERNAL_GB'
            )
            return user
        except CustomU<PERSON>.DoesNotExist:
            return None
        return None 