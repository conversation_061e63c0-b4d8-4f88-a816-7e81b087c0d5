{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ page_title }}{% endblock %}

{% block content %}
<div class="min-h-screen bg-[#172b56]">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto">
        <div class="flex justify-between items-center mb-4">
            <div>
                <h1 class="text-3xl font-bold text-white">{% trans "Super Admin Dashboard" %}</h1>
                <p class="text-gray-300">{% trans "Complete system overview and administration" %}</p>
            </div>
            <div class="flex space-x-4">
                <a href="{% url 'website:settings' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary hover:bg-primary/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                    {% trans "System Settings" %}
                </a>
                <a href="{% url 'website:calendar' %}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-secondary hover:bg-secondary/90">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    {% trans "Calendar" %}
                </a>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20 mb-8">
            <h2 class="text-xl font-semibold text-white mb-4">{% trans "Quick Actions" %}</h2>
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                <a href="{% url 'website:corporate_admins_view' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-indigo-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Manage Corporate Admins" %}</span>
                </a>
                
                <a href="{% url 'website:rooms' %}?tab=equipment" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-yellow-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-yellow-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Equipments And Rooms" %}</span>
                </a>
                
                <a href="{% url 'website:admin_reservations' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-pink-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-pink-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "User Management" %}</span>
                </a>
                
                <a href="{% url 'website:settings' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-blue-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "System Settings" %}</span>
                </a>
                
                <a href="{% url 'website:calendar' %}" class="flex items-center p-4 bg-white/5 rounded-lg hover:bg-white/10 transition-colors">
                    <div class="p-2 mr-3 bg-teal-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-teal-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                    </div>
                    <span class="text-sm text-white">{% trans "Calendar Management" %}</span>
                </a>
            </div>
        </div>
        
        <!-- Stats Overview -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-8">
            <!-- Active Trainers -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Active Trainers" %}</p>
                        <p class="text-2xl font-bold text-white">{{ active_trainers }}</p>
                    </div>
                    <div class="p-3 bg-primary/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Active Rooms -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Active Rooms" %}</p>
                        <p class="text-2xl font-bold text-white">{{ active_rooms }}</p>
                    </div>
                    <div class="p-3 bg-green-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Active Trainees -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Active Trainees" %}</p>
                        <p class="text-2xl font-bold text-white">{{ active_trainees }}</p>
                    </div>
                    <div class="p-3 bg-blue-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- Completed Courses -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Completed Courses" %}</p>
                        <p class="text-2xl font-bold text-white">{{ completed_courses }}</p>
                    </div>
                    <div class="p-3 bg-purple-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path d="M12 14l9-5-9-5-9 5 9 5z" />
                            <path d="M12 14l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 14l9-5-9-5-9 5 9 5zm0 0l6.16-3.422a12.083 12.083 0 01.665 6.479A11.952 11.952 0 0012 20.055a11.952 11.952 0 00-6.824-2.998 12.078 12.078 0 01.665-6.479L12 14zm-4 6v-7.5l4-2.222" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <!-- Course Categories and Corporate Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <!-- Course Categories -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">{% trans "Course Categories" %}</h2>
                    <div class="p-2 bg-purple-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                    </div>
                </div>
                <div class="space-y-4">
                    {% for category in course_categories %}
                    <div class="relative pt-1">
                        <div class="flex mb-2 items-center justify-between">
                            <div>
                                <span class="text-xs font-semibold inline-block py-1 px-2 uppercase rounded-full text-white bg-white/10">
                                    {{ category.category }}
                                </span>
                            </div>
                            <div class="text-right">
                                <span class="text-xs font-semibold inline-block text-white">
                                    {{ category.count }}
                                </span>
                            </div>
                        </div>
                        <div class="overflow-hidden h-2 mb-4 text-xs flex rounded bg-white/5">
                            <div style="width:{{ category.count }}0%" class="shadow-none flex flex-col text-center whitespace-nowrap text-white justify-center bg-purple-500"></div>
                        </div>
                    </div>
                    {% empty %}
                    <p class="text-center text-white/60">{% trans "No course categories available" %}</p>
                    {% endfor %}
                </div>
            </div>

            <!-- Corporate Stats -->
            <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-xl font-semibold text-white">{% trans "Corporate Stats" %}</h2>
                    <div class="p-2 bg-indigo-500/20 rounded-full">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                        </svg>
                    </div>
                </div>
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "External Corporate Admins" %}</p>
                        <p class="text-2xl font-bold text-white">{{ external_corporate_admins }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Online Course Instances" %}</p>
                        <p class="text-2xl font-bold text-white">{{ online_course_instances }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "Corporate Requests" %}</p>
                        <p class="text-2xl font-bold text-white">{{ pending_corporate_requests }}</p>
                    </div>
                    <div>
                        <p class="text-sm font-medium text-white/60">{% trans "New Course Requests" %}</p>
                        <p class="text-2xl font-bold text-white">{{ pending_new_course_requests }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sessions -->
        <div class="grid grid-cols-1 gap-4 mb-8">
            <!-- In Progress Sessions -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-blue-800/40 to-indigo-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "In Progress Sessions" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-green-500/30 text-green-300 border border-green-500/30">{{ in_progress_sessions.count }} {% trans "Active" %}</span>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-white/10">
                        <thead class="bg-white/5">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Course" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Trainer" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Room" %}
                                </th>
                                <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                    {% trans "Time" %}
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white/5 divide-y divide-white/10">
                            {% if in_progress_sessions %}
                                {% for session in in_progress_sessions %}
                                    <tr>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.course.name_en }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {% for trainer in session.trainers.all %}
                                                {{ trainer.user.first_name }} {{ trainer.user.last_name }}{% if not forloop.last %}, {% endif %}
                                            {% endfor %}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.room.name|default:"Online" }}
                                        </td>
                                        <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                            {{ session.start_date|date:"H:i" }} - {{ session.end_date|date:"H:i" }}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="4" class="px-6 py-4 text-center text-sm text-white/70">
                                        {% trans "No sessions currently in progress" %}
                                    </td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Upcoming Events Section -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-8">
            <!-- Upcoming Events -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-6 border-b border-white/10 bg-gradient-to-r from-indigo-800/40 to-blue-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-indigo-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "Upcoming Events" %}</h2>
                        </div>
                        <span class="px-3 py-1 text-xs font-medium rounded-full bg-indigo-500/30 text-indigo-300 border border-indigo-500/30">{% trans "Next 5" %}</span>
                    </div>
                </div>
                <div id="dashboardUpcomingEvents" class="p-4 space-y-2 max-h-80 overflow-y-auto scrollbar-thin scrollbar-thumb-white/20 scrollbar-track-transparent">
                    <div class="flex items-center justify-center h-24">
                        <div class="text-white/70 flex items-center">
                            <div class="animate-spin mr-3 h-5 w-5 border-2 border-white/20 border-t-white rounded-full"></div>
                            {% trans "Loading events..." %}
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Emergency Cancellation Requests -->
            <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15">
                <div class="p-4 border-b border-white/10 bg-gradient-to-r from-red-800/40 to-pink-900/40">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h2 class="text-xl font-semibold text-white">{% trans "Emergency Cancellation Requests" %}</h2>
                        </div>
                        <span id="emergency-count" class="px-3 py-1 text-xs font-medium rounded-full bg-red-500/30 text-red-300 border border-red-500/30">
                            <div class="animate-pulse">{% trans "Loading..." %}</div>
                        </span>
                    </div>
                </div>
                <div id="emergency-requests-container" class="p-3">
                    <div class="flex justify-center p-2">
                        <svg class="animate-spin h-8 w-8 text-red-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                            <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                    </div>
                </div>
                <div class="p-3 bg-white/5 border-t border-white/10 flex justify-center">
                    <a href="{% url 'website:admin_reservations' %}" class="px-4 py-2 text-sm font-medium text-white hover:bg-red-600 bg-red-500 rounded-md shadow-sm transition-colors duration-200 flex items-center">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {% trans "View All Emergency Requests" %}
                    </a>
                </div>
            </div>
        </div>

        <!-- Notification Stats -->
        <div class="bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20 overflow-hidden transition-all duration-300 hover:shadow-xl hover:bg-white/15 mb-8">
            <div class="flex items-center justify-between mb-4">
                <div class="flex items-center space-x-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                    <h2 class="text-xl font-semibold text-white">{% trans "Pending Actions" %}</h2>
                </div>
                <div class="p-2 bg-red-500/20 rounded-full">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                    </svg>
                </div>
            </div>
            <div class="grid grid-cols-2 gap-4">
                <a href="{% url 'website:course_requests_list' %}" class="block p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                    <p class="text-sm font-medium text-white/60">{% trans "Corporate Requests" %}</p>
                    <p class="text-2xl font-bold text-white flex items-center justify-between">
                        {{ pending_corporate_requests }}
                        {% if pending_corporate_requests > 0 %}
                        <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-purple-500 rounded-full">{{ pending_corporate_requests }}</span>
                        {% endif %}
                    </p>
                </a>
                <a href="{% url 'website:course_requests_list' %}" class="block p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 cursor-pointer">
                    <p class="text-sm font-medium text-white/60">{% trans "New Course Requests" %}</p>
                    <p class="text-2xl font-bold text-white flex items-center justify-between">
                        {{ pending_new_course_requests }}
                        {% if pending_new_course_requests > 0 %}
                        <span class="inline-flex items-center justify-center w-5 h-5 text-xs font-bold text-white bg-green-500 rounded-full">{{ pending_new_course_requests }}</span>
                        {% endif %}
                    </p>
                </a>
                <a href="{% url 'website:admin_reservations' %}" class="col-span-2 block p-3 rounded-lg bg-white/5 hover:bg-white/10 transition-colors duration-200 cursor-pointer relative group">
                    <p class="text-sm font-medium text-white/60 flex items-center">
                        {% trans "Emergency Cancellation Requests" %}
                        {% if emergency_cancellation_requests > 0 %}
                        <span class="ml-2 inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium text-red-100 bg-red-500 rounded">{% trans "Urgent" %}</span>
                        {% endif %}
                    </p>
                    <p class="text-2xl font-bold text-white flex items-center justify-between">
                        {{ emergency_cancellation_requests }}
                        {% if emergency_cancellation_requests > 0 %}
                        <span class="absolute right-3 text-xs opacity-0 group-hover:opacity-100 transition-opacity text-white/70 flex items-center">
                            {% trans "View details" %}
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                        </span>
                        {% endif %}
                    </p>
                </a>
            </div>
        </div>

        
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Function to fetch upcoming events
        function fetchUpcomingEvents() {
            // Get current date and end of week
            const today = new Date();
            const endOfWeek = new Date(today);
            endOfWeek.setDate(today.getDate() + 14); // Get events for next two weeks
            
            // Format dates for API
            const formatDate = (date) => {
                return date.toISOString().split('T')[0]; // YYYY-MM-DD format
            };
            
            // Build API URL
            const url = `{% url 'website:calendar_events_api' %}?start=${formatDate(today)}&end=${formatDate(endOfWeek)}`;
            
            // Show loading state
            const container = document.getElementById('dashboardUpcomingEvents');
            container.innerHTML = `
                <div class="flex items-center justify-center h-24">
                    <div class="text-white/70 flex flex-col items-center">
                        <div class="w-8 h-8 mb-2 relative">
                            <div class="absolute inset-0 rounded-full border-t-2 border-b-2 border-indigo-500/60 animate-spin"></div>
                            <div class="absolute inset-1 rounded-full border-r-2 border-l-2 border-indigo-300/40 animate-spin animation-delay-150"></div>
                            <div class="absolute inset-2 rounded-full border-t-2 border-indigo-400/50 animate-pulse"></div>
                        </div>
                        <p class="text-sm">{% trans "Loading events..." %}</p>
                    </div>
                </div>
            `;
            
            // Fetch events from API
            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error('Network response was not ok');
                    }
                    return response.json();
                })
                .then(response => {
                    if (response.events && Array.isArray(response.events)) {
                        // Display upcoming events
                        showUpcomingEvents(response.events);
                    } else {
                        throw new Error('No events found in response');
                    }
                })
                .catch(error => {
                    console.error('Error fetching events:', error);
                    document.getElementById('dashboardUpcomingEvents').innerHTML = `
                        <div class="text-center py-8">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 mx-auto mb-2 text-red-400/70" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <p class="text-white/70">{% trans "Error loading events. Please try again later." %}</p>
                        </div>
                    `;
                });
        }
        
        // Function to display upcoming events
        function showUpcomingEvents(events) {
            const container = document.getElementById('dashboardUpcomingEvents');
            
            // Sort events by date
            const sortedEvents = [...events].sort((a, b) => {
                const dateA = new Date(a.start);
                const dateB = new Date(b.start);
                return dateA - dateB;
            });
            
            // Filter to only future events
            const today = new Date();
            const upcomingEvents = sortedEvents.filter(event => {
                const eventDate = new Date(event.start);
                return eventDate >= today;
            }).slice(0, 5); // Limit to 5 events
            
            // Clear container
            container.innerHTML = '';
            
            // Display upcoming events
            if (upcomingEvents.length === 0) {
                container.innerHTML = `
                    <div class="text-center py-8">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto mb-2 text-indigo-400/50" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                        <p class="text-white/70">{% trans "No upcoming events" %}</p>
                    </div>
                `;
                return;
            }
            
            // Create elements for each event
            upcomingEvents.forEach(event => {
                const eventDate = new Date(event.start);
                const day = eventDate.getDate();
                const month = eventDate.toLocaleString('en-US', { month: 'short' });
                const time = eventDate.toLocaleString('en-US', { hour: 'numeric', minute: '2-digit', hour12: true });
                
                // Create event element
                const eventElement = document.createElement('div');
                eventElement.className = 'flex items-start space-x-4 p-3 rounded-lg hover:bg-white/10 transition-all duration-200 cursor-pointer border border-transparent hover:border-white/20';
                
                // Add click action
                eventElement.addEventListener('click', function() {
                    // Could add navigation to event details in the future
                    eventElement.classList.add('bg-white/5');
                    setTimeout(() => eventElement.classList.remove('bg-white/5'), 200);
                });
                
                // Date box
                const dateBox = document.createElement('div');
                dateBox.className = 'flex-shrink-0 w-12 text-center bg-white/5 rounded-lg py-1 border border-white/10';
                dateBox.innerHTML = `
                    <div class="text-sm font-semibold text-primary">${day}</div>
                    <div class="text-xs text-white/70">${month}</div>
                `;
                
                // Event details
                const eventDetails = document.createElement('div');
                eventDetails.className = 'flex-1 min-w-0';
                
                // Get icon based on event type
                let typeIcon, typeClass, typeBadgeClass;
                
                if (event.type === 'session') {
                    typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" /></svg>';
                    typeClass = 'text-green-400';
                    typeBadgeClass = 'bg-green-400/20 text-green-400 border-green-400/20';
                } else if (event.type === 'holiday') {
                    typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z" /></svg>';
                    typeClass = 'text-red-400';
                    typeBadgeClass = 'bg-red-400/20 text-red-400 border-red-400/20';
                } else {
                    typeIcon = '<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" /></svg>';
                    typeClass = 'text-blue-400';
                    typeBadgeClass = 'bg-blue-400/20 text-blue-400 border-blue-400/20';
                }
                
                // Add holiday emoji for holidays
                const holidayPrefix = event.type === 'holiday' ? '🎉 ' : '';
                
                // For holidays, show "All day" instead of time
                const timeDisplay = event.type === 'holiday' ? 'All day' : time;
                
                // Location info if available
                const locationDisplay = event.room ? `· ${event.room}` : '';
                
                // Show title and info with improved styling
                eventDetails.innerHTML = `
                    <p class="text-sm font-medium text-white truncate">${holidayPrefix}${event.title || 'Untitled Event'}</p>
                    <div class="flex items-center mt-1">
                        <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium border ${typeBadgeClass}">
                            ${typeIcon}
                            ${event.type || 'Event'}
                        </span>
                        <span class="mx-1.5 text-white/40">•</span>
                        <span class="text-xs text-white/70">${timeDisplay} ${locationDisplay}</span>
                    </div>
                `;
                
                // Add elements to container
                eventElement.appendChild(dateBox);
                eventElement.appendChild(eventDetails);
                container.appendChild(eventElement);
            });
        }

        // Function to fetch and display emergency cancellation requests
        function fetchEmergencyCancellationRequests() {
            fetch('{% url "website:emergency_cancellation_api" %}')
                .then(response => response.json())
                .then(data => {
                    if (data.status !== 'success') {
                        throw new Error(data.message || 'Failed to fetch emergency cancellation requests');
                    }
                    
                    const pendingCount = data.count;
                    const pendingRequests = data.requests;
                    
                    // Update count badge
                    document.getElementById('emergency-count').textContent = pendingCount + ' {% trans "Pending" %}';
                    
                    // Get container
                    const container = document.getElementById('emergency-requests-container');
                    
                    // Clear loading indicator
                    container.innerHTML = '';
                    
                    if (pendingCount === 0) {
                        // Show no requests message
                        container.innerHTML = `
                            <div class="text-center py-4">
                                <svg class="mx-auto h-10 w-10 text-green-500/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"/>
                                </svg>
                                <h3 class="mt-2 text-sm font-medium text-white">{% trans "No Pending Requests" %}</h3>
                                <p class="mt-1 text-sm text-gray-400">{% trans "There are no emergency cancellation requests waiting for review." %}</p>
                            </div>
                        `;
                    } else {
                        // Create a table to display the requests
                        const table = document.createElement('div');
                        table.className = 'overflow-hidden'; // Remove overflow-x-auto to prevent scrolling
                        table.innerHTML = `
                            <table class="min-w-full divide-y divide-white/10">
                                <thead class="bg-white/5">
                                    <tr>
                                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                            {% trans "User" %}
                                        </th>
                                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                            {% trans "Course" %}
                                        </th>
                                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                            {% trans "Date" %}
                                        </th>
                                        <th scope="col" class="px-3 py-2 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                            {% trans "Reason" %}
                                        </th>
                                    </tr>
                                </thead>
                                <tbody class="bg-transparent divide-y divide-white/10" id="requests-table-body">
                                </tbody>
                            </table>
                        `;
                        
                        container.appendChild(table);
                        const tableBody = document.getElementById('requests-table-body');
                        
                        // Add up to 3 most recent requests
                        const requestsToShow = pendingRequests.slice(0, 3);
                        requestsToShow.forEach(request => {
                            // Format dates
                            const startDate = new Date(request.course_instance.start_date);
                            const createdAt = new Date(request.created_at);
                            
                            const row = document.createElement('tr');
                            row.className = 'hover:bg-white/5';
                            row.innerHTML = `
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-xs font-medium text-white">${request.user.full_name}</div>
                                    <div class="text-xs text-gray-400 truncate" style="max-width: 100px;">${request.user.email}</div>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-xs text-white truncate" style="max-width: 120px;">${request.course.name}</div>
                                </td>
                                <td class="px-3 py-2 whitespace-nowrap">
                                    <div class="text-xs text-white">${startDate.toLocaleDateString()}</div>
                                </td>
                                <td class="px-3 py-2">
                                    <div class="text-xs text-white overflow-hidden text-ellipsis" style="max-width: 140px; white-space: nowrap;">${request.reason.length > 30 ? request.reason.substring(0, 30) + '...' : request.reason}</div>
                                    ${request.has_attachment ? `
                                    <div class="text-xs text-red-400">{% trans "Has attachment" %}</div>
                                    ` : ''}
                                </td>
                            `;
                            
                            tableBody.appendChild(row);
                        });
                        
                        // If there are more requests than shown, add a note
                        if (pendingCount > 3) {
                            const moreMsg = document.createElement('div');
                            moreMsg.className = 'text-center py-1 text-red-400 text-xs';
                            moreMsg.textContent = `{% trans "and" %} ${pendingCount - 3} {% trans "more pending requests..." %}`;
                            container.appendChild(moreMsg);
                        }
                    }
                })
                .catch(error => {
                    console.error('Error fetching emergency cancellation requests:', error);
                    const container = document.getElementById('emergency-requests-container');
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <svg class="mx-auto h-10 w-10 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"/>
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-white">{% trans "Error Loading Requests" %}</h3>
                            <p class="mt-1 text-sm text-red-400">{% trans "Unable to load emergency cancellation requests." %}</p>
                        </div>
                    `;
                    document.getElementById('emergency-count').textContent = "!";
                });
        }
        
        // Fetch events when page loads
        fetchUpcomingEvents();
        
        // Fetch emergency cancellation requests
        fetchEmergencyCancellationRequests();
        
        // Refresh events every 5 minutes
        setInterval(fetchUpcomingEvents, 300000);
        
        // Refresh emergency requests every 5 minutes
        setInterval(fetchEmergencyCancellationRequests, 300000);
    });
</script>
{% endblock %} 