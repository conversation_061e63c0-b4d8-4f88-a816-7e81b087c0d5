import logging
import uuid
from django.core.mail import send_mail
from django.conf import settings
from django.utils.translation import gettext as _
from django.utils import timezone

logger = logging.getLogger(__name__)

def send_gb_notification_email(recipient_email, subject, message, cc_emails=None):
    """
    Generic function to send email notifications for GB employee workflows.
    """
    if not cc_emails:
        cc_emails = []
    
    if not all([settings.EMAIL_HOST, settings.EMAIL_PORT, settings.EMAIL_HOST_USER, settings.EMAIL_HOST_PASSWORD]):
        logger.error(f"Email settings are not properly configured. Check EMAIL_HOST, EMAIL_PORT, EMAIL_HOST_USER, and EMAIL_HOST_PASSWORD settings.")
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='FAILED',
                error_message='Email settings not configured properly'
            )
        except Exception as db_err:
            logger.debug(f"Could not log to database: {str(db_err)}")
        return False

    transaction_id = uuid.uuid4().hex[:8]
    logger.info(f"[GBEmail:{transaction_id}] Preparing to send GB notification email to {recipient_email} with subject '{subject}'")

    try:
        from_email = settings.EMAIL_HOST_USER
        all_recipients = [recipient_email]
        if cc_emails:
            all_recipients.extend(cc_emails)
        
        result = send_mail(
            subject=subject,
            message="",
            html_message=message,
            from_email=from_email,
            recipient_list=all_recipients,
            fail_silently=False
        )
        
        if result:
            logger.info(f"[GBEmail:{transaction_id}] SUCCESS: Email sent to {recipient_email}, CC: {', '.join(cc_emails) if cc_emails else 'none'}")
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='SUCCESS',
                    transaction_id=transaction_id,
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log successful email to database: {str(db_err)}")
        else:
            logger.warning(f"[GBEmail:{transaction_id}] FAILED: Email not sent to {recipient_email}")
            try:
                from website.models import EmailLog
                EmailLog.objects.create(
                    recipient=recipient_email,
                    subject=subject,
                    cc_emails=','.join(cc_emails) if cc_emails else '',
                    status='FAILED',
                    transaction_id=transaction_id,
                    error_message='Email send returned False',
                    sent_at=timezone.now()
                )
            except Exception as db_err:
                logger.debug(f"Could not log failed email to database: {str(db_err)}")
        return bool(result)
        
    except Exception as e:
        logger.error(f"[GBEmail:{transaction_id}] ERROR: Failed to send email to {recipient_email}: {str(e)}", exc_info=True)
        try:
            from website.models import EmailLog
            EmailLog.objects.create(
                recipient=recipient_email,
                subject=subject,
                cc_emails=','.join(cc_emails) if cc_emails else '',
                status='ERROR',
                transaction_id=transaction_id,
                error_message=str(e),
                sent_at=timezone.now()
            )
        except Exception as db_err:
            logger.debug(f"Could not log email error to database: {str(db_err)}")
        return False

def send_supervisor_approval_request(supervisor_user, employee_name, course_name, course_date, request_url):
    """Email to supervisor requesting approval for an employee's course enrollment."""
    logger.info(f"Preparing supervisor approval request email for {supervisor_user.email}")
    
    subject = _('Action Required: Course Enrollment Request from {}').format(employee_name)
    
    message = f"""
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c5aa0; margin: 0; font-size: 24px;">GB Academy</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Learning Management System</p>
            </div>
            <h2 style="color: #333; margin-bottom: 20px;">Course Enrollment Request</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">Dear {supervisor_user.get_full_name() or supervisor_user.username},</p>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Your team member, <strong>{employee_name}</strong>, has requested to enroll in the following course and requires your approval:
            </p>
            <div style="background-color: #f0f8ff; border-left: 4px solid #2c5aa0; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; font-size: 16px;"><strong>Course:</strong> {course_name}</p>
                <p style="margin: 0; font-size: 16px;"><strong>Date:</strong> {course_date}</p>
            </div>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Please review the request and take action by clicking the button below:
            </p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{request_url}" style="background-color: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;">Review Request</a>
            </div>
            <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #999; font-size: 12px; margin: 0;">This is an automated email from GB Academy. Please do not reply.</p>
            </div>
        </div>
    </div>
    """
    return send_gb_notification_email(supervisor_user.email, subject, message)

def send_supervisor_account_created_notification(supervisor_user, employee_name, course_name, course_date, request_url):
    """Email to a new supervisor whose account was just created, notifying them of a pending request."""
    logger.info(f"Preparing new supervisor account notification for {supervisor_user.email}")

    subject = _('Welcome to GB Academy & Pending Action Required')

    message = f"""
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c5aa0; margin: 0; font-size: 24px;">GB Academy</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Learning Management System</p>
            </div>
            <h2 style="color: #333; margin-bottom: 20px;">Welcome to GB Academy!</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">Dear {supervisor_user.get_full_name() or supervisor_user.username},</p>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                An account has been created for you on the GB Academy platform because one of your team members, <strong>{employee_name}</strong>, has requested approval to enroll in a course.
            </p>
            <div style="background-color: #fff3cd; border-left: 4px solid #ffeeba; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; font-size: 16px;"><strong>Action Required</strong></p>
                <p style="margin: 0; font-size: 14px;">You have a pending enrollment request to review:</p>
                <p style="margin: 0; font-size: 14px;"><strong>Course:</strong> {course_name}</p>
                <p style="margin: 0; font-size: 14px;"><strong>Date:</strong> {course_date}</p>
            </div>
             <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Please click the button below to log in, set up your password, and review the request.
            </p>
            <div style="text-align: center; margin: 30px 0;">
                <a href="{request_url}" style="background-color: #2c5aa0; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-size: 16px;">Log In & Review Request</a>
            </div>
            <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #999; font-size: 12px; margin: 0;">This is an automated email from GB Academy. Please do not reply.</p>
            </div>
        </div>
    </div>
    """
    return send_gb_notification_email(supervisor_user.email, subject, message)

def send_employee_request_confirmation(employee_user, supervisor_name, course_name, course_date):
    """Confirmation email to employee after they request enrollment."""
    logger.info(f"Preparing enrollment request confirmation for {employee_user.email}")
    
    subject = _('Course Enrollment Request Submitted')
    
    message = f"""
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c5aa0; margin: 0; font-size: 24px;">GB Academy</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Learning Management System</p>
            </div>
            <h2 style="color: #333; margin-bottom: 20px;">Request Submitted Successfully</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">Dear {employee_user.get_full_name() or employee_user.username},</p>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Your request to enroll in the course <strong>{course_name}</strong> (starting {course_date}) has been sent to your supervisor, <strong>{supervisor_name}</strong>, for approval.
            </p>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                You will receive another email notification once a decision has been made.
            </p>
            <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #999; font-size: 12px; margin: 0;">This is an automated email from GB Academy. Please do not reply.</p>
            </div>
        </div>
    </div>
    """
    return send_gb_notification_email(employee_user.email, subject, message)

def send_approval_decision_notification(employee_user, supervisor_name, course_name, course_date, approved, notes):
    """Email to employee with the supervisor's decision on their enrollment request."""
    logger.info(f"Preparing approval decision email for {employee_user.email}. Approved: {approved}")
    
    if approved:
        subject = _('Your Course Enrollment has been Approved')
        status_html = """
            <h2 style="color: #28a745; margin-bottom: 20px;">Enrollment Approved!</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Great news! Your supervisor, <strong>{supervisor_name}</strong>, has approved your request to enroll in:
            </p>
        """.format(supervisor_name=supervisor_name)
    else:
        subject = _('Your Course Enrollment has been Rejected')
        status_html = """
            <h2 style="color: #dc3545; margin-bottom: 20px;">Enrollment Rejected</h2>
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                Your supervisor, <strong>{supervisor_name}</strong>, has rejected your request to enroll in:
            </p>
        """.format(supervisor_name=supervisor_name)
    
    notes_html = ""
    if notes:
        notes_html = """
        <div style="background-color: #f8f9fa; border-left: 4px solid #6c757d; padding: 15px; margin: 20px 0;">
            <p style="margin: 0; font-size: 16px;"><strong>Supervisor's Notes:</strong></p>
            <p style="margin: 0; font-size: 14px;"><em>{notes}</em></p>
        </div>
        """.format(notes=notes)

    message = f"""
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9f9f9;">
        <div style="background-color: #ffffff; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <div style="text-align: center; margin-bottom: 30px;">
                <h1 style="color: #2c5aa0; margin: 0; font-size: 24px;">GB Academy</h1>
                <p style="color: #666; margin: 5px 0 0 0;">Learning Management System</p>
            </div>
            {status_html}
            <div style="background-color: #f0f8ff; border-left: 4px solid #2c5aa0; padding: 15px; margin: 20px 0;">
                <p style="margin: 0; font-size: 16px;"><strong>Course:</strong> {course_name}</p>
                <p style="margin: 0; font-size: 16px;"><strong>Date:</strong> {course_date}</p>
            </div>
            {notes_html}
            <p style="color: #555; font-size: 16px; line-height: 1.6;">
                {'You can view your upcoming courses in the "My Reservations" section of the GB Academy portal.' if approved else 'If you have questions, please contact your supervisor.'}
            </p>
            <div style="border-top: 1px solid #eee; margin-top: 30px; padding-top: 20px; text-align: center;">
                <p style="color: #999; font-size: 12px; margin: 0;">This is an automated email from GB Academy. Please do not reply.</p>
            </div>
        </div>
    </div>
    """
    return send_gb_notification_email(employee_user.email, subject, message) 