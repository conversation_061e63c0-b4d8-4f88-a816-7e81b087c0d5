{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block content %}
<div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl md:text-3xl font-bold text-white flex items-center">
            <svg class="w-8 h-8 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"/>
            </svg>
            {% trans "All Surveys" %}
        </h1>
        <a href="#" id="add-survey-btn" class="inline-flex items-center px-4 py-2 text-sm font-medium rounded-md bg-primary text-white hover:bg-primary/80 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary">
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
            </svg>
            {% trans "Add Survey" %}
        </a>
    </div>

    <!-- Surveys table -->
    <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden shadow-lg">
        <div class="px-6 py-4 border-b border-white/10 flex justify-between items-center">
            <h2 class="text-xl font-semibold text-white">{% trans "All Surveys" %}</h2>
        </div>

        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-white/10">
                <thead class="bg-white/5">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Title" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Course Instance" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Start Date" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Is Active" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Created At" %}
                        </th>
                        <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-white uppercase tracking-wider">
                            {% trans "Actions" %}
                        </th>
                    </tr>
                </thead>
                <tbody id="surveys-table-body" class="bg-transparent divide-y divide-white/10">
                    <!-- Survey data will be loaded here through JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Pagination -->
    <div class="flex justify-between items-center mt-4">
        <span class="text-sm text-gray-200" id="surveys-count">1 {% trans "Survey" %}</span>
        <div class="inline-flex rounded-md shadow-sm" id="pagination-controls">
            <!-- Pagination will be generated here -->
        </div>
    </div>
</div>

<!-- Survey Template for JavaScript -->
<template id="survey-row-template">
    <tr class="survey-row hover:bg-white/5">
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm font-medium text-white survey-title"></div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-200 survey-course-instance"></div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-200 survey-start-date"></div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <span class="survey-status-badge"></span>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="text-sm text-gray-200 survey-created-at"></div>
        </td>
        <td class="px-6 py-4 whitespace-nowrap">
            <div class="flex justify-end items-center space-x-4">
                <a href="#" class="text-blue-400 hover:text-blue-300 text-sm survey-view" title="{% trans 'View' %}">
                    {% trans "View Survey" %}
                </a>
                <a href="#" class="text-blue-400 hover:text-blue-300 text-sm survey-edit" title="{% trans 'Edit' %}">
                    {% trans "Edit" %}
                </a>
                <a href="#" class="text-red-400 hover:text-red-300 text-sm survey-delete" title="{% trans 'Delete' %}">
                    {% trans "Delete" %}
                </a>
            </div>
        </td>
    </tr>
</template>

<!-- Add Survey Modal -->
<div id="addSurveyModal" class="fixed inset-0 overflow-y-auto hidden z-50">
    <div class="flex items-center justify-center min-h-screen px-4">
        <div class="fixed inset-0 bg-black opacity-50" id="addSurveyModalBackdrop"></div>
        <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl z-50 relative">
            <div class="flex justify-between items-center p-6 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">{% trans "Add New Survey" %}</h3>
                <button type="button" class="close-modal text-gray-400 hover:text-gray-500">
                    <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                    </svg>
                </button>
            </div>
            
            <form id="addSurveyForm" class="p-6">
                <div class="space-y-4">
                    <!-- Survey Creation Type -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "Survey Creation Method" %}</label>
                        <div class="flex space-x-4">
                            <label class="inline-flex items-center">
                                <input type="radio" name="creation_type" value="new" checked class="form-radio text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">{% trans "Create New Survey" %}</span>
                            </label>
                            <label class="inline-flex items-center">
                                <input type="radio" name="creation_type" value="existing" class="form-radio text-blue-600">
                                <span class="ml-2 text-sm text-gray-700">{% trans "Copy from Existing Survey" %}</span>
                            </label>
                        </div>
                    </div>

                    <!-- Existing Survey Selection (hidden by default) -->
                    <div id="existing_survey_section" class="hidden">
                        <label for="existing_survey" class="block text-sm font-medium text-gray-700">{% trans "Select Existing Survey" %}</label>
                        <select name="existing_survey" id="existing_survey" class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Survey to Copy" %}</option>
                            <!-- Existing surveys will be loaded via API -->
                        </select>
                        <p class="mt-1 text-xs text-gray-500">{% trans "Questions and structure will be copied from the selected survey" %}</p>
                    </div>
                    
                    <!-- Survey Title -->
                    <div>
                        <label for="survey_title" class="block text-sm font-medium text-gray-700">{% trans "Survey Title" %}</label>
                        <input type="text" name="survey_title" id="survey_title" required class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    
                    <!-- Course Instance -->
                    <div>
                        <label for="course_instance" class="block text-sm font-medium text-gray-700">{% trans "Course Instance" %}</label>
                        <select name="course_instance" id="course_instance" required class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                            <option value="">{% trans "Select Course Instance" %}</option>
                            <!-- Course instances will be loaded via API -->
                        </select>
                    </div>
                    
                    <!-- Questions Section (only for new surveys) -->
                    <div id="questions_section">
                        <div class="flex justify-between items-center mb-2">
                            <label class="block text-sm font-medium text-gray-700">{% trans "Questions" %}</label>
                            <button type="button" id="addQuestionBtn" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                {% trans "Add Question" %}
                            </button>
                        </div>
                        
                        <div id="questionsContainer" class="space-y-4">
                            <!-- Questions will be added here dynamically -->
                        </div>
                    </div>

                    <!-- Survey Preview Section (for copied surveys) -->
                    <div id="survey_preview_section" class="hidden">
                        <label class="block text-sm font-medium text-gray-700 mb-2">{% trans "Survey Preview" %}</label>
                        <div id="survey_preview_content" class="bg-gray-50 border border-gray-200 rounded-md p-4 min-h-24">
                            <p class="text-gray-500 text-sm">{% trans "Select a survey to see its preview" %}</p>
                        </div>
                    </div>
                </div>
                
                <div class="mt-6 flex justify-end space-x-3">
                    <button type="button" class="close-modal px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {% trans "Cancel" %}
                    </button>
                    <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                        {% trans "Save Survey" %}
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Question Template -->
<template id="question-template">
    <div class="question-item border border-gray-200 rounded-md p-4">
        <div class="flex justify-between items-start mb-3">
            <div class="flex-grow">
                <input type="text" name="question_text[]" placeholder="{% trans 'Enter question text' %}" required class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            </div>
            <button type="button" class="delete-question ml-2 text-red-600 hover:text-red-800">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                </svg>
            </button>
        </div>
        
        <div class="mb-3">
            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Question Type" %}</label>
            <select name="question_type[]" class="question-type mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                <option value="rating">{% trans "Rating (1-5)" %}</option>
                <option value="multiple_choice">{% trans "Multiple Choice" %}</option>
            </select>
        </div>
        
        <div class="multiple-choice-options hidden">
            <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Options" %}</label>
            <div class="options-container space-y-2">
                <div class="option-item flex items-center">
                    <input type="text" name="option_text[0][]" placeholder="{% trans 'Option text' %}" class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <button type="button" class="delete-option ml-2 text-red-600 hover:text-red-800">
                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <button type="button" class="add-option mt-2 inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                {% trans "Add Option" %}
            </button>
        </div>
    </div>
</template>

<!-- Option Template -->
<template id="option-template">
    <div class="option-item flex items-center mt-2">
        <input type="text" name="option_text[INDEX][]" placeholder="{% trans 'Option text' %}" class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
        <button type="button" class="delete-option ml-2 text-red-600 hover:text-red-800">
            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
        </button>
    </div>
</template>

{% endblock %}

{% block extra_css %}
<style>
    /* Dark background for the entire page */
    body {
        background-color: #1e3a8a; /* Tailwind indigo-800 */
    }

    /* Remove old survey row styling since we're using the new style */
    .survey-row:nth-child(odd) td,
    .survey-row:nth-child(even) td {
        background-color: transparent;
    }
    
    /* Style for select dropdowns */
    select {
        background-color: #1a2542 !important;
        color: white !important;
        border: 1px solid #374151 !important; /* Tailwind gray-700 */
    }

    select option {
        background-color: #1a2542 !important;
        color: white !important;
        padding: 8px;
    }

    /* Style for dropdown hover/focus state */
    select option:hover,
    select option:focus,
    select option:active,
    select option:checked {
        background-color: #2a3b5e !important;
    }

    /* Adjust dropdown arrow color for better visibility */
    .pointer-events-none.absolute.inset-y-0.right-0.flex.items-center.px-2.text-gray-700 svg {
        fill: white !important;
    }

    /* Modal styles */
    .modal-open {
        overflow: hidden;
    }
    
    /* Dark themed modals */
    #addSurveyModal .bg-white,
    #surveyDetailsModal .bg-white,
    #editSurveyModal .bg-white {
        background-color: #1a2542 !important;
        color: white !important;
    }
    
    /* Modal headers */
    #addSurveyModal .border-b,
    #surveyDetailsModal .border-b,
    #editSurveyModal .border-b {
        border-color: #374151 !important; /* Tailwind gray-700 */
    }
    
    /* Modal text colors */
    #addSurveyModal .text-gray-900,
    #surveyDetailsModal .text-gray-900,
    #editSurveyModal .text-gray-900 {
        color: white !important;
    }
    
    #addSurveyModal .text-gray-700,
    #surveyDetailsModal .text-gray-700,
    #editSurveyModal .text-gray-700 {
        color: #e5e7eb !important; /* Tailwind gray-200 */
    }
    
    /* Form inputs in modals */
    #addSurveyModal input[type="text"],
    #editSurveyModal input[type="text"],
    #addSurveyModal select,
    #editSurveyModal select {
        background-color: #273151 !important;
        color: white !important;
        border-color: #4b5563 !important; /* Tailwind gray-600 */
    }
    
    #addSurveyModal input[type="text"]:focus,
    #editSurveyModal input[type="text"]:focus,
    #addSurveyModal select:focus,
    #editSurveyModal select:focus {
        border-color: #60a5fa !important; /* Tailwind blue-400 */
        box-shadow: 0 0 0 2px rgba(96, 165, 250, 0.3) !important;
    }
    
    /* Question items background */
    .question-item {
        background-color: #273151 !important;
        border-color: #4b5563 !important; /* Tailwind gray-600 */
    }
    
    /* Option items in multiple choice questions */
    .option-item input[type="text"] {
        background-color: #273151 !important;
        color: white !important;
    }
    
    /* Footer of modals */
    #surveyDetailsModal .bg-gray-50,
    #editSurveyModal .bg-gray-50 {
        background-color: #1f2937 !important; /* Tailwind gray-800 */
    }
    
    /* Ensure modals stack properly above all content */
    #editSurveyModal {
        z-index: 9999 !important; /* Force highest z-index */
    }
    
    /* Ensure modal content is above backdrop */
    #editSurveyModal > div > div:last-child {
        position: relative;
        z-index: 10000 !important; /* Even higher z-index */
    }
    
    /* Modal buttons styling */
    .close-modal, .close-details-modal, .close-edit-modal {
        color: #e5e7eb !important;
    }
    
    /* Survey details view styling */
    #surveyDetailsModal .text-base {
        color: #e5e7eb !important;
    }
    
    #surveyDetailsModal .text-sm {
        color: #d1d5db !important;
    }
    
    #surveyDetailsModal .bg-gray-100 {
        background-color: #1f2937 !important;
    }
    
    /* Checkboxes in modals */
    #editSurveyModal input[type="checkbox"] {
        background-color: #273151 !important;
        border-color: #4b5563 !important;
    }
    
    #editSurveyModal input[type="checkbox"]:checked {
        background-color: #3b82f6 !important;
        border-color: #3b82f6 !important;
    }
    
    /* Radio buttons in modals */
    #addSurveyModal input[type="radio"] {
        background-color: #273151 !important;
        border-color: #4b5563 !important;
        color: #3b82f6 !important;
    }
    
    #addSurveyModal input[type="radio"]:checked {
        background-color: #3b82f6 !important;
        border-color: #3b82f6 !important;
    }
    
    /* Survey preview section */
    #survey_preview_content {
        background-color: #273151 !important;
        border-color: #4b5563 !important;
        color: #e5e7eb !important;
    }
    
    #survey_preview_content p {
        color: #9ca3af !important;
    }
    
    /* Enhanced Close button */
    #surveyDetailsModal button.close-details-modal.px-4.py-2,
    #editSurveyModal button.close-edit-modal.px-4.py-2,
    #surveyDetailsModal .bg-gray-50 button {
        background-color: #4b5563 !important; /* Darker gray */
        color: white !important;
        font-weight: 600 !important;
        border: 1px solid #6b7280 !important;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
        transition: all 0.2s ease !important;
    }
    
    #surveyDetailsModal button.close-details-modal.px-4.py-2:hover,
    #editSurveyModal button.close-edit-modal.px-4.py-2:hover,
    #surveyDetailsModal .bg-gray-50 button:hover {
        background-color: #6b7280 !important;
        transform: translateY(-1px) !important;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initial load of surveys
        loadSurveys();

        // Set up event listeners
        const selectAllCheckbox = document.getElementById('select-all');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', function() {
                const isChecked = this.checked;
                document.querySelectorAll('.survey-checkbox').forEach(checkbox => {
                    checkbox.checked = isChecked;
                });
                updateSelectedCount();
            });
        }

        // Add Survey Modal Functionality
        const addSurveyBtn = document.getElementById('add-survey-btn');
        const addSurveyModal = document.getElementById('addSurveyModal');
        const addSurveyModalBackdrop = document.getElementById('addSurveyModalBackdrop');
        const closeModalButtons = document.querySelectorAll('.close-modal');
        const addSurveyForm = document.getElementById('addSurveyForm');
        const addQuestionBtn = document.getElementById('addQuestionBtn');
        const questionsContainer = document.getElementById('questionsContainer');
        const courseInstanceSelect = document.getElementById('course_instance');
        
        // Load course instances for dropdown
        loadCourseInstances();
        
        // Load existing surveys for dropdown
        loadExistingSurveys();
        
        // Handle creation type radio buttons
        const creationTypeRadios = document.querySelectorAll('input[name="creation_type"]');
        const existingSurveySection = document.getElementById('existing_survey_section');
        const questionsSection = document.getElementById('questions_section');
        const surveyPreviewSection = document.getElementById('survey_preview_section');
        const existingSurveySelect = document.getElementById('existing_survey');
        
        creationTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'existing') {
                    existingSurveySection.classList.remove('hidden');
                    questionsSection.classList.add('hidden');
                    surveyPreviewSection.classList.remove('hidden');
                } else {
                    existingSurveySection.classList.add('hidden');
                    questionsSection.classList.remove('hidden');
                    surveyPreviewSection.classList.add('hidden');
                }
            });
        });
        
        // Handle existing survey selection
        if (existingSurveySelect) {
            existingSurveySelect.addEventListener('change', function() {
                if (this.value) {
                    loadSurveyPreview(this.value);
                } else {
                    const previewContent = document.getElementById('survey_preview_content');
                    previewContent.innerHTML = '<p class="text-gray-500 text-sm">{% trans "Select a survey to see its preview" %}</p>';
                }
            });
        }
        
        // Open modal
        if (addSurveyBtn) {
            addSurveyBtn.addEventListener('click', function(e) {
                e.preventDefault();
                addSurveyModal.classList.remove('hidden');
                document.body.classList.add('modal-open');
            });
        }
        
        // Close modal
        function closeModal() {
            if (addSurveyModal) {
                addSurveyModal.classList.add('hidden');
                document.body.classList.remove('modal-open');
                // Reset form
                if (addSurveyForm) {
                    addSurveyForm.reset();
                }
                if (questionsContainer) {
                    questionsContainer.innerHTML = '';
                }
                // Reset to default view (new survey)
                document.querySelector('input[name="creation_type"][value="new"]').checked = true;
                existingSurveySection.classList.add('hidden');
                questionsSection.classList.remove('hidden');
                surveyPreviewSection.classList.add('hidden');
                // Reset preview content
                const previewContent = document.getElementById('survey_preview_content');
                previewContent.innerHTML = '<p class="text-gray-500 text-sm">{% trans "Select a survey to see its preview" %}</p>';
            }
        }
        
        if (closeModalButtons) {
            closeModalButtons.forEach(button => {
                button.addEventListener('click', closeModal);
            });
        }
        
        if (addSurveyModalBackdrop) {
            addSurveyModalBackdrop.addEventListener('click', closeModal);
        }
        
        // Add question
        if (addQuestionBtn) {
            addQuestionBtn.addEventListener('click', function() {
                addQuestion();
            });
        }
        
        // Submit form
        if (addSurveyForm) {
            addSurveyForm.addEventListener('submit', function(e) {
                e.preventDefault();
                submitSurvey();
            });
        }
        
        // Function to add a new question
        function addQuestion() {
            const template = document.getElementById('question-template');
            const questionElement = template.content.cloneNode(true);
            const questionItems = document.querySelectorAll('.question-item');
            const index = questionItems.length;
            
            // Update name attributes with the correct index
            const options = questionElement.querySelectorAll('input[name="option_text[0][]"]');
            options.forEach(option => {
                option.name = `option_text[${index}][]`;
            });
            
            // Add event listeners for question type change
            const questionTypeSelect = questionElement.querySelector('.question-type');
            questionTypeSelect.addEventListener('change', function() {
                toggleMultipleChoiceOptions(this);
            });
            
            // Add event listener for delete question button
            const deleteQuestionBtn = questionElement.querySelector('.delete-question');
            deleteQuestionBtn.addEventListener('click', function() {
                this.closest('.question-item').remove();
                updateQuestionIndices();
            });
            
            // Add event listener for add option button
            const addOptionBtn = questionElement.querySelector('.add-option');
            addOptionBtn.addEventListener('click', function() {
                addOption(this.closest('.question-item'), index);
            });
            
            // Add event listeners for delete option buttons
            const deleteOptionBtns = questionElement.querySelectorAll('.delete-option');
            deleteOptionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.closest('.options-container').querySelectorAll('.option-item').length > 1) {
                        this.closest('.option-item').remove();
                    } else {
                        alert('{% trans "You must have at least one option" %}');
                    }
                });
            });
            
            questionsContainer.appendChild(questionElement);
        }
        
        // Function to toggle multiple choice options based on question type
        function toggleMultipleChoiceOptions(selectElement) {
            const questionItem = selectElement.closest('.question-item');
            const multipleChoiceOptions = questionItem.querySelector('.multiple-choice-options');
            
            if (selectElement.value === 'multiple_choice') {
                multipleChoiceOptions.classList.remove('hidden');
            } else {
                multipleChoiceOptions.classList.add('hidden');
            }
        }
        
        // Function to add a new option to a multiple choice question
        function addOption(questionItem, questionIndex) {
            const template = document.getElementById('option-template');
            const optionElement = template.content.cloneNode(true);
            const optionInput = optionElement.querySelector('input');
            
            // Update the name attribute with the correct question index
            optionInput.name = `option_text[${questionIndex}][]`;
            
            // Add event listener for delete option button
            const deleteOptionBtn = optionElement.querySelector('.delete-option');
            deleteOptionBtn.addEventListener('click', function() {
                const optionsContainer = this.closest('.options-container');
                if (optionsContainer.querySelectorAll('.option-item').length > 1) {
                    this.closest('.option-item').remove();
                } else {
                    alert('{% trans "You must have at least one option" %}');
                }
            });
            
            questionItem.querySelector('.options-container').appendChild(optionElement);
        }
        
        // Function to update question indices after deletion
        function updateQuestionIndices() {
            const questionItems = document.querySelectorAll('.question-item');
            questionItems.forEach((item, index) => {
                const options = item.querySelectorAll('input[name^="option_text["]');
                options.forEach(option => {
                    option.name = `option_text[${index}][]`;
                });
            });
        }
        
        // Function to load course instances
        function loadCourseInstances() {
            fetch('/api/course-instances/')
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        courseInstanceSelect.innerHTML = '<option value="">{% trans "Select Course Instance" %}</option>';
                        
                        data.course_instances.forEach(instance => {
                            const option = document.createElement('option');
                            option.value = instance.id;
                            option.textContent = instance.name;
                            courseInstanceSelect.appendChild(option);
                        });
                    } else {
                        console.error('Error loading course instances:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching course instances:', error);
                });
        }
        
        // Function to load existing surveys for selection
        function loadExistingSurveys() {
            const existingSurveySelect = document.getElementById('existing_survey');
            
            // Get the current URL path to determine language prefix
            const currentPath = window.location.pathname;
            const langPrefix = currentPath.startsWith('/en/') ? '/en' : '';
            
            fetch(`${langPrefix}/api/surveys/existing/`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        existingSurveySelect.innerHTML = '<option value="">{% trans "Select Survey to Copy" %}</option>';
                        
                        data.surveys.forEach(survey => {
                            const option = document.createElement('option');
                            option.value = survey.survey_id;
                            option.textContent = `${survey.title} (${survey.course_name}) - ${survey.questions_count} questions`;
                            existingSurveySelect.appendChild(option);
                        });
                    } else {
                        console.error('Error loading existing surveys:', data.message);
                    }
                })
                .catch(error => {
                    console.error('Error fetching existing surveys:', error);
                });
        }
        
        // Function to load survey preview
        function loadSurveyPreview(surveyId) {
            const previewContent = document.getElementById('survey_preview_content');
            
            // Get the current URL path to determine language prefix
            const currentPath = window.location.pathname;
            const langPrefix = currentPath.startsWith('/en/') ? '/en' : '';
            
            fetch(`${langPrefix}/api/surveys/${surveyId}/view/`)
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'success') {
                        const survey = data.survey;
                        let previewHtml = `
                            <div class="mb-3">
                                <h4 class="font-medium text-white">${survey.title}</h4>
                                <p class="text-sm text-gray-300">${survey.course_instance.name}</p>
                            </div>
                            <div class="space-y-3">
                                <h5 class="text-sm font-medium text-gray-200">{% trans "Questions:" %}</h5>
                        `;
                        
                        survey.questions.forEach((question, index) => {
                            previewHtml += `
                                <div class="bg-gray-600/50 p-3 rounded">
                                    <p class="text-sm text-white font-medium">${index + 1}. ${question.text}</p>
                                    <p class="text-xs text-gray-300 mt-1">
                                        {% trans "Type:" %} ${question.type === 'rating' ? '{% trans "Rating (1-5)" %}' : '{% trans "Multiple Choice" %}'}
                                    </p>
                                    ${question.type === 'multiple_choice' && question.options ? 
                                        '<div class="mt-2 text-xs text-gray-400">' +
                                        '{% trans "Options:" %} ' + question.options.map(opt => opt.text).join(', ') +
                                        '</div>' : ''}
                                </div>
                            `;
                        });
                        
                        previewHtml += '</div>';
                        previewContent.innerHTML = previewHtml;
                    } else {
                        previewContent.innerHTML = '<p class="text-red-400 text-sm">{% trans "Error loading survey preview" %}</p>';
                    }
                })
                .catch(error => {
                    console.error('Error loading survey preview:', error);
                    previewContent.innerHTML = '<p class="text-red-400 text-sm">{% trans "Error loading survey preview" %}</p>';
                });
        }
        
        // Function to submit the survey
        function submitSurvey() {
            const title = document.getElementById('survey_title').value;
            const courseInstanceId = document.getElementById('course_instance').value;
            const creationType = document.querySelector('input[name="creation_type"]:checked').value;
            const existingSurveyId = document.getElementById('existing_survey').value;
            
            if (!title || !courseInstanceId) {
                alert('{% trans "Please fill in all required fields" %}');
                return;
            }
            
            let surveyData = {
                title: title,
                course_instance_id: courseInstanceId
            };
            
            if (creationType === 'existing') {
                // Copying from existing survey
                if (!existingSurveyId) {
                    alert('{% trans "Please select a survey to copy from" %}');
                    return;
                }
                surveyData.existing_survey_id = existingSurveyId;
            } else {
                // Creating new survey
                const questionItems = document.querySelectorAll('.question-item');
                
                if (questionItems.length === 0) {
                    alert('{% trans "Please add at least one question" %}');
                    return;
                }
                
                // Build questions array
            const questions = [];
            
            questionItems.forEach((item, index) => {
                const questionText = item.querySelector('input[name="question_text[]"]').value;
                const questionType = item.querySelector('select[name="question_type[]"]').value;
                
                if (!questionText) {
                    alert('{% trans "Please fill in all question texts" %}');
                    return;
                }
                
                const question = {
                    text: questionText,
                    type: questionType
                };
                
                // If multiple choice, add options
                if (questionType === 'multiple_choice') {
                    const optionInputs = item.querySelectorAll(`input[name="option_text[${index}][]"]`);
                    const options = [];
                    
                    optionInputs.forEach(input => {
                        if (input.value) {
                            options.push(input.value);
                        }
                    });
                    
                    if (options.length === 0) {
                        alert('{% trans "Please add at least one option for each multiple choice question" %}');
                        return;
                    }
                    
                    question.options = options;
                }
                
                    questions.push(question);
                });
                
                surveyData.questions = questions;
            }
            
            // Get the current URL path to determine language prefix
            const currentPath = window.location.pathname;
            const langPrefix = currentPath.startsWith('/en/') ? '/en' : '';
            
            // Submit data to API
            fetch(`${langPrefix}/api/surveys/create/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                body: JSON.stringify(surveyData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.status === 'success') {
                    closeModal();
                    loadSurveys(); // Reload surveys list
                    alert('{% trans "Survey created successfully" %}');
                } else {
                    alert(data.message || '{% trans "Error creating survey" %}');
                }
            })
            .catch(error => {
                console.error('Error creating survey:', error);
                alert('{% trans "An error occurred while creating the survey" %}');
            });
        }
    });

    function loadSurveys(page = 1) {
        // Without filters, we just need to handle pagination
        const params = new URLSearchParams();
        params.append('page', page);

        fetch(`/api/surveys/?${params.toString()}`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            renderSurveys(data);
            updateSelectedCount();
        })
        .catch(error => {
            console.error('Error fetching surveys:', error);
        });
    }

    function renderSurveys(data) {
        const tableBody = document.getElementById('surveys-table-body');
        const template = document.getElementById('survey-row-template');
        const surveysCount = document.getElementById('surveys-count');

        // Clear existing rows
        if (tableBody) {
            tableBody.innerHTML = '';

            if (data.surveys.length === 0) {
                const emptyRow = document.createElement('tr');
                emptyRow.innerHTML = `<td colspan="6" class="px-6 py-4 text-center text-gray-300">{% trans "No surveys found" %}</td>`;
                tableBody.appendChild(emptyRow);
                if (surveysCount) {
                    surveysCount.textContent = '0 {% trans "Surveys" %}';
                }
                return;
            }

            // Add survey rows
            data.surveys.forEach(survey => {
                const clone = template.content.cloneNode(true);
                
                // Set data
                clone.querySelector('.survey-title').textContent = survey.title;
                clone.querySelector('.survey-course-instance').textContent = survey.course_instance_name || '-';
                
                const startDate = survey.start_date ? new Date(survey.start_date).toLocaleDateString() : '{% trans "Not set" %}';
                clone.querySelector('.survey-start-date').textContent = startDate;
                
                const createdAt = new Date(survey.created_at).toLocaleDateString();
                clone.querySelector('.survey-created-at').textContent = createdAt;
                
                // Status badge with new styling to match admin_reservations.html
                const statusBadge = clone.querySelector('.survey-status-badge');
                if (survey.is_active) {
                    statusBadge.classList.add('px-2', 'inline-flex', 'text-xs', 'leading-5', 'font-semibold', 'rounded-full', 'bg-green-800/30', 'text-green-400');
                    statusBadge.textContent = '{% trans "Completed" %}';
                } else {
                    statusBadge.classList.add('px-2', 'inline-flex', 'text-xs', 'leading-5', 'font-semibold', 'rounded-full', 'bg-red-800/30', 'text-red-400');
                    statusBadge.textContent = '{% trans "Cancelled" %}';
                }
                
                // Action links
                const viewLink = clone.querySelector('.survey-view');
                const editLink = clone.querySelector('.survey-edit');
                const deleteLink = clone.querySelector('.survey-delete');
                
                if (viewLink) {
                    viewLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        viewSurvey(survey.survey_id);
                    });
                }
                
                if (editLink) {
                    editLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        editSurvey(survey.survey_id);
                    });
                }
                
                if (deleteLink) {
                    deleteLink.addEventListener('click', function(e) {
                        e.preventDefault();
                        if (confirm('{% trans "Are you sure you want to delete this survey?" %}')) {
                            deleteSurvey(survey.survey_id);
                        }
                    });
                }
                
                tableBody.appendChild(clone);
            });

            // Update survey count
            if (surveysCount) {
                const count = data.total_count;
                surveysCount.textContent = count === 1 ? 
                    '1 {% trans "Survey" %}' : 
                    `${count} {% trans "Surveys" %}`;
            }

            // Render pagination if needed
            renderPagination(data.page, data.total_pages);
        }
    }

    function renderPagination(currentPage, totalPages) {
        const paginationControls = document.getElementById('pagination-controls');
        paginationControls.innerHTML = '';

        if (totalPages <= 1) return;

        // Previous button
        const prevButton = document.createElement('button');
        prevButton.className = `relative inline-flex items-center px-4 py-2 rounded-l-md border ${currentPage === 1 ? 'border-blue-700 bg-blue-800/50 text-gray-400 cursor-not-allowed' : 'border-blue-700 bg-blue-800/50 text-white hover:bg-blue-700/70'} text-sm font-medium`;
        prevButton.innerHTML = '<span>{% trans "Previous" %}</span>';
        if (currentPage > 1) {
            prevButton.addEventListener('click', () => loadSurveys(currentPage - 1));
        }
        paginationControls.appendChild(prevButton);

        // Page buttons
        const maxVisiblePages = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages && startPage > 1) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.className = `relative inline-flex items-center px-4 py-2 border ${i === currentPage ? 'bg-primary text-white z-10 border-primary' : 'bg-blue-800/50 border-blue-700 text-white hover:bg-blue-700/70'} text-sm font-medium`;
            pageButton.textContent = i;
            if (i !== currentPage) {
                pageButton.addEventListener('click', () => loadSurveys(i));
            }
            paginationControls.appendChild(pageButton);
        }

        // Next button
        const nextButton = document.createElement('button');
        nextButton.className = `relative inline-flex items-center px-4 py-2 rounded-r-md border ${currentPage === totalPages ? 'border-blue-700 bg-blue-800/50 text-gray-400 cursor-not-allowed' : 'border-blue-700 bg-blue-800/50 text-white hover:bg-blue-700/70'} text-sm font-medium`;
        nextButton.innerHTML = '<span>{% trans "Next" %}</span>';
        if (currentPage < totalPages) {
            nextButton.addEventListener('click', () => loadSurveys(currentPage + 1));
        }
        paginationControls.appendChild(nextButton);
    }

    function deleteSurvey(id) {
        fetch(`/api/surveys/${id}/`, {
            method: 'DELETE',
            headers: {
                'X-CSRFToken': getCookie('csrftoken')
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                loadSurveys();
            } else {
                alert(data.message || 'Error deleting survey');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{% trans "An error occurred" %}');
        });
    }

    function updateSelectedCount() {
        const selectedCountElement = document.getElementById('selected-count');
        if (!selectedCountElement) return; // Skip if element doesn't exist
        
        const total = document.querySelectorAll('.survey-checkbox').length;
        const selected = document.querySelectorAll('.survey-checkbox:checked').length;
        selectedCountElement.textContent = `${selected} of ${total} selected`;
    }

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    // Function to view survey details
    function viewSurvey(surveyId) {
        fetch(`/api/surveys/${surveyId}/view/`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Display survey details in a modal
                showSurveyDetailsModal(data.survey);
            } else {
                alert(data.message || 'Error loading survey details');
            }
        })
        .catch(error => {
            console.error('Error fetching survey details:', error);
            alert('{% trans "An error occurred while loading survey details" %}');
        });
    }
    
    // Function to edit survey
    function editSurvey(surveyId) {
        fetch(`/api/surveys/${surveyId}/view/`, {
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Open edit modal with survey data
                showEditSurveyModal(data.survey);
            } else {
                alert(data.message || 'Error loading survey details');
            }
        })
        .catch(error => {
            console.error('Error fetching survey details for edit:', error);
            alert('{% trans "An error occurred while loading survey details" %}');
        });
    }
    
    // Function to show survey details modal
    function showSurveyDetailsModal(survey) {
        // Create a modal for viewing survey details
        const modalHTML = `
        <div id="surveyDetailsModal" class="fixed inset-0 overflow-y-auto z-50">
            <div class="flex items-center justify-center min-h-screen px-4">
                <div class="fixed inset-0 bg-black opacity-50"></div>
                <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl z-50 relative">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">${survey.title}</h3>
                        <button type="button" class="close-details-modal text-gray-400 hover:text-gray-500">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    
                    <div class="p-6">
                        <div class="mb-4">
                            <div class="text-sm font-medium text-gray-500 mb-1">{% trans "Course Instance" %}</div>
                            <div class="text-base text-gray-900">${survey.course_instance.name}</div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="text-sm font-medium text-gray-500 mb-1">{% trans "Start Date" %}</div>
                            <div class="text-base text-gray-900">${survey.start_date ? new Date(survey.start_date).toLocaleDateString() : '{% trans "Not set" %}'}</div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="text-sm font-medium text-gray-500 mb-1">{% trans "Status" %}</div>
                            <div class="text-base text-gray-900">
                                <span class="${survey.is_active ? 'text-green-600' : 'text-red-600'}">
                                    ${survey.is_active ? '{% trans "Active" %}' : '{% trans "Inactive" %}'}
                                </span>
                            </div>
                        </div>
                        
                        <div class="mb-4">
                            <div class="text-sm font-medium text-gray-500 mb-1">{% trans "Questions" %}</div>
                            <div class="space-y-4 mt-2">
                                ${survey.questions.map((question, index) => `
                                    <div class="bg-gray-100 p-4 rounded-md">
                                        <div class="font-medium mb-2">${index + 1}. ${question.text}</div>
                                        <div class="text-sm text-gray-600">
                                            ${question.type === 'rating' ? 
                                                '{% trans "Rating scale (1-5)" %}' : 
                                                `{% trans "Multiple Choice" %}<br>
                                                <ul class="list-disc list-inside mt-1">
                                                    ${question.options.map(option => `
                                                        <li>${option.text}</li>
                                                    `).join('')}
                                                </ul>`
                                            }
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-gray-50 px-6 py-3 flex justify-end">
                        <button type="button" class="close-details-modal px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 focus:outline-none">
                            {% trans "Close" %}
                        </button>
                    </div>
                </div>
            </div>
        </div>
        `;
        
        // Append modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        document.body.classList.add('modal-open');
        
        // Add event listeners for close buttons
        document.querySelectorAll('.close-details-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.getElementById('surveyDetailsModal').remove();
                document.body.classList.remove('modal-open');
            });
        });
    }
    
    // Function to show edit survey modal
    function showEditSurveyModal(survey) {
        // Create a modal for editing survey
        const modalHTML = `
        <div id="editSurveyModal" class="fixed inset-0 overflow-y-auto z-50">
            <div class="flex items-center justify-center min-h-screen px-4">
                <div class="fixed inset-0 bg-black opacity-50"></div>
                <div class="bg-white rounded-lg shadow-xl w-full max-w-3xl z-50 relative">
                    <div class="flex justify-between items-center p-6 border-b border-gray-200">
                        <h3 class="text-lg font-medium text-gray-900">{% trans "Edit Survey" %}</h3>
                        <button type="button" class="close-edit-modal text-gray-400 hover:text-gray-500">
                            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    
                    <form id="editSurveyForm" class="p-6">
                        <input type="hidden" id="edit_survey_id" value="${survey.survey_id}">
                        <div class="space-y-4">
                            <!-- Survey Title -->
                            <div>
                                <label for="edit_survey_title" class="block text-sm font-medium text-gray-700">{% trans "Survey Title" %}</label>
                                <input type="text" name="edit_survey_title" id="edit_survey_title" required class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500" value="${survey.title}">
                            </div>
                            
                            <!-- Course Instance -->
                            <div>
                                <label for="edit_course_instance" class="block text-sm font-medium text-gray-700">{% trans "Course Instance" %}</label>
                                <select name="edit_course_instance" id="edit_course_instance" required class="mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <option value="">{% trans "Select Course Instance" %}</option>
                                    <!-- Course instances will be loaded via API -->
                                </select>
                            </div>
                            
                            <!-- Is Active -->
                            <div>
                                <label class="flex items-center">
                                    <input type="checkbox" id="edit_is_active" name="edit_is_active" class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded" ${survey.is_active ? 'checked' : ''}>
                                    <span class="ml-2 text-sm text-gray-700">{% trans "Is Active" %}</span>
                                </label>
                            </div>
                            
                            <!-- Questions Section -->
                            <div>
                                <div class="flex justify-between items-center mb-2">
                                    <label class="block text-sm font-medium text-gray-700">{% trans "Questions" %}</label>
                                    <button type="button" id="editAddQuestionBtn" class="inline-flex items-center px-3 py-1 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                        {% trans "Add Question" %}
                                    </button>
                                </div>
                                
                                <div id="editQuestionsContainer" class="space-y-4">
                                    <!-- Questions will be added here dynamically -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 flex justify-end space-x-3">
                            <button type="button" class="close-edit-modal px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                {% trans "Cancel" %}
                            </button>
                            <button type="submit" class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                                {% trans "Update Survey" %}
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        `;
        
        // Append modal to body
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        document.body.classList.add('modal-open');
        
        // Load course instances for dropdown
        const editCourseInstanceSelect = document.getElementById('edit_course_instance');
        
        fetch('/api/course-instances/')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    editCourseInstanceSelect.innerHTML = '<option value="">{% trans "Select Course Instance" %}</option>';
                    
                    data.course_instances.forEach(instance => {
                        const option = document.createElement('option');
                        option.value = instance.id;
                        option.textContent = instance.name;
                        if (instance.id === survey.course_instance.id) {
                            option.selected = true;
                        }
                        editCourseInstanceSelect.appendChild(option);
                    });
                } else {
                    console.error('Error loading course instances:', data.message);
                }
            })
            .catch(error => {
                console.error('Error fetching course instances:', error);
            });
        
        // Load existing questions
        const editQuestionsContainer = document.getElementById('editQuestionsContainer');
        
        survey.questions.forEach((question, index) => {
            const questionItem = document.createElement('div');
            questionItem.className = 'question-item border border-gray-200 rounded-md p-4';
            
            let optionsHTML = '';
            if (question.type === 'multiple_choice' && question.options) {
                optionsHTML = `
                    <div class="multiple-choice-options">
                        <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Options" %}</label>
                        <div class="options-container space-y-2">
                            ${question.options.map(option => `
                                <div class="option-item flex items-center">
                                    <input type="text" name="edit_option_text[${index}][]" value="${option.text}" placeholder="{% trans 'Option text' %}" class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                                    <button type="button" class="delete-option ml-2 text-red-600 hover:text-red-800">
                                        <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                    </button>
                                </div>
                            `).join('')}
                        </div>
                        <button type="button" class="add-option mt-2 inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            {% trans "Add Option" %}
                        </button>
                    </div>
                `;
            } else {
                optionsHTML = `<div class="multiple-choice-options hidden"></div>`;
            }
            
            questionItem.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <div class="flex-grow">
                        <input type="text" name="edit_question_text[]" value="${question.text}" placeholder="{% trans 'Enter question text' %}" required class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    </div>
                    <button type="button" class="delete-question ml-2 text-red-600 hover:text-red-800">
                        <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                    </button>
                </div>
                
                <div class="mb-3">
                    <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Question Type" %}</label>
                    <select name="edit_question_type[]" class="question-type mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <option value="rating" ${question.type === 'rating' ? 'selected' : ''}>{% trans "Rating (1-5)" %}</option>
                        <option value="multiple_choice" ${question.type === 'multiple_choice' ? 'selected' : ''}>{% trans "Multiple Choice" %}</option>
                    </select>
                </div>
                
                ${optionsHTML}
            `;
            
            editQuestionsContainer.appendChild(questionItem);
            
            // Add event listeners to the new question item
            const questionTypeSelect = questionItem.querySelector('.question-type');
            questionTypeSelect.addEventListener('change', function() {
                toggleMultipleChoiceOptions(this);
            });
            
            const deleteQuestionBtn = questionItem.querySelector('.delete-question');
            deleteQuestionBtn.addEventListener('click', function() {
                this.closest('.question-item').remove();
                updateQuestionIndices('edit');
            });
            
            const addOptionBtn = questionItem.querySelector('.add-option');
            if (addOptionBtn) {
                addOptionBtn.addEventListener('click', function() {
                    addOption(this.closest('.question-item'), index, 'edit');
                });
            }
            
            const deleteOptionBtns = questionItem.querySelectorAll('.delete-option');
            deleteOptionBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    if (this.closest('.options-container').querySelectorAll('.option-item').length > 1) {
                        this.closest('.option-item').remove();
                    } else {
                        alert('{% trans "You must have at least one option" %}');
                    }
                });
            });
        });
        
        // Add event listeners for close buttons
        document.querySelectorAll('.close-edit-modal').forEach(btn => {
            btn.addEventListener('click', () => {
                document.getElementById('editSurveyModal').remove();
                document.body.classList.remove('modal-open');
            });
        });
        
        // Add event listener for add question button
        document.getElementById('editAddQuestionBtn').addEventListener('click', () => {
            addEditQuestion();
        });
        
        // Add event listener for form submission
        document.getElementById('editSurveyForm').addEventListener('submit', function(e) {
            e.preventDefault();
            submitEditSurvey();
        });
    }
    
    // Function to add a new question in edit mode
    function addEditQuestion() {
        const questionItems = document.querySelectorAll('#editQuestionsContainer .question-item');
        const index = questionItems.length;
        
        const questionItem = document.createElement('div');
        questionItem.className = 'question-item border border-gray-200 rounded-md p-4';
        
        questionItem.innerHTML = `
            <div class="flex justify-between items-start mb-3">
                <div class="flex-grow">
                    <input type="text" name="edit_question_text[]" placeholder="{% trans 'Enter question text' %}" required class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                </div>
                <button type="button" class="delete-question ml-2 text-red-600 hover:text-red-800">
                    <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                </button>
            </div>
            
            <div class="mb-3">
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Question Type" %}</label>
                <select name="edit_question_type[]" class="question-type mt-1 block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                    <option value="rating">{% trans "Rating (1-5)" %}</option>
                    <option value="multiple_choice">{% trans "Multiple Choice" %}</option>
                </select>
            </div>
            
            <div class="multiple-choice-options hidden">
                <label class="block text-sm font-medium text-gray-700 mb-1">{% trans "Options" %}</label>
                <div class="options-container space-y-2">
                    <div class="option-item flex items-center">
                        <input type="text" name="edit_option_text[${index}][]" placeholder="{% trans 'Option text' %}" class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
                        <button type="button" class="delete-option ml-2 text-red-600 hover:text-red-800">
                            <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                </div>
                <button type="button" class="add-option mt-2 inline-flex items-center px-2 py-1 border border-transparent text-xs leading-4 font-medium rounded text-blue-700 bg-blue-100 hover:bg-blue-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                    {% trans "Add Option" %}
                </button>
            </div>
        `;
        
        document.getElementById('editQuestionsContainer').appendChild(questionItem);
        
        // Add event listeners to the new question item
        const questionTypeSelect = questionItem.querySelector('.question-type');
        questionTypeSelect.addEventListener('change', function() {
            toggleMultipleChoiceOptions(this);
        });
        
        const deleteQuestionBtn = questionItem.querySelector('.delete-question');
        deleteQuestionBtn.addEventListener('click', function() {
            this.closest('.question-item').remove();
            updateQuestionIndices('edit');
        });
        
        const addOptionBtn = questionItem.querySelector('.add-option');
        if (addOptionBtn) {
            addOptionBtn.addEventListener('click', function() {
                addOption(this.closest('.question-item'), index, 'edit');
            });
        }
        
        const deleteOptionBtns = questionItem.querySelectorAll('.delete-option');
        deleteOptionBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                if (this.closest('.options-container').querySelectorAll('.option-item').length > 1) {
                    this.closest('.option-item').remove();
                } else {
                    alert('{% trans "You must have at least one option" %}');
                }
            });
        });
    }
    
    // Function to add option for edit mode
    function addOption(questionItem, questionIndex, mode) {
        const prefix = mode === 'edit' ? 'edit_' : '';
        
        const optionItem = document.createElement('div');
        optionItem.className = 'option-item flex items-center mt-2';
        
        optionItem.innerHTML = `
            <input type="text" name="${prefix}option_text[${questionIndex}][]" placeholder="{% trans 'Option text' %}" class="block w-full py-2 px-3 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500">
            <button type="button" class="delete-option ml-2 text-red-600 hover:text-red-800">
                <svg class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                </svg>
            </button>
        `;
        
        const deleteOptionBtn = optionItem.querySelector('.delete-option');
        deleteOptionBtn.addEventListener('click', function() {
            const optionsContainer = this.closest('.options-container');
            if (optionsContainer.querySelectorAll('.option-item').length > 1) {
                this.closest('.option-item').remove();
            } else {
                alert('{% trans "You must have at least one option" %}');
            }
        });
        
        questionItem.querySelector('.options-container').appendChild(optionItem);
    }
    
    // Function to update question indices after deletion in edit mode
    function updateQuestionIndices(mode) {
        const prefix = mode === 'edit' ? 'edit_' : '';
        const questionItems = document.querySelectorAll(`#${mode === 'edit' ? 'editQuestionsContainer' : 'questionsContainer'} .question-item`);
        
        questionItems.forEach((item, index) => {
            const options = item.querySelectorAll(`input[name^="${prefix}option_text["]`);
            options.forEach(option => {
                option.name = `${prefix}option_text[${index}][]`;
            });
        });
    }
    
    // Function to submit edited survey
    function submitEditSurvey() {
        const surveyId = document.getElementById('edit_survey_id').value;
        const title = document.getElementById('edit_survey_title').value;
        const courseInstanceId = document.getElementById('edit_course_instance').value;
        const isActive = document.getElementById('edit_is_active').checked;
        const questionItems = document.querySelectorAll('#editQuestionsContainer .question-item');
        
        if (!title || !courseInstanceId) {
            alert('{% trans "Please fill in all required fields" %}');
            return;
        }
        
        if (questionItems.length === 0) {
            alert('{% trans "Please add at least one question" %}');
            return;
        }
        
        // Build questions array
        const questions = [];
        
        questionItems.forEach((item, index) => {
            const questionText = item.querySelector('input[name="edit_question_text[]"]').value;
            const questionType = item.querySelector('select[name="edit_question_type[]"]').value;
            
            if (!questionText) {
                alert('{% trans "Please fill in all question texts" %}');
                return;
            }
            
            const question = {
                text: questionText,
                type: questionType
            };
            
            // If multiple choice, add options
            if (questionType === 'multiple_choice') {
                const optionInputs = item.querySelectorAll(`input[name="edit_option_text[${index}][]"]`);
                const options = [];
                
                optionInputs.forEach(input => {
                    if (input.value) {
                        options.push(input.value);
                    }
                });
                
                if (options.length === 0) {
                    alert('{% trans "Please add at least one option for each multiple choice question" %}');
                    return;
                }
                
                question.options = options;
            }
            
            questions.push(question);
        });
        
        // Create survey data object
        const surveyData = {
            title: title,
            course_instance_id: courseInstanceId,
            is_active: isActive,
            questions: questions
        };
        
        // Get the current URL path to determine language prefix
        const currentPath = window.location.pathname;
        const langPrefix = currentPath.startsWith('/en/') ? '/en' : '';
        
        // Submit data to API
        fetch(`${langPrefix}/api/surveys/${surveyId}/edit/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCookie('csrftoken')
            },
            body: JSON.stringify(surveyData)
        })
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            if (data.status === 'success') {
                // Close modal
                document.getElementById('editSurveyModal').remove();
                document.body.classList.remove('modal-open');
                
                // Reload surveys list
                loadSurveys();
                
                alert('{% trans "Survey updated successfully" %}');
            } else {
                alert(data.message || '{% trans "Error updating survey" %}');
            }
        })
        .catch(error => {
            console.error('Error updating survey:', error);
            alert('{% trans "An error occurred while updating the survey" %}');
        });
    }
</script>
{% endblock %} 