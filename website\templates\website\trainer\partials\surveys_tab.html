{% load i18n %}

<div class="mb-4 flex justify-between items-center">
    {% comment %} <h4 class="font-semibold text-lg text-white flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        {% trans "Surveys" %}
    </h4> {% endcomment %}
    
    
</div>

{% if surveys %}
    <!-- Survey Statistics Cards -->
    <div class="mb-6">
        <h5 class="font-semibold text-md text-white mb-3 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            {% trans "Survey Completion Statistics" %}
        </h5>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {% for survey_id, stats in survey_stats.items %}
                <div class="bg-gray-800 border border-gray-700 rounded-lg p-4 shadow-sm">
                    <h6 class="font-semibold text-white text-sm mb-1">{{ stats.survey.title }}</h6>
                    <div class="flex justify-between items-center mt-2">
                        <div class="text-gray-400 text-xs">
                            {% trans "Completion" %}:
                        </div>
                        <div class="text-sm font-medium text-white">
                            {{ stats.completed_count }}/{{ stats.total_users }}
                        </div>
                    </div>
                    <div class="mt-2 bg-gray-700 rounded-full h-2.5 overflow-hidden">
                        <div class="bg-blue-500 h-2.5 rounded-full" style="width: {{ stats.completion_percentage }}%"></div>
                    </div>
                    <div class="text-right mt-1 text-xs text-gray-400">
                        {{ stats.completion_percentage }}% {% trans "completed" %}
                    </div>
                </div>
            {% endfor %}
        </div>
    </div>

    <!-- User Survey Completion Table -->
    <div class="mb-6">
        <h5 class="font-semibold text-md text-white mb-3 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
            </svg>
            {% trans "User Survey Status" %}
        </h5>
        
        <div class="overflow-x-auto rounded-lg border border-gray-700">
            <table class="min-w-full divide-y divide-gray-700">
                <thead class="bg-gray-700/50">
                    <tr>
                        <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-300 uppercase tracking-wider">
                            {% trans "User" %}
                        </th>
                        {% for survey in surveys %}
                            <th scope="col" class="px-6 py-3 text-center text-xs font-medium text-gray-300 uppercase tracking-wider">
                                {{ survey.title }}
                            </th>
                        {% endfor %}
                    </tr>
                </thead>
                <tbody class="divide-y divide-gray-700 bg-gray-800">
                    {% for user_data in users_survey_status %}
                        <tr class="hover:bg-gray-700 transition-colors duration-150">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-white">
                                {{ user_data.user.get_full_name }}
                            </td>
                            
                            {% for survey_data in user_data.surveys %}
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-center">
                                    {% if survey_data.completed %}
                                        <span class="inline-flex items-center justify-center text-green-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                            </svg>
                                        </span>
                                    {% else %}
                                        <span class="inline-flex items-center justify-center text-yellow-500">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                                            </svg>
                                        </span>
                                    {% endif %}
                                </td>
                            {% endfor %}
                        </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
{% else %}
    <div class="text-center py-10 bg-gray-800/50 rounded-lg border border-gray-700">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 mx-auto text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
        </svg>
        <h3 class="text-xl font-medium text-white mb-2">{% trans "No Survey Responses" %}</h3>
        <p class="text-gray-400 max-w-md mx-auto mb-6">{% trans "No one has completed any surveys for this course instance yet." %}</p>
        
        <a href="{% url 'website:surveys' %}" class="inline-flex items-center px-4 py-2 border border-blue-700 rounded-md bg-blue-800 hover:bg-blue-700 text-white transition-colors duration-150">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6" />
            </svg>
            {% trans "Go to Surveys" %}
        </a>
    </div>
{% endif %} 