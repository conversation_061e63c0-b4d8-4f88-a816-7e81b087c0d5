from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db import transaction
from django.contrib import messages
import logging
import json

from .models import CustomUser, Reservation, Survey, SurveyResponse, Questionnaire, QuestionnaireResponse, CourseInstance, Session, Notification

# Import email utility functions
from individual_email_utils import (
    send_questionnaire_submission_confirmation,
    send_reservation_cancellation_confirmation,
    send_pending_survey_reminder,
    send_pending_questionnaire_reminder,
    send_waitlist_notification
)

logger = logging.getLogger(__name__)

@login_required
def individual_user_surveys_view(request):
    """
    View to display all available surveys for individual users to complete.
    Shows both pending and completed survey responses.
    """
    # Check if this is an individual user (not a corporate user)
    if request.user.company_name:
        messages.warning(request, _('Corporate users have a different survey view.'))
        return redirect('website:user_surveys')
    
    # Get all survey responses for this user
    survey_responses = SurveyResponse.objects.filter(
        user=request.user
    ).select_related(
        'survey', 
        'reservation', 
        'reservation__course_instance', 
        'reservation__course_instance__course'
    ).order_by('-created_at')
    
    # Separate into pending and completed
    pending_responses = survey_responses.filter(status='PENDING')
    completed_responses = survey_responses.filter(status='COMPLETED')
    
    context = {
        'pending_responses': pending_responses,
        'completed_responses': completed_responses,
        'total_pending': pending_responses.count(),
        'total_completed': completed_responses.count(),
        'page_title': _('My Course Feedback')
    }
    
    return render(request, 'website/survays/individual_user_surveys.html', context)


@login_required
@require_http_methods(["GET"])
def get_individual_pending_surveys_count(request):
    """
    API endpoint to get the count of pending surveys for individual users.
    Returns count and a list of pending survey responses.
    """
    # Check if this is an individual user
    if request.user.company_name:
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for individual users only.')
        }, status=403)
    
    try:
        # Find completed reservations for this user
        completed_reservations = Reservation.objects.filter(
            user=request.user,
            status='COMPLETED',
            completed_at__isnull=False
        ).select_related('course_instance', 'course_instance__course')
        
        # Find survey responses that are pending
        pending_responses = SurveyResponse.objects.filter(
            user=request.user, 
            status='PENDING',
            reservation__in=completed_reservations
        ).select_related('survey', 'reservation', 'reservation__course_instance', 'reservation__course_instance__course')
        
        # Count pending surveys
        count = pending_responses.count()
        
        # Format response data
        surveys_data = []
        for response in pending_responses:
            course_instance = response.reservation.course_instance
            course = course_instance.course if course_instance else None
            
            if not course:
                continue
                
            surveys_data.append({
                'response_id': response.response_id,
                'survey': {
                    'survey_id': response.survey.survey_id,
                    'title': response.survey.title,
                    'question_count': len(response.survey.questions) if response.survey.questions else 0
                },
                'reservation': {
                    'reservation_id': response.reservation.reservation_id,
                    'completed_at': response.reservation.completed_at.isoformat() if response.reservation.completed_at else None
                },
                'course': {
                    'name': course.name_en,
                    'category': course.get_category_display()
                }
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count,
            'surveys': surveys_data
        })
        
    except Exception as e:
        logger.error(f"Error getting pending surveys count for individual user: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500) 

@login_required
@require_http_methods(["GET"])
def get_individual_pending_questionnaires_count(request):
    """
    API endpoint to get count of pending questionnaires for individual users
    """
    # Check if this is an individual user
    if request.user.company_name:
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for individual users only.')
        }, status=403)
    
    try:
        # Count pending questionnaire responses
        count = QuestionnaireResponse.objects.filter(
            user=request.user,
            status='PENDING_SUBMISSION'
        ).count()
        
        # If detailed is requested, return questionnaire details
        if request.GET.get('detailed', 'false').lower() == 'true':
            responses = QuestionnaireResponse.objects.filter(
                user=request.user,
                status='PENDING_SUBMISSION'
            ).select_related('questionnaire', 'course').order_by('-created_at')
            
            # Try to fetch associated reservations
            questionnaires_data = []
            for response in responses:
                # Look for associated reservation
                reservation = Reservation.objects.filter(
                    user=request.user,
                    course_instance__course=response.course
                ).first()
                
                questionnaires_data.append({
                    'questionnaire': {
                        'id': response.questionnaire.questionnaire_id,
                        'questionnaire_id': response.questionnaire.questionnaire_id,  # Add this for compatibility
                        'title': response.questionnaire.title,
                        'session_number': response.questionnaire.session_number,
                        'question_count': len(response.questionnaire.questions) if response.questionnaire.questions else 0,
                        'total_score': response.questionnaire.total_score
                    },
                    'course': {
                        'name': response.course.name_en,
                        'category': response.course.get_category_display() if hasattr(response.course, 'get_category_display') else response.course.category
                    },
                    'response_id': response.response_id,
                    'created_at': response.created_at.isoformat() if response.created_at else None,
                    'reservation': {
                        'reservation_id': reservation.reservation_id if reservation else None,
                        'created_at': reservation.created_at.isoformat() if reservation and reservation.created_at else None
                    }
                })
        
            return JsonResponse({
                'status': 'success',
                'count': count,
                'questionnaires': questionnaires_data
            })
        
        return JsonResponse({
            'status': 'success',
            'count': count
        })
        
    except Exception as e:
        logger.error(f"Error getting pending questionnaires count for individual user: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def individual_user_questionnaires_view(request):
    """
    View to display all available questionnaires for individual users to complete.
    Shows both pending and completed questionnaire responses.
    """
    # Check if this is an individual user (not a corporate user)
    if request.user.company_name:
        messages.warning(request, _('Corporate users have a different questionnaires view.'))
        return redirect('website:user_questionnaires')
    
    # Get all questionnaire responses for this user
    questionnaire_responses = QuestionnaireResponse.objects.filter(
        user=request.user
    ).select_related(
        'questionnaire', 
        'course'
    ).order_by('-created_at')
    
    # Separate by status
    pending_responses = questionnaire_responses.filter(status='PENDING_SUBMISSION')
    pending_review_responses = questionnaire_responses.filter(status='PENDING_TRAINER_REVIEW')
    completed_responses = questionnaire_responses.filter(
        status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED']
    )
    
    context = {
        'pending_responses': pending_responses,
        'pending_review_responses': pending_review_responses,
        'completed_responses': completed_responses,
        'total_pending': pending_responses.count(),
        'total_pending_review': pending_review_responses.count(),
        'total_completed': completed_responses.count(),
        'page_title': _('My Course Questionnaires')
    }
    
    return render(request, 'website/questionnaires/individual_user_questionnaires.html', context)

@login_required
def individual_take_questionnaire_view(request, questionnaire_id, reservation_id=None):
    """
    View for individual users to take a specific questionnaire.
    """
    # Check if this is an individual user
    if request.user.company_name:
        messages.warning(request, _('Corporate users have a different questionnaires view.'))
        return redirect('website:take_questionnaire', questionnaire_id=questionnaire_id, reservation_id=reservation_id)
    
    try:
        questionnaire = Questionnaire.objects.get(questionnaire_id=questionnaire_id)
        
        # Find the reservation if ID is provided
        reservation = None
        course_instance = None
        session = None
        
        if reservation_id:
            reservation = Reservation.objects.get(reservation_id=reservation_id, user=request.user)
            course_instance = reservation.course_instance
            
            # Try to find an appropriate session based on the session number of the questionnaire
            if course_instance:
                session = Session.objects.filter(
                    course_instance=course_instance,
                    course=questionnaire.course
                ).first()
        
        # Check if the user has already completed this questionnaire
        existing_response = QuestionnaireResponse.objects.filter(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            status__in=['COMPLETED_SUBMITTED', 'COMPLETED_TRAINER_REVIEWED', 'PENDING_TRAINER_REVIEW']
        ).exists()
        
        if existing_response:
            messages.warning(request, _("You have already submitted this questionnaire."))
            return redirect('website:individual_user_questionnaires')
        
        # Get or create a pending response
        questionnaire_response, created = QuestionnaireResponse.objects.get_or_create(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            defaults={
                'status': 'PENDING',
                'course_instance': course_instance,
                'session': session
            }
        )
        
        context = {
            'questionnaire': questionnaire,
            'reservation': reservation,
            'questionnaire_response': questionnaire_response,
            'course_instance': course_instance,
            'session': session,
            'page_title': _('Take Questionnaire')
        }
        
        return render(request, 'website/questionnaires/take_questionnaire.html', context)
        
    except Questionnaire.DoesNotExist:
        messages.error(request, _("Questionnaire not found."))
        return redirect('website:individual_user_questionnaires')

@login_required
@require_http_methods(["POST"])
def individual_create_questionnaire_response_api(request):
    """
    API endpoint for individual users to submit a questionnaire response.
    """
    # Check if this is an individual user
    if request.user.company_name:
        return JsonResponse({
            'status': 'error',
            'message': _('This API is for individual users only.')
        }, status=403)
    
    try:
        data = json.loads(request.body)
        questionnaire_id = data.get('questionnaire_id')
        question_responses = data.get('question_responses', {})
        comment = data.get('comment', '')
        course_instance_id = data.get('course_instance_id')
        session_id = data.get('session_id')
        
        # Validate required fields
        if not questionnaire_id:
            return JsonResponse({
                'status': 'error',
                'message': _("Questionnaire ID is required.")
            }, status=400)
            
        # Get the questionnaire
        questionnaire = Questionnaire.objects.get(questionnaire_id=questionnaire_id)
        
        # Get optional related objects
        course_instance = None
        session = None
        
        if course_instance_id:
            try:
                course_instance = CourseInstance.objects.get(instance_id=course_instance_id)
            except CourseInstance.DoesNotExist:
                logger.warning(f"Course instance with ID {course_instance_id} not found")
        
        if session_id:
            try:
                session = Session.objects.get(session_id=session_id)
            except Session.DoesNotExist:
                logger.warning(f"Session with ID {session_id} not found")
        
        # Get or create the questionnaire response
        questionnaire_response, created = QuestionnaireResponse.objects.get_or_create(
            questionnaire=questionnaire,
            user=request.user,
            course=questionnaire.course,
            session_number=questionnaire.session_number,
            defaults={
                'status': 'PENDING_SUBMISSION',
                'max_total_score': questionnaire.total_score,
                'course_instance': course_instance,
                'session': session
            }
        )
        
        # Calculate the total score based on correct answers
        total_score = 0
        if questionnaire.questions:
            for i, question in enumerate(questionnaire.questions):
                question_type = question.get('type')
                question_score = question.get('score', 0)
                
                # Get user's answer for this question
                user_answer = question_responses.get(str(i))
                
                # For multiple choice and checkbox questions, check if answer is correct
                if question_type in ['multiplechoice', 'checkbox']:
                    correct_answers = set(question.get('correct_answers', []))
                    
                    # For multiplechoice, user can only select one option
                    if question_type == 'multiplechoice' and user_answer and int(user_answer) in correct_answers:
                        total_score += question_score
                    
                    # For checkbox, user can select multiple options
                    elif question_type == 'checkbox' and user_answer:
                        user_selections = set([int(ans) for ans in user_answer if ans])
                        if user_selections == correct_answers:
                            total_score += question_score
        
        # Update the questionnaire response
        questionnaire_response.question_responses = question_responses
        questionnaire_response.comment = comment
        questionnaire_response.max_total_score = questionnaire.total_score
        questionnaire_response.total_score = total_score
        questionnaire_response.status = 'PENDING_TRAINER_REVIEW'
        questionnaire_response.completed_at = timezone.now()
        
        # Update course instance and session if they weren't set earlier
        if course_instance and not questionnaire_response.course_instance:
            questionnaire_response.course_instance = course_instance
        if session and not questionnaire_response.session:
            questionnaire_response.session = session
            
        questionnaire_response.save()
        
        # Send confirmation email to the user
        try:
            send_questionnaire_submission_confirmation(
                user=request.user,
                questionnaire_title=questionnaire.title,
                score=total_score,
                max_score=questionnaire.total_score
            )
            logger.info(f"Sent questionnaire submission confirmation to {request.user.email}")
        except Exception as e:
            logger.error(f"Failed to send questionnaire submission email: {str(e)}")
        
        return JsonResponse({
            'status': 'success',
            'message': _("Questionnaire response submitted and recorded successfully."),
            'response_id': questionnaire_response.response_id,
            'user_type': request.user.user_type.code if request.user.user_type else 'UNKNOWN',
            'total_score': total_score,
            'max_score': questionnaire.total_score
        })
        
    except Questionnaire.DoesNotExist:
        return JsonResponse({
            'status': 'error',
            'message': _("Questionnaire not found.")
        }, status=404)
    except Exception as e:
        logger.error(f"Error submitting questionnaire response for individual user: {str(e)}")
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500) 

@login_required
def user_reservations_view(request):
    """
    View for individual users to see and manage their reservations
    """
    # First, auto-cancel any expired reservations for this user
    from .views import auto_cancel_expired_reservations
    auto_cancel_expired_reservations(user=request.user)
    
    if request.method == 'POST' and 'cancel_reservation' in request.POST:
        reservation_id = request.POST.get('reservation_id')
        
        try:
            # Get the reservation
            reservation = Reservation.objects.get(
                reservation_id=reservation_id,
                user=request.user
            )
            
            # Check if cancellation is allowed
            now = timezone.now()
            
            # Get the business days for cancellation (default to 3 if not specified)
            business_days = getattr(reservation.course_instance, 'cancellation_deadline_business_days', 3)
            
            # Calculate the deadline based on business days
            from .views import calculate_business_days_deadline
            deadline_time = calculate_business_days_deadline(reservation.course_instance.start_date, business_days)
            
            if now > deadline_time:
                messages.error(request, _('The cancellation deadline has passed. Please submit an emergency cancellation request if you need to cancel.'))
            else:
                # Update the reservation status
                reservation.status = 'CANCELLED'
                reservation.save(update_fields=['status'])
                
                # Create a notification
                Notification.objects.create(
                    user=request.user,
                    message=_('Your reservation for course: {} ({}) has been cancelled.').format(
                        reservation.course_instance.course.name_en,
                        reservation.course_instance.start_date.strftime('%Y-%m-%d')
                    ),
                    delivered_at=timezone.now()
                )
                
                # Send email confirmation of cancellation
                try:
                    course_name = reservation.course_instance.course.name_en
                    start_date = reservation.course_instance.start_date.strftime('%Y-%m-%d')
                    
                    # Check if the course was paid to include refund info
                    refund_info = None
                    if hasattr(reservation, 'payment') and reservation.payment:
                        refund_info = _("Your payment refund will be processed according to our refund policy.")
                    
                    send_reservation_cancellation_confirmation(
                        user=request.user,
                        course_name=course_name,
                        start_date=start_date,
                        refund_info=refund_info
                    )
                    logger.info(f"Sent reservation cancellation confirmation to {request.user.email}")
                except Exception as e:
                    logger.error(f"Failed to send reservation cancellation email: {str(e)}")
                
                # Process the waiting list for this course instance
                if reservation.course_instance.has_waiting_list:
                    reservation.course_instance.process_waiting_list()
                
                messages.success(request, _('Your reservation has been cancelled successfully.'))
        except Reservation.DoesNotExist:
            messages.error(request, _('Reservation not found.'))
    
    # Get all reservations for this user (only individual course types)
    user_reservations = Reservation.objects.filter(
        user=request.user,
        course_instance__course_type='INDIVIDUAL'  # Only show INDIVIDUAL course types to individual users
    ).select_related(
        'course_instance', 'course_instance__course'
    ).order_by('-created_at')
    
    # Current date and time for status checks
    now = timezone.now()

    # Try to update reservation statuses based on current time
    try:
        for reservation in user_reservations:
            # Skip if the status is already CANCELLED, WAITING_LIST, or WAITING_TO_PAY
            if hasattr(reservation, 'status') and reservation.status in ['CANCELLED', 'WAITING_LIST', 'WAITING_TO_PAY']:
                continue
                
            # Determine the correct status based on dates
            if reservation.course_instance.start_date > now:
                new_status = 'UPCOMING'
            elif reservation.course_instance.end_date < now:
                new_status = 'COMPLETED'
                # Set the completed_at field if status is changing to COMPLETED
                if reservation.status != 'COMPLETED':
                    reservation.completed_at = now
            else:
                new_status = 'IN_PROGRESS'
                
            # Update if status has changed and the field exists
            if hasattr(reservation, 'status') and reservation.status != new_status:
                reservation.status = new_status
                reservation.save()  # Save all fields to ensure completed_at is saved
    except Exception as e:
        # Log the error but continue - field might not exist yet
        logger.error(f"Error updating reservation statuses: {e}", exc_info=True)
    
    return render(request, 'website/reservations/user_reservations.html', {
        'user_reservations': user_reservations,
        'now': now
    }) 