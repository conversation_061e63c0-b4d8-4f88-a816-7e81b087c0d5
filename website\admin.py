from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from .models import (
    CustomUser, QuestionnaireResponse, UserType, Section, Feature, Course, Room, RoomAvailability,
    TrainerAvailability, Trainer, Corporate, CorporateAdmin, Session, Attendance, Role,
    Form, FormContent, Event, Reservation, Questionnaire, Notification,
    Payment, ContactInfo, Slide, EventType, RoomType, Equipment, EquipmentMaintenance,
    EquipmentAvailability, Category, Brand, Model, CourseInstance, EmergencyCancellationRequest,
    Holiday, CourseContent, CorporateAdminRequest, CorporateUserCancellationRequest,
    Survey, SurveyResponse, CorporateAdminNewCourseRequest, EmailLog, GB_Employee, 
    OTPVerification, GBEmployeeRequest
)
from django.utils import timezone
import json
from django.utils.safestring import mark_safe
from django.db import transaction
import logging

# Register your models here.

@admin.register(UserType)
class UserTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'code', 'is_active')
    search_fields = ('name', 'code')
    list_filter = ('is_active',)

@admin.register(CustomUser)
class CustomUserAdmin(UserAdmin):
    list_display = ('username', 'email', 'first_name', 'last_name', 'user_type', 'employee_id', 'is_staff', 'account_status', 'force_password_reset')
    list_filter = ('is_staff', 'is_active', 'account_status', 'user_type', 'force_password_reset', 'oracle_department')
    search_fields = ('username', 'email', 'first_name', 'last_name', 'employee_id', 'oracle_department', 'oracle_position')
    ordering = ('username',)
    
    fieldsets = (
        (_('Basic Information'), {'fields': ('username', 'email', 'password', 'user_type')}),
        (_('Personal Information'), {
            'fields': ('first_name', 'last_name', 'phone_number', 'nationality',
                      'passport_number', 'national_id', 'address', 'date_of_birth')
        }),
        (_('Corporate Information'), {
            'fields': ('company_name', 'commercial_registration_number',
                      'tax_registration_number', 'key_person_name',
                      'key_person_phone', 'key_person_email')
        }),
        (_('Internal GB Employee Information'), {
            'fields': ('employee_id', 'gb_employee'),
            'description': _('Information for GB internal employees')
        }),
        (_('Oracle Integration Data'), {
            'fields': ('oracle_department', 'oracle_position', 'oracle_supervisor_id', 
                      'oracle_hire_date', 'oracle_grade_name'),
            'classes': ('collapse',),
            'description': _('Cached Oracle employee data. This can be manually updated.')
        }),
        (_('Status'), {
            'fields': ('is_active', 'account_status', 'is_profile_complete', 'force_password_reset')
        }),
        (_('Permissions'), {
            'fields': ('is_staff', 'is_superuser', 'groups', 'user_permissions')
        }),
    )
    
    readonly_fields = ()
    
    def get_readonly_fields(self, request, obj=None):
        """Dynamically set readonly fields."""
        # This method is kept for potential future use, but for now,
        # all Oracle fields are editable.
        return super().get_readonly_fields(request, obj)
    
    actions = ['sync_oracle_data']
    
    def sync_oracle_data(self, request, queryset):
        """Action to sync Oracle data for selected users"""
        synced_count = 0
        for user in queryset:
            if user.gb_employee:
                user.sync_oracle_data()
                synced_count += 1
        
        self.message_user(request, _('Oracle data synced for {} users.').format(synced_count))
    sync_oracle_data.short_description = _('Sync Oracle data for selected users')

@admin.register(GB_Employee)
class GB_EmployeeAdmin(admin.ModelAdmin):
    list_display = ('employee_number', 'english_name', 'oracle_username', 'department', 'position_name', 
                   'oracle_email_address', 'get_supervisor_name', 'get_subordinates_count', 'hire_date', 'is_active', 'last_sync_date')
    list_filter = ('is_active', 'department', 'grade_name', 'organization_id', 'last_sync_date')
    search_fields = ('employee_number', 'english_name', 'arabic_name', 'full_name', 'oracle_username', 
                    'oracle_email_address', 'department', 'position_name', 'supervisor_user_name')
    ordering = ('employee_number',)
    readonly_fields = ('employee_number', 'last_sync_date', 'created_at', 'updated_at', 'get_subordinates_display', 'get_supervisor_display')
    date_hierarchy = 'hire_date'
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('employee_number', 'full_name', 'english_name', 'arabic_name', 'oracle_username')
        }),
        (_('Contact Information'), {
            'fields': ('oracle_email_address', 'oracle_phone_number')
        }),
        (_('Job Information'), {
            'fields': ('department', 'position_name', 'organization_id', 'hire_date', 
                      'grade_name', 'grade_id', 'payroll_id')
        }),
        (_('Organizational Hierarchy'), {
            'fields': ('supervisor_id', 'supervisor_user_name', 'head_department', 'cost_center',
                      'get_supervisor_display', 'get_subordinates_display'),
            'description': _('Shows supervisor information and team members reporting to this employee'),
            'classes': ('wide',)
        }),
        (_('System Information'), {
            'fields': ('is_active', 'last_sync_date', 'created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['sync_to_users', 'mark_active', 'mark_inactive', 'show_team_hierarchy']
    
    def sync_to_users(self, request, queryset):
        """Action to sync selected GB employees to their linked CustomUser accounts"""
        synced_count = 0
        created_count = 0
        
        for gb_employee in queryset:
            # Try to find existing user by employee_id or email
            user = None
            if gb_employee.employee_number:
                user = CustomUser.objects.filter(employee_id=gb_employee.employee_number).first()
            
            if not user and gb_employee.oracle_email_address:
                user = CustomUser.objects.filter(email=gb_employee.oracle_email_address).first()
            
            if user:
                # Link and sync existing user
                user.gb_employee = gb_employee
                user.sync_oracle_data()
                synced_count += 1
            else:
                # Optionally create new user (commented out for safety)
                # You might want to implement user creation logic here
                pass
        
        message = _('Synced {} GB employees to existing users.').format(synced_count)
        if created_count:
            message += _(' Created {} new users.').format(created_count)
        self.message_user(request, message)
    sync_to_users.short_description = _('Sync selected employees to CustomUser accounts')
    
    def mark_active(self, request, queryset):
        updated = queryset.update(is_active=True)
        self.message_user(request, _('Marked {} employees as active.').format(updated))
    mark_active.short_description = _('Mark selected employees as active')
    
    def mark_inactive(self, request, queryset):
        updated = queryset.update(is_active=False)
        self.message_user(request, _('Marked {} employees as inactive.').format(updated))
    mark_inactive.short_description = _('Mark selected employees as inactive')
    
    def show_team_hierarchy(self, request, queryset):
        """Custom action to show team hierarchy for selected employees"""
        from django.http import HttpResponse
        import json
        
        hierarchy_data = []
        
        for employee in queryset:
            # Get subordinates
            subordinates = GB_Employee.objects.filter(
                supervisor_id=employee.employee_number,
                is_active=True
            ).select_related()
            
            # Get supervisor
            supervisor = None
            if employee.supervisor_id:
                supervisor = GB_Employee.objects.filter(
                    employee_number=employee.supervisor_id,
                    is_active=True
                ).first()
            
            from .models import CustomUser
            
            employee_data = {
                'employee': {
                    'name': employee.english_name or employee.full_name,
                    'employee_number': employee.employee_number,
                    'department': employee.department,
                    'position': employee.position_name,
                    'email': employee.oracle_email_address,
                    'has_system_account': CustomUser.objects.filter(gb_employee=employee).exists()
                },
                'supervisor': None,
                'team_members': []
            }
            
            if supervisor:
                employee_data['supervisor'] = {
                    'name': supervisor.english_name or supervisor.full_name,
                    'employee_number': supervisor.employee_number,
                    'department': supervisor.department,
                    'position': supervisor.position_name,
                    'has_system_account': CustomUser.objects.filter(gb_employee=supervisor).exists()
                }
            
            for subordinate in subordinates:
                employee_data['team_members'].append({
                    'name': subordinate.english_name or subordinate.full_name,
                    'employee_number': subordinate.employee_number,
                    'department': subordinate.department,
                    'position': subordinate.position_name,
                    'email': subordinate.oracle_email_address,
                    'has_system_account': CustomUser.objects.filter(gb_employee=subordinate).exists()
                })
            
            hierarchy_data.append(employee_data)
        
        # Generate HTML report
        html_content = f'''
        <!DOCTYPE html>
        <html>
        <head>
            <title>Team Hierarchy Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .employee-card {{ border: 1px solid #ddd; margin: 20px 0; padding: 15px; border-radius: 5px; }}
                .supervisor {{ background-color: #e8f4fd; }}
                .current {{ background-color: #fff2cc; border-left: 5px solid #ffb92e; }}
                .team-member {{ background-color: #f0f8e8; margin: 10px 0; padding: 10px; border-radius: 3px; }}
                .account-status {{ padding: 2px 6px; border-radius: 3px; font-size: 12px; }}
                .has-account {{ background-color: #d4edda; color: #155724; }}
                .no-account {{ background-color: #f8d7da; color: #721c24; }}
                h1, h2, h3 {{ color: #333; }}
                table {{ border-collapse: collapse; width: 100%; }}
                th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
                th {{ background-color: #f2f2f2; }}
            </style>
        </head>
        <body>
            <h1>🏢 Team Hierarchy Report</h1>
            <p>Generated on: {timezone.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Number of employees analyzed: {len(hierarchy_data)}</p>
        '''
        
        for emp_data in hierarchy_data:
            employee = emp_data['employee']
            supervisor = emp_data['supervisor']
            team_members = emp_data['team_members']
            
            html_content += f'''
            <div class="employee-card current">
                <h2>👤 {employee['name']} (#{employee['employee_number']})</h2>
                <p><strong>Department:</strong> {employee['department'] or 'N/A'}</p>
                <p><strong>Position:</strong> {employee['position'] or 'N/A'}</p>
                <p><strong>Email:</strong> {employee['email'] or 'N/A'}</p>
                <p><strong>System Account:</strong> 
                    <span class="account-status {'has-account' if employee['has_system_account'] else 'no-account'}">
                        {'✅ Yes' if employee['has_system_account'] else '❌ No'}
                    </span>
                </p>
            '''
            
            if supervisor:
                html_content += f'''
                <div class="employee-card supervisor">
                    <h3>👑 Reports to: {supervisor['name']} (#{supervisor['employee_number']})</h3>
                    <p><strong>Department:</strong> {supervisor['department'] or 'N/A'}</p>
                    <p><strong>Position:</strong> {supervisor['position'] or 'N/A'}</p>
                    <p><strong>System Account:</strong> 
                        <span class="account-status {'has-account' if supervisor['has_system_account'] else 'no-account'}">
                            {'✅ Yes' if supervisor['has_system_account'] else '❌ No'}
                        </span>
                    </p>
                </div>
                '''
            else:
                html_content += '<p><em>No supervisor assigned</em></p>'
            
            if team_members:
                html_content += f'<h3>👥 Team Members ({len(team_members)}):</h3>'
                for member in team_members:
                    html_content += f'''
                    <div class="team-member">
                        <strong>{member['name']} (#{member['employee_number']})</strong><br>
                        <small>{member['department'] or 'N/A'} - {member['position'] or 'N/A'}</small><br>
                        <small>Email: {member['email'] or 'N/A'}</small><br>
                        <span class="account-status {'has-account' if member['has_system_account'] else 'no-account'}">
                            {'✅ Has Account' if member['has_system_account'] else '❌ No Account'}
                        </span>
                    </div>
                    '''
            else:
                html_content += '<p><em>No team members</em></p>'
            
            html_content += '</div>'
        
        html_content += '''
        </body>
        </html>
        '''
        
        response = HttpResponse(html_content, content_type='text/html')
        response['Content-Disposition'] = f'attachment; filename="team_hierarchy_report_{timezone.now().strftime("%Y%m%d_%H%M%S")}.html"'
        return response
    show_team_hierarchy.short_description = _('Generate team hierarchy report')
    
    def has_add_permission(self, request):
        """Restrict manual addition - these records should come from Oracle integration"""
        return request.user.is_superuser
    
    def has_change_permission(self, request, obj=None):
        """Allow editing for superusers, read-only for others"""
        return request.user.is_superuser
    
    def has_delete_permission(self, request, obj=None):
        """Only superusers can delete GB employee records"""
        return request.user.is_superuser
    
    def get_queryset(self, request):
        """Optimize queries"""
        return super().get_queryset(request).select_related()
    
    def get_supervisor_name(self, obj):
        """Display supervisor name for list view"""
        if obj.supervisor_id:
            supervisor = GB_Employee.objects.filter(
                employee_number=obj.supervisor_id,
                is_active=True
            ).first()
            if supervisor:
                return f"{supervisor.english_name or supervisor.full_name} ({supervisor.employee_number})"
            return f"Supervisor ID: {obj.supervisor_id}"
        return "-"
    get_supervisor_name.short_description = _('Supervisor')
    get_supervisor_name.admin_order_field = 'supervisor_id'
    
    def get_subordinates_count(self, obj):
        """Display count of direct reports"""
        count = GB_Employee.objects.filter(
            supervisor_id=obj.employee_number,
            is_active=True
        ).count()
        if count > 0:
            return mark_safe(f'<strong style="color: #2e8b57;">{count}</strong>')
        return "0"
    get_subordinates_count.short_description = _('Team Size')
    
    def get_supervisor_display(self, obj):
        """Enhanced supervisor display for detail view"""
        if not obj.supervisor_id:
            return _("No supervisor assigned")
        
        supervisor = GB_Employee.objects.filter(
            employee_number=obj.supervisor_id,
            is_active=True
        ).first()
        
        if not supervisor:
            return mark_safe(f'<span style="color: #dc3545;">⚠️ Supervisor not found (ID: {obj.supervisor_id})</span>')
        
        # Check if supervisor has system account
        from .models import CustomUser
        supervisor_user = CustomUser.objects.filter(
            gb_employee=supervisor
        ).first()
        
        html = f'''
        <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; background-color: #f8f9fa;">
            <h4 style="margin-top: 0; color: #495057;">👤 Supervisor Information</h4>
            <table style="width: 100%; border-collapse: collapse;">
                <tr>
                    <td style="padding: 5px; font-weight: bold; width: 150px;">Name:</td>
                    <td style="padding: 5px;">{supervisor.english_name or supervisor.full_name}</td>
                </tr>
                <tr>
                    <td style="padding: 5px; font-weight: bold;">Employee ID:</td>
                    <td style="padding: 5px;">{supervisor.employee_number}</td>
                </tr>
                <tr>
                    <td style="padding: 5px; font-weight: bold;">Department:</td>
                    <td style="padding: 5px;">{supervisor.department or '-'}</td>
                </tr>
                <tr>
                    <td style="padding: 5px; font-weight: bold;">Position:</td>
                    <td style="padding: 5px;">{supervisor.position_name or '-'}</td>
                </tr>
                <tr>
                    <td style="padding: 5px; font-weight: bold;">Email:</td>
                    <td style="padding: 5px;">{supervisor.oracle_email_address or '-'}</td>
                </tr>
                <tr>
                    <td style="padding: 5px; font-weight: bold;">System Account:</td>
                    <td style="padding: 5px;">
                        {"✅ Yes" if supervisor_user else "❌ No"}
                        {f" ({supervisor_user.username})" if supervisor_user else ""}
                    </td>
                </tr>
            </table>
        </div>
        '''
        return mark_safe(html)
    get_supervisor_display.short_description = _('Supervisor Details')
    
    def get_subordinates_display(self, obj):
        """Enhanced team members display for detail view"""
        subordinates = GB_Employee.objects.filter(
            supervisor_id=obj.employee_number,
            is_active=True
        ).select_related().order_by('english_name')
        
        if not subordinates.exists():
            return mark_safe('<p style="color: #6c757d; font-style: italic;">📄 No team members reporting to this employee</p>')
        
        from .models import CustomUser
        
        html = [f'''
        <div style="border: 1px solid #ddd; padding: 15px; border-radius: 5px; background-color: #f8f9fa;">
            <h4 style="margin-top: 0; color: #495057;">👥 Team Members ({subordinates.count()})</h4>
            <table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
                <thead>
                    <tr style="background-color: #e9ecef;">
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">Employee</th>
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">Department</th>
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">Position</th>
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: left;">Email</th>
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">System Account</th>
                        <th style="padding: 10px; border: 1px solid #dee2e6; text-align: center;">Recent Requests</th>
                    </tr>
                </thead>
                <tbody>
        ''']
        
        for subordinate in subordinates:
            # Check if subordinate has system account
            subordinate_user = CustomUser.objects.filter(
                gb_employee=subordinate
            ).first()
            
            # Get recent GB employee requests
            from .models import GBEmployeeRequest
            recent_requests = GBEmployeeRequest.objects.filter(
                employee=subordinate
            ).order_by('-created_at')[:3]
            
            # Format recent requests
            requests_html = ""
            if recent_requests:
                requests_list = []
                for req in recent_requests:
                    status_color = {
                        'PENDING': '#ffc107',
                        'APPROVED': '#28a745', 
                        'REJECTED': '#dc3545'
                    }.get(req.status, '#6c757d')
                    requests_list.append(f'<span style="color: {status_color}; font-size: 12px;">●</span> {req.get_status_display()} ({req.created_at.strftime("%m/%d")})')
                requests_html = '<br>'.join(requests_list)
            else:
                requests_html = '<span style="color: #6c757d; font-size: 12px;">No requests</span>'
            
            html.append(f'''
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 8px; border: 1px solid #dee2e6;">
                        <strong>{subordinate.english_name or subordinate.full_name}</strong><br>
                        <small style="color: #6c757d;">#{subordinate.employee_number}</small>
                    </td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{subordinate.department or '-'}</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">{subordinate.position_name or '-'}</td>
                    <td style="padding: 8px; border: 1px solid #dee2e6;">
                        {subordinate.oracle_email_address or '-'}
                    </td>
                    <td style="padding: 8px; border: 1px solid #dee2e6; text-align: center;">
                        {"✅" if subordinate_user else "❌"}
                        {f"<br><small>({subordinate_user.username})</small>" if subordinate_user else ""}
                    </td>
                    <td style="padding: 8px; border: 1px solid #dee2e6; font-size: 12px;">
                        {requests_html}
                    </td>
                </tr>
            ''')
        
        html.append('''
                </tbody>
            </table>
        </div>
        ''')
        
        return mark_safe(''.join(html))
    get_subordinates_display.short_description = _('Team Members Details')

@admin.register(Section)
class SectionAdmin(admin.ModelAdmin):
    list_display = ('code', 'name_en', 'name_ar', 'order', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active',)
    search_fields = ('code', 'name_en', 'name_ar', 'description_en', 'description_ar')
    ordering = ('order',)
    readonly_fields = ('created_at', 'updated_at')
    list_per_page = 20

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('code', 'name_en', 'name_ar', 'description_en', 'description_ar', 'icon_svg')
        }),
        (_('Display Settings'), {
            'fields': ('order', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return self.readonly_fields + ('code',)
        return self.readonly_fields

@admin.register(Feature)
class FeatureAdmin(admin.ModelAdmin):
    list_display = ('title_en', 'section', 'order', 'is_active', 'created_at')
    list_filter = ('section', 'is_active')
    search_fields = ('title_en', 'title_ar', 'description_en', 'description_ar')
    ordering = ('section', 'order')
    readonly_fields = ('created_at', 'updated_at')
    list_per_page = 20

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('section', 'title_en', 'title_ar', 'description_en', 'description_ar', 'icon_svg')
        }),
        (_('Display Settings'), {
            'fields': ('order', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Course)
class CourseAdmin(admin.ModelAdmin):
    list_display = ('course_id', 'name_en', 'category', 'location', 'status', 'has_trainers', 'get_trainers', 'get_session_types', 'is_active')
    list_filter = ('category', 'location', 'status', 'is_active', 'session_type', 'trainer__user')
    search_fields = ('name_en', 'name_ar', 'description_en', 'description_ar', 'trainer__user__username', 'trainer__user__first_name', 'trainer__user__last_name')
    
    # Add a button for assigning trainers
    change_form_template = 'admin/website/course/change_form.html'
    
    def has_trainers(self, obj):
        has_trainer = obj.trainer_set.exists()
        icon = '✅' if has_trainer else '❌'
        return mark_safe(f'<span style="font-size: 18px;">{icon}</span>')
    has_trainers.short_description = _('Trainer')
    has_trainers.admin_order_field = 'trainer'
    
    def get_trainers(self, obj):
        trainers = obj.trainer_set.all()
        if not trainers:
            return "No trainer assigned"
        return ", ".join([f"{trainer.user.get_full_name()} ({trainer.user.username})" for trainer in trainers])
    get_trainers.short_description = _('Trainer Details')
    
    def get_session_types(self, obj):
        """Display room types required for each session"""
        if not obj.session_room_types:
            return "-"
            
        # Check if session_room_types is a string (JSON)
        if isinstance(obj.session_room_types, str):
            try:
                session_room_types = json.loads(obj.session_room_types)
            except json.JSONDecodeError:
                return "Invalid data format"
        else:
            session_room_types = obj.session_room_types
            
        # If empty dictionary or not a dict
        if not session_room_types or not isinstance(session_room_types, dict):
            return "-"
        
        try:    
            from .models import RoomType
            room_types = {str(rt.room_type_id): rt.name for rt in RoomType.objects.filter(is_active=True)}
            
            sessions = []
            # Sort session numbers numerically for consistent display
            session_numbers = sorted(session_room_types.keys(), key=lambda x: int(x) if x.isdigit() else float('inf'))
            
            for session_num in session_numbers:
                room_type_id = session_room_types[session_num]
                room_type_name = room_types.get(str(room_type_id), f"Type #{room_type_id}")
                sessions.append(f"S{session_num}: {room_type_name}")
                
            return ", ".join(sessions)
        except Exception as e:
            return f"Error: {str(e)}"
    get_session_types.short_description = _('Session Room Types')
    
    def get_equipment_categories(self, obj):
        if not obj.equipment_categories:
            return "-"
        categories = []
        for cat_id, cat_data in obj.equipment_categories.items():
            categories.append(f"{cat_data.get('name', 'Unknown')} (x{cat_data.get('quantity', 0)})")
        return ", ".join(categories)
    get_equipment_categories.short_description = _('Equipment Categories')
    
    fieldsets = (
        (None, {
            'fields': ('name_en', 'name_ar', 'category', 'location')
        }),
        (_('Course Details'), {
            'fields': ('description_en', 'description_ar', 'session_type',
                      'num_of_sessions', 'capacity', 'prerequisite')
        }),
        (_('Ownership Information'), {
            'fields': ('trainers_display',),
            'description': _('Trainers who own this course'),
            'classes': ('wide',)
        }),
        (_('Session Room Types'), {
            'fields': ('session_room_types_display',),
            'classes': ('wide',)
        }),
        (_('Equipment Requirements'), {
            'fields': ('equipment_categories_display',),
            'description': _('Equipment categories with quantities'),
            'classes': ('wide',)
        }),
        (_('Display Settings'), {
            'fields': ('icon_svg', 'order'),
        }),
        (_('Status'), {
            'fields': ('status', 'is_active', 'cancellation_reason')
        })
    )
    
    readonly_fields = ('equipment_categories_display', 'session_room_types_display', 'trainers_display')
    
    def trainers_display(self, obj):
        """Display the trainers who own this course"""
        trainers = obj.trainer_set.all()
        if not trainers:
            return _("No trainers assigned to this course")
            
        html = ['<table style="width: 100%; border-collapse: collapse;">',
                '<tr style="background-color: #f5f5f5;"><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Trainer</th><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Username</th><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Email</th><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Status</th></tr>']
        
        for trainer in trainers:
            user = trainer.user
            html.append(f'<tr><td style="padding: 8px; border: 1px solid #ddd;">{user.get_full_name()}</td><td style="padding: 8px; border: 1px solid #ddd;">{user.username}</td><td style="padding: 8px; border: 1px solid #ddd;">{user.email}</td><td style="padding: 8px; border: 1px solid #ddd;"><span style="display: inline-block; padding: 3px 8px; border-radius: 3px; background-color: {self._get_status_color(trainer.status)}; color: white;">{trainer.status}</span></td></tr>')
        
        html.append('</table>')
        return mark_safe(''.join(html))
    trainers_display.short_description = _('Course Trainers')
    
    def _get_status_color(self, status):
        """Return color for trainer status badges"""
        status_colors = {
            'READY': '#28a745',    # Green
            'BUSY': '#dc3545',     # Red
            'BOOKED': '#fd7e14'    # Orange
        }
        return status_colors.get(status, '#6c757d')  # Default gray
    
    def session_room_types_display(self, obj):
        """Enhanced display for session room types"""
        if not obj.session_room_types:
            return _("No session room types specified")
            
        # Check if session_room_types is a string (JSON)
        if isinstance(obj.session_room_types, str):
            try:
                session_room_types = json.loads(obj.session_room_types)
            except json.JSONDecodeError:
                return _("Invalid session room types data format")
        else:
            session_room_types = obj.session_room_types
            
        # If empty dictionary
        if not session_room_types:
            return _("No session room types specified")
            
        from .models import RoomType
        all_room_types = RoomType.objects.filter(is_active=True)
        room_types = {str(rt.room_type_id): {'name': rt.name, 'id': rt.room_type_id} for rt in all_room_types}
        
        html = ['<table class="session-room-types-table" style="width: 100%; border-collapse: collapse;">',
                '<tr style="background-color: #f5f5f5;">',
                '<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Session #</th>',
                '<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Room Type</th>',
                '<th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Available Rooms</th>',
                '</tr>']
        
        try:
            # Sort session numbers numerically
            session_numbers = sorted(session_room_types.keys(), key=lambda x: int(x) if x.isdigit() else float('inf'))
            
            for session_num in session_numbers:
                room_type_id = session_room_types[session_num]
                
                # Convert room_type_id to string for comparison
                str_room_type_id = str(room_type_id)
                
                if str_room_type_id in room_types:
                    room_type_data = room_types[str_room_type_id]
                    room_type_name = room_type_data['name']
                    room_type_obj_id = room_type_data['id']
                else:
                    room_type_name = f"Unknown Type #{room_type_id}"
                    room_type_obj_id = None
                
                # Get available rooms count for this room type
                room_count = 0
                if room_type_obj_id:
                    room_count = self._get_room_count_by_type(room_type_obj_id)
                
                # Color based on availability
                availability_color = '#28a745' if room_count > 0 else '#dc3545'  # Green if available, red if not
                
                html.append(f'<tr>')
                html.append(f'<td style="padding: 8px; border: 1px solid #ddd;">Session {session_num}</td>')
                html.append(f'<td style="padding: 8px; border: 1px solid #ddd;">{room_type_name}</td>')
                html.append(f'<td style="padding: 8px; border: 1px solid #ddd;"><span style="color: {availability_color}; font-weight: bold;">{room_count} rooms</span></td>')
                html.append(f'</tr>')
            
            html.append('</table>')
            return mark_safe(''.join(html))
            
        except Exception as e:
            return f"Error displaying session room types: {str(e)}"
    session_room_types_display.short_description = _('Session Room Types')
    
    def _get_room_count_by_type(self, room_type_id):
        """Helper method to get available room count by type"""
        from .models import Room
        return Room.objects.filter(room_type_id=room_type_id, is_active=True).count()
    
    def equipment_categories_display(self, obj):
        """Custom display for the equipment_categories JSON field in the admin detail view"""
        if not obj.equipment_categories:
            return _("No equipment categories added")
            
        html = ['<table class="equipment-categories-table" style="width: 100%; border-collapse: collapse;">',
                '<tr style="background-color: #f5f5f5;"><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Category</th><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Quantity</th><th style="padding: 8px; border: 1px solid #ddd; text-align: left;">Available</th></tr>']
        
        from .models import Category
        categories = {str(cat.category_id): {'name': cat.name, 'stock': cat.stock} 
                     for cat in Category.objects.filter(is_active=True)}
        
        for cat_id, cat_data in obj.equipment_categories.items():
            cat_name = cat_data.get('name', 'Unknown')
            cat_quantity = cat_data.get('quantity', 0)
            
            # Get stock information
            stock = categories.get(str(cat_id), {}).get('stock', 0) if cat_id.isdigit() else 0
            
            # Color based on availability
            availability_color = '#28a745' if stock >= cat_quantity else '#dc3545'  # Green if enough, red if not
            
            html.append(f'<tr>')
            html.append(f'<td style="padding: 8px; border: 1px solid #ddd;">{cat_name}</td>')
            html.append(f'<td style="padding: 8px; border: 1px solid #ddd;">{cat_quantity}</td>')
            html.append(f'<td style="padding: 8px; border: 1px solid #ddd;"><span style="color: {availability_color}; font-weight: bold;">{stock} in stock</span></td>')
            html.append(f'</tr>')
        
        html.append('</table>')
        return mark_safe(''.join(html))
    equipment_categories_display.short_description = _('Equipment Categories')
    
    def has_change_permission(self, request, obj=None):
        """Limit edit permissions for non-superusers"""
        if request.user.is_superuser:
            return True
        # For staff users who are not superusers, restrict editing to view only
        return False
    
    def get_form(self, request, obj=None, **kwargs):
        """
        Use a custom form that properly handles the equipment_categories JSON field
        """
        from django import forms
        from website.models import Category
        
        form = super().get_form(request, obj, **kwargs)
        
        # Only process if the form doesn't already have equipment_categories field
        if 'equipment_categories' not in form.base_fields:
            # Add equipment_categories field to the form
            form.base_fields['equipment_categories'] = forms.CharField(
                widget=forms.HiddenInput(),
                required=False,
                initial="{}" if obj is None or not obj.equipment_categories else json.dumps(obj.equipment_categories)
            )
        
        return form
    
    def save_model(self, request, obj, form, change):
        """
        Handle saving the equipment_categories JSON field
        """
        equipment_categories = form.cleaned_data.get('equipment_categories', '{}')
        try:
            if equipment_categories:
                obj.equipment_categories = json.loads(equipment_categories)
        except json.JSONDecodeError:
            obj.equipment_categories = {}
        
        super().save_model(request, obj, form, change)
    
    class Media:
        js = ('js/course_admin.js',)
        css = {
            'all': ('css/course_admin.css',)
        }
        
    def get_urls(self):
        from django.urls import path
        urls = super().get_urls()
        custom_urls = [
            path('<path:object_id>/assign-trainers/', 
                 self.admin_site.admin_view(self.assign_trainers_view), 
                 name='course-assign-trainers'),
        ]
        return custom_urls + urls
    
    def assign_trainers_view(self, request, object_id):
        """View for assigning trainers to a course"""
        from django.shortcuts import render, redirect, get_object_or_404
        from django.contrib import messages
        
        # Get the course object
        course = get_object_or_404(Course, pk=object_id)
        
        if request.method == 'POST':
            trainer_ids = request.POST.getlist('trainers')
            try:
                from .models import Trainer
                # Clear existing trainer associations
                for trainer in course.trainer_set.all():
                    trainer.courses.remove(course)
                    
                # Add new trainer associations
                for trainer_id in trainer_ids:
                    trainer = Trainer.objects.get(pk=trainer_id)
                    trainer.courses.add(course)
                
                messages.success(request, _('Trainers assigned successfully'))
                return redirect('admin:website_course_change', object_id=object_id)
            except Exception as e:
                messages.error(request, _('Error assigning trainers: {}').format(str(e)))
                
        # Get all trainers
        from .models import Trainer
        trainers = Trainer.objects.all()
        current_trainers = course.trainer_set.all().values_list('trainer_id', flat=True)
        
        context = {
            'title': _('Assign Trainers to Course: {}').format(course.name_en),
            'course': course,
            'trainers': trainers,
            'current_trainers': current_trainers,
            # Include required admin context variables
            'opts': self.model._meta,
            'app_label': self.model._meta.app_label,
            'has_change_permission': self.has_change_permission(request, course),
            'original': course,
        }
        
        return render(request, 'admin/website/course/assign_trainers.html', context)

@admin.register(RoomType)
class RoomTypeAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('name', 'description')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(Room)
class RoomAdmin(admin.ModelAdmin):
    list_display = ('room_id', 'name', 'room_type', 'capacity', 'status', 'get_fixed_equipment', 'is_active')
    list_filter = ('room_type', 'status', 'is_active')
    search_fields = ('name', 'description', 'room_type__name', 'fixed_equipment__code', 'fixed_equipment__name')
    ordering = ('name',)
    filter_horizontal = ('fixed_equipment',)
    readonly_fields = ('status',)
    
    def get_fixed_equipment(self, obj):
        return ", ".join([f"{eq.code} ({eq.name})" for eq in obj.fixed_equipment.all()])
    get_fixed_equipment.short_description = _('Fixed Equipment')
    
    fieldsets = (
        (None, {
            'fields': ('name', 'room_type', 'capacity', 'description')
        }),
        (_('Equipment'), {
            'fields': ('fixed_equipment',),
            'description': _('Equipment that is permanently assigned to this room')
        }),
        (_('Status Information'), {
            'fields': ('status', 'is_active'),
            'description': _('Room status is automatically managed based on availability')
        })
    )

    def save_model(self, request, obj, form, change):
        is_new = not obj.pk
        super().save_model(request, obj, form, change)
        if is_new:
            # Create availability record for new rooms
            RoomAvailability.objects.get_or_create(room=obj)

@admin.register(Session)
class SessionAdmin(admin.ModelAdmin):
    list_display = (
        'session_id',
        'course',
        'get_trainers',
        'room',
        'start_date',
        'end_date',
        'location',
        'is_active',
        'created_at',
        'updated_at'
    )
    
    list_filter = (
        'location',
        'is_active',
        'course',
        'trainers',
        'room'
    )
    
    search_fields = (
        'session_id',
        'course__name_en',
        'course__name_ar',
        'trainers__user__username',
        'trainers__user__first_name',
        'trainers__user__last_name',
        'room__name'
    )
    
    readonly_fields = (
        'session_id',
        'created_at',
        'updated_at'
    )
    
    fieldsets = (
        ('Basic Information', {
            'fields': ('session_id', 'course', 'trainers', 'room', 'required_equipment')
        }),
        ('Schedule', {
            'fields': ('start_date', 'end_date', 'location', 'slot_type')
        }),
        ('Status', {
            'fields': ('is_active',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    filter_horizontal = ('required_equipment', 'trainers')
    
    ordering = ('-created_at',)
    date_hierarchy = 'start_date'
    
    def get_trainers(self, obj):
        return ", ".join([str(trainer) for trainer in obj.trainers.all()])
    get_trainers.short_description = _('Trainers')
    
    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return self.readonly_fields
        return ('created_at', 'updated_at')  # Allow session_id to be empty for new objects

@admin.register(Trainer)
class TrainerAdmin(admin.ModelAdmin):
    list_display = ('trainer_id', 'get_trainer_name', 'status', 'is_supervisor', 'get_courses', 'get_availability')
    list_filter = ('status', 'courses', 'is_supervisor')
    search_fields = ('user__username', 'user__email', 'user__first_name', 'user__last_name')
    filter_horizontal = ('courses',)
    
    def get_trainer_name(self, obj):
        return f"{obj.user.get_full_name()} ({obj.user.username})"
    get_trainer_name.short_description = _('Trainer')
    
    def get_courses(self, obj):
        return ", ".join([course.name_en for course in obj.courses.all()])
    get_courses.short_description = _('Courses')
    
    def get_availability(self, obj):
        current_slots = obj.availability_slots.filter(
            end_date__gt=timezone.now()
        ).order_by('start_date')[:3]
        
        if not current_slots:
            return _('No upcoming bookings')
            
        slots = []
        for slot in current_slots:
            slot_type = slot.get_booking_type_display() if slot.booking_type else 'Unknown'
            slot_info = f"{slot_type}: {slot.start_date.strftime('%Y-%m-%d %H:%M')} - {slot.end_date.strftime('%H:%M')}"
            slots.append(slot_info)
            
        return "\n".join(slots)
    get_availability.short_description = _('Current Availability')

    fieldsets = (
        (_('Basic Information'), {
            'fields': ('user', 'status', 'is_supervisor')
        }),
        (_('Courses'), {
            'fields': ('courses',)
        })
    )

@admin.register(Corporate)
class CorporateModelAdmin(admin.ModelAdmin):
    list_display = ('corporate_id', 'legal_name', 'category')
    search_fields = ('legal_name', 'commercial_registration_number')

@admin.register(CorporateAdmin)
class CorporateAdminAdmin(admin.ModelAdmin):
    list_display = ('admin_id', 'get_admin_name', 'get_corporate_name', 'position', 'department', 
                   'direct_phone', 'get_email', 'is_active', 'created_at')
    list_filter = ('is_active', 'corporate__category', 'department', 'created_at')
    search_fields = ('user__username', 'user__email', 'user__first_name', 'user__last_name', 
                     'corporate__legal_name', 'position', 'department', 'direct_phone')
    raw_id_fields = ('user', 'corporate')
    readonly_fields = ('created_at', 'updated_at')
    
    def get_admin_name(self, obj):
        return obj.user.get_full_name()
    get_admin_name.short_description = _('Admin Name')
    get_admin_name.admin_order_field = 'user__last_name'
    
    def get_corporate_name(self, obj):
        return obj.corporate.legal_name
    get_corporate_name.short_description = _('Corporate')
    get_corporate_name.admin_order_field = 'corporate__legal_name'
    
    def get_email(self, obj):
        return obj.user.email
    get_email.short_description = _('Email')
    get_email.admin_order_field = 'user__email'
    
    fieldsets = (
        (_('Admin Information'), {
            'fields': ('user', 'corporate', 'position', 'department')
        }),
        (_('Contact Details'), {
            'fields': ('direct_phone', 'alternative_email')
        }),
        (_('Additional Information'), {
            'fields': ('notes', 'is_active')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Attendance)
class AttendanceAdmin(admin.ModelAdmin):
    list_display = ('attendance_id', 'user', 'session', 'first_half', 'second_half')
    list_filter = ('first_half', 'second_half')

@admin.register(ContactInfo)
class ContactInfoAdmin(admin.ModelAdmin):
    list_display = ('info_type', 'value_en', 'is_active', 'order')
    list_filter = ('info_type', 'is_active')
    ordering = ('order',)

@admin.register(Slide)
class SlideAdmin(admin.ModelAdmin):
    list_display = ('title_en', 'is_active', 'order')
    list_filter = ('is_active',)
    ordering = ('order',)

@admin.register(EventType)
class EventTypeAdmin(admin.ModelAdmin):
    list_display = ('event_type_id', 'type')
    search_fields = ('type',)
    ordering = ('type',)

@admin.register(Category)
class CategoryAdmin(admin.ModelAdmin):
    list_display = ('category_id', 'name', 'stock', 'is_active', 'created_at', 'updated_at')
    list_display_links = ('category_id', 'name')
    search_fields = ('name',)
    list_filter = ('is_active',)
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name',)
        }),
        (_('Stock Information'), {
            'fields': ('stock',)
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )

@admin.register(Brand)
class BrandAdmin(admin.ModelAdmin):
    list_display = ('brand_id', 'name', 'is_active', 'created_at', 'updated_at', 'model_count')
    list_display_links = ('brand_id', 'name')
    search_fields = ('name',)
    list_filter = ('is_active',)
    ordering = ('name',)
    readonly_fields = ('created_at', 'updated_at', 'model_count')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name',)
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Related Information'), {
            'fields': ('model_count',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def model_count(self, obj):
        return obj.models.count()
    model_count.short_description = _('Number of Models')

@admin.register(Model)
class ModelAdmin(admin.ModelAdmin):
    list_display = ('model_id', 'name', 'brand', 'stock', 'is_active', 'created_at', 'updated_at', 'equipment_count')
    list_display_links = ('model_id', 'name')
    search_fields = ('name', 'brand__name')
    list_filter = ('brand', 'is_active')
    ordering = ('brand__name', 'name')
    autocomplete_fields = ['brand']
    readonly_fields = ('created_at', 'updated_at', 'equipment_count')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('name', 'brand')
        }),
        (_('Stock Information'), {
            'fields': ('stock',)
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Related Information'), {
            'fields': ('equipment_count',),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def equipment_count(self, obj):
        return Equipment.objects.filter(model=obj).count()
    equipment_count.short_description = _('Number of Equipment')

@admin.register(Equipment)
class EquipmentAdmin(admin.ModelAdmin):
    list_display = ('code', 'name', 'category', 'brand', 'model', 'status', 'is_active')
    list_filter = ('category', 'status', 'brand', 'is_active')
    search_fields = ('code', 'name', 'brand__name', 'model__name')
    ordering = ('code',)
    readonly_fields = ('created_at', 'updated_at')
    autocomplete_fields = ['category', 'brand', 'model']
    fieldsets = (
        (None, {
            'fields': ('code', 'name', 'category', 'brand', 'model')
        }),
        (_('Status Information'), {
            'fields': ('status', 'is_active')
        }),
        (_('Additional Information'), {
            'fields': ('description',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(EquipmentMaintenance)
class EquipmentMaintenanceAdmin(admin.ModelAdmin):
    list_display = ('equipment', 'maintenance_date', 'performed_by', 'next_maintenance_date')
    list_filter = ('maintenance_date', 'next_maintenance_date')
    search_fields = ('equipment__code', 'equipment__name', 'description', 'performed_by')
    ordering = ('-maintenance_date',)
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('equipment', 'maintenance_date', 'performed_by')
        }),
        (_('Maintenance Details'), {
            'fields': ('description', 'cost', 'next_maintenance_date')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(EquipmentAvailability)
class EquipmentAvailabilityAdmin(admin.ModelAdmin):
    list_display = ('equipment', 'booking_type', 'get_booking_info', 'start_date', 'end_date')
    list_filter = ('booking_type', 'equipment__status')
    search_fields = ('equipment__code', 'equipment__name', 'booking_id')
    raw_id_fields = ('equipment', 'session', 'event')
    
    fieldsets = (
        (None, {
            'fields': ('equipment',)
        }),
        (_('Booking Details'), {
            'fields': ('booking_type', 'booking_id', 'start_date', 'end_date')
        }),
        (_('Related Records'), {
            'fields': ('session', 'event'),
            'description': _('Associated session or event for this availability record')
        })
    )

    def get_booking_info(self, obj):
        if obj.session:
            return f"Session: {obj.session.session_id}"
        elif obj.event:
            return f"Event: {obj.event.name}"
        elif obj.booking_id:
            return f"Booking: {obj.booking_id}"
        return "No booking"
    get_booking_info.short_description = _('Booking Information')

    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return ('equipment', 'session', 'event', 'booking_id', 'booking_type')
        return ()

@admin.register(TrainerAvailability)
class TrainerAvailabilityAdmin(admin.ModelAdmin):
    list_display = ('trainer', 'booking_type', 'start_date', 'end_date', 'get_booking_info')
    list_filter = ('booking_type', 'trainer')
    search_fields = (
        'trainer__user__username',
        'trainer__user__first_name',
        'trainer__user__last_name',
        'booking_id'
    )
    raw_id_fields = ('trainer', 'session', 'event')
    date_hierarchy = 'start_date'
    
    def get_booking_info(self, obj):
        if obj.session:
            return f"Session: {obj.session.session_id}"
        elif obj.event:
            return f"Event: {obj.event.name}"
        return f"Booking ID: {obj.booking_id}" if obj.booking_id else "-"
    get_booking_info.short_description = _('Booking Information')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('trainer', 'booking_type')
        }),
        (_('Schedule'), {
            'fields': ('start_date', 'end_date')
        }),
        (_('Booking Details'), {
            'fields': ('booking_id', 'session', 'event')
        })
    )
    
    def get_readonly_fields(self, request, obj=None):
        if obj:  # Editing an existing object
            return ('booking_id', 'session', 'event')
        return ()

@admin.register(RoomAvailability)
class RoomAvailabilityAdmin(admin.ModelAdmin):
    list_display = ('room', 'get_booking_info', 'start_date', 'end_date', 'booking_type')
    list_filter = ('booking_type', 'room__room_type', 'room')
    search_fields = ('room__name', 'booking_id')
    ordering = ('-start_date',)
    readonly_fields = ('room', 'session', 'event', 'booking_id', 'booking_type', 'start_date', 'end_date')
    
    def get_booking_info(self, obj):
        if obj.session:
            return f"Session: {obj.session.session_id}"
        elif obj.event:
            return f"Event: {obj.event.name}"
        elif obj.booking_id:
            return f"Booking: {obj.booking_id}"
        return "No booking"
    get_booking_info.short_description = _('Booking Information')
    
    fieldsets = (
        (_('Room Information'), {
            'fields': ('room',)
        }),
        (_('Booking Details'), {
            'fields': ('booking_type', 'booking_id', 'start_date', 'end_date')
        }),
        (_('Related Records'), {
            'fields': ('session', 'event'),
            'description': _('Associated session or event for this availability record')
        })
    )
    
    def has_add_permission(self, request):
        return False  # Prevent manual creation as these are managed automatically
    
    def has_delete_permission(self, request, obj=None):
        return True  # Allow deletion

@admin.register(CourseInstance)
class CourseInstanceAdmin(admin.ModelAdmin):
    list_display = ('instance_id', 'get_course_name', 'course_type', 'get_corporate_name', 'start_date', 'end_date', 'capacity', 'get_available_seats', 'get_waiting_list_count', 'get_published_status', 'is_active')
    list_filter = ('is_active', 'published', 'course__category', 'start_date', 'course_type')
    search_fields = ('course__name_en', 'batch_id', 'corporate__legal_name')
    ordering = ('-start_date',)
    date_hierarchy = 'start_date'
    raw_id_fields = ('course', 'corporate')
    filter_horizontal = ('sessions',)
    readonly_fields = ('created_at', 'updated_at', 'get_available_seats', 'get_waiting_list_count', 'get_waiting_list_users')
    
    def get_course_name(self, obj):
        return obj.course.name_en if obj.course else '-'
    get_course_name.short_description = _('Course')
    get_course_name.admin_order_field = 'course__name_en'
    
    def get_corporate_name(self, obj):
        if obj.corporate:
            return obj.corporate.legal_name
        return '-'
    get_corporate_name.short_description = _('Corporate')
    get_corporate_name.admin_order_field = 'corporate__legal_name'
    
    def get_available_seats(self, obj):
        return obj.available_seats
    get_available_seats.short_description = _('Available Seats')
    
    def get_waiting_list_count(self, obj):
        count = obj.waiting_list_count
        if count > 0:
            return mark_safe(f'<span style="color: #e74c3c; font-weight: bold;">{count}</span>')
        return count
    get_waiting_list_count.short_description = _('Waiting List')
    
    def get_published_status(self, obj):
        if obj.published:
            return mark_safe('<span style="color: #28a745; font-weight: bold;">✅ Published</span>')
        else:
            return mark_safe('<span style="color: #dc3545; font-weight: bold;">❌ Draft</span>')
    get_published_status.short_description = _('Status')
    get_published_status.admin_order_field = 'published'
    
    def get_waiting_list_users(self, obj):
        waiting_list = obj.get_waiting_list()
        if not waiting_list.exists():
            return _('No users on waiting list')
        
        html = '<table style="width: 100%;"><tr><th>User</th><th>Added On</th></tr>'
        for reservation in waiting_list:
            user_info = f"{reservation.user.get_full_name()} ({reservation.user.email})"
            date_info = reservation.created_at.strftime('%Y-%m-%d %H:%M')
            html += f'<tr><td>{user_info}</td><td>{date_info}</td></tr>'
        html += '</table>'
        return mark_safe(html)
    get_waiting_list_users.short_description = _('Waiting List Users')
    
    fieldsets = (
        (None, {
            'fields': ('course', 'course_type', 'corporate', 'batch_id', 'start_date', 'end_date', 'capacity')
        }),
        (_('Status & Visibility'), {
            'fields': ('published', 'is_active'),
            'description': _('Published courses are visible for enrollment. Unpublished courses still reserve time slots.')
        }),
        (_('Waiting List Information'), {
            'fields': ('get_available_seats', 'get_waiting_list_count', 'get_waiting_list_users'),
            'description': _('Information about course capacity and waiting list')
        }),
        (_('Session Information'), {
            'fields': ('sessions',),
            'description': _('Sessions associated with this course instance')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    def has_add_permission(self, request):
        return True
    
    # Custom action to process waiting list
    actions = ['process_waiting_lists']
    
    def process_waiting_lists(self, request, queryset):
        promoted_count = 0
        for instance in queryset:
            if instance.process_waiting_list():
                promoted_count += 1
        
        if promoted_count:
            self.message_user(request, f"{promoted_count} users promoted from waiting lists.")
        else:
            self.message_user(request, "No users were promoted from waiting lists.")
    process_waiting_lists.short_description = _("Process waiting lists for selected instances")

# Register remaining models with default admin interface
admin.site.register(Role)
admin.site.register(Form)
admin.site.register(FormContent)
admin.site.register(Event)
# Replace simple registration with custom admin interface
@admin.register(Reservation)
class ReservationAdmin(admin.ModelAdmin):
    list_display = ('reservation_id', 'user', 'get_course_name', 'get_instance_dates', 'status', 'created_at')
    list_filter = ('status', 'created_at', 'course_instance__course__category')
    search_fields = ('user__username', 'user__email', 'user__first_name', 'user__last_name', 'course_instance__course__name_en')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    raw_id_fields = ('user', 'course_instance')
    readonly_fields = ('created_at',)
    
    def get_course_name(self, obj):
        if obj.course_instance and obj.course_instance.course:
            return obj.course_instance.course.name_en
        elif obj.session and obj.session.course:
            return obj.session.course.name_en
        return '-'
    get_course_name.short_description = _('Course')
    get_course_name.admin_order_field = 'course_instance__course__name_en'
    
    def get_instance_dates(self, obj):
        if obj.course_instance:
            return f"{obj.course_instance.start_date.strftime('%Y-%m-%d')} to {obj.course_instance.end_date.strftime('%Y-%m-%d')}"
        return '-'
    get_instance_dates.short_description = _('Course Dates')
    
    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.select_related('user', 'course_instance', 'course_instance__course', 'session', 'session__course')
    
    def has_add_permission(self, request):
        return False  # Prevent manual creation as these are typically created by users enrolling
    
    fieldsets = (
        (None, {
            'fields': ('user', 'course_instance', 'status')
        }),
        (_('Additional Information'), {
            'fields': ('session', 'event'),
            'classes': ('collapse',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    
    # Custom actions
    actions = ['mark_as_waiting', 'mark_as_upcoming', 'mark_as_completed', 'mark_as_cancelled']
    
    def mark_as_waiting(self, request, queryset):
        updated = queryset.update(status='WAITING_LIST')
        self.message_user(request, f"{updated} reservations marked as waiting list entries.")
    mark_as_waiting.short_description = _("Mark selected reservations as Waiting List")
    
    def mark_as_upcoming(self, request, queryset):
        updated = queryset.update(status='UPCOMING')
        self.message_user(request, f"{updated} reservations marked as upcoming.")
    mark_as_upcoming.short_description = _("Mark selected reservations as Upcoming")
    
    def mark_as_completed(self, request, queryset):
        updated = queryset.update(status='COMPLETED')
        self.message_user(request, f"{updated} reservations marked as completed.")
    mark_as_completed.short_description = _("Mark selected reservations as Completed")
    
    def mark_as_cancelled(self, request, queryset):
        from django.db import transaction
        from .models import Notification
        logger = logging.getLogger(__name__)
        
        with transaction.atomic():
            # Group reservations by course instance for efficient processing
            course_instances_to_process = {}
            for reservation in queryset:
                if reservation.course_instance and reservation.status != 'CANCELLED':
                    # Track original status for logging
                    original_status = reservation.status
                    
                    # Update status to cancelled
                    reservation.status = 'CANCELLED'
                    reservation.save(update_fields=['status'])
                    
                    # Add course instance to processing list
                    if reservation.course_instance not in course_instances_to_process:
                        course_instances_to_process[reservation.course_instance] = []
                    course_instances_to_process[reservation.course_instance].append(original_status)
                    
                    logger.info(f"Admin cancelled reservation {reservation.reservation_id} (was {original_status})")
            
            # Process waiting lists for affected course instances
            promotions = 0
            for course_instance, original_statuses in course_instances_to_process.items():
                # Check if this course has a waiting list
                if course_instance.has_waiting_list:
                    # For each cancelled reservation in this course instance, try to promote someone
                    for _ in original_statuses:
                        # Get the first person on the waiting list
                        next_reservation = course_instance.get_waiting_list().first()
                        if next_reservation:
                            # Update waiting list user to UPCOMING status
                            next_reservation.status = 'UPCOMING'
                            next_reservation.save(update_fields=['status'])
                            
                            # Create a notification for the promoted user
                            Notification.objects.create(
                                user=next_reservation.user,
                                message=_('A spot has become available in the course: {} ({}). You have been automatically enrolled from the waiting list.').format(
                                    course_instance.course.name_en,
                                    course_instance.start_date.strftime('%Y-%m-%d')
                                ),
                                delivered_at=timezone.now()
                            )
                            
                            promotions += 1
                            logger.info(f"Admin action: User {next_reservation.user.username} promoted from waiting list to UPCOMING for course instance {course_instance.instance_id}")
        
        # Provide feedback to the admin
        updated = queryset.count()
        message = f"{updated} reservations marked as cancelled."
        if promotions:
            message += f" {promotions} users were promoted from waiting lists."
        self.message_user(request, message)
    mark_as_cancelled.short_description = _("Mark selected reservations as Cancelled")
# admin.site.register(Questionnaire)
admin.site.register(Notification)
admin.site.register(Payment)

@admin.register(EmergencyCancellationRequest)
class EmergencyCancellationRequestAdmin(admin.ModelAdmin):
    list_display = ('request_id', 'reservation', 'status', 'created_at', 'processed_by')
    list_filter = ('status', 'created_at')
    search_fields = ('reservation__user__username', 'reservation__user__email', 'reason')
    readonly_fields = ('request_id', 'created_at', 'updated_at')
    fieldsets = (
        (_('Request Details'), {
            'fields': ('request_id', 'reservation', 'reason', 'attachment', 'status')
        }),
        (_('Administration'), {
            'fields': ('admin_notes', 'processed_by', 'created_at', 'updated_at')
        }),
    )
    actions = ['approve_requests', 'reject_requests']

    def approve_requests(self, request, queryset):
        queryset.update(status='APPROVED', processed_by=request.user)
        for cancellation_request in queryset:
            if cancellation_request.reservation.status != 'CANCELLED':
                cancellation_request.reservation.status = 'CANCELLED'
                cancellation_request.reservation.save()
        self.message_user(request, _('Selected requests have been approved and reservations cancelled.'))
    approve_requests.short_description = _('Approve selected requests')

    def reject_requests(self, request, queryset):
        queryset.update(status='REJECTED', processed_by=request.user)
        self.message_user(request, _('Selected requests have been rejected.'))
    reject_requests.short_description = _('Reject selected requests')

@admin.register(CorporateUserCancellationRequest)
class CorporateUserCancellationRequestAdmin(admin.ModelAdmin):
    list_display = ('request_id', 'reservation', 'status', 'created_at', 'processed_by')
    list_filter = ('status', 'created_at')
    search_fields = ('reservation__user__username', 'reservation__user__email', 'reason', 'admin_notes')
    readonly_fields = ('request_id', 'created_at', 'updated_at')
    fieldsets = (
        (_('Request Details'), {
            'fields': ('request_id', 'reservation', 'reason', 'status')
        }),
        (_('Administration'), {
            'fields': ('admin_notes', 'processed_by', 'created_at', 'updated_at')
        }),
    )
    actions = ['approve_requests', 'reject_requests']

    def approve_requests(self, request, queryset):
        with transaction.atomic():
            queryset.update(status='APPROVED', processed_by=request.user)
            for cancellation_request in queryset:
                if cancellation_request.reservation.status != 'CANCELLED':
                    # Update reservation
                    reservation = cancellation_request.reservation
                    reservation.status = 'CANCELLED'
                    reservation.cancellation_reason = f"Corporate cancellation request approved by: {request.user.get_full_name() or request.user.email}"
                    reservation.cancelled_at = timezone.now()
                    reservation.cancelled_by = request.user
                    reservation.save()
                    
                    # Process waiting list if needed
                    try:
                        reservation.course_instance.process_waiting_list()
                    except Exception as e:
                        # Log but continue
                        logger = logging.getLogger(__name__)
                        logger.error(f"Error processing waiting list: {str(e)}")
                        
        self.message_user(request, _('Selected requests have been approved and reservations cancelled.'))
    approve_requests.short_description = _('Approve selected requests')

    def reject_requests(self, request, queryset):
        queryset.update(status='REJECTED', processed_by=request.user)
        self.message_user(request, _('Selected requests have been rejected.'))
    reject_requests.short_description = _('Reject selected requests')

@admin.register(Holiday)
class HolidayAdmin(admin.ModelAdmin):
    list_display = ('name', 'date', 'description', 'is_active', 'created_at', 'updated_at')
    list_filter = ('is_active', 'date')
    search_fields = ('name', 'description')
    ordering = ('-date',)
    readonly_fields = ('created_at', 'updated_at')
    
    fieldsets = (
        (_('Holiday Information'), {
            'fields': ('name', 'date', 'description')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def has_module_permission(self, request):
        """Only show this in admin to staff users"""
        return request.user.is_authenticated and request.user.is_staff

@admin.register(CourseContent)
class CourseContentAdmin(admin.ModelAdmin):
    list_display = ('title', 'course', 'get_content_type_display', 'created_by', 'is_active', 'created_at')
    list_filter = ('content_type', 'is_active', 'course')
    search_fields = ('title', 'description', 'course__name_en', 'created_by__username')
    readonly_fields = ('created_at', 'updated_at')
    
    def get_content_type_display(self, obj):
        return obj.get_content_type_display()
    get_content_type_display.short_description = _('Content Type')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('course', 'title', 'description', 'content_type', 'file')
        }),
        (_('Status'), {
            'fields': ('is_active',)
        }),
        (_('Creator'), {
            'fields': ('created_by',)
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )

@admin.register(CorporateAdminRequest)
class CorporateAdminRequestAdmin(admin.ModelAdmin):
    list_display = ('request_id', 'corporate', 'corporate_admin', 'course', 'capacity', 'get_course_type_display', 'status')
    list_filter = ('status', 'course_type')
    search_fields = ('corporate__legal_name', 'corporate_admin__user__email', 'corporate_admin__user__first_name', 'corporate_admin__user__last_name', 'notes')
    readonly_fields = ('request_id', 'created_at')
    
    def get_course_type_display(self, obj):
        return obj.get_course_type_display()
    get_course_type_display.short_description = _('Course Type')
    
    fieldsets = (
        (_('Request Details'), {
            'fields': ('request_id', 'corporate', 'corporate_admin', 'course', 'capacity', 'course_type', 'notes')
        }),
        (_('Status Information'), {
            'fields': ('status', 'admin_notes')
        }),
        (_('Timestamps'), {
            'fields': ('created_at',),
            'classes': ('collapse',)
        })
    )
    
    actions = ['approve_requests', 'reject_requests', 'approve_cancellation_requests', 'reject_cancellation_requests']
    
    def approve_requests(self, request, queryset):
        updated = queryset.filter(status='PENDING').update(status='APPROVED')
        self.message_user(request, _(f"{updated} course requests have been approved."))
    approve_requests.short_description = _("Approve selected course requests")
    
    def reject_requests(self, request, queryset):
        updated = queryset.filter(status='PENDING').update(status='REJECTED')
        self.message_user(request, _(f"{updated} course requests have been rejected."))
    reject_requests.short_description = _("Reject selected course requests")
    
    def approve_cancellation_requests(self, request, queryset):
        from django.utils import timezone
        
        # Only process PENDING_CANCEL requests
        pending_cancel_requests = queryset.filter(status='PENDING_CANCEL')
        cancel_count = 0
        reservation_count = 0
        
        for req in pending_cancel_requests:
            # Mark request as cancelled
            req.status = 'CANCELLED'
            req.admin_notes = (req.admin_notes or '') + '\n\n' + _(f'Cancellation approved by system admin {request.user.get_full_name()} on {timezone.now().strftime("%Y-%m-%d %H:%M")}')
            req.save()
            cancel_count += 1
            
            # Cancel associated reservations
            if req.course:
                # Find all course instances related to this course request
                from website.models import CourseInstance, Reservation
                course_instances = CourseInstance.objects.filter(
                    course=req.course,
                    corporate=req.corporate
                )
                
                # Update all reservations for these instances
                for instance in course_instances:
                    reservations = Reservation.objects.filter(
                        course_instance=instance,
                        status__in=['CONFIRMED', 'UPCOMING', 'IN_PROGRESS']
                    )
                    for reservation in reservations:
                        reservation.status = 'CANCELLED'
                        reservation.cancellation_reason = _('Course cancelled by corporate admin and approved by system admin')
                        reservation.cancelled_at = timezone.now()
                        reservation.cancelled_by = request.user
                        reservation.save()
                        reservation_count += 1
        
        message = _(f"{cancel_count} cancellation requests approved. {reservation_count} reservations were automatically cancelled.")
        self.message_user(request, message)
    approve_cancellation_requests.short_description = _("Approve selected cancellation requests (will cancel reservations)")
    
    def reject_cancellation_requests(self, request, queryset):
        from django.utils import timezone
        
        updated = queryset.filter(status='PENDING_CANCEL').update(
            status='APPROVED',
            admin_notes=models.F('admin_notes') + '\n\n' + _(f'Cancellation rejected by system admin {request.user.get_full_name()} on {timezone.now().strftime("%Y-%m-%d %H:%M")}')
        )
        self.message_user(request, _(f"{updated} cancellation requests have been rejected."))
    reject_cancellation_requests.short_description = _("Reject selected cancellation requests")

@admin.register(Survey)
class SurveyAdmin(admin.ModelAdmin):
    list_display = ('survey_id', 'title', 'get_course_instance', 'is_active', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('title', 'course_instance__course__name_en')
    date_hierarchy = 'created_at'
    
    def get_course_instance(self, obj):
        return f"{obj.course_instance.course.name_en} ({obj.course_instance.start_date.strftime('%Y-%m-%d')})"
    get_course_instance.short_description = _('Course Instance')

@admin.register(SurveyResponse)
class SurveyResponseAdmin(admin.ModelAdmin):
    list_display = ('response_id', 'get_survey_title', 'get_user', 'get_course_name', 'status', 'created_at', 'completed_at')
    list_filter = ('status', 'created_at', 'completed_at')
    search_fields = ('survey__title', 'user__username', 'user__email', 'comment')
    readonly_fields = ('created_at', 'completed_at')
    
    def get_survey_title(self, obj):
        return obj.survey.title
    get_survey_title.short_description = _('Survey')
    
    def get_user(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name} ({obj.user.email})"
    get_user.short_description = _('User')
    
    def get_course_name(self, obj):
        return obj.reservation.course_instance.course.name_en
    get_course_name.short_description = _('Course')
    get_course_name.admin_order_field = 'course_instance__course__name_en'

class QuestionnaireAdmin(admin.ModelAdmin):
    list_display = ('questionnaire_id', 'title', 'course', 'session_number', 'total_score', 'is_active', 'created_at', 'created_by')
    list_filter = ('is_active', 'created_at', 'course', 'created_by')
    search_fields = ('title', 'course__name_en', 'comments')
    date_hierarchy = 'created_at'
    readonly_fields = ('created_at', 'updated_at', 'total_score')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('title', 'course', 'session_number', 'comments', 'is_active')
        }),
        (_('Questions & Scoring'), {
            'fields': ('questions', 'total_score'),
            'description': _('Questions in JSON format. Total score is auto-calculated.')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at', 'created_by'),
            'classes': ('collapse',)
        })
    )

class QuestionnaireResponseAdmin(admin.ModelAdmin):
    list_display = ('response_id', 'get_questionnaire_title', 'get_user', 'get_course_name', 'status', 'total_score', 'max_total_score', 'created_at', 'completed_at')
    list_filter = ('status', 'created_at', 'completed_at', 'course', 'course_instance', 'session')
    search_fields = ('questionnaire__title', 'user__username', 'user__email', 'comment')
    readonly_fields = ('created_at', 'completed_at', 'max_total_score', 'total_score')
    
    fieldsets = (
        (_('Basic Information'), {
            'fields': ('questionnaire', 'user', 'course', 'session_number', 'status', 'course_instance', 'session')
        }),
        (_('Response Data & Scoring'), {
            'fields': ('question_responses', 'comment', 'max_total_score', 'total_score')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'completed_at'),
            'classes': ('collapse',)
        })
    )
    
    def get_questionnaire_title(self, obj):
        return obj.questionnaire.title
    get_questionnaire_title.short_description = _('Questionnaire')
    get_questionnaire_title.admin_order_field = 'questionnaire__title'
    
    def get_user(self, obj):
        return f"{obj.user.get_full_name()} ({obj.user.email})"
    get_user.short_description = _('User')
    get_user.admin_order_field = 'user__last_name'
    
    def get_course_name(self, obj):
        return obj.course.name_en
    get_course_name.short_description = _('Course')
    get_course_name.admin_order_field = 'course__name_en'

# Register Questionnaire and QuestionnaireResponse models
admin.site.register(Questionnaire, QuestionnaireAdmin)
admin.site.register(QuestionnaireResponse, QuestionnaireResponseAdmin)
# admin.site.register(QuestionnaireResponse, QuestionnaireResponseAdmin)

@admin.register(CorporateAdminNewCourseRequest)
class CorporateAdminNewCourseRequestAdmin(admin.ModelAdmin):
    list_display = ('request_id', 'corporate', 'corporate_admin', 'course_name', 'capacity', 'get_course_type_display', 'status')
    list_filter = ('status', 'course_type', 'created_at')
    search_fields = ('corporate__legal_name', 'corporate_admin__user__email', 'corporate_admin__user__first_name', 'corporate_admin__user__last_name', 'course_name', 'course_description', 'notes')
    readonly_fields = ('request_id', 'created_at', 'updated_at')
    
    def get_course_type_display(self, obj):
        return obj.get_course_type_display()
    get_course_type_display.short_description = _('Course Type')
    
    fieldsets = (
        (_('Request Details'), {
            'fields': ('request_id', 'corporate', 'corporate_admin', 'course_name', 'course_description', 'capacity', 'course_type', 'notes')
        }),
        (_('Status Information'), {
            'fields': ('status', 'admin_notes')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        })
    )
    
    actions = ['approve_requests', 'reject_requests']
    
    def approve_requests(self, request, queryset):
        updated = queryset.filter(status='PENDING').update(status='APPROVED')
        self.message_user(request, _(f"{updated} new course requests have been approved."))
    approve_requests.short_description = _("Approve selected new course requests")
    
    def reject_requests(self, request, queryset):
        updated = queryset.filter(status='PENDING').update(status='REJECTED')
        self.message_user(request, _(f"{updated} new course requests have been rejected."))
    reject_requests.short_description = _("Reject selected new course requests")

# Admin site configuration
admin.site.site_header = _('Learning Management System Administration')
admin.site.site_title = _('LMS Admin Portal')
admin.site.index_title = _('Administration Panel')

@admin.register(EmailLog)
class EmailLogAdmin(admin.ModelAdmin):
    list_display = ('recipient', 'subject', 'status', 'sent_at', 'transaction_id')
    list_filter = ('status', 'sent_at')
    search_fields = ('recipient', 'subject', 'transaction_id', 'error_message')
    ordering = ('-sent_at',)
    readonly_fields = ('sent_at', 'transaction_id')
    
    def has_add_permission(self, request):
        # Email logs should only be created by the system
        return False
    
    def has_change_permission(self, request, obj=None):
        # Email logs should not be editable
        return False

@admin.register(OTPVerification)
class OTPVerificationAdmin(admin.ModelAdmin):
    list_display = ('otp_id', 'employee_id', 'email', 'get_status_display', 'attempts', 'max_attempts', 'created_at', 'expires_at', 'used_at')
    list_filter = ('is_used', 'is_expired', 'created_at', 'expires_at')
    search_fields = ('employee_id', 'email', 'otp_code')
    ordering = ('-created_at',)
    readonly_fields = ('otp_id', 'otp_code', 'created_at', 'expires_at', 'used_at', 'get_status_display', 'is_expired_by_time')
    
    fieldsets = (
        (_('OTP Information'), {
            'fields': ('otp_id', 'employee_id', 'email', 'otp_code')
        }),
        (_('Verification Status'), {
            'fields': ('get_status_display', 'is_used', 'is_expired', 'is_expired_by_time')
        }),
        (_('Attempt Information'), {
            'fields': ('attempts', 'max_attempts')
        }),
        (_('Timestamps'), {
            'fields': ('created_at', 'expires_at', 'used_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_status_display(self, obj):
        return obj.get_status_display()
    get_status_display.short_description = _('Status')
    
    def is_expired_by_time(self, obj):
        return obj.is_expired_by_time()
    is_expired_by_time.short_description = _('Expired by Time')
    is_expired_by_time.boolean = True
    
    def has_add_permission(self, request):
        # OTP records should only be created by the system
        return False
    
    def has_change_permission(self, request, obj=None):
        # OTP records should be read-only
        return False
    
    def has_delete_permission(self, request, obj=None):
        # Allow deletion for cleanup purposes
        return request.user.is_superuser
    
    actions = ['cleanup_expired_otps']
    
    def cleanup_expired_otps(self, request, queryset):
        """Action to clean up expired OTP records"""
        OTPVerification.cleanup_expired()
        self.message_user(request, _('Expired OTP records have been cleaned up.'))
    cleanup_expired_otps.short_description = _('Clean up expired OTP records')


@admin.register(GBEmployeeRequest)
class GBEmployeeRequestAdmin(admin.ModelAdmin):
    list_display = ('request_id', 'get_employee_info', 'get_supervisor_info', 'get_course_info', 
                   'status', 'supervisor_account_created', 'created_at', 'processed_at')
    list_filter = ('status', 'supervisor_account_created', 'created_at', 'processed_at')
    search_fields = ('employee__employee_number', 'employee__english_name', 
                    'supervisor__employee_number', 'supervisor__english_name',
                    'course_instance__course__name_en', 'user__username', 'user__email')
    ordering = ('-created_at',)
    date_hierarchy = 'created_at'
    readonly_fields = ('request_id', 'created_at', 'processed_at', 'supervisor_account_created')
    raw_id_fields = ('employee', 'supervisor', 'course_instance', 'user', 'supervisor_user', 'processed_by')
    
    def get_employee_info(self, obj):
        return f"{obj.employee.english_name or obj.employee.full_name} ({obj.employee.employee_number})"
    get_employee_info.short_description = _('Employee')
    get_employee_info.admin_order_field = 'employee__english_name'
    
    def get_supervisor_info(self, obj):
        supervisor_info = f"{obj.supervisor.english_name or obj.supervisor.full_name} ({obj.supervisor.employee_number})"
        if obj.supervisor_user:
            supervisor_info += f" - {obj.supervisor_user.email}"
        return supervisor_info
    get_supervisor_info.short_description = _('Supervisor')
    get_supervisor_info.admin_order_field = 'supervisor__english_name'
    
    def get_course_info(self, obj):
        return f"{obj.course_instance.course.name_en} ({obj.course_instance.start_date.strftime('%Y-%m-%d')})"
    get_course_info.short_description = _('Course')
    get_course_info.admin_order_field = 'course_instance__course__name_en'
    
    fieldsets = (
        (_('Request Information'), {
            'fields': ('request_id', 'employee', 'supervisor', 'course_instance', 'user', 'supervisor_user')
        }),
        (_('Request Details'), {
            'fields': ('request_message', 'status', 'approval_notes')
        }),
        (_('System Information'), {
            'fields': ('supervisor_account_created', 'processed_by', 'created_at', 'processed_at'),
            'classes': ('collapse',)
        }),
    )
    
    actions = ['approve_selected_requests', 'reject_selected_requests']
    
    def approve_selected_requests(self, request, queryset):
        """Action to approve selected enrollment requests"""
        approved_count = 0
        for gb_request in queryset.filter(status='PENDING'):
            try:
                gb_request.approve(
                    processed_by_user=request.user,
                    notes="Approved via admin interface"
                )
                approved_count += 1
            except Exception as e:
                self.message_user(
                    request, 
                    _('Error approving request {}: {}').format(gb_request.request_id, str(e)),
                    level='ERROR'
                )
        
        if approved_count > 0:
            self.message_user(
                request, 
                _('Successfully approved {} enrollment requests.').format(approved_count)
            )
    approve_selected_requests.short_description = _('Approve selected enrollment requests')
    
    def reject_selected_requests(self, request, queryset):
        """Action to reject selected enrollment requests"""
        rejected_count = 0
        for gb_request in queryset.filter(status='PENDING'):
            try:
                gb_request.reject(
                    processed_by_user=request.user,
                    notes="Rejected via admin interface"
                )
                rejected_count += 1
            except Exception as e:
                self.message_user(
                    request, 
                    _('Error rejecting request {}: {}').format(gb_request.request_id, str(e)),
                    level='ERROR'
                )
        
        if rejected_count > 0:
            self.message_user(
                request, 
                _('Successfully rejected {} enrollment requests.').format(rejected_count)
            )
    reject_selected_requests.short_description = _('Reject selected enrollment requests')
    
    def has_add_permission(self, request):
        """Prevent manual creation as these should be created by the enrollment process"""
        return False
