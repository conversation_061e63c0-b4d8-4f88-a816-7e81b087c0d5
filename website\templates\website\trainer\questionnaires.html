{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Questionnaires" %}{% endblock %}

{% block content %}
<div class="min-h-screen bg-[#172b56]">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-2">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-white">{% trans "Questionnaires" %}</h1>
            <p class="mt-2 text-gray-300">{% trans "Review and manage questionnaire responses" %}</p>
        </div>

        <!-- Filter Controls -->
        <div class="mb-6 bg-white/10 rounded-lg shadow-lg p-6 backdrop-blur-sm border border-white/20">
            <form method="get" class="flex flex-wrap items-center gap-4">
                <div>
                    <label for="status_filter" class="block text-sm font-medium text-white mb-1">{% trans "Status" %}</label>
                    <select id="status_filter" name="status" class="bg-white/10 border border-white/20 text-white text-sm rounded-lg focus:ring-primary focus:border-primary block w-full p-2.5">
                        <option value="all" {% if status_filter == 'all' %}selected{% endif %}>{% trans "All" %}</option>
                        <option value="pending" {% if status_filter == 'pending' %}selected{% endif %}>{% trans "Pending Review" %}</option>
                        <option value="completed" {% if status_filter == 'completed' %}selected{% endif %}>{% trans "Completed" %}</option>
                    </select>
                </div>
                <div class="mt-auto">
                    <button type="submit" class="bg-primary hover:bg-primary-dark text-white font-medium rounded-lg text-sm px-5 py-2.5 focus:outline-none">
                        {% trans "Filter" %}
                    </button>
                </div>
            </form>
        </div>

        <!-- Questionnaires List -->
        <div class="bg-white/10 rounded-lg shadow-lg backdrop-blur-sm border border-white/20 overflow-hidden">
            <div class="p-6 border-b border-white/10 bg-gradient-to-r from-purple-800/40 to-pink-900/40">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-purple-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <h2 class="text-xl font-semibold text-white">
                            {% if status_filter == 'pending' %}
                                {% trans "Pending Questionnaire Responses" %}
                            {% elif status_filter == 'completed' %}
                                {% trans "Completed Questionnaire Reviews" %}
                            {% else %}
                                {% trans "All Questionnaire Responses" %}
                            {% endif %}
                        </h2>
                    </div>
                    <span class="px-3 py-1 text-xs font-medium rounded-full bg-purple-500/30 text-purple-300 border border-purple-500/30">
                        {{ questionnaire_responses.count|default:"0" }} {% trans "Responses" %}
                    </span>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-white/10">
                    <thead class="bg-white/5">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Title" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Course" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Student" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Submitted" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Status" %}
                            </th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-white/70 uppercase tracking-wider">
                                {% trans "Actions" %}
                            </th>
                        </tr>
                    </thead>
                    <tbody class="bg-white/5 divide-y divide-white/10">
                        {% if questionnaire_responses %}
                            {% for response in questionnaire_responses %}
                                <tr class="hover:bg-white/10 cursor-pointer transition-colors duration-150">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        {{ response.questionnaire.title }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        {{ response.course.name_en }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        {{ response.user.get_full_name|default:response.user.username }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        {{ response.created_at|date:"d M Y, H:i" }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm">
                                        {% if response.status == 'PENDING_TRAINER_REVIEW' %}
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-yellow-500/20 text-yellow-300 border border-yellow-500/30">
                                                {% trans "Pending Review" %}
                                            </span>
                                        {% elif response.status == 'COMPLETED_TRAINER_REVIEWED' %}
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-green-500/20 text-green-300 border border-green-500/30">
                                                {% trans "Reviewed" %}
                                            </span>
                                        {% else %}
                                            <span class="px-2 py-1 text-xs font-medium rounded-full bg-blue-500/20 text-blue-300 border border-blue-500/30">
                                                {{ response.get_status_display }}
                                            </span>
                                        {% endif %}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-white">
                                        {% if response.status == 'PENDING_TRAINER_REVIEW' %}
                                            <a href="{% url 'website:review_questionnaire_response' response_id=response.response_id %}" class="px-3 py-1.5 bg-yellow-600/60 hover:bg-yellow-600/80 rounded text-white font-medium transition-colors inline-flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                                </svg>
                                                {% trans "Review" %}
                                            </a>
                                        {% else %}
                                            <a href="{% url 'website:review_questionnaire_response' response_id=response.response_id %}" class="px-3 py-1.5 bg-blue-600/60 hover:bg-blue-600/80 rounded text-white font-medium transition-colors inline-flex items-center">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                                </svg>
                                                {% trans "View" %}
                                            </a>
                                        {% endif %}
                                    </td>
                                </tr>
                            {% endfor %}
                        {% else %}
                            <tr>
                                <td colspan="6" class="px-6 py-4 text-center text-sm text-white/70">
                                    {% if status_filter == 'pending' %}
                                        {% trans "No pending questionnaires to review" %}
                                    {% elif status_filter == 'completed' %}
                                        {% trans "No completed questionnaire reviews" %}
                                    {% else %}
                                        {% trans "No questionnaire responses found" %}
                                    {% endif %}
                                </td>
                            </tr>
                        {% endif %}
                    </tbody>
                </table>
            </div>
        </div>

        <!-- Back to Dashboard Button -->
        <div class="mt-6 flex justify-start">
            <a href="{% url 'website:trainer_dashboard' %}" class="inline-flex items-center px-4 py-2 bg-white/10 hover:bg-white/20 rounded-lg transition-colors text-white text-sm">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 17l-5-5m0 0l5-5m-5 5h12" />
                </svg>
                {% trans "Back to Dashboard" %}
            </a>
        </div>
    </div>
</div>
{% endblock %} 