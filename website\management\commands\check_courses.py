from django.core.management.base import BaseCommand
from django.utils import timezone
from website.models import Course, CourseInstance


class Command(BaseCommand):
    help = 'Check courses and course instances in the database'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('=== COURSE ANALYSIS ==='))
        
        # Check all courses
        courses = Course.objects.all()
        self.stdout.write(f'\nTotal Courses: {courses.count()}')
        
        for course in courses:
            self.stdout.write(f'\nCourse ID: {course.course_id}')
            self.stdout.write(f'  Name: {course.name_en}')
            self.stdout.write(f'  Status: {course.status}')
            self.stdout.write(f'  Is Active: {course.is_active}')
            self.stdout.write(f'  Category: {course.get_category_display()}')
            
            # Check course instances for this course
            instances = CourseInstance.objects.filter(course=course)
            self.stdout.write(f'  Total Instances: {instances.count()}')
            
            for instance in instances:
                self.stdout.write(f'    Instance ID: {instance.instance_id}')
                self.stdout.write(f'      Course Type: {instance.course_type}')
                self.stdout.write(f'      Published: {instance.published}')
                self.stdout.write(f'      Is Active: {instance.is_active}')
                self.stdout.write(f'      Start Date: {instance.start_date}')
                self.stdout.write(f'      End Date: {instance.end_date}')
                self.stdout.write(f'      Capacity: {instance.capacity}')
                
                # Check if this instance would be visible to individual users
                now = timezone.now()
                is_visible_to_individual = (
                    instance.is_active and 
                    instance.published and 
                    instance.course_type == 'INDIVIDUAL' and
                    course.is_active
                )
                self.stdout.write(f'      Visible to Individual Users: {is_visible_to_individual}')
                
                if not is_visible_to_individual:
                    reasons = []
                    if not instance.is_active:
                        reasons.append('Instance not active')
                    if not instance.published:
                        reasons.append('Instance not published')
                    if instance.course_type != 'INDIVIDUAL':
                        reasons.append(f'Course type is {instance.course_type}, not INDIVIDUAL')
                    if not course.is_active:
                        reasons.append('Course not active')
                    self.stdout.write(f'      Reasons not visible: {", ".join(reasons)}')
        
        # Summary for individual users
        self.stdout.write(self.style.SUCCESS('\n=== SUMMARY FOR INDIVIDUAL USERS ==='))
        
        # Get courses that should be visible to individual users
        active_instances = CourseInstance.objects.filter(
            is_active=True,
            published=True,
            course_type='INDIVIDUAL'
        ).select_related('course')
        
        course_ids = active_instances.values_list('course_id', flat=True).distinct()
        visible_courses = Course.objects.filter(course_id__in=course_ids, is_active=True)
        
        self.stdout.write(f'Courses visible to individual users: {visible_courses.count()}')
        for course in visible_courses:
            self.stdout.write(f'  - {course.name_en} (ID: {course.course_id})')
            
        if visible_courses.count() == 0:
            self.stdout.write(self.style.WARNING('No courses are currently visible to individual users!'))
            self.stdout.write('To make courses visible to individual users, you need:')
            self.stdout.write('1. Active courses (Course.is_active = True)')
            self.stdout.write('2. Course instances with course_type = "INDIVIDUAL"')
            self.stdout.write('3. Course instances that are published (CourseInstance.published = True)')
            self.stdout.write('4. Course instances that are active (CourseInstance.is_active = True)')
