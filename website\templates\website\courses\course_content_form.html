{% extends 'website/base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{{ title }} - {{ course.name_en }}{% endblock %}

{% block extra_head %}
<style>
    /* Enhanced select element styling */
    select {
        background-color: #1a2542 !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        color: white !important;
        padding-right: 2.5rem !important;
    }
    
    select:hover {
        border-color: rgba(255, 255, 255, 0.3) !important;
        background-color: #20304f !important;
    }
    
    select:focus {
        border-color: var(--color-primary) !important;
        box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.3) !important;
        outline: none !important;
    }
    
    /* Styling for the dropdown container */
    .dropdown-container {
        position: relative;
    }
    
    /* Style for the dropdown when focused */
    .dropdown-container:focus-within {
        border-radius: 0.375rem;
        box-shadow: 0 0 0 2px rgba(var(--color-primary-rgb), 0.3);
    }
    
    /* Improve content type dropdown visibility */
    select[name="content_type"] {
        font-weight: 500;
        letter-spacing: 0.01em;
    }
    
    select[name="content_type"] option {
        background-color: #1a2542;
        color: white;
        padding: 8px;
    }
    
    /* File error styling */
    .file-error {
        border: 1px solid rgba(239, 68, 68, 0.5) !important;
        background-color: rgba(239, 68, 68, 0.1) !important;
    }
    
    .file-error-message {
        color: #ef4444;
        padding: 0.5rem;
        margin-top: 0.5rem;
        background-color: rgba(239, 68, 68, 0.1);
        border: 1px solid rgba(239, 68, 68, 0.2);
        border-radius: 0.375rem;
    }
</style>

<script>
    // Supported file extensions must match those in CourseContentForm.SUPPORTED_EXTENSIONS
    const supportedExtensions = ['pdf', 'pptx', 'docx', 'jpg', 'jpeg', 'png', 'mp4'];
    
    document.addEventListener('DOMContentLoaded', function() {
        const fileInput = document.querySelector('input[type="file"]');
        const submitButton = document.querySelector('button[type="submit"]');
        const form = document.querySelector('form');
        let fileErrorElement = null;
        
        // Function to validate file extension
        function validateFileExtension(file) {
            if (!file) return true;
            
            const fileName = file.name;
            const fileExtension = fileName.split('.').pop().toLowerCase();
            
            return supportedExtensions.includes(fileExtension);
        }
        
        // Function to show error message
        function showFileError(input, message) {
            // Remove previous error if exists
            if (fileErrorElement) {
                fileErrorElement.remove();
                fileErrorElement = null;
            }
            
            // Add error class to input
            input.classList.add('file-error');
            
            // Create error message
            fileErrorElement = document.createElement('div');
            fileErrorElement.className = 'file-error-message';
            fileErrorElement.innerHTML = `
                <div class="flex items-start">
                    <svg class="h-5 w-5 text-red-400 mt-0.5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                    </svg>
                    <div>
                        <p class="font-medium">${message}</p>
                        <p class="mt-1">Supported formats: ${supportedExtensions.map(ext => ext.toUpperCase()).join(', ')}</p>
                    </div>
                </div>
            `;
            
            // Insert error message after file input
            input.parentNode.insertBefore(fileErrorElement, input.nextSibling);
            
            // Disable submit button
            submitButton.disabled = true;
            submitButton.classList.add('opacity-50', 'cursor-not-allowed');
        }
        
        // Function to clear error
        function clearFileError(input) {
            if (fileErrorElement) {
                fileErrorElement.remove();
                fileErrorElement = null;
            }
            
            input.classList.remove('file-error');
            
            // Enable submit button
            submitButton.disabled = false;
            submitButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
        
        // Validate on file selection
        fileInput.addEventListener('change', function(e) {
            const file = this.files[0];
            
            if (file) {
                if (!validateFileExtension(file)) {
                    showFileError(this, 'Unsupported file format');
                } else {
                    clearFileError(this);
                }
            } else {
                clearFileError(this);
            }
        });
        
        // Validate before form submission
        form.addEventListener('submit', function(e) {
            const file = fileInput.files[0];
            
            if (file && !validateFileExtension(file)) {
                e.preventDefault();
                showFileError(fileInput, 'Unsupported file format');
                
                // Scroll to error
                fileErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        });
    });
</script>
{% endblock %}

{% block content %}
<div class="min-h-screen">
    {% include 'website/header.html' %}

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Notifications -->
        {% if messages %}
        <div class="mb-6" id="notifications-container">
            {% for message in messages %}
                <div class="{% if message.tags == 'success' %}bg-green-500/10 border-green-500/30 text-green-400{% elif message.tags == 'warning' %}bg-amber-500/10 border-amber-500/30 text-amber-400{% elif message.tags == 'error' %}bg-red-500/10 border-red-500/30 text-red-400{% else %}bg-blue-500/10 border-blue-500/30 text-blue-400{% endif %} p-4 rounded-md border mb-3 notification-message" data-type="{{ message.tags }}">
                    <div class="flex">
                        <div class="flex-shrink-0">
                            {% if message.tags == 'success' %}
                                <svg class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                                </svg>
                            {% elif message.tags == 'warning' %}
                                <svg class="h-5 w-5 text-amber-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            {% elif message.tags == 'error' %}
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            {% else %}
                                <svg class="h-5 w-5 text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            {% endif %}
                        </div>
                        <div class="ml-3">
                            <p class="text-sm">{{ message }}</p>
                        </div>
                    </div>
                </div>
            {% endfor %}
        </div>
        {% endif %}

        <!-- Page Header -->
        <div class="flex items-center space-x-3 mb-8">
            <a href="{% url 'website:course_content_list' course.course_id %}" class="text-white/70 hover:text-white">
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18"/>
                </svg>
            </a>
            <h1 class="text-2xl font-bold text-white">{{ title }}</h1>
        </div>

        <!-- Content Form -->
        <div class="bg-white/5 backdrop-blur-md rounded-lg border border-white/10 overflow-hidden">
            <div class="p-6 border-b border-white/10">
                <h2 class="text-xl font-semibold text-white">{% trans "Course" %}: {{ course.name_en }}</h2>
                <p class="text-white/70 text-sm mt-1">
                    {% trans "Add content like presentations, documents, images, and videos to your course." %}
                </p>
            </div>

            <div class="p-6">
                <form method="post" enctype="multipart/form-data" class="space-y-6">
                    {% csrf_token %}
                    
                    {% if form.non_field_errors %}
                    <div class="bg-red-900/30 border border-red-500/30 rounded-md p-4 mb-6">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-red-300">{% trans "Error" %}</h3>
                                <div class="mt-2 text-sm text-red-200">
                                    <ul class="list-disc pl-5 space-y-1">
                                        {% for error in form.non_field_errors %}
                                        <li>{{ error }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Title -->
                    <div>
                        <label for="{{ form.title.id_for_label }}" class="block text-sm font-medium text-white">
                            {% trans "Title" %} <span class="text-red-500">*</span>
                        </label>
                        {{ form.title }}
                        {% if form.title.errors %}
                        <p class="mt-2 text-sm text-red-400">{{ form.title.errors.0 }}</p>
                        {% endif %}
                    </div>

                    <!-- Content Type -->
                    <div>
                        <label for="{{ form.content_type.id_for_label }}" class="block text-sm font-medium text-white">
                            {% trans "Content Type" %} <span class="text-red-500">*</span>
                        </label>
                        <div class="relative mt-1 dropdown-container">
                            {{ form.content_type }}
                            <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-white">
                                <svg class="h-5 w-5 text-white/70" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                            </div>
                        </div>
                        {% if form.content_type.errors %}
                        <p class="mt-2 text-sm text-red-400">{{ form.content_type.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-sm text-white/70">{% trans "Select the type of content you are uploading" %}</p>
                    </div>

                    <!-- Description -->
                    <div>
                        <label for="{{ form.description.id_for_label }}" class="block text-sm font-medium text-white">
                            {% trans "Description" %}
                        </label>
                        {{ form.description }}
                        {% if form.description.errors %}
                        <p class="mt-2 text-sm text-red-400">{{ form.description.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-sm text-white/50">{% trans "Brief explanation of what this content contains." %}</p>
                    </div>

                    <!-- File Upload -->
                    <div>
                        <label for="{{ form.file.id_for_label }}" class="block text-sm font-medium text-white">
                            {% trans "File" %} <span class="text-red-500">*</span>
                        </label>
                        {% if course_content and course_content.file %}
                        <div class="mt-2 mb-2">
                            <span class="text-white/70 text-sm">
                                {% trans "Current file" %}: <a href="{% url 'website:download_course_content' course.course_id course_content.content_id %}" class="text-primary hover:underline">{{ course_content.file.name|slice:"15:" }}</a>
                            </span>
                        </div>
                        {% endif %}
                        {{ form.file }}
                        {% if form.file.errors %}
                        <p class="mt-2 text-sm text-red-400">{{ form.file.errors.0 }}</p>
                        {% endif %}
                        <p class="mt-1 text-sm text-white/50">{% trans "Supported formats include PDF, PPTX, DOCX, JPG, PNG, MP4, etc." %}</p>
                    </div>

                    <!-- Form Actions -->
                    <div class="flex justify-end space-x-3 pt-4">
                        <a href="{% url 'website:course_content_list' course.course_id %}" class="px-4 py-2 border border-white/10 hover:bg-white/5 rounded-md text-white">
                            {% trans "Cancel" %}
                        </a>
                        <button type="submit" class="px-4 py-2 bg-primary hover:bg-primary/90 rounded-md text-white">
                            {{ submit_text }}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %} 