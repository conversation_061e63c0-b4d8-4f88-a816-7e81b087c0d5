from django.urls import path, include
from rest_framework import routers
from rest_framework.authtoken.views import obtain_auth_token

from project.settings import DEBUG

router = routers.DefaultRouter()

urlpatterns = [
    path('', include(router.urls)),
    # path('api-auth/', include('rest_framework.urls')),
    # path('api-token-auth/', obtain_auth_token, name='api_token_auth'),
]
if DEBUG:
    urlpatterns += [path('api-auth/', include('rest_framework.urls'))]